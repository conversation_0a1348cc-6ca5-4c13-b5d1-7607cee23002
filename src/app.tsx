import Footer from '@/components/Footer';
import RightContent from '@/components/RightContent';
import { Settings as LayoutSettings } from '@ant-design/pro-components';
import { history, Link, Navigate, RequestConfig, RunTimeLayoutConfig } from '@umijs/max';
import defaultSettings from '../config/defaultSettings';
import { errorConfig } from './requestErrorConfig';

import { LinkOutlined } from '@ant-design/icons';
import UnAccessible from '../src/pages/403';
import { currentInfo } from './services/oa/auth';
import { BASE_URL } from './utils/setting';

const isDev = process.env.NODE_ENV === 'development';
const loginPath = '/user/login';

// 注入 COMMIT_HASH
window.COMMIT_HASH = process.env.COMMIT_HASH;

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.UserInfo;
  loading?: boolean;
  fetchUserInfo?: () => Promise<API.UserInfo | undefined>;
}> {
  const fetchUserInfo = async () => {
    try {
      const user = await currentInfo();
      const { code, data } = user || {};
      if (code === 200) {
        const isPermissionAccount = data?.pagePermissions?.includes('ROLE');
        localStorage.setItem('IS_PERMISSION_ACCOUNT', `${isPermissionAccount}`);
        return data;
      }
    } catch (error) {
      history.push(loginPath);
    }
    return {};
  };
  // 如果不是登录页面，执行

  const token = localStorage.getItem('RKLINK_OA_TOKEN') || '';

  /**
   * pathname==='/'
   * 如果token存在-->会跳转到默认首页
   * 如果token不存在-->会跳转到登录页面
   * 所以如果在登录也有可能为判断 pathname 可能为 '/' 或者 loginPath,
   * 当pathname==='/' 并且没有token 是登录页面
   * 当pathname === loginPath 是登录页面
   */

  // 如果是登录页面

  if (
    history.location.pathname.includes(loginPath) ||
    (history.location.pathname === '/' && !token)
  ) {
    return {
      settings: defaultSettings,
    };
  }

  const currentUser = await fetchUserInfo();
  return {
    currentUser,
    fetchUserInfo,
    settings: defaultSettings,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState }) => {
  // const formRef = createRef<FormInstance>();

  return {
    breakpoint: 'xxl',
    waterMarkProps: {
      content: initialState?.currentUser?.username,
      // gapX: 70,
      // gapY: 70,
    },
    rightContentRender: () => <RightContent />,
    footerRender: () => <Footer />,
    onPageChange: () => {
      // 如果没有登录，重定向到 login
      const token = localStorage.getItem('RKLINK_OA_TOKEN');
      if (!token && location.pathname !== loginPath) {
        history.push(loginPath);
      }
    },
    unAccessible: <UnAccessible />,
    token: {
      pageContainer: {
        paddingBlockPageContainerContent: 24,
        paddingInlinePageContainerContent: 24,
      },
      sider: {
        colorMenuBackground: '#fff',
        colorTextMenuSelected: '#13c2c2',
        colorTextMenuActive: '#13c2c2',
        colorTextMenuItemHover: '#13c2c2',
      },
    },
    links: isDev
      ? [
          <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
            <LinkOutlined />
            <span>OpenAPI 文档</span>
          </Link>,
        ]
      : [],
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request: RequestConfig = {
  timeout: 30000,
  headers: { 'X-Requested-With': 'XMLHttpRequest' },
  baseURL: BASE_URL,
  ...errorConfig,
};

export const patchClientRoutes = async ({ routes }: any) => {
  const isPermissionAccount = localStorage.getItem('IS_PERMISSION_ACCOUNT') === 'true';
  if (isPermissionAccount) {
    routes.unshift({
      path: '/',
      element: <Navigate to="/permission" replace />,
    });
  }
};
