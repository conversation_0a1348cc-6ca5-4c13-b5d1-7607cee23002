.ant-layout {
  min-height: 100vh;
}
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

// login
.ant-pro-form-login-container {
  flex: none;
  justify-content: center;
  .ant-pro-form-login-header {
    margin-bottom: 48px;
  }
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

// 布局

.ant-pro-layout .ant-pro-layout-content {
  min-width: 1140px;
}

.ant-layout-content.ant-pro-layout-content .ant-pro-page-container {
  min-height: calc(100vh - 124px);
  .ant-pro-grid-content {
    .ant-pro-page-container-children-container {
      padding-block-end: 0;
    }
  }
}
.ant-pro-global-footer {
  margin: 24px;
}
.ant-breadcrumb {
  li {
    a {
      &:hover {
        background-color: transparent;
      }
    }
    &:last-child a {
      color: @colorText;
    }
  }
}
.ant-pro-page-container-children-content {
  padding-top: 24px !important;
}
.ant-page-header {
  background-color: #fff;
}
.ant-pro-layout-has-footer {
  margin-block-start: 0 !important;
}

.ant-typography .ant-typography-copy {
  color: var(--primary-color);
  &:not(:disabled):hover {
    color: var(--primary-hover-color);
  }
}

.ant-btn-link {
  color: var(--primary-color);
  &:not(:disabled):hover {
    color: var(--primary-hover-color);
  }
}
.ant-typography a {
  color: var(--primary-color) !important;
}

// modal

.ant-modal {
  .ant-modal-header {
    margin-bottom: 24px;
  }
  .ant-modal-confirm-content {
    margin-block-start: 24px !important;
    margin-inline-start: 24px !important;
  }
}

.ant-modal-confirm-btns {
  .ant-btn-primary:not(:disabled) {
    background-color: var(--primary-color);
    &:hover {
      background-color: var(--primary-hover-color);
    }
    &:active {
      color: #fff;
      background-color: #08979c;
    }
  }
  .ant-btn-default:not(:disabled):hover {
    color: var(--cyan-5);
    border-color: var(--cyan-5);
  }
}

.ant-modal-content {
  .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
    border-color: var(--cyan-5);
  }
  .ant-input-affix-wrapper:focus,
  .ant-input-affix-wrapper-focused {
    border-color: var(--cyan-5);
  }
}

// logo
.ant-pro-global-header-logo img,
.ant-pro-form-login-logo img {
  // filter: invert(13%) sepia(97%) saturate(4906%) hue-rotate(174deg) brightness(160%) contrast(90%) grayscale(.14);
  // filter: invert(13%) sepia(97%) saturate(4906%) hue-rotate(174deg) brightness(160%) contrast(90%)
  //   grayscale(0.4);
}

.ant-layout-content.ant-pro-layout-content .ant-pro-page-container.detail-container {
  min-height: calc(100vh - 190px);
  .ant-form {
    padding: 16px 24px;
    background-color: #fff;
  }
}

// 时间
.ant-picker-panel-container {
  .ant-picker-footer {
    .ant-picker-today-btn {
      color: var(--primary-color);
    }
  }
}

.ant-picker {
  width: 100%;
}

// collapse
.ant-collapse-item {
  .ant-collapse-header {
    .ant-collapse-header-text {
      font-weight: 600;
      font-size: 16px;
    }
  }
}

.ant-typography {
  .ant-typography-edit,
  .ant-typography-copy {
    color: var(--primary-color) !important;
  }
}

.ant-form-item-control-input {
  flex: 1;
}

.ant-table-cell-fix-right {
  > div {
    justify-content: center;
  }
}

//
.ant-collapse-item {
  .ant-collapse-header {
    padding: 12px 0 !important;
  }
  .ant-collapse-content-box {
    padding: 16px 0 !important;
  }
}

.ant-table-cell {
  .ant-input-number {
    width: 100%;
  }
  .ant-form-item-control {
    > div {
      width: 100% !important;
    }
  }
}

.ant-form-item {
  .ant-form-item-label {
    > label {
      color: rgba(0, 0, 0, 0.65);
    }
  }
}

// input

.ant-input {
  word-break: break-all;
}

// 文件上传 图片卡片样式
.ant-upload-list-picture-card {
  .ant-upload-list-item-actions {
    .ant-btn {
      width: 36px !important;
    }
    .anticon {
      &.anticon-eye,
      &.anticon-delete {
        margin: 0 10px !important;
      }
    }
  }
}

// 菜单
.ant-layout-sider-collapsed {
  .ant-layout-sider-children {
    display: block !important;
  }
}

.ant-input-number-status-error {
  border-color: #ff4d4f !important;
}
