:root {
  --primary-color: #13c2c2; // 修改主题色
  --primary-hover-color: #36cfc9; // 修改主题色
  --cyan-5: #36cfc9;
}

@font-face {
  font-weight: 700;
  font-family: '阿里妈妈数黑体 Bold';
  src: url('/fonts/noL0FY2kynRv6UZONbDeH.woff2') format('woff2'),
    url('/fonts/noL0FY2kynRv6UZONbDeH.woff') format('woff');
  font-display: swap;
}
html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.colorWeak {
  filter: invert(80%);
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

.rklink-container {
  background-color: #fff;
}
.rklink-title {
  display: block;
  margin-bottom: 16px;
  color: @colorText;
  font-weight: 600;
}
.rklink-des-title {
  flex: auto;
  overflow: hidden;
  color: rgba(0, 0, 0, 0.88);
  font-weight: 600;
  font-size: 16px;
  line-height: 1.5;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 在使用浏览器保存的数据时 输入框的样式
input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  // 背景颜色
  background-color: transparent !important;
  // 背景图片
  background-image: none !important;
  //设置input输入框的背景颜色为透明色
  -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
  transition: background-color 50000s ease-in-out 0s;
}

.rklink-form-box {
  padding: 24px 24px 0 24px;
  overflow: hidden;
  background-color: #fff;
}

a {
  color: var(--primary-color);
  &:hover {
    color: var(--primary-hover-color);
  }
}

.inner-table {
  .ant-pro-card .ant-pro-card-body {
    padding-inline: 0;
  }
}

.rk-none {
  display: none;
}

.rklink-change-password {
  .ant-modal-content {
    .ant-modal-confirm-content {
      max-width: 100% !important;
      margin-inline-start: 0 !important;
      margin-inline-end: 0 !important;
    }
  }
}
.rklink-statistic {
  .ant-statistic-content {
    font-size: 20px;
    white-space: nowrap;
    text-overflow: ellipsis;
    .ant-statistic-content-value,
    .ant-statistic-content-value-int {
      font-size: 20px !important;
    }
  }
}

.inner-table-link {
  height: auto;
  padding: 0;
  line-height: 16px;
}

.rk-a-span {
  span {
    color: var(--primary-color);
  }
}

.rk-page-header {
  padding: 16px 24px 0 24px;
}

/*滚动条样式*/
::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 12px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 12px;
}
::-webkit-scrollbar-thumb {
  border: none;
  /*滚动条里面小方块*/
  border-radius: 0;
}
::-webkit-scrollbar-thumb:hover {
  background: #d4d9e2;
  border: 4px solid rgba(0, 0, 0, 0);
  /*滚动条里面小方块*/
  border-radius: 12px;
  box-shadow: 6px 6px #d4d9e2 inset;
}
::-webkit-scrollbar-track {
  background-color: transparent;
  /*滚动条里面轨道*/
  border-radius: 0;
}
