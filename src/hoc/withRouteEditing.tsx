import React from 'react';
import { useParams } from 'react-router-dom';

export interface WithRouteEditingProps {
  isEditPage: boolean;
  id: string;
}

const withRouteEditing = <P extends WithRouteEditingProps>(Component: React.ComponentType<P>) => {
  return (props: Omit<P, keyof WithRouteEditingProps>) => {
    const { id } = useParams();
    const isEditPage = !!id;

    // Merge the original component props with the route editing props
    const mergedProps = {
      ...props,
      isEditPage,
      id,
    } as P;

    return <Component {...mergedProps} />;
  };
};

export default withRouteEditing;
