// 手机号校验
export const phoneReg = /^1[3-9]\d{9}$/;

// 密码
export const passwordReg = /(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,}/;

// 名称
export const nameReg = /[a-zA-Z\u4e00-\u9fa5]/;

// ip
export const IPReg =
  /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;

// port
export const PortReg = /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/;

//身份证号
export const IdCard = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;

/**
 * 该正则表达式用于匹配包含中文“从”到“到”的字符串。
 * “从”和“到”之间必须至少有一个中文字符。
 * 例如：“从A到B”不会被匹配，而“从一到二”会被匹配。
 */
export const chineseRangePattern = /^从[\u4e00-\u9fa5]+到[\u4e00-\u9fa5]+$/;
