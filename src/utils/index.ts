import { RequestOptionsType } from '@ant-design/pro-components';
import { message } from 'antd';
import dayjs from 'dayjs';
import forge from 'node-forge';
import { Key, SyntheticEvent } from 'react';
import { PRIVATEKEY, PUBLICKEY } from './setting';

/**
 * 取消事件冒泡
 * @param e
 */
export function cancelBubble(e: SyntheticEvent) {
  e.stopPropagation();
  e.nativeEvent.stopImmediatePropagation();
}

export function renderEmpty(value: string | number | undefined) {
  return value ?? '-';
}

/**
 * Key[] 转antd option
 * @param arr Key[]
 * @returns option
 */

export const getOptions = (arr?: Key[]): RequestOptionsType[] => {
  if (!arr || !arr.length) return [];
  return arr.map((item) => ({
    value: item,
    label: item,
  }));
};

/**
 * 获取表格参数
 * @param 表格参数
 * @returns 分页表格所需参数
 */

export const getTableParams = (params: Record<string, any>): BaseListRequest => {
  const { current, pageSize, restProps } = params;
  return {
    pageNum: current,
    pageSize,
    ...restProps,
  };
};

// 接口修改、新增成功 回调返回上一级
export const onSuccessAndGoBack = (res: API.ResultAuthResp) => {
  if (res.code === 200) {
    message.success('操作成功！');
    history.go(-1);
  }
};

// 接口修改、新增成功 刷新页面
export const onSuccessAndRefresh = (
  res: API.ResultAuthResp,
  refresh: ((delta?: number | undefined) => void) | any,
) => {
  if (res.code === 200) {
    message.success('保存成功！');
    refresh();
  }
};

// option2enum
export const option2enum = (options: { value: React.Key; label: string }[]) => {
  const obj: Record<string, any> = {};
  options.forEach((item) => {
    const { value, label } = item;
    obj[value] = { text: label, ...item };
  });
  return obj;
};

/**
 * 字符串、int转时间
 * @param dateString、dateInt
 * @returns
 */

export const formatDate = (text: string | number) => {
  if (!text) return '-';
  return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
};

export const queryPagingTable = async <U>(
  params: AntTableParams & { [k: string]: any },
  api?: (data: U) => Promise<Record<string, any>>,
) => {
  if (!api)
    return {
      data: [],
      success: true,
      total: 0,
    };
  const { current, pageSize, search, filter, scope, extra, sort, ...restProps } = params;
  const data = {
    pageNum: current,
    pageSize,
    search: {
      ...search,
      ...restProps,
    },
    ...sort,
    extra,
    filter,
    scope,
  };
  const msg = await api(data as U);
  return {
    ...msg?.data,
    data: msg?.data?.records || [],
    success: true,
    total: msg?.data?.total,
  };
};

/**
 *  获取随机id
 */
export const getRandomId = () => {
  return (Math.random() * 1000000).toFixed(0);
};

/**
 * 描述信息
 * @param value
 * @returns
 */
export function renderDescriptions(value: string | number | undefined) {
  return value ?? '-';
}

/**
 * 获取form数据
 * @param params 参数
 * @param ready 是否请求
 * @param api 接口名称
 * @returns
 */

export const queryFormData = async <U>(
  params: { [k: string]: any },
  ready: boolean,
  api?: (data: U) => Promise<Record<string, any>>,
) => {
  if (!ready) return {};
  if (!api) return {};
  const msg = await api(params as U);
  if (msg.code === 200) {
    return msg.data;
  }
  return {};
};

/**
 * 获取下拉数据
 * @param params 参数
 * @param api 接口名称
 * @returns
 */

export const queryOptions = async <U>(
  params: { [k: string]: any },
  api?: (data: U, options?: Record<string, any>) => Promise<Record<string, any>>,
  options?: { [key: string]: any },
) => {
  if (!api) return [];

  const msg = await api(params as U, options);
  if (msg.code === 200) {
    return msg?.data;
  }
  return [];
};

/**
 *  计算税率
 * @param amount 金额
 * @param rate 税率
 * @returns taxInclusiveAmount 税额  taxExclusiveAmount 不含税金额
 */
export const calcTaxRate = (amount: number = 0, rate: number = 0) => {
  // 不含税金额 = 含税金额 / (1+税率）
  const taxExclusiveAmount = Number((amount / (1 + rate / 100)).toFixed(2));

  return {
    // 税额 = 不含税金额 * 税率
    taxInclusiveAmount: (taxExclusiveAmount * (rate / 100)).toFixed(2),
    taxExclusiveAmount,
  };
};

// 查询审批分页
export const queryApprovalTable = async <U>(
  params: AntTableParams & { [k: string]: any },
  api?: (data: U) => Promise<Record<string, any>>,
) => {
  if (!api)
    return {
      data: [],
      success: true,
      total: 0,
    };
  const { current, pageSize, ...restProps } = params;
  const data = {
    pageNum: current,
    pageSize,
    ...restProps,
  };
  const msg = await api(data as U);
  return {
    data: msg?.data?.historicProcessInstanceRespList || [],
    success: true,
    total: msg?.data?.count,
  };
};

/**
 * 数组按照指定字段分组
 * @param arr 数组
 * @param groupingFields 数据分组的特定属性或字段
 */
export const groupedData = <T extends Record<string, any>, K extends keyof T>(
  arr: T[],
  groupingFields: K,
) => {
  return arr.reduce((result, current) => {
    const fields = current[groupingFields];
    if (!result[fields]) {
      result[fields] = [];
    }
    result[fields].push(current);
    return result;
  }, {} as Record<T[K], T[]>);
};

/**
 * 计算百分比
 * @param val 值
 * @param total 总数
 * @returns
 */
export const calculatePercentage = (val: number, total: number) => {
  if (typeof val !== 'number' || typeof total !== 'number' || total === 0) {
    return '';
  }

  // 计算百分比
  const percentage = (val * 100) / total;

  // 判断是否有小数
  const hasDecimal = percentage % 1 !== 0;

  // 根据有无小数来处理结果
  const formattedPercentage = hasDecimal ? percentage.toFixed(2) : Math.round(percentage);

  return formattedPercentage;
};

/**
 * 获取头像颜色
 * @param charCode
 * @returns
 */
export const getGradientColorFromCharCode = (charCode: number) => {
  if (isNaN(charCode)) return {};
  const hue = charCode % 360;
  const colorArray = [
    ['#21D4FD', '#B721FF'],
    ['#4158D0', '#C850C0'],
    ['#5BDED1', '#DC32B9'],
    ['#36A415', '#1F65A6'],
    ['#FBDA61', '#FF5ACD'],
    ['#3EECAC', '#EE74E1'],
  ];
  // 根据 charCode 计算索引
  const combinationIndex = charCode % colorArray.length;

  // 获取对应索引的颜色组合
  const randomCombination = colorArray[combinationIndex];
  const [color1, color2] = randomCombination;
  return {
    backgroundColor: color1,
    backgroundImage: `linear-gradient(${hue}deg, ${color1} 0%, ${color2} 100%)`,
    backgroundSize: 'cover',
    backgroundRepeat: 'no-repeat',
    border: 'none',
  };
};

const NodeRSA = require('node-rsa');

// RSA 加密函数
export const encryptWithRSA = (data: string) => {
  const key = new NodeRSA(PUBLICKEY, 'pkcs8-public', { encryptionScheme: 'pkcs1' });
  const encryptedData = key.encrypt(data, 'base64');
  return encryptedData;
};

// RSA 解密函数
export const decryptWithRSA = (encryptedData: string) => {
  const key = new NodeRSA(PRIVATEKEY, 'pkcs8-private', { encryptionScheme: 'pkcs1' });
  const decryptedData = key.decrypt(encryptedData, 'utf8');
  return decryptedData;
};

// 生成 AES-256 密钥（Base64 编码）
export function generateAESKey(): string {
  const keyBytes = forge.random.getBytesSync(32); // 32 bytes = 256 bits
  return forge.util.encode64(keyBytes);
}

// 手动添加 PKCS#7 填充
export function pad(data: string): string {
  const blockSize = 16;
  const paddingLen = blockSize - (data.length % blockSize);
  return data + String.fromCharCode(paddingLen).repeat(paddingLen);
}

// 移除 PKCS#7 填充
export function unpad(data: string): string {
  const paddingLen = data.charCodeAt(data.length - 1);
  return data.slice(0, data.length - paddingLen);
}
// 使用 AES-CBC 加密数据（固定 IV，不安全但可用）
export function encryptWithAES(data: string, key: string): string {
  try {
    const rawKey = forge.util.decode64(key);
    const iv = 'abcdef1234567890'; // 固定 IV

    const cipher = forge.cipher.createCipher('AES-CBC', rawKey);
    cipher.start({ iv });
    cipher.update(forge.util.createBuffer(pad(data), 'utf8'));
    cipher.finish();

    const encryptedData = cipher.output;
    return forge.util.encode64(encryptedData.getBytes()); // 直接返回加密内容
  } catch (error) {
    console.error('AES Encryption Error:', error);
    throw new Error('AES encryption failed');
  }
}

// 使用 AES-CBC 解密数据（固定 IV）
export function decryptWithAES(encryptedData: string, key: string): string {
  try {
    const rawKey = forge.util.decode64(key);
    const iv = 'abcdef1234567890';

    const decipher = forge.cipher.createDecipher('AES-CBC', rawKey);
    decipher.start({ iv });
    decipher.update(forge.util.createBuffer(forge.util.decode64(encryptedData)));
    decipher.finish();

    const decrypted = decipher.output.toString();
    return unpad(decrypted);
  } catch (error) {
    console.error('AES Decryption Error:', error);
    throw new Error('AES decryption failed');
  }
}

export const sortMap = {
  ascend: 'ASC',
  descend: 'DESC',
};
