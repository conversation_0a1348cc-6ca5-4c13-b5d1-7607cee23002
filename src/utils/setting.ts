export const defaultTableConfig: Record<string, any> = {
  rowKey: 'id',
  tableAlertRender: false,
  search: false,
  options: {
    reload: true,
    density: false,
    setting: false,
  },
  pagination: {
    defaultPageSize: 10,
    showSizeChanger: true,
    disabled: false,
  },
  scroll: { x: 'max-content' },
  form: {
    syncToUrl: true,
    syncToInitialValues: false,
  },
};

export const BASE_URL = '';

export const requiredRule = {
  required: true,
  message: '此项为必填项',
};

export const PUBLICKEY =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCqwP70v+H0SHaTWbyPJ+7KPRayXN+OhtfyjkL6tbOajodbFOCX43a3qWz83TWxucGEl2n5BnjJ+orAXaSYh26u8zPS//viLlhOUSj7JC7agnpxFVMDnkV7qg0fR0/Cl+tqnvmOmHKHMUVbJunDqPhCFx7ealc5ArradgsZOieJ4QIDAQAB';

export const PRIVATEKEY =
  'MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKrA/vS/4fRIdpNZvI8n7so9FrJc346G1/KOQvq1s5qOh1sU4JfjdrepbPzdNbG5wYSXafkGeMn6isBdpJiHbq7zM9L/++IuWE5RKPskLtqCenEVUwOeRXuqDR9HT8KX62qe+Y6YcocxRVsm6cOo+EIXHt5qVzkCutp2Cxk6J4nhAgMBAAECgYAA947QTbjyRbSCGLTL7wfrdlETzDXS2ak7On/GTZHFB/1b8hhWIophs+wAr/4U1yEuCG2n8t7rnLBycmuKT4sKiwZkz0vHjLX8h4nILSjVPyOKUG+4F0CM42Mq+NtWKjbqR06Gu2yfwhfxMgBOkTo1Uco5v7U6okWb7mARdTc2iQJBAOtAlGBSblzN8pg3KQ3tWjEeD2kajpAFrd9oNmn+ggiJlC5Om1Id3qx25XvN2Ybhh1uCuKjXLVxuiPTJSpteQlkCQQC50DNtmoTFkdFpd4Vkt5uZ69eWQokY4ZX8CX0am0d60RK05TTuH6bw+iK/KG+c82VKy9tPOpYdZgi6iO/eDsLJAkBNlQ2ZunWU+4JOupo6cpU075HreoubaZQOwkJ3sCxX/m802k2Uxi8CN2+05Y0gU04dWr3lSJk4C2f1OlD1bh95AkEApnCooHTYmWCUhDRbeGw/JRDse9v9khv/jVOyjDKrRIgiiYcGcoZHs3NbzH8b+yrKgMU6HS4NaSgMiYo/0ljGgQJBANuUZwh71RwLSpCdW3pwEc+orfXcFO7A/HhiUjQVRxCx/Vg/iETl8dFtL/cMx1yOtft6GMIpqZGuvp+CNY6h7g8=';
