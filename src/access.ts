/**
 * @see https://umijs.org/zh-CN/plugins/plugin-access
 * */
export default function access(initialState: { currentUser?: API.UserInfo } | undefined) {
  const { currentUser } = initialState ?? {};
  if (!currentUser) return {};
  // 权限字段
  const { pagePermissions: permissions = [], dataPermissions = [] } = currentUser;

  return {
    // 权限管理员
    isRoleAdministrator: permissions.includes('ROLE'),
    isRoleUser: !permissions.includes('ROLE'),
    // 个人中心
    canReadHome: permissions.includes('HOME'),
    // 开票信息
    canEditBillingInformation: permissions.includes('BILLING-INFO-EDIT'),
    // 人力资源
    canReadHR:
      permissions.includes('EMPLOYEE-VIEW') ||
      permissions.includes('LEAVE-APPLICATION-VIEW') ||
      permissions.includes('REVOKE-LEAVE-APPLICATION-VIEW') ||
      permissions.includes('ATTENDANCE') ||
      permissions.includes('ADDRESS-BOOK') ||
      permissions.includes('INVOICE-INFORMATION'),
    // 员工列表
    canReadEmployee: permissions.includes('EMPLOYEE-VIEW'),
    canAddEmployee: permissions.includes('EMPLOYEE-ADD'),
    canEditEmployee: permissions.includes('EMPLOYEE-EDIT'),
    canUpdateEmployee:
      permissions.includes('EMPLOYEE-EDIT') || permissions.includes('EMPLOYEE-ADD'),
    canResetEmployee: permissions.includes('EMPLOYEE-RESET'),
    canSuperEditEmployee:
      permissions.includes('EMPLOYEE-EDIT') && dataPermissions.includes('EMPLOYEE'),

    // 休假申请
    canReadLeaveApplication: permissions.includes('LEAVE-APPLICATION-VIEW'),
    canAddLeaveApplication: permissions.includes('LEAVE-APPLICATION-ADD'),
    canEditLeaveApplication: permissions.includes('LEAVE-APPLICATION-EDIT'),
    canDeleteLeaveApplication: permissions.includes('LEAVE-APPLICATION-DELETE'),
    // 销假申请
    canReadRevokeLeaveApplication: permissions.includes('REVOKE-LEAVE-APPLICATION-VIEW'),
    canAddRevokeLeaveApplication: permissions.includes('REVOKE-LEAVE-APPLICATION-ADD'),
    canEditRevokeLeaveApplication: permissions.includes('REVOKE-LEAVE-APPLICATION-EDIT'),
    canDeleteRevokeLeaveApplication: permissions.includes('REVOKE-LEAVE-APPLICATION-DELETE'),
    // 考勤
    canReadAttendance: permissions.includes('ATTENDANCE'),
    // 通讯录
    canReadAddressBook: permissions.includes('ADDRESS-BOOK'),
    // 开票信息
    canReadInvoiceInformation: permissions.includes('INVOICE-INFORMATION'),

    // 招聘申请
    canReadRecruitmentApplication: permissions.includes('RECRUITMENT-APPLICATION-VIEW'),
    canAddRecruitmentApplication: permissions.includes('RECRUITMENT-APPLICATION-ADD'),
    canEditRecruitmentApplication: permissions.includes('RECRUITMENT-APPLICATION-EDIT'),
    canDeleteRecruitmentApplication: permissions.includes('RECRUITMENT-APPLICATION-DELETE'),

    // 人才库
    canReadTalentPool: permissions.includes('TALENT-POOL-VIEW'),
    canAddTalentPool: permissions.includes('TALENT-POOL-ADD'),
    canEditTalentPool: permissions.includes('TALENT-POOL-EDIT'),
    canDeleteTalentPool: permissions.includes('TALENT-POOL-DELETE'),

    // 公告管理
    canAddAnnouncement: permissions.includes('ANNOUNCEMENT-ADD'),
    canEditAnnouncement: permissions.includes('ANNOUNCEMENT-EDIT'),
    canDeleteAnnouncement: permissions.includes('ANNOUNCEMENT-DELETE'),
    canPublishAnnouncement: permissions.includes('ANNOUNCEMENT-PUBLISH'),

    // 制度管理
    canReadSystemManagement: permissions.includes('SYSTEM-MANAGEMENT-VIEW'),
    canAddSystemManagement: permissions.includes('SYSTEM-MANAGEMENT-ADD'),
    canEditSystemManagement: permissions.includes('SYSTEM-MANAGEMENT-EDIT'),
    canDeleteSystemManagement: permissions.includes('SYSTEM-MANAGEMENT-DELETE'),
    canReadCollect: permissions.includes('SYSTEM-MANAGEMENT-COLLECT-VIEW'),

    // 资源使用申请
    canReadResourceApplication: permissions.includes('RESOURCE-APPLICATION-VIEW'),
    canAddResourceApplication: permissions.includes('RESOURCE-APPLICATION-ADD'),
    canEditResourceApplication: permissions.includes('RESOURCE-APPLICATION-EDIT'),
    canDeleteResourceApplication: permissions.includes('RESOURCE-APPLICATION-DELETE'),

    // 商务管理
    canReadBusiness:
      permissions.includes('QUALIFICATION-MANAGEMENT-VIEW') ||
      permissions.includes('BIDDING-ACCOUNT-MANAGEMENT-VIEW') ||
      permissions.includes('CERTIFICATE-MANAGEMENT-VIEW'),

    // 公司资质
    canReadQualificationManagement: permissions.includes('QUALIFICATION-MANAGEMENT-VIEW'),
    canAddQualificationManagement: permissions.includes('QUALIFICATION-MANAGEMENT-ADD'),
    canEditQualificationManagement: permissions.includes('QUALIFICATION-MANAGEMENT-EDIT'),
    canDeleteQualificationManagement: permissions.includes('QUALIFICATION-MANAGEMENT-DELETE'),
    // 投标账号管理
    canReadBiddingAccountManagement: permissions.includes('BIDDING-ACCOUNT-MANAGEMENT-VIEW'),
    canAddBiddingAccountManagement: permissions.includes('BIDDING-ACCOUNT-MANAGEMENT-ADD'),
    canEditBiddingAccountManagement: permissions.includes('BIDDING-ACCOUNT-MANAGEMENT-EDIT'),
    canDeleteBiddingAccountManagement: permissions.includes('BIDDING-ACCOUNT-MANAGEMENT-DELETE'),
    // 人员证书管理
    canReadCertificateManagement: permissions.includes('CERTIFICATE-MANAGEMENT-VIEW'),
    canAddCertificateManagement: permissions.includes('CERTIFICATE-MANAGEMENT-ADD'),
    canEditCertificateManagement: permissions.includes('CERTIFICATE-MANAGEMENT-EDIT'),
    canDeleteCertificateManagement: permissions.includes('CERTIFICATE-MANAGEMENT-DELETE'),

    // 业务伙伴
    canReadCRM:
      permissions.includes('CUSTOMER-VIEW') ||
      permissions.includes('SUPPLIER-VIEW') ||
      permissions.includes('SUPPLIER-VIEW') ||
      permissions.includes('CORRELATION-VIEW'),
    // 客户
    canReadCustomer: permissions.includes('CUSTOMER-VIEW'),
    canAddCustomer: permissions.includes('CUSTOMER-ADD'),
    canEditCustomer: permissions.includes('CUSTOMER-EDIT'),
    canSuperEditCustomer:
      permissions.includes('CUSTOMER-EDIT') && dataPermissions.includes('CUSTOMER'),
    canDeleteCustomer: permissions.includes('CUSTOMER-DELETE'),

    // 招标机构
    canReadTenderingAgency: permissions.includes('TENDERING-AGENCY-VIEW'),
    canAddTenderingAgency: permissions.includes('TENDERING-AGENCY-ADD'),
    canEditTenderingAgency: permissions.includes('TENDERING-AGENCY-EDIT'),
    canDeleteTenderingAgency: permissions.includes('TENDERING-AGENCY-DELETE'),
    canSuperEditTenderingAgency:
      permissions.includes('TENDERING-AGENCY-EDIT') && dataPermissions.includes('TENDERING-AGENCY'),

    // 供应商
    canReadSupplier: permissions.includes('SUPPLIER-VIEW'),
    canAddSupplier: permissions.includes('SUPPLIER-ADD'),
    canEditSupplier: permissions.includes('SUPPLIER-EDIT'),
    canDeleteSupplier: permissions.includes('SUPPLIER-DELETE'),
    canSuperEditSupplier:
      permissions.includes('SUPPLIER-EDIT') && dataPermissions.includes('SUPPLIER'),

    //单位名称与业务伙伴关联表
    canReadCorrelation: permissions.includes('CORRELATION-VIEW'),
    canAddCorrelation: permissions.includes('CORRELATION-ADD'),
    canDeleteCorrelation: permissions.includes('CORRELATION-DELETE'),
    canEditCorrelationSales: permissions.includes('CORRELATION-SALES'),

    // 合同管理
    canReadContract:
      permissions.includes('MASTER-CONTRACT-VIEW') ||
      permissions.includes('PURCHASE-CONTRACT-VIEW') ||
      permissions.includes('INTERNAL-CONTRACT-VIEW') ||
      permissions.includes('COOPERATION-AGREEMENT-VIEW'),
    // 主合同
    canReadMasterContract: permissions.includes('MASTER-CONTRACT-VIEW'),
    canAddMasterContract: permissions.includes('MASTER-CONTRACT-ADD'),
    canEditMasterContract: permissions.includes('MASTER-CONTRACT-EDIT'),
    canDeleteMasterContract: permissions.includes('MASTER-CONTRACT-DELETE'),
    // 售前成本配置
    canReadPreSalesCostConfig: permissions.includes('MASTER-CONTRACT-PRE-SALES-COST-CONFIG'),
    canSuperMasterContract: permissions.includes('MASTER-CONTRACT-SUPPER'),
    canExportMasterContract: permissions.includes('MASTER-CONTRACT-EXPORT'),
    // 采购合同
    canReadPurchaseContract: permissions.includes('PURCHASE-CONTRACT-VIEW'),
    canAddPurchaseContract: permissions.includes('PURCHASE-CONTRACT-ADD'),
    canEditPurchaseContract: permissions.includes('PURCHASE-CONTRACT-EDIT'),
    canDeletePurchaseContract: permissions.includes('PURCHASE-CONTRACT-DELETE'),
    canSuperPurchaseContract: permissions.includes('PURCHASE-CONTRACT-SUPPER'),
    canExportPurchaseContract: permissions.includes('PURCHASE-CONTRACT-EXPORT'),
    // 内部合同
    canReadInternalContract: permissions.includes('INTERNAL-CONTRACT-VIEW'),
    canAddInternalContract: permissions.includes('INTERNAL-CONTRACT-ADD'),
    canEditInternalContract: permissions.includes('INTERNAL-CONTRACT-EDIT'),
    canDeleteInternalContract: permissions.includes('INTERNAL-CONTRACT-DELETE'),
    canSuperInternalContract: permissions.includes('INTERNAL-CONTRACT-SUPPER'),
    canExportInternalContract: permissions.includes('INTERNAL-CONTRACT-EXPORT'),
    // 合作协议
    canReadCooperationAgreement: permissions.includes('COOPERATION-AGREEMENT-VIEW'),
    canAddCooperationAgreement: permissions.includes('COOPERATION-AGREEMENT-ADD'),
    canEditCooperationAgreement: permissions.includes('COOPERATION-AGREEMENT-EDIT'),
    canDeleteCooperationAgreement: permissions.includes('COOPERATION-AGREEMENT-DELETE'),

    // 财务
    canReadFinance:
      permissions.includes('EXPENSE-REIMBURSEMENT-LIST-VIEW') ||
      permissions.includes('TRAINING-EXPENSE-REIMBURSEMENT-LIST-VIEW') ||
      permissions.includes('EXPENSE-REIMBURSEMENT-STATISTICS') ||
      permissions.includes('GENERAL-VIEW') ||
      permissions.includes('CONTRACT-PAYMENT-VIEW') ||
      permissions.includes('INVOICE-TRANSACTION-RECORD-VIEW') ||
      permissions.includes('INVOICE-COLLECT-RECORD-VIEW') ||
      permissions.includes('INVOICE-PENDING-COLLECTION-VIEW') ||
      permissions.includes('RECEIPT-TRANSACTION-RECORD-VIEW') ||
      permissions.includes('OUTSTANDING-PAYMENT-TRANSACTION-RECORD-VIEW') ||
      permissions.includes('PAYMENT-RECORD-VIEW') ||
      permissions.includes('REFUND-HISTORY-VIEW') ||
      permissions.includes('ACCOUNT-RECEIVABLE') ||
      permissions.includes('SALARY-RECORD-VIEW'),
    // 报销管理
    canReadExpenseReimbursementManagement:
      permissions.includes('EXPENSE-REIMBURSEMENT-LIST-VIEW') ||
      permissions.includes('TRAINING-EXPENSE-REIMBURSEMENT-LIST-VIEW') ||
      permissions.includes('EXPENSE-REIMBURSEMENT-STATISTICS'),
    // 报销列表
    canReadExpenseReimbursement: permissions.includes('EXPENSE-REIMBURSEMENT-LIST-VIEW'),
    canAddExpenseReimbursement: permissions.includes('EXPENSE-REIMBURSEMENT-LIST-ADD'),
    canEditExpenseReimbursement: permissions.includes('EXPENSE-REIMBURSEMENT-LIST-EDIT'),
    canSuperEditExpenseReimbursement: permissions.includes(
      'EXPENSE-REIMBURSEMENT-LIST-EDIT-SUPPER',
    ),
    canDeleteExpenseReimbursement: permissions.includes('EXPENSE-REIMBURSEMENT-LIST-DELETE'),
    // 培训报销
    canSuperEditTrainingExpenseReimbursement: permissions.includes(
      'TRAINING-EXPENSE-REIMBURSEMENT-LIST-EDIT-SUPPER',
    ),
    canReadTrainingExpenseReimbursement: permissions.includes(
      'TRAINING-EXPENSE-REIMBURSEMENT-LIST-VIEW',
    ),
    canAddTrainingExpenseReimbursement: permissions.includes(
      'TRAINING-EXPENSE-REIMBURSEMENT-LIST-ADD',
    ),
    canEditTrainingExpenseReimbursement: permissions.includes(
      'TRAINING-EXPENSE-REIMBURSEMENT-LIST-EDIT',
    ),
    canDeleteTrainingExpenseReimbursement: permissions.includes(
      'TRAINING-EXPENSE-REIMBURSEMENT-LIST-DELETE',
    ),
    canUpdateTrainingStatus: permissions.includes(
      'TRAINING-EXPENSE-REIMBURSEMENT-LIST-UPDATE-STATUS',
    ),

    // 报销统计
    canReadExpenseReimbursementStatistics: permissions.includes('EXPENSE-REIMBURSEMENT-STATISTICS'),

    // 支付申请管理
    canReadPaymentRequest:
      permissions.includes('GENERAL-VIEW') || permissions.includes('CONTRACT-PAYMENT-VIEW'),

    // 项目通用付款申请
    canReadGeneral: permissions.includes('GENERAL-VIEW'),
    canAddGeneral: permissions.includes('GENERAL-ADD'),
    canEditGeneral: permissions.includes('GENERAL-EDIT'),
    canDeleteGeneral: permissions.includes('GENERAL-DELETE'),
    // 合同付款申请
    canReadContractPayment: permissions.includes('CONTRACT-PAYMENT-VIEW'),
    canAddContractPayment: permissions.includes('CONTRACT-PAYMENT-ADD'),
    canEditContractPayment: permissions.includes('CONTRACT-PAYMENT-EDIT'),
    canRevokeContractPayment: permissions.includes('CONTRACT-PAYMENT-REVOKE'),
    //发票管理
    canReadInvoice:
      permissions.includes('INVOICE-TRANSACTION-RECORD-VIEW') ||
      permissions.includes('INVOICE-COLLECT-RECORD-VIEW') ||
      permissions.includes('INVOICE-PENDING-COLLECTION-VIEW'),
    // 收支管理
    canReadTransaction:
      permissions.includes('RECEIPT-TRANSACTION-RECORD-VIEW') ||
      permissions.includes('OUTSTANDING-PAYMENT-TRANSACTION-RECORD-VIEW') ||
      permissions.includes('PAYMENT-RECORD-VIEW') ||
      permissions.includes('REFUND-HISTORY-VIEW') ||
      permissions.includes('ACCOUNT-RECEIVABLE'),
    // 工资表
    canReadPayroll: permissions.includes('SALARY-RECORD-VIEW'),
    // 开票记录
    canReadInvoiceTransactionRecord: permissions.includes('INVOICE-TRANSACTION-RECORD-VIEW'),
    canAddInvoiceTransactionRecord: permissions.includes('INVOICE-TRANSACTION-RECORD-ADD'),
    canEditInvoiceTransactionRecord: permissions.includes('INVOICE-TRANSACTION-RECORD-EDIT'),
    canRevokeInvoiceTransactionRecord: permissions.includes('INVOICE-TRANSACTION-RECORD-REVOKE'),
    canCancelInvoiceTransactionRecord: permissions.includes('INVOICE-TRANSACTION-RECORD-CANCEL'),
    canFlowStateInvoiceTransactionRecord: permissions.includes(
      'INVOICE-TRANSACTION-RECORD-FLOW-STATE',
    ),
    // 收票记录
    canReadInvoiceCollectRecord: permissions.includes('INVOICE-COLLECT-RECORD-VIEW'),
    canAddInvoiceCollectRecord: permissions.includes('INVOICE-COLLECT-RECORD-ADD'),
    canEditInvoiceCollectRecord: permissions.includes('INVOICE-COLLECT-RECORD-EDIT'),
    canRevokeInvoiceCollectRecord: permissions.includes('INVOICE-COLLECT-RECORD-REVOKE'),
    canDeleteInvoiceCollectRecord: permissions.includes('INVOICE-COLLECT-RECORD-DELETE'),
    canClaimInvoiceCollectRecord: permissions.includes('INVOICE-COLLECT-RECORD-CLAIM'),
    //合同待收票记录
    canReadInvoicePendingCollection: permissions.includes('INVOICE-PENDING-COLLECTION-VIEW'),
    // 收款记录
    canReadReceiptTransactionRecord: permissions.includes('RECEIPT-TRANSACTION-RECORD-VIEW'),
    canAddReceiptTransactionRecord: permissions.includes('RECEIPT-TRANSACTION-RECORD-ADD'),
    canEditReceiptTransactionRecord: permissions.includes('RECEIPT-TRANSACTION-RECORD-EDIT'),
    canDeleteReceiptTransactionRecord: permissions.includes('RECEIPT-TRANSACTION-RECORD-DELETE'),
    canClaimedReceiptTransactionRecord: permissions.includes('RECEIPT-TRANSACTION-RECORD-CLAIMED'),
    canUnclaimReceiptTransactionRecord: permissions.includes('RECEIPT-TRANSACTION-RECORD-UNCLAIM'),

    // 待付款记录
    canReadOutstandingPaymentTransactionRecord: permissions.includes(
      'OUTSTANDING-PAYMENT-TRANSACTION-RECORD-VIEW',
    ),
    canEditOutstandingPaymentTransactionRecord: permissions.includes(
      'OUTSTANDING-PAYMENT-TRANSACTION-RECORD-EDIT',
    ),
    // 付款记录
    canReadPaymentRecord: permissions.includes('PAYMENT-RECORD-VIEW'),
    canEditPaymentRecord: permissions.includes('PAYMENT-RECORD-EDIT'),
    canAllowPaymentRecord: permissions.includes('PAYMENT-RECORD-ALLOW'),
    canCancelPaymentRecord: permissions.includes('PAYMENT-TRANSACTION-RECORD-CANCEL'),
    // 工资基础信息
    canReadSalaryRecord: permissions.includes('SALARY-RECORD-VIEW'),
    canEditSalaryRecord: permissions.includes('SALARY-RECORD-EDIT'),
    canAddSalaryRecord: permissions.includes('SALARY-RECORD-ADD-RECORD'),
    canDeleteSalaryRecord: permissions.includes('SALARY-RECORD-DELETE'),
    // 退款记录
    canReadRefundHistory: permissions.includes('REFUND-HISTORY'),
    // 销账管理
    canAccountReceivable: permissions.includes('ACCOUNT-RECEIVABLE'),
    //开票银行
    canInvoiceBank: permissions.includes('INVOICE-BANK-LIST'),

    // 项目
    canReadProject:
      permissions.includes('INTERNAL-PROJECT-VIEW') ||
      permissions.includes('PRE-SALES-PROJECT-VIEW') ||
      permissions.includes('AFTER-SALES-PROJECT-VIEW') ||
      permissions.includes('SALES-PROJECT-VIEW') ||
      permissions.includes('DEVELOP-PROJECT-VIEW'),
    // 内部项目
    canReadInternalProject: permissions.includes('INTERNAL-PROJECT-VIEW'),
    canAddInternalProject: permissions.includes('INTERNAL-PROJECT-ADD'),
    canEditInternalProject: permissions.includes('INTERNAL-PROJECT-EDIT'),
    canSuperEditInternalProject:
      permissions.includes('INTERNAL-PROJECT-EDIT') && dataPermissions.includes('INTERNAL-PROJECT'),
    canDeleteInternalProject: permissions.includes('INTERNAL-PROJECT-DELETE'),
    canReopenInternalProject: permissions.includes('INTERNAL-PROJECT-REOPEN'),
    canCloseInternalProject: permissions.includes('INTERNAL-PROJECT-CLOSE'),
    canHandOverInternalProject: permissions.includes('INTERNAL-PROJECT-HANDOVER'),
    // 售前项目
    canReadPreSalesProject: permissions.includes('PRE-SALES-PROJECT-VIEW'),
    canAddPreSalesProject: permissions.includes('PRE-SALES-PROJECT-ADD'),
    canEditPreSalesProject: permissions.includes('PRE-SALES-PROJECT-EDIT'),
    canSuperEditPreSalesProject:
      permissions.includes('PRE-SALES-PROJECT-EDIT') &&
      dataPermissions.includes('PRE-SALES-PROJECT'),
    canDeletePreSalesProject: permissions.includes('PRE-SALES-PROJECT-DELETE'),
    canReopenPreSalesProject: permissions.includes('PRE-SALES-PROJECT-REOPEN'),
    canClosingAccountingPreSalesProject: permissions.includes(
      'PRE-SALES-PROJECT-CLOSING-ACCOUNTING',
    ),
    canClosePreSalesProject: permissions.includes('PRE-SALES-PROJECT-CLOSE'),
    canHandOverPreSalesProject: permissions.includes('PRE-SALES-PROJECT-HANDOVER'),
    // 售后项目
    canReadAfterSalesProject: permissions.includes('AFTER-SALES-PROJECT-VIEW'),
    canAddAfterSalesProject: permissions.includes('AFTER-SALES-PROJECT-ADD'),
    canEditAfterSalesProject: permissions.includes('AFTER-SALES-PROJECT-EDIT'),
    canSuperEditAfterSalesProject:
      permissions.includes('AFTER-SALES-PROJECT-EDIT') &&
      dataPermissions.includes('AFTER-SALES-PROJECT'),
    canDeleteAfterSalesProject: permissions.includes('AFTER-SALES-PROJECT-DELETE'),
    canReopenAfterSalesProject: permissions.includes('AFTER-SALES-PROJECT-REOPEN'),
    canCloseAfterSalesProject: permissions.includes('AFTER-SALES-PROJECT-CLOSE'),
    canHandOverAfterSalesProject: permissions.includes('AFTER-SALES-PROJECT-HANDOVER'),
    // 开发项目
    canReadDevelopProject: permissions.includes('DEVELOP-PROJECT-VIEW'),
    canAddDevelopProject: permissions.includes('DEVELOP-PROJECT-ADD'),
    canEditDevelopProject: permissions.includes('DEVELOP-PROJECT-EDIT'),
    canSuperEditDevelopProject:
      permissions.includes('DEVELOP-PROJECT-EDIT') && dataPermissions.includes('DEVELOP-PROJECT'),
    canDeleteDevelopProject: permissions.includes('DEVELOP-PROJECT-DELETE'),
    canReopenDevelopProject: permissions.includes('DEVELOP-PROJECT-REOPEN'),
    canCloseDevelopProject: permissions.includes('AFTER-SALES-PROJECT-CLOSE'),
    canHandOverDevelopProject: permissions.includes('AFTER-SALES-PROJECT-HANDOVER'),
    // 销售项目
    canReadSalesProject: permissions.includes('SALES-PROJECT-VIEW'),
    canAddSalesProject: permissions.includes('SALES-PROJECT-ADD'),
    canEditSalesProject: permissions.includes('SALES-PROJECT-EDIT'),
    canSuperEditSalesProject:
      permissions.includes('SALES-PROJECT-EDIT') && dataPermissions.includes('SALES-PROJECT'),
    canDeleteSalesProject: permissions.includes('SALES-PROJECT-DELETE'),
    canReopenSalesProject: permissions.includes('SALES-PROJECT-REOPEN'),
    canCloseSalesProject: permissions.includes('SALES-PROJECT-CLOSE'),
    canHandOverSalesProject: permissions.includes('SALES-PROJECT-HANDOVER'),
    // 周报
    canReadWR:
      permissions.includes('WEEKLY-REPORT-VIEW') ||
      permissions.includes('WEEKLY-REPORT-ADD') ||
      permissions.includes('WEEKLY-REPORT-EDIT') ||
      permissions.includes('WEEKLY-REPORT-RESET'),
    canReadWeeklyReport: permissions.includes('WEEKLY-REPORT-VIEW'),
    canAddWeeklyReport: permissions.includes('WEEKLY-REPORT-ADD'),
    canEditWeeklyReport: permissions.includes('WEEKLY-REPORT-EDIT'),
    canResetWeeklyReport: permissions.includes('WEEKLY-REPORT-RESET'),
    canExportWeeklyReport: permissions.includes('WEEKLY-REPORT-EXPORT'),

    // 工单管理
    canReadWO:
      permissions.includes('WORK-ORDER-VIEW') ||
      permissions.includes('WORK-ORDER-ADD') ||
      permissions.includes('WORK-ORDER-EDIT'),
    canReadWorkOrder: permissions.includes('WORK-ORDER-VIEW'),
    canAddWorkOrder: permissions.includes('WORK-ORDER-ADD'),
    canEditWorkOrder: permissions.includes('WORK-ORDER-EDIT'),
    canDeleteWorkOrder: permissions.includes('WORK-ORDER-DELETE'),

    // 审批管理
    canReadApproval: permissions.includes('APPROVAL'),

    // 系统设置
    canSetting:
      permissions.includes('SETTING') || permissions.includes('PARAMETER-CONFIGURATION-VIEW'),
    canReadParameterConfiguration: permissions.includes('PARAMETER-CONFIGURATION-VIEW'),
    canEditParameterConfiguration: permissions.includes('PARAMETER-CONFIGURATION-EDIT'),

    // 报表分析
    canReadAnalysis: permissions.some((item) => item.includes('ANALYSIS-')),
    canReadAnalysisMasterContract: permissions.includes('ANALYSIS-MASTER-CONTRACT'),
    canReadAnalysisInternalProject: permissions.includes('ANALYSIS-INTERNAL-PROJECT'),
    canReadAnalysisSalesProject: permissions.includes('ANALYSIS-SALES-PROJECT'),
    canReadAnalysisPreSalesProject: permissions.includes('ANALYSIS-PRE-SALES-PROJECT'),
    canReadAnalysisAfterSalesProject: permissions.includes('ANALYSIS-AFTER-SALES-PROJECT'),

    //汇总表
    canReadTableSalesPlan: permissions.includes('ANALYSIS-SALES-PLAN-TABLE'),
    canReadTableCollectionPlan: permissions.includes('ANALYSIS-COLLECTION-PLAN-TABLE'),
    canReadTableProcurementPaymentPlan: permissions.includes(
      'ANALYSIS-PROCUREMENT-PAYMENT-PLAN-TABLE',
    ),
    canReadTableWeeklyReport: permissions.includes('ANALYSIS-WEEKLY-REPORT-TABLE'),
    // 工作量统计
    canReadWorkloadStatistics: permissions.includes('ANALYSIS-WORKLOAD-STATISTICS'),

    // 销售管理
    canReadSale:
      permissions.includes('PAYMENT-PLAN-TABLE') ||
      permissions.includes('COLLECTION-PLAN-TABLE') ||
      permissions.includes('SALES-PLAN-TABLE') ||
      permissions.includes('PERFORMANCE-CONFIG-TABLE-VIEW') ||
      permissions.includes('PERFORMANCE-RESULT-TABLE-VIEW') ||
      permissions.includes('BID-PROJECT-REPORT-VIEW'),

    // 投标项目报备表
    canReadBidProjectReport: permissions.includes('BID-PROJECT-REPORT-VIEW'),
    canAddBidProjectReport: permissions.includes('BID-PROJECT-REPORT-ADD'),
    canEditBidProjectReport: permissions.includes('BID-PROJECT-REPORT-EDIT'),
    canDeleteBidProjectReport: permissions.includes('BID-PROJECT-REPORT-DELETE'),
    canCloseBidProjectReport: permissions.includes('BID-PROJECT-REPORT-CLOSE'),
    canAssignBidProjectReport: permissions.includes('BID-PROJECT-REPORT-ASSIGN'),

    // 付款计划表
    canReadPaymentPlanTable: permissions.includes('PAYMENT-PLAN-TABLE'),
    // 收款计划表
    canReadCollectionPlanTable: permissions.includes('COLLECTION-PLAN-TABLE'),
    // 销售计划表
    canReadSalePlanTable: permissions.includes('SALES-PLAN-TABLE'),
    // 项目核算表
    canReadPerformanceConfig: permissions.includes('PERFORMANCE-CONFIG-TABLE-VIEW'),
    canStartPerformanceConfig: permissions.includes('PERFORMANCE-CONFIG-TABLE-START'),
    canEditPerformanceConfig: permissions.includes('PERFORMANCE-CONFIG-TABLE-EDIT'),
    canEndPerformanceConfig: permissions.includes('PERFORMANCE-CONFIG-TABLE-END'),
    // 年度业绩考核表
    canReadPerformanceResult: permissions.includes('PERFORMANCE-RESULT-TABLE-VIEW'),
    canConfirmPerformanceResult: permissions.includes('PERFORMANCE-RESULT-TABLE-CONFIRM'),
    canRejectPerformanceResult: permissions.includes('PERFORMANCE-RESULT-TABLE-REJECT'),
    canPrintPerformanceResult: permissions.includes('PERFORMANCE-RESULT-PRINT'),
    canEditPrintPerformanceResult: permissions.includes('PERFORMANCE-RESULT-PRINT-EDIT'),
    // 年度业绩考核表-超管数据权限
    canReadAllPerformanceResult: dataPermissions.includes('PERFORMANCE-RESULT-TABLE'),

    //利润管理
    canReadProfit: permissions.some((item) => item.includes('PROFIT-')),
    canReadProfitProjectDelivery: permissions.includes('PROFIT-PROJECT-DELIVERY-SH'),
    canReadProfitDevelopProjectDelivery: permissions.includes('PROFIT-PROJECT-DELIVERY-KF'),
    canReadProfitMainContract: permissions.includes('PROFIT-MAIN-CONTRACT-VIEW'),
    canEditProfitMainContract: permissions.includes('PROFIT-MAIN-CONTRACT-EDIT'),

    // 工作流
    canReadWorkflow: permissions.includes('WORKFLOW'),

    // 文档管理
    canReadFileManage: permissions.includes('FILE-MANAGE-VIEW'),
    canDeleteFileManage: permissions.includes('FILE-MANAGE-DELETE'),
    canRenameFileManage: permissions.includes('FILE-MANAGE-RENAME'),
    canCommonEditFileManage: permissions.includes('FILE-MANAGE-COMMON'),
  };
}
