// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 创建用户日程 创建用户日程 POST /api/userSchedule/createUserSchedule */
export async function createUserSchedule(
  body: API.UserScheduleReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/userSchedule/createUserSchedule', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除用户日程 删除用户日程 DELETE /api/userSchedule/deleteUserSchedule */
export async function deleteUserSchedule(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/userSchedule/deleteUserSchedule', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取用户日程集合 获取用户日程集合 GET /api/userSchedule/getUserScheduleList */
export async function getUserScheduleList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getUserScheduleListParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultListUserScheduleResp>('/api/userSchedule/getUserScheduleList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** operations 日程操作 POST /api/userSchedule/operations */
export async function operationsSchedule(
  body: API.UserScheduleOperationsReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/userSchedule/operations', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询 分页查询 POST /api/userSchedule/pageUserSchedule */
export async function pageUserSchedule(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageUserScheduleResp>('/api/userSchedule/pageUserSchedule', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改用户日程 修改用户日程 PUT /api/userSchedule/updateUserSchedule */
export async function updateUserSchedule(
  body: API.UserScheduleReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/userSchedule/updateUserSchedule', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
