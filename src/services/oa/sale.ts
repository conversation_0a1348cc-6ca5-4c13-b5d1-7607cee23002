// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 收款计划表 POST /api/sale/collection-plan/list */
export async function collectionPlanSaleList(
  body: API.SaleCollectionPageReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/sale/collection-plan/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 刷新 POST /api/sale/performance/flushed-data-active */
export async function flushedDataActive(options?: { [key: string]: any }) {
  return request<API.ResultPerformanceConfResp>('/api/sale/performance/flushed-data-active', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 业绩考核初始表 POST /api/sale/performance/initial-performance-appraisal-table */
export async function initialPerformanceAppraisalTable(
  body: API.PageReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultPageInitialPerformanceAppraisalTableResp>(
    '/api/sale/performance/initial-performance-appraisal-table',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 合同配置详情 POST /api/sale/performance/performance-conf */
export async function performanceConf(
  body: API.PerformanceIdReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultPerformanceConfResp>('/api/sale/performance/performance-conf', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 确认、拒绝核算 POST /api/sale/performance/performance-confirm */
export async function performanceConfirm(
  body: API.PerformanceConfirmReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/sale/performance/performance-confirm', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 结束核算 POST /api/sale/performance/performance-end */
export async function performanceEnd(body: API.PerformanceIdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/sale/performance/performance-end', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 终表 POST /api/sale/performance/performance-result-table */
export async function performanceResultTable(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPerformancePagePerMainResultResp>(
    '/api/sale/performance/performance-result-table',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 终表年份 POST /api/sale/performance/performance-result-year */
export async function performanceResultYear(options?: { [key: string]: any }) {
  return request<API.ResultCollectionString>('/api/sale/performance/performance-result-year', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 保存配置 POST /api/sale/performance/performance-save */
export async function performanceSave(
  body: API.PerformanceConfReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/sale/performance/performance-save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 开始核算 POST /api/sale/performance/performance-start */
export async function performanceStart(
  body: API.PerformanceIdReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/sale/performance/performance-start', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 终表打印-确认 POST /api/sale/performance/print-confirm */
export async function printConfirm(body: API.PrintInfoReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/sale/performance/print-confirm', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 终表打印 POST /api/sale/performance/print-info */
export async function printInfo(body: API.PrintInfoReq, options?: { [key: string]: any }) {
  return request<API.ResultPrintInfoResp>('/api/sale/performance/print-info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 终表打印-提交 POST /api/sale/performance/print-submit */
export async function printSubmit(body: API.PrintSubmitReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/sale/performance/print-submit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 采购付款计划表 POST /api/sale/purchase-payment-plan/list */
export async function purchasePaymentPlanSaleList(
  body: API.SalePaymentPageReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/sale/purchase-payment-plan/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 销售计划表 POST /api/sale/sales-plan/list */
export async function salesPlanSaleList(
  body: API.SalesPlanSalePageReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/sale/sales-plan/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
