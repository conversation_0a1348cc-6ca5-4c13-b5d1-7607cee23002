// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as analysis from './analysis';
import * as announcement from './announcement';
import * as attendance from './attendance';
import * as auth from './auth';
import * as bid from './bid';
import * as business from './business';
import * as certificate from './certificate';
import * as collectTicket from './collectTicket';
import * as conInvoiced from './conInvoiced';
import * as contract from './contract';
import * as department from './department';
import * as dictionary from './dictionary';
import * as employee from './employee';
import * as employmentType from './employmentType';
import * as fina from './fina';
import * as firmResource from './firmResource';
import * as flow from './flow';
import * as grade from './grade';
import * as holiday from './holiday';
import * as holidayDetails from './holidayDetails';
import * as home from './home';
import * as institutionCustomer from './institutionCustomer';
import * as leaveApp from './leaveApp';
import * as leavePolicy from './leavePolicy';
import * as leaveSell from './leaveSell';
import * as leaveType from './leaveType';
import * as nextcloud from './nextcloud';
import * as operationRecord from './operationRecord';
import * as partner from './partner';
import * as paymentApplication from './paymentApplication';
import * as position from './position';
import * as profit from './profit';
import * as project from './project';
import * as recruit from './recruit';
import * as rules from './rules';
import * as salary from './salary';
import * as sale from './sale';
import * as scheduler from './scheduler';
import * as sysCertificate from './sysCertificate';
import * as talent from './talent';
import * as task from './task';
import * as train from './train';
import * as userSchedule from './userSchedule';
import * as week from './week';
import * as workOrder from './workOrder';
export default {
  userSchedule,
  train,
  rules,
  project,
  partner,
  contract,
  conInvoiced,
  certificate,
  business,
  auth,
  flow,
  workOrder,
  week,
  task,
  sysCertificate,
  sale,
  profit,
  nextcloud,
  home,
  fina,
  salary,
  paymentApplication,
  institutionCustomer,
  talent,
  recruit,
  position,
  leaveType,
  leaveSell,
  employee,
  leavePolicy,
  leaveApp,
  holiday,
  holidayDetails,
  grade,
  firmResource,
  employmentType,
  department,
  attendance,
  announcement,
  analysis,
  dictionary,
  collectTicket,
  bid,
  scheduler,
  operationRecord,
};
