// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 创建休假类型 POST /api/employee/leave-type/leave-type-create */
export async function createLeaveType(
  body: API.LeaveTypeInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/leave-type/leave-type-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 休假类型列表 GET /api/employee/leave-type/leave-type-list */
export async function leaveTypeList(options?: { [key: string]: any }) {
  return request<API.ResultListLeaveTypeListResp>('/api/employee/leave-type/leave-type-list', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 休假类型分页 POST /api/employee/leave-type/leave-type-page */
export async function leaveTypePage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageLeaveTypePageResp>('/api/employee/leave-type/leave-type-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新休假类型 POST /api/employee/leave-type/leave-type-update */
export async function updateLeaveType(
  body: API.LeaveTypeUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/leave-type/leave-type-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
