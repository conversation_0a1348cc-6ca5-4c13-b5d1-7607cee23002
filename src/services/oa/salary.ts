// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加工资记录 POST /api/fina/salary/summary-add-record */
export async function summaryAddRecord(
  body: API.SalaryInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/fina/salary/summary-add-record', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 调整记录删除 POST /api/fina/salary/summary-del */
export async function summaryRemove(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/fina/salary/summary-del', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 工资调整 POST /api/fina/salary/summary-edit */
export async function summaryEdit(body: API.SalaryUpdateReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/fina/salary/summary-edit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 工资汇总表 POST /api/fina/salary/summary-page */
export async function summaryPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageUserSalaryResp>('/api/fina/salary/summary-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
