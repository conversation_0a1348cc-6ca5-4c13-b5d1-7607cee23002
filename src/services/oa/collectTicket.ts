// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 取消领票 POST /api/collect-ticket/cancel */
export async function collectTicketCancel(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/collect-ticket/cancel', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 领票 POST /api/collect-ticket/claim */
export async function collectTicketClaim(
  body: API.CollectTicketClaimReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/collect-ticket/claim', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 合同列表-领票 POST /api/collect-ticket/contract-list */
export async function contractList(
  body: API.CollectTicketContractListReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultListCollectTicketContractListResp>('/api/collect-ticket/contract-list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除收票 POST /api/collect-ticket/delete */
export async function collectTicketDelete(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/collect-ticket/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询收票信息 POST /api/collect-ticket/info */
export async function collectTicketInfo(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultCollectTicketInfoResp>('/api/collect-ticket/info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增收票 POST /api/collect-ticket/insert */
export async function collectTicketInsert(
  body: API.CollectTicketInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/collect-ticket/insert', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 缺票记录-采购合同 POST /api/collect-ticket/missing-track-contract */
export async function missingTrackContract(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPagePendingReceiptContractResp>(
    '/api/collect-ticket/missing-track-contract',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 缺票记录-项目付款 POST /api/collect-ticket/missing-track-project */
export async function missingTrackProject(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPagePendingReceiptProjectResp>(
    '/api/collect-ticket/missing-track-project',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 分页查询收票信息 POST /api/collect-ticket/page */
export async function collectTicketPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageCollectTicketPageResp>('/api/collect-ticket/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新收票 POST /api/collect-ticket/update */
export async function collectTicketUpdate(
  body: API.CollectTicketUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/collect-ticket/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
