// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 创建人才 POST /api/employee/talent/talent-create */
export async function createTalent(body: API.TalentInsertReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/talent/talent-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 人才删除 POST /api/employee/talent/talent-del */
export async function talentDel(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/talent/talent-del', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 人才详情 POST /api/employee/talent/talent-info */
export async function talentInfo(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultTalentInfoResp>('/api/employee/talent/talent-info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 人才分页 POST /api/employee/talent/talent-page */
export async function talentPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageTalentInfoResp>('/api/employee/talent/talent-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新人才 POST /api/employee/talent/talent-update */
export async function updateTalent(body: API.TalentUpdateReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/talent/talent-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
