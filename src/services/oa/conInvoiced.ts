// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 开票作废 GET /api/conInvoiced/abandonedInvoicing */
export async function abandonedInvoicing(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.abandonedInvoicingParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/conInvoiced/abandonedInvoicing', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/conInvoiced/cancelClaim */
export async function cancelClaim(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.cancelClaimParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/conInvoiced/cancelClaim', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 取消开票信息 取消开票信息 GET /api/conInvoiced/cancelInvoicing */
export async function cancelInvoicing(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.cancelInvoicingParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/conInvoiced/cancelInvoicing', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 取消支付申请 取消支付申请 GET /api/conInvoiced/cancelPayApplication */
export async function cancelPayApplication(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.cancelPayApplicationParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/conInvoiced/cancelPayApplication', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 取消付款流水 取消付款流水 POST /api/conInvoiced/cancelPayment */
export async function cancelPayment(body: API.CancelPaymentReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/conInvoiced/cancelPayment', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改收款流水 修改收款流水 POST /api/conInvoiced/confirmCollect */
export async function confirmCollect(body: API.ReceiptsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/conInvoiced/confirmCollect', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新建收款流水 新建收款流水 POST /api/conInvoiced/createCollect */
export async function createCollect(body: API.ReceiptsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/conInvoiced/createCollect', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除收款流水 删除收款流水 POST /api/conInvoiced/deleteCollect */
export async function deleteCollect(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/conInvoiced/deleteCollect', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询待付款流水 分页查询待付款流水 POST /api/conInvoiced/pageAwaitPayment */
export async function pageAwaitPayment(
  body: API.AwaitPaymentPageReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultPaymentPagePaymentResp>('/api/conInvoiced/pageAwaitPayment', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询合同开票信息 分页查询合同开票信息 POST /api/conInvoiced/pageInvoiced */
export async function pageInvoiced(body: API.InvoicedPageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageInvoicingResp>('/api/conInvoiced/pageInvoiced', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询付款流水 分页查询付款流水 POST /api/conInvoiced/pagePayment */
export async function pagePayment(body: API.PaymentPageReq, options?: { [key: string]: any }) {
  return request<API.ResultFlowPagePaymentResp>('/api/conInvoiced/pagePayment', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询合同支付申请 分页查询合同支付申请 POST /api/conInvoiced/pagePaymentApp */
export async function pagePaymentApp(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPagePaymentAppResp>('/api/conInvoiced/pagePaymentApp', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询收款流水 分页查询收款流水 POST /api/conInvoiced/pageReceipts */
export async function pageReceipts(body: API.ReceiptsPageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageReceiptsResp>('/api/conInvoiced/pageReceipts', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询退款流水 分页查询退款流水 POST /api/conInvoiced/pageRefund */
export async function pageRefund(body: API.RefundPageReq, options?: { [key: string]: any }) {
  return request<API.ResultFlowPageRefundResp>('/api/conInvoiced/pageRefund', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量修改允许付款状态 批量修改允许付款状态 POST /api/conInvoiced/passPayApplication */
export async function passPayApplication(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/conInvoiced/passPayApplication', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据客户经理查询相关收款计划 根据客户经理查询相关收款计划 GET /api/conInvoiced/selectCollPlan */
export async function selectCollPlan(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.selectCollPlanParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultListCollectionPlanResp>('/api/conInvoiced/selectCollPlan', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 查询收款计划 查询收款计划 GET /api/conInvoiced/selectCollPlan1 */
export async function selectCollPlan1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.selectCollPlan1Params,
  options?: { [key: string]: any },
) {
  return request<API.ResultListCollectionPlanResp>('/api/conInvoiced/selectCollPlan1', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询当前用户存在未付款的合同 查询当前用户存在未付款的合同 GET /api/conInvoiced/selectContractPayList */
export async function selectContractPayList(options?: { [key: string]: any }) {
  return request<API.ResultListContractPaymentResp>('/api/conInvoiced/selectContractPayList', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 根据主键查询开票详情 根据主键查询开票详情 GET /api/conInvoiced/selectInvoicedById */
export async function selectInvoicedById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.selectInvoicedByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultInvoicingResp>('/api/conInvoiced/selectInvoicedById', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 查询当前用户相关的保证金付款流水 查询当前用户相关的保证金付款流水 GET /api/conInvoiced/selectMarginPayment */
export async function selectMarginPayment(options?: { [key: string]: any }) {
  return request<API.ResultListPaymentResp>('/api/conInvoiced/selectMarginPayment', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 根据主键查询合同支付申请 根据主键查询合同支付申请 GET /api/conInvoiced/selectPaymentAppById */
export async function selectPaymentAppById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.selectPaymentAppByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultPaymentAppResp>('/api/conInvoiced/selectPaymentAppById', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 根据主键查询合同付款流水详细信息 根据主键查询合同付款流水详细信息 GET /api/conInvoiced/selectPaymentById */
export async function selectPaymentById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.selectPaymentByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultPaymentResp>('/api/conInvoiced/selectPaymentById', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 根据主键查询收款流水详细信息 根据主键查询收款流水详细信息 GET /api/conInvoiced/selectReceiptById */
export async function selectReceiptById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.selectReceiptByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultReceiptsResp>('/api/conInvoiced/selectReceiptById', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 根据主键查询退款流水 根据主键查询退款流水 GET /api/conInvoiced/selectRefundById */
export async function selectRefundById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.selectRefundByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultRefundResp>('/api/conInvoiced/selectRefundById', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 更新付款流水 更新付款流水 PUT /api/conInvoiced/updatePayment */
export async function updatePayment(body: API.PaymentReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/conInvoiced/updatePayment', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新退款流水信息 更新退款流水信息 PUT /api/conInvoiced/updateRefund */
export async function updateRefund(body: API.RefundReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/conInvoiced/updateRefund', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
