// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 设置类别 POST /api/nextcloud/category */
export async function category(body: API.CategoryReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/nextcloud/category', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建文件夹 POST /api/nextcloud/create_directory */
export async function createDirectory(body: API.FilePathReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/nextcloud/create_directory', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 目录 POST /api/nextcloud/directory */
export async function getFiles(body: API.FilePathReq, options?: { [key: string]: any }) {
  return request<API.ResultListDavFileVO>('/api/nextcloud/directory', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 下载 GET /api/nextcloud/download */
export async function download(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.downloadParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/nextcloud/download', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 预览 GET /api/nextcloud/preview */
export async function preview(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.previewParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/nextcloud/preview', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 删除文件 POST /api/nextcloud/remove */
export async function remove(body: API.FileRemoveReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/nextcloud/remove', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 上传文件 POST /api/nextcloud/upload */
export async function upload(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.uploadParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/nextcloud/upload', {
    method: 'POST',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 上传文件（公有） POST /api/nextcloud/upload/public */
export async function uploadPublic(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.uploadPublicParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/nextcloud/upload/public', {
    method: 'POST',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}
