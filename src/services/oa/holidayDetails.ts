// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 创建假期表明细 POST /api/employee/holiday-details/holiday-details-create */
export async function createHolidayDetails(
  body: API.HolidayDetailsInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/holiday-details/holiday-details-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 假期表明细分页 POST /api/employee/holiday-details/holiday-details-page */
export async function holidayDetailsPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageHolidayDetailsPageResp>(
    '/api/employee/holiday-details/holiday-details-page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 更新假期表明细 POST /api/employee/holiday-details/holiday-details-update */
export async function updateHolidayDetails(
  body: API.HolidayDetailsUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/holiday-details/holiday-details-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
