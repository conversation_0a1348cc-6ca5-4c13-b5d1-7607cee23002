// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 操作记录 GET /api/common/operation-record/operation-record-del */
export async function dictionaryDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.dictionaryDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/common/operation-record/operation-record-del', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 操作记录 GET /api/common/operation-record/operation-record-list */
export async function dictionaryList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.dictionaryListParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultListOperationRecord>(
    '/api/common/operation-record/operation-record-list',
    {
      method: 'GET',
      params: {
        ...params,
        req: undefined,
        ...params['req'],
      },
      ...(options || {}),
    },
  );
}
