// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 创建员工类型 POST /api/employee/employment-type/employment-type-create */
export async function createEmploymentType(
  body: API.EmploymentTypeInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/employment-type/employment-type-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除员工类型 POST /api/employee/employment-type/employment-type-del */
export async function employeeTypeDel(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/employment-type/employment-type-del', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 员工类型列表 GET /api/employee/employment-type/employment-type-list */
export async function getEmploymentTypeList(options?: { [key: string]: any }) {
  return request<API.ResultListEmploymentTypeListResp>(
    '/api/employee/employment-type/employment-type-list',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 更新员工类型 POST /api/employee/employment-type/employment-type-update */
export async function updateEmploymentType(
  body: API.EmploymentTypeUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/employment-type/employment-type-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
