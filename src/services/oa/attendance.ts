// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 考勤日历 POST /api/employee/attendance/attendance-calendar */
export async function attendanceCalendar(body: API.CalendarReq, options?: { [key: string]: any }) {
  return request<API.ResultListCalendarResp>('/api/employee/attendance/attendance-calendar', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 考勤导出 GET /api/employee/attendance/attendance-export */
export async function attendanceCalendar2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.attendanceCalendar2Params,
  options?: { [key: string]: any },
) {
  return request<any>('/api/employee/attendance/attendance-export', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 考勤记录分页 POST /api/employee/attendance/attendance-list */
export async function attendancePage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageAttRecordListResp>('/api/employee/attendance/attendance-list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据月份查询考勤数据 POST /api/employee/attendance/attendance-month */
export async function attendanceMonth(body: API.AttListReq, options?: { [key: string]: any }) {
  return request<API.ResultAttendanceMonthResp>('/api/employee/attendance/attendance-month', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
