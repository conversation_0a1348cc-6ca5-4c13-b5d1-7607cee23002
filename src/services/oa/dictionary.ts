// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 开票信息 GET /api/common/dictionary/billing-information-data */
export async function billingInformationData(options?: { [key: string]: any }) {
  return request<API.ResultBillingInformationDTO>(
    '/api/common/dictionary/billing-information-data',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 系统配置参数更新 POST /api/common/dictionary/billing-information-data-update */
export async function billingInformationEdit(
  body: API.BillingInformationDTO,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/common/dictionary/billing-information-data-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询单个信息 GET /api/common/dictionary/dictionary-data */
export async function getDataDictionary(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDataDictionaryParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultDictionaryInfoResp>('/api/common/dictionary/dictionary-data', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 数据列表 GET /api/common/dictionary/dictionary-list */
export async function dictionaryList1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.dictionaryList1Params,
  options?: { [key: string]: any },
) {
  return request<API.ResultListDictionaryListResp>('/api/common/dictionary/dictionary-list', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 创建开票申请收款银行 POST /api/common/dictionary/invoicing-bank-create */
export async function createInvoicingBank(body: API.BanksDto, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/common/dictionary/invoicing-bank-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/**  设置默认开票申请收款银行 POST /api/common/dictionary/invoicing-bank-default */
export async function defaultInvoicingBank(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/common/dictionary/invoicing-bank-default', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除开票申请收款银行 POST /api/common/dictionary/invoicing-bank-delete */
export async function deleteInvoicingBank(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/common/dictionary/invoicing-bank-delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 开票申请收款银行选项 GET /api/common/dictionary/invoicing-bank-option */
export async function getInvoicingBankOption(options?: { [key: string]: any }) {
  return request<API.ResultListBanksDto>('/api/common/dictionary/invoicing-bank-option', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 系统配置参数 GET /api/common/dictionary/sys-data */
export async function sysData(options?: { [key: string]: any }) {
  return request<API.ResultSysDataDTO>('/api/common/dictionary/sys-data', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 系统配置参数更新 POST /api/common/dictionary/sys-data-update */
export async function sysDataEdit(body: API.SysDataDTO, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/common/dictionary/sys-data-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 部门列表 GET /api/common/export/user/department */
export async function exportDepartment(options?: { [key: string]: any }) {
  return request<API.ResultListExportDepartmentResp>('/api/common/export/user/department', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 用户列表 GET /api/common/export/user/user */
export async function exportUser(options?: { [key: string]: any }) {
  return request<API.ResultListExportUserResp>('/api/common/export/user/user', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取所有的日志文件 GET /api/common/file/get-logs */
export async function getLogs(options?: { [key: string]: any }) {
  return request<API.ResultString>('/api/common/file/get-logs', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取文件 GET /api/common/file/get-url */
export async function getFile(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getFileParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultString>('/api/common/file/get-url', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 预览文件 POST /api/common/file/image/preview/ */
export async function filePreview(body: API.FileNameReq, options?: { [key: string]: any }) {
  return request<any>('/api/common/file/image/preview/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 上传文件 POST /api/common/file/upload */
export async function addFile(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.addFileParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultString>('/api/common/file/upload', {
    method: 'POST',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 上传文件 POST /api/common/file/wechat-upload */
export async function addWechatFile(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.addWechatFileParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultString>('/api/common/file/wechat-upload', {
    method: 'POST',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}
