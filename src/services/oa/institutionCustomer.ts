// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 移除关联 POST /api/fina/institution/ins-del */
export async function insDel(body: API.InstitutionCustomerIdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/fina/institution/ins-del', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 编辑销售 POST /api/fina/institution/ins-edit-sale-person */
export async function insEditSalePersson(
  body: API.InstitutionEditSalePerssonReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/fina/institution/ins-edit-sale-person', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增关联 POST /api/fina/institution/ins-insert */
export async function insInsert(
  body: API.InstitutionCustomerIdsReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/fina/institution/ins-insert', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 关联表列表 POST /api/fina/institution/ins-list */
export async function insPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageInstitutionListResp>('/api/fina/institution/ins-list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
