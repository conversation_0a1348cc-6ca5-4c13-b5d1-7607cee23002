// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 校验日期是否是休假 true休假 false上班 POST /api/employee/holiday/holiday-check-holiday */
export async function checkHoliday(body: API.CheckHolidayReq, options?: { [key: string]: any }) {
  return request<API.ResultBoolean>('/api/employee/holiday/holiday-check-holiday', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 校验日期段有几天是工作日 POST /api/employee/holiday/holiday-check-section-holiday */
export async function checkSectionHoliday(
  body: API.CheckSectionHolidayReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/employee/holiday/holiday-check-section-holiday', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建假期表 POST /api/employee/holiday/holiday-create */
export async function createHoliday(body: API.HolidayInsertReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/holiday/holiday-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 假期表信息 GET /api/employee/holiday/holiday-info */
export async function holidayInfo(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.holidayInfoParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultHolidayInfoResp>('/api/employee/holiday/holiday-info', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 查询指定月份假期 POST /api/employee/holiday/holiday-month */
export async function holidayMonth(body: API.CalendarReq, options?: { [key: string]: any }) {
  return request<API.ResultListHolidayDetailsListResp>('/api/employee/holiday/holiday-month', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 假期表分页 POST /api/employee/holiday/holiday-page */
export async function holidayPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageHolidayPageResp>('/api/employee/holiday/holiday-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新假期表 POST /api/employee/holiday/holiday-update */
export async function updateHoliday(body: API.HolidayUpdateReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/holiday/holiday-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
