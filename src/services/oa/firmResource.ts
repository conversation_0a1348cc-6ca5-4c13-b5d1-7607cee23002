// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 资源确认归还 POST /api/employee/firm-resource/resource-confirm */
export async function confirmFirmResource(
  body: API.FirmResourceConfirmReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/firm-resource/resource-confirm', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建资源申请 POST /api/employee/firm-resource/resource-create */
export async function createFirmResource(
  body: API.FirmResourceInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/firm-resource/resource-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 资源申请删除 POST /api/employee/firm-resource/resource-del */
export async function firmResourceDel(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/firm-resource/resource-del', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 资源申请详情 POST /api/employee/firm-resource/resource-info */
export async function firmResourceInfo(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultFirmResourceInfoResp>('/api/employee/firm-resource/resource-info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 资源申请分页 POST /api/employee/firm-resource/resource-page */
export async function firmResourcePage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageFirmResourceInfoResp>('/api/employee/firm-resource/resource-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新资源申请 POST /api/employee/firm-resource/resource-update */
export async function updateFirmResource(
  body: API.FirmResourceUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/firm-resource/resource-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
