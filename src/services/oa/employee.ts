// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 员工信息 GET /api/employee/employee/employee-info */
export async function employeeInfo(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.employeeInfoParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultEmployeeInfoResp>('/api/employee/employee/employee-info', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 更新员工 POST /api/employee/employee/employee-update */
export async function updateEmployee(
  body: API.EmployeeUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/employee/employee-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 申请休假 POST /api/employee/leave-app-create-mobile */
export async function createLeaveAppMobile(
  body: API.LeaveAppInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/leave-app-create-mobile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 申请销假 POST /api/employee/leave-sell-create-mobile */
export async function createLeaveSellMobile(
  body: API.LeaveSellInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/leave-sell-create-mobile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
