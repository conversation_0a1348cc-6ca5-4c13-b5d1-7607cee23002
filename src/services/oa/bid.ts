// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加备注 POST /api/business/bid-project-report/add-remarks */
export async function bidProjectReportRemarks(
  body: API.BidRemarksReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/business/bid-project-report/add-remarks', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 投标分配 POST /api/business/bid-project-report/assignment */
export async function bidProjectReportAssignment(
  body: API.BidAssignmentReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/business/bid-project-report/assignment', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 投标关闭 POST /api/business/bid-project-report/close */
export async function bidProjectReportClose(
  body: API.BidCloseReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/business/bid-project-report/close', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建投标报备 POST /api/business/bid-project-report/create */
export async function bidProjectReportCreate(
  body: API.BidProjectReportInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/business/bid-project-report/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除投标报备 POST /api/business/bid-project-report/delete */
export async function bidProjectReportDelete(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/business/bid-project-report/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取投标报备列表 POST /api/business/bid-project-report/list */
export async function bidProjectReportPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageBidProjectReportResp>('/api/business/bid-project-report/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 主键查询 主键查询 POST /api/business/bid-project-report/selectById */
export async function bidProjectReportById(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultBidProjectReportResp>('/api/business/bid-project-report/selectById', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 未关闭的投标数 POST /api/business/bid-project-report/spend-close */
export async function bidProjectReportSpendClose(
  body: API.IdReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultBidSpendCloseResp>('/api/business/bid-project-report/spend-close', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新投标报备 POST /api/business/bid-project-report/update */
export async function bidProjectReportUpdate(
  body: API.BidProjectReportUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/business/bid-project-report/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
