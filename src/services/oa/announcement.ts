// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 创建公告 POST /api/employee/announcement/create */
export async function createAnnouncement(
  body: API.AnnouncementInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/announcement/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除公告 POST /api/employee/announcement/delete */
export async function deleteAnnouncement(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/announcement/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 个人中心公告 POST /api/employee/announcement/home-page */
export async function announcementHomePage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageAnnouncementPageResp>('/api/employee/announcement/home-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 公告详情 POST /api/employee/announcement/info */
export async function announcementInfo(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultAnnouncementPageResp>('/api/employee/announcement/info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 公告分页 POST /api/employee/announcement/page */
export async function announcementPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageAnnouncementPageResp>('/api/employee/announcement/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 发布公告 POST /api/employee/announcement/publish */
export async function publishAnnouncement(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/announcement/publish', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新公告 POST /api/employee/announcement/update */
export async function updateAnnouncement(
  body: API.AnnouncementUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/announcement/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
