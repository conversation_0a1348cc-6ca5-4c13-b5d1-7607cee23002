// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 合同付款申请 POST /api/fina/contract-pay-create-mobile */
export async function contractPayCreate(body: API.PaymentAppReq, options?: { [key: string]: any }) {
  return request<API.ResultPageContractPayResp>('/api/fina/contract-pay-create-mobile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 合同付款列表 POST /api/fina/contract-pay-list-mobile */
export async function contractPayList(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageContractPayResp>('/api/fina/contract-pay-list-mobile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 计算补贴 POST /api/fina/fina/compute-subsidy */
export async function computeSubsidy(body: API.FinaSubsidyReq, options?: { [key: string]: any }) {
  return request<API.ResultString>('/api/fina/fina/compute-subsidy', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取当前用户工单列表 POST /api/fina/fina/current-user-work */
export async function currentUserWork(
  body: API.CurrentUserWorkReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultListCurrentUserWorkResp>('/api/fina/fina/current-user-work', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 报销汇总 POST /api/fina/fina/fina-count */
export async function finaCount(body: API.FinaCountReq, options?: { [key: string]: any }) {
  return request<API.ResultFinaCountResp>('/api/fina/fina/fina-count', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增报销 POST /api/fina/fina/fina-create */
export async function finaCreate(body: API.FinaInsertReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/fina/fina/fina-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 报销信息 GET /api/fina/fina/fina-info */
export async function finaInfo(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.finaInfoParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultFinaInfoResp>('/api/fina/fina/fina-info', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 报销分页 POST /api/fina/fina/fina-page */
export async function finaPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageFinaPageResp>('/api/fina/fina/fina-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新报销 POST /api/fina/fina/fina-update */
export async function finaUpdate(body: API.FinaUpdateReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/fina/fina/fina-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除报销 POST /api/fina/fina/work-order-del */
export async function finaDel(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/fina/fina/work-order-del', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询付款流水 POST /api/fina/page-payment-mobile */
export async function pagePaymentMobile(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultFlowPagePaymentResp>('/api/fina/page-payment-mobile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 待开票列表 POST /api/fina/pending-invoice-list-mobile */
export async function pendingInvoiceList(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPagePendingInvoiceResp>('/api/fina/pending-invoice-list-mobile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 项目付款申请 POST /api/fina/project-pay-create-mobile */
export async function projectPayCreate(
  body: API.PaymentApplicationInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultPageContractPayResp>('/api/fina/project-pay-create-mobile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询流水列表 POST /api/fina/select-receipt-list-mobile */
export async function selectReceiptList(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageReceiptsResp>('/api/fina/select-receipt-list-mobile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据主键查询收款流水详细信息 GET /api/fina/select-receipt-mobile */
export async function selectReceipt(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.selectReceiptParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultReceiptsInfoResp>('/api/fina/select-receipt-mobile', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}
