// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 创建资质 创建资质 POST /api/business/createAptitude */
export async function createAptitude(body: API.AptitudeReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/business/createAptitude', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建投标账号信息 创建投标账号信息 POST /api/business/createTender */
export async function createTender(body: API.TenderReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/business/createTender', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除资质 删除资质 DELETE /api/business/deleteAptitude */
export async function deleteAptitude(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/business/deleteAptitude', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除投标账号信息 删除投标账号信息 DELETE /api/business/deleteTender */
export async function deleteTender(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/business/deleteTender', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 主键查询资质 主键查询资质 GET /api/business/getAptitudeById */
export async function getAptitudeById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAptitudeByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultAptitudeResp>('/api/business/getAptitudeById', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 主键查询投标账号相关信息 主键查询投标账号相关信息 GET /api/business/getTenderById */
export async function getTenderById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getTenderByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultTenderResp>('/api/business/getTenderById', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 更新资质 更新资质 PUT /api/business/modifyAptitude */
export async function modifyAptitude(body: API.AptitudeReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/business/modifyAptitude', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新投标账号 更新投标账号 PUT /api/business/modifyTender */
export async function modifyTender(body: API.TenderReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/business/modifyTender', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询资质信息 分页查询资质信息 POST /api/business/pageAptitude */
export async function pageAptitude(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageAptitudeResp>('/api/business/pageAptitude', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询投标账号 分页查询投标账号 POST /api/business/pageTender */
export async function pageTender(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageTenderResp>('/api/business/pageTender', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
