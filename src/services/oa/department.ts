// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 部门树带员工 GET /api/employee/department/department-and-user-tree */
export async function getDepartmentAndUserTree(options?: { [key: string]: any }) {
  return request<API.ResultListDepartmentAndUserTreeResp>(
    '/api/employee/department/department-and-user-tree',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 创建部门 POST /api/employee/department/department-create */
export async function createDepartment(
  body: API.DepartmentInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/department/department-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除部门 POST /api/employee/department/department-del */
export async function departmentDel(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/department/department-del', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 部门列表 GET /api/employee/department/department-list */
export async function getDepartmentList(options?: { [key: string]: any }) {
  return request<API.ResultListDepartmentListResp>('/api/employee/department/department-list', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 部门树 GET /api/employee/department/department-tree */
export async function getDepartmentTree(options?: { [key: string]: any }) {
  return request<API.ResultListDepartmentTreeResp>('/api/employee/department/department-tree', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新部门 POST /api/employee/department/department-update */
export async function updateDepartment(
  body: API.DepartmentUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/department/department-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 需要写周报的部门树 GET /api/employee/department/department-weekly-tree */
export async function getDepartmentWeeklyTree(options?: { [key: string]: any }) {
  return request<API.ResultListDepartmentWeekTreeResp>(
    '/api/employee/department/department-weekly-tree',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}
