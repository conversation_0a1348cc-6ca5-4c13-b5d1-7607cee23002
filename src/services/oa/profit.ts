// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 主合同利润报表编辑费用 POST /api/profit/main-contract-edit */
export async function profitMainContractEdit(
  body: API.MainCostEditReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/profit/main-contract-edit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 主合同利润报表 POST /api/profit/main-contract-page */
export async function profitMainContract(
  body: API.ProfitPageReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultContractProfitPageContractProfitResp>('/api/profit/main-contract-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 开发项目交付成本 POST /api/profit/project-delivery-kf */
export async function projectDeliveryKf(body: API.ProfitPageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageProjectDeliveryResp>('/api/profit/project-delivery-kf', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 售后项目交付成本 POST /api/profit/project-delivery-sh */
export async function projectDeliverySh(body: API.ProfitPageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageProjectDeliveryResp>('/api/profit/project-delivery-sh', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
