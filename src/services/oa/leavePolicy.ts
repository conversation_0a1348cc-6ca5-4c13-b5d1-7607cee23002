// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 创建休假政策 POST /api/employee/leave-policy/leave-policy-create */
export async function createLeavePolicy(
  body: API.LeavePolicyInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/leave-policy/leave-policy-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 休假政策详情 POST /api/employee/leave-policy/leave-policy-info */
export async function leavePolicyInfo(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultLeavePolicyInfoResp>('/api/employee/leave-policy/leave-policy-info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 休假政策分页 POST /api/employee/leave-policy/leave-policy-page */
export async function leavePolicyPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageLeavePolicyPageResp>(
    '/api/employee/leave-policy/leave-policy-page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 更新假期(列表) POST /api/employee/leave-policy/leave-policy-update-detail */
export async function updateLeavePolicy(
  body: API.LeavePolicyDetailUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/leave-policy/leave-policy-update-detail', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
