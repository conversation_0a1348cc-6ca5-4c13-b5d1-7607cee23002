// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 数据列表 GET /api/common/scheduler/scheduler-list */
export async function schedulerList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.schedulerListParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/common/scheduler/scheduler-list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
