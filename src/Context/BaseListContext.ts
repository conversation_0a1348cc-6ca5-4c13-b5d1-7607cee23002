import { createContext } from 'react';

type BaseListContextProps = {
  userList?: API.UserPageResp[];
  userLoading?: boolean;
  contractList?: API.MainContractInfoResp[];
  contractLoading?: boolean;
  projectList?: API.ProBaseInfoResp[];
  projectLoading?: boolean;
  partnerList?: API.PartnerInfoResp[];
  partnerLoading?: boolean;
  departmentList?: API.DepartmentListResp[];
  departmentLoading?: boolean;
  data?: Record<string, any>;

  shWorkOrderList?: API.CurrentUserWorkResp[];
  shWorkOrderLoading?: boolean;
  sqWorkOrderList?: API.CurrentUserWorkResp[];
  sqWorkOrderLoading?: boolean;

  availableProjectList?: API.ProBaseInfoResp[];
  availableProjectLoading?: boolean;

  historyContactList?: API.HistoricalContactsResp[];

  certificateTypeList?: API.SysCertificateListResp[];
  certificateTypeLoading?: boolean;
};

const BaseListContext = createContext<BaseListContextProps>({});

export default BaseListContext;
