import { userDropDownList } from '@/services/oa/auth';
import { useRequest } from '@umijs/max';
// 不受权限控制的用户列表，下拉内用
/**
 * @title 不受权限控制的用户列表
 * @param activation 是否激活
 * @returns userList 用户列表
 * @returns loading 加载状态
 */
export function useUserList(activation = false) {
  const { data, loading } = useRequest(() => userDropDownList({ activation }));
  return {
    userList:
      data?.map((item) => ({
        ...item,
        disabled: item?.activation !== 1,
      })) || [],
    loading,
  };
}
