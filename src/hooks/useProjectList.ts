import { pageSearchProject } from '@/services/oa/project';
import { useRequest } from '@umijs/max';

/**
 * @title 获取项目列表
 * @description 审核通过且状态正常的项目
 * @returns projectList 项目列表
 * @returns loading 项目列表加载状态
 */
export function useProjectList() {
  const { data, loading } = useRequest(() =>
    pageSearchProject({
      pageNum: 1,
      pageSize: 10000,
      filter: { status: 'NORMAL', activiStatus: '2' },
    }),
  );
  return {
    projectList: data?.records || [],
    loading,
  };
}
