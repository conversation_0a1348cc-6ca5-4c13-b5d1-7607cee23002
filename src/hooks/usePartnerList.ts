import { pagePartner } from '@/services/oa/partner';
import { useRequest } from '@umijs/max';

/**
 * @title 审核通过的业务伙伴
 * @description 仅下拉框使用
 * @returns partnerList 业务伙伴列表
 * @returns loading 是否加载中
 */
export function usePartnerList() {
  const { data, loading } = useRequest(() =>
    pagePartner({
      pageNum: 1,
      pageSize: 1000,
      search: { activiStatus: '2' },
    }),
  );
  return {
    partnerList: data?.records || [],
    loading,
  };
}
