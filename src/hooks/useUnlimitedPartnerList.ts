import { selectList } from '@/services/oa/partner';
import { useRequest } from '@umijs/max';

/**
 * @title 无限制客户列表
 * @description 目前仅收款记录使用
 * @description 收款记录中对方单位仅为客户和招标机构
 * @description 招标机构和客户不可能重复
 * @returns partnerList 客户列表
 * @returns loading 加载状态
 */

export function useUnlimitedPartnerList() {
  const { data, loading } = useRequest(() => selectList());
  return {
    //note: 目前仅收款记录使用,无权限限制,收款记录中对方单位仅为客户和招标机构,招标机构和客户不可能重复
    partnerList: data || [],
    loading,
  };
}
