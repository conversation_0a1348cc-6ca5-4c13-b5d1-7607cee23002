import { projectList } from '@/services/oa/project';
import { useRequest } from '@umijs/max';

/**
 * @title 查询可用项目（审核通过且状态正常）
 * @description 目前仅在报销、周报、工单、项目付款等场景中使用
 * @description 仅项目成员可见的列表
 *
 * @returns  availableProjectList 项目列表
 * @returns  loading 加载状态
 */

export function useAvailableProjectList() {
  const { data, loading } = useRequest(() => projectList());
  return {
    availableProjectList: data || [],
    loading,
  };
}
