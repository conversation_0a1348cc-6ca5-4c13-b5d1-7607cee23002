import { mainContractList } from '@/services/oa/contract';
import { useRequest } from '@umijs/max';

/**
 * @title 用户可见的正常状态和已审批通过的主合同列表(如果合同状态不为执行中会被禁用)
 * @returns contractList 主合同列表
 * @returns loading 主合同列表加载状态
 */

export function useAvailableMainContractList() {
  const { data, loading } = useRequest(() => mainContractList());
  return {
    contractList:
      data?.map((item: API.MainContractListResp) => ({
        ...item,
        disabled: item.contractStatus !== 'EXECUTING',
      })) || [],
    loading,
  };
}
