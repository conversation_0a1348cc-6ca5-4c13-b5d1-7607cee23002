import { CUSTOMER_LEVEL, PROJECT_BRAND, PROJECT_PROGRESS } from '@/enums';
import { useDepartmentUserList } from '@/hooks/useDepartmentUserList';
import { salesPlanSaleList } from '@/services/oa/sale';
import { option2enum, queryPagingTable, sortMap } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProTable,
  StatisticCard,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { useRef, useState } from 'react';

const SalesPlanTable: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [statisticData, setStatisticData] = useState<Record<string, any>>();
  const { userList: saleUserList, loading: saleUserLoading } = useDepartmentUserList('6');

  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '客户简称',
      dataIndex: 'clientAbbreviation',
      hideInTable: true,
    },
    {
      title: '客户简称',
      dataIndex: 'clientAbbreviation',
      fixed: 'left',
      ellipsis: true,
      copyable: true,
      width: 150,
      render: (dom, entity) => {
        return (
          <a
            className="rk-a-span"
            onClick={() => {
              history.push(`/crm/customer/edit/${entity.partnerId}`);
            }}
          >
            {dom}
          </a>
        );
      },
      search: false,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 150,
      search: false,
      ellipsis: true,
    },
    {
      title: '项目描述',
      dataIndex: 'projectDescription',
      width: 150,
      search: false,
      ellipsis: true,
    },
    {
      title: '项目类型',
      dataIndex: 'projectClassify',
      valueType: 'select',
      valueEnum: option2enum(PROJECT_BRAND),
      ellipsis: true,
      width: 100,
    },
    {
      title: '销售金额',
      dataIndex: 'saleAmount',
      valueType: 'money',
      width: 120,
      search: false,
      ellipsis: true,
    },
    {
      title: '采购成本',
      dataIndex: 'purchaseCost',
      width: 120,
      search: false,
      valueType: 'money',
      ellipsis: true,
    },
    {
      title: '招标计划',
      dataIndex: 'remark',
      valueType: 'dateMonth',
      width: 120,
      search: false,
      ellipsis: true,
    },
    {
      title: '项目概率',
      dataIndex: 'proProgress',
      valueType: 'select',
      valueEnum: option2enum(PROJECT_PROGRESS),
      ellipsis: true,
      width: 90,
    },
    {
      title: '客户经理',
      dataIndex: 'clientManger',
      width: 90,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '客户经理',
      dataIndex: 'clientMangerId',
      valueType: 'select',
      fieldProps: {
        options: saleUserList,
        loading: saleUserLoading,
        showSearch: true,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        filterOption: true,
        optionFilterProp: 'label',
      },
      hideInTable: true,
    },
    {
      title: '客户等级',
      dataIndex: 'grade',
      width: 80,
      ellipsis: true,
      valueEnum: option2enum(CUSTOMER_LEVEL),
    },
    {
      title: '销售成员',
      dataIndex: 'salePerson',
      width: 90,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedTime',
      hideInSearch: true,
      sorter: true,
      width: 180,
    },
  ];
  return (
    <PageContainer header={{ title: false }}>
      <StatisticCard.Group style={{ marginBottom: 16 }}>
        <StatisticCard
          statistic={{
            title: '销售总金额',
            value: statisticData?.salesAmount || 0,
            prefix: '￥',
            status: 'processing',
          }}
        />
      </StatisticCard.Group>
      <ProTable
        {...defaultTableConfig}
        headerTitle="销售计划表"
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        search={{
          defaultCollapsed: false,
          filterType: 'query',
        }}
        request={async (params, sort) => {
          const {
            current,
            pageSize,
            clientAbbreviation,
            projectClassify,
            proProgress,
            salePersonId,
            clientMangerId,
            grade,
            ...rest
          } = params as Record<string, any>;
          const filter = { projectClassify, proProgress, salePersonId, clientMangerId, grade };
          const search = { clientAbbreviation };
          const sortKey: 'ascend' | 'descend' | undefined =
            sort && (sort.updatedTime === 'ascend' || sort.updatedTime === 'descend')
              ? sort.updatedTime
              : undefined;
          const res = await queryPagingTable(
            {
              current,
              pageSize,
              search,
              filter,
              sort: {
                sortUpdatedTime: sortKey ? sortMap[sortKey] : undefined,
              },
              ...rest,
            },
            salesPlanSaleList,
          );
          const { saleAmountCount, total, records } = (res as Record<string, any>) || {};
          setStatisticData({
            salesAmount: saleAmountCount,
          });
          return { success: true, data: records || [], total: total };
        }}
      />
    </PageContainer>
  );
};

export default SalesPlanTable;
