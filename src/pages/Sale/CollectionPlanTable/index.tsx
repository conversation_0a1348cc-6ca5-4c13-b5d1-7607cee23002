import { COLLECTION_STATUS, CUSTOMER_LEVEL } from '@/enums';
import { useDepartmentUserList } from '@/hooks/useDepartmentUserList';
import { sortMap } from '@/pages/ReportAnalysis/WorkloadStatistics';
import { collectionPlanSaleList } from '@/services/oa/sale';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProTable,
  StatisticCard,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';

const CollectionPlanTable: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [statisticData, setStatisticData] = useState<Record<string, any>>();
  const { userList, loading } = useDepartmentUserList('6');

  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '客户简称',
      dataIndex: 'clientAbbreviation',
      hideInTable: true,
    },
    {
      title: '日期范围',
      dataIndex: 'month',
      valueType: 'dateMonthRange',
      hideInTable: true,
    },
    {
      title: '收款状态',
      dataIndex: 'status',
      valueEnum: option2enum(COLLECTION_STATUS),
      hideInTable: true,
    },

    {
      title: '客户简称',
      dataIndex: 'clientAbbreviation',
      width: 100,
      fixed: 'left',
      ellipsis: true,
      copyable: true,
      search: false,
      render: (dom, entity) => {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/crm/customer/edit/${entity.partnerId}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      width: 100,
      search: false,
      ellipsis: true,
      render: (dom, entity) => {
        return (
          <a
            className="rk-a-span"
            onClick={() => {
              history.push(`/contract/main/edit/${entity.contractId}`);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
      width: 220,
      search: false,
      ellipsis: true,
    },
    {
      title: '期次',
      dataIndex: 'period',
      width: 60,
      search: false,
      ellipsis: true,
    },
    {
      title: '计划收款金额',
      valueType: 'money',
      dataIndex: 'estimateReAmount',
      width: 150,
      search: false,
      ellipsis: true,
      sorter: true,
    },
    {
      title: '计划收款日期',
      valueType: 'date',
      dataIndex: 'estimateReTime',
      width: 120,
      search: false,
      ellipsis: true,
      sorter: true,
    },
    {
      title: '开票日期',
      valueType: 'date',
      dataIndex: 'ticketTime',
      width: 120,
      search: false,
      ellipsis: true,
      sorter: true,
    },
    {
      title: '收款状态',
      dataIndex: 'status',
      valueEnum: option2enum(COLLECTION_STATUS),
      width: 100,
      search: false,
    },
    {
      title: '客户等级',
      dataIndex: 'grade',
      width: 100,
      ellipsis: true,
      valueEnum: option2enum(CUSTOMER_LEVEL),
    },
    {
      title: '销售',
      dataIndex: 'salePerson',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '销售',
      dataIndex: 'salePersonId',
      valueType: 'select',
      fieldProps: {
        options: userList,
        loading,
        showSearch: true,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        filterOption: true,
        optionFilterProp: 'label',
      },
      hideInTable: true,
    },
  ];

  return (
    <PageContainer header={{ title: false }}>
      <StatisticCard.Group style={{ marginBottom: 16 }}>
        <StatisticCard
          statistic={{
            title: '计划收款总金额',
            value: statisticData?.collectionAmount || 0,
            prefix: '￥',
            status: 'processing',
          }}
        />
      </StatisticCard.Group>
      <ProTable
        {...defaultTableConfig}
        headerTitle="收款计划表"
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        search={{
          defaultCollapsed: false,
          filterType: 'query',
        }}
        request={async (params, sort) => {
          const { current, pageSize, status, month, clientAbbreviation, salePersonId, grade } =
            params;
          const extra = { status };
          const search = { clientAbbreviation };
          const scope =
            month &&
            [
              {
                key: 'ge',
                name: 'estimateReTime',
                val: dayjs(month[0]).startOf('month').format('YYYY-MM-DD'),
              },
              {
                key: 'le',
                name: 'estimateReTime',
                val: dayjs(month[1]).endOf('month').format('YYYY-MM-DD'),
              },
            ].filter(Boolean);
          const filter = { salePersonId, grade };
          const sortEstimateReAmount =
            sort && sort?.estimateReAmount ? sortMap[sort.estimateReAmount] : undefined;
          const sortEstimateReTime =
            sort && sort?.estimateReTime ? sortMap[sort.estimateReTime] : undefined;
          const sortTicketTime = sort && sort?.ticketTime ? sortMap[sort.ticketTime] : undefined;

          const msg = await collectionPlanSaleList({
            pageNum: current,
            pageSize,
            search,
            scope,
            extra,
            filter,
            sortEstimateReAmount,
            sortEstimateReTime,
            sortTicketTime,
          });
          const { estimateReAmount, records, total } = (msg.data as Record<string, any>) || {};
          setStatisticData({
            collectionAmount: estimateReAmount,
          });
          return {
            data: records || [],
            success: true,
            total: total,
          };
        }}
      />
    </PageContainer>
  );
};

export default CollectionPlanTable;
