import { PERFORMANCE_STATUS } from '@/enums';
import { useDepartmentUserList } from '@/hooks/useDepartmentUserList';
import {
  initialPerformanceAppraisalTable,
  performanceEnd,
  performanceStart,
} from '@/services/oa/sale';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { Button, message, Space } from 'antd';
import React, { useRef } from 'react';

const PerformanceConfig: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const {
    canStartPerformanceConfig = false,
    canEditPerformanceConfig = false,
    canEndPerformanceConfig = false,
  } = useAccess();

  const { userList: salesList, loading: salesListLoading } = useDepartmentUserList('6');

  //开始核算
  const { run: handleStart, fetches: startFetches } = useRequest(
    (id, receiveYear) => performanceStart({ id, receiveYear }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('操作成功');
        tableRef.current?.reload?.();
      },
      fetchKey: (res) => {
        return res;
      },
      formatResult: (res) => res,
    },
  );

  // 结束核算
  const { run: handleEnd, fetches: endFetches } = useRequest(
    (id, receiveYear) => performanceEnd({ id, receiveYear }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('操作成功');
        tableRef.current?.reload?.();
      },
      fetchKey: (res) => {
        return res;
      },
      formatResult: (res) => res,
    },
  );

  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '核算编号',
      dataIndex: 'documentNumber',
      width: 280,
      copyable: true,
    },
    {
      title: '核算状态',
      dataIndex: 'performanceStatus',
      width: 120,
      valueEnum: option2enum(PERFORMANCE_STATUS),
    },
    {
      title: '核算年度',
      dataIndex: 'receiveYear',
      valueType: 'dateYear',
      width: 120,
    },
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      copyable: true,
      search: false,
      width: 280,
      render(dom, entity) {
        return (
          <a className="rk-a-span" onClick={() => history.push(`/contract/main/edit/${entity.id}`)}>
            {dom}
          </a>
        );
      },
    },

    {
      title: '合同名称',
      dataIndex: 'contractName',
      width: 400,
      ellipsis: true,
    },
    {
      title: '客户简称',
      dataIndex: 'customerAbbreviation',
      width: 120,
      ellipsis: true,
    },
    {
      title: '销售',
      dataIndex: 'salePerson',
      width: 120,
      ellipsis: true,
      search: false,
    },
    {
      title: '销售',
      dataIndex: 'salePersonId',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: salesList,
        loading: salesListLoading,
        showSearch: true,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        optionFilterProp: 'label',
        filterOption: true,
      },
    },
    {
      title: '操作',
      fixed: 'right',
      width: 230,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        const { id, performanceStatus, receiveYear } = record;
        return (
          <Space>
            <Access accessible={canEditPerformanceConfig}>
              <Button
                type="link"
                key="allow"
                className="inner-table-link"
                style={{ color: '#08979c' }}
                onClick={() => {
                  history.push(`/sale/performance-config/edit/${record.id}/${record.receiveYear}`);
                }}
              >
                核算配置
              </Button>
            </Access>
            {['BEEN_ACCOUNTED', 'ACCOUNT_CONFIRMED', 'ACCOUNT_REJECTED'].includes(
              performanceStatus,
            ) && (
              <Button
                type="link"
                className="inner-table-link"
                style={{ color: '#2f54eb' }}
                key="details"
                onClick={() => {
                  history.push(`/sale/performance-config/initDetails/${id}/${record.receiveYear}`);
                }}
              >
                核算详情
              </Button>
            )}
            <Access accessible={canStartPerformanceConfig}>
              {['NOT_ACCOUNT', 'ACCOUNT_REJECTED'].includes(performanceStatus) && (
                <Button
                  type="link"
                  className="inner-table-link"
                  style={{ color: '#7cb305' }}
                  key="cancel"
                  loading={startFetches[id]?.loading}
                  disabled={endFetches[id]?.loading}
                  onClick={() => handleStart(id, receiveYear)}
                >
                  开始核算
                </Button>
              )}
            </Access>
            <Access accessible={canEndPerformanceConfig}>
              {['NOT_ACCOUNT', 'ACCOUNTING', 'ACCOUNT_REJECTED'].includes(performanceStatus) && (
                <Button
                  type="link"
                  className="inner-table-link"
                  style={{ color: '#f5222d' }}
                  key="cancel"
                  loading={endFetches[id]?.loading}
                  disabled={startFetches[id]?.loading}
                  onClick={() => handleEnd(id, receiveYear)}
                >
                  结束核算
                </Button>
              )}
            </Access>
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer header={{ title: false }}>
      <ProTable
        {...defaultTableConfig}
        headerTitle="项目核算表"
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        search={{
          defaultCollapsed: false,
          filterType: 'query',
        }}
        request={async (params) => {
          const {
            current,
            pageSize,
            receiveYear,
            performanceStatus,
            documentNumber,
            salePersonId,
            contractName,
            customerAbbreviation,
          } = params;
          const msg = await initialPerformanceAppraisalTable({
            pageNum: current,
            pageSize,
            extra: {
              receiveYear,
              performanceStatus,
              documentNumber,
              salePersonId,
              contractName,
              customerAbbreviation,
            },
          });

          return {
            ...msg?.data,
            data: msg?.data?.records || [],
            success: true,
            total: msg?.data?.total,
          };
        }}
      />
    </PageContainer>
  );
};

export default PerformanceConfig;
