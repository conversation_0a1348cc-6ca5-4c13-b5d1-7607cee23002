import RKCol from '@/components/RKCol';
import BaseContext from '@/Context/BaseContext';
import { useDepartmentUserList } from '@/hooks/useDepartmentUserList';
import {
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormList,
  ProFormMoney,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useReactive } from 'ahooks';
import { Alert, Drawer, Row, Space } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { FC, useContext, useState } from 'react';

interface PerformanceConfigProps {
  formRef: React.MutableRefObject<ProFormInstance<any> | undefined>;
  setFormData: React.Dispatch<React.SetStateAction<API.PerformanceConfResp | undefined>>;
}

const PerformanceConfig: FC<PerformanceConfigProps> = ({ formRef, setFormData }) => {
  const { isEditPage } = useContext(BaseContext);

  const [drawerVisible, setDrawerVisible] = useState(false);
  const errorState = useReactive({
    errorMessage: '',
    errorVisible: false,
  });

  const { userList: salesList, loading: salesListLoading } = useDepartmentUserList('6');

  return (
    <Row gutter={24}>
      <RKCol lg={8} md={8} sm={12}>
        <ProFormDigit
          label={
            <Space>
              <span>比例</span>
              <a onClick={() => setDrawerVisible(true)}>比例分配</a>
            </Space>
          }
          min={0}
          disabled
          fieldProps={{
            addonAfter: '%',
          }}
          name={['performanceInfoResp', 'ratio']}
          rules={[
            {
              required: true,
              message: '比例为必填项',
            },
          ]}
        />
      </RKCol>
      <RKCol lg={8} md={8} sm={12}>
        <ProFormMoney
          disabled={!isEditPage}
          label="咨询费用"
          min={0}
          name={['performanceInfoResp', 'consultingFee']}
        />
      </RKCol>
      <RKCol lg={8} md={8} sm={12}>
        <ProFormMoney
          disabled={!isEditPage}
          label="实施成本"
          min={0}
          name={['performanceInfoResp', 'implementationCost']}
        />
      </RKCol>
      <RKCol lg={8} md={8} sm={12}>
        <ProFormMoney
          disabled={!isEditPage}
          label="资金成本"
          min={0}
          name={['performanceInfoResp', 'fundCost']}
        />
      </RKCol>
      <RKCol lg={16} md={16} sm={12}>
        <ProFormTextArea
          name={['performanceInfoResp', 'remark']}
          disabled={!isEditPage}
          label="备注"
          fieldProps={{
            autoSize: {
              minRows: 1,
              maxRows: 3,
            },
          }}
        />
      </RKCol>
      <Drawer
        width={753}
        title="比例分配"
        onClose={() => {
          if (isEditPage) {
            const currentArr = formRef.current?.getFieldValue('performanceInfoResp')?.salesList;

            //检测salesList数组是否为空
            // if (!currentArr?.length) {
            //   errorState.errorMessage = '请至少选择一个销售人员！';
            //   errorState.errorVisible = true;
            //   return;
            // }

            //检测salesList数组中对象是否有空值
            if (
              currentArr?.some(
                (obj: API.SalesRatioReq) => (!obj.ratio && obj.ratio !== 0) || !obj.userId,
              )
            ) {
              errorState.errorMessage = '销售或比例不能为空！';
              errorState.errorVisible = true;
              return;
            }

            //检测salesList数组中是否有重复的销售
            const userIds = new Set();
            for (const obj of currentArr) {
              if (userIds.has(obj.userId)) {
                errorState.errorMessage = '不能重复选择销售人员！';
                errorState.errorVisible = true;
                return;
              }
              userIds.add(obj.userId);
            }

            const sum = currentArr?.reduce(
              (acc: number, cur: API.SalesRatioReq) => acc + cur.ratio,
              0,
            );

            const newFormData = formRef.current?.getFieldsValue();
            if (newFormData?.performanceInfoResp) {
              newFormData.performanceInfoResp.ratio = sum >= 0 ? sum : 100;
              // 同步更新formData和表单值
              setFormData(newFormData);
              formRef.current?.setFieldValue('performanceInfoResp', {
                ...formRef.current?.getFieldValue('performanceInfoResp'),
                ratio: sum >= 0 ? sum : 100,
              });
            }
          }

          setDrawerVisible(false);
          errorState.errorVisible = false;
        }}
        open={drawerVisible}
      >
        {errorState.errorVisible && (
          <Alert
            message={errorState.errorMessage}
            type="error"
            style={{ marginBottom: 30 }}
            closable
            showIcon
            onClose={() => {
              errorState.errorVisible = false;
            }}
          />
        )}
        <ProFormList
          name={['performanceInfoResp', 'salesList']}
          creatorButtonProps={
            isEditPage
              ? {
                  creatorButtonText: '新建',
                }
              : false
          }
          max={salesList?.length || 0}
          copyIconProps={false}
          deleteIconProps={isEditPage ? {} : false}
          itemRender={({ listDom, action }) => (
            <div
              style={{
                display: 'inline-flex',
              }}
            >
              {listDom}
              {action}
            </div>
          )}
        >
          <ProForm.Group>
            <ProFormSelect
              width={280}
              name="userId"
              disabled={!isEditPage}
              label="销售"
              fieldProps={{
                loading: salesListLoading,
                fieldNames: {
                  value: 'id',
                  label: 'username',
                },
                showSearch: true,
                onChange: (value) => {
                  // 选择销售后需要存储此销售的username
                  const currentArray =
                    formRef.current?.getFieldValue('performanceInfoResp')?.salesList;

                  const username = (salesList as Record<string, any>[])?.find(
                    (i) => i.id === value,
                  )?.username;

                  const editingItem = currentArray?.find(
                    (i: API.SalesRatioReq) => i.userId === value,
                  );

                  const newItem = {
                    ...editingItem,
                    username,
                  };

                  const ind = currentArray?.findIndex((i: API.SalesRatioReq) => i.userId === value);

                  currentArray.splice(ind, 1, newItem);

                  formRef.current?.setFieldValue('performanceInfoResp', {
                    ...formRef.current?.getFieldValue('performanceInfoResp'),
                    salesList: currentArray,
                  });
                },
              }}
              options={salesList as DefaultOptionType[]}
            />
            <ProFormDigit
              label="比例"
              width={280}
              min={0}
              initialValue={100}
              disabled={!isEditPage}
              fieldProps={{
                addonAfter: '%',
              }}
              name="ratio"
            />
          </ProForm.Group>
        </ProFormList>
      </Drawer>
    </Row>
  );
};
export default PerformanceConfig;
