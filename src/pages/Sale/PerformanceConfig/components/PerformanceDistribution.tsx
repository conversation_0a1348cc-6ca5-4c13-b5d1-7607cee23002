import BaseContext from '@/Context/BaseContext';
import { Table } from 'antd';
import { useContext } from 'react';

interface PerformanceDistributionProps {
  salesList?: API.SalesRatioResp[];
}

const PerformanceDistribution: React.FC<PerformanceDistributionProps> = ({ salesList = [] }) => {
  const { totalAchievement = 0 } = useContext(BaseContext);

  const columns = [
    {
      title: '销售',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '比例',
      dataIndex: 'ratio',
      key: 'ratio',
      render: (value: number) => `${value}%`,
    },
    {
      title: '业绩',
      key: 'performance',
      render: (_: any, record: Record<string, any>) => {
        return '¥' + ((totalAchievement * record.ratio) / 100).toFixed(2);
      },
    },
  ];

  return (
    <Table
      dataSource={salesList}
      columns={columns}
      pagination={false}
      style={{ width: '100%', marginBottom: 24 }}
    />
  );
};

export default PerformanceDistribution;
