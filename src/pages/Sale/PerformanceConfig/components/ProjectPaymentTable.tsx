import BaseContext from '@/Context/BaseContext';
import { PAYMENT, PAYMENT_TYPE, PERFORMANCE_STATUS, WRITE_OFF_STATUS } from '@/enums';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Space } from 'antd';
import React, { forwardRef, useContext, useEffect, useImperativeHandle, useState } from 'react';

const columns: ProColumns<API.ProjectPayResp>[] = [
  {
    title: '核算状态',
    dataIndex: 'performanceStatus',
    width: 100,
    tooltip: '状态为核算中且本条数据被标红则代表该条数据正在被其他合同核算，请注意',
    render: (_, record) => {
      const statusObj = option2enum(PERFORMANCE_STATUS)?.[record?.performanceStatus as string];
      return (
        <Space>
          <span
            style={{
              color:
                !record.currentPerformance && record.performanceStatus === 'ACCOUNTING'
                  ? 'red'
                  : undefined,
            }}
          >
            {statusObj.label}
          </span>
        </Space>
      );
    },
  },
  {
    title: '付款编号',
    dataIndex: 'documentNumber',
    width: 200,
    ellipsis: true,
  },
  {
    title: '付款人',
    dataIndex: 'username',
    ellipsis: true,
    width: 180,
  },
  {
    title: '付款类型',
    dataIndex: 'paymentType',
    valueEnum: option2enum(PAYMENT_TYPE),
    width: 110,
  },
  {
    title: '付款方式',
    dataIndex: 'paymentMethod',
    valueEnum: option2enum(PAYMENT),
    width: 110,
  },
  {
    title: '付款金额',
    dataIndex: 'paymentAmount',
    valueType: 'money',
    width: 130,
  },
  {
    title: '付款日期',
    dataIndex: 'payTime',
    ellipsis: true,
    width: 120,
  },
  {
    title: '销账标识',
    dataIndex: 'refundTag',
    valueEnum: option2enum(WRITE_OFF_STATUS),
    width: 110,
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    ellipsis: true,
    width: 180,
  },
  {
    title: '核算编号',
    dataIndex: 'perfNumber',
    ellipsis: true,
    width: 200,
  },
];

interface ReceiptPlanTableProps {
  value?: API.ProjectPayResp[];
  onChangeSelect: (selectedRows: Record<string, any>[]) => void;
}

export interface ProjectPaymentTableRef {
  getCheckedKeys(): string[];
}

const ProjectPaymentTable: React.ForwardRefRenderFunction<
  ProjectPaymentTableRef,
  ReceiptPlanTableProps
> = ({ value, onChangeSelect }, ref) => {
  const { isEditPage, isFinalDetailPage } = useContext(BaseContext);

  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
  useImperativeHandle(ref, () => ({
    getCheckedKeys: () => checkedKeys,
  }));

  useEffect(() => {
    const selectedArr = value?.filter((i) => !!i?.currentPerformance)?.map((i) => i?.id);
    if (Array.isArray(selectedArr) && selectedArr.length) {
      setCheckedKeys(selectedArr as string[]);
    }
  }, [value]);

  useEffect(() => {
    const arr = value?.filter((i) => {
      return checkedKeys?.includes(i.id as string);
    });

    onChangeSelect(arr || []);
  }, [checkedKeys]);

  return (
    <ProTable<API.ProjectPayResp>
      {...defaultTableConfig}
      rowKey="id"
      rowSelection={{
        selectedRowKeys: checkedKeys,
        onSelectAll: (selected, selectedRows, changeRows) => {
          if (!selected) {
            setCheckedKeys((prevState) =>
              prevState.filter((item) => !changeRows.some((row) => row.id === item)),
            );
          } else {
            setCheckedKeys(
              (prevState) =>
                [
                  ...prevState,
                  ...selectedRows.filter((item) => item).map((item) => item.id),
                ] as string[],
            );
          }
        },
        onSelect: (record, selected) => {
          if (selected) {
            setCheckedKeys((prevState) => [...prevState, record.id] as string[]);
          } else {
            setCheckedKeys((prevState) => prevState.filter((item) => item !== record.id));
          }
        },
        getCheckboxProps: (record) => {
          return {
            disabled:
              !isEditPage ||
              record.performanceStatus === 'BEEN_ACCOUNTED' ||
              (record.performanceStatus === 'ACCOUNTING' && !record.currentPerformance),
          };
        },
      }}
      scroll={{ x: '100%' }}
      dataSource={!isFinalDetailPage ? value : value?.filter((i) => !!i.currentPerformance)}
      options={false}
      className="inner-table"
      columns={columns}
    />
  );
};
export default forwardRef(ProjectPaymentTable);
