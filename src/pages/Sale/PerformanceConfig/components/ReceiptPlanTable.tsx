import BaseContext from '@/Context/BaseContext';
import { PAYMENT, PERFORMANCE_STATUS } from '@/enums';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProDescriptions, ProTable } from '@ant-design/pro-components';
import { useReactive } from 'ahooks';
import { Card, Space } from 'antd';
import React, { forwardRef, useContext, useEffect, useImperativeHandle, useState } from 'react';

const columns: ProColumns<API.PerfReceiptsResp>[] = [
  {
    title: '核算状态',
    dataIndex: 'performanceStatus',
    width: 100,
    tooltip: '状态为核算中且本条数据被标红则代表该条数据正在被其他合同核算，请注意',
    render: (_, record) => {
      const statusObj = option2enum(PERFORMANCE_STATUS)?.[record?.performanceStatus as string];
      return (
        <Space>
          <span
            style={{
              color:
                !record.currentPerformance && record.performanceStatus === 'ACCOUNTING'
                  ? 'red'
                  : undefined,
            }}
          >
            {statusObj.label}
          </span>
        </Space>
      );
    },
  },
  {
    title: '期次',
    width: 60,
    dataIndex: 'period',
  },
  {
    title: '计划收款金额',
    dataIndex: 'estimateReAmount',
    valueType: 'money',
    width: 130,
  },
  {
    title: '计划收款时间',
    dataIndex: 'estimateReTime',
    width: 120,
  },
  {
    title: '收款状态',
    dataIndex: 'collectionStatus',
    width: 110,
  },
  {
    title: '税额',
    dataIndex: 'rateAmount',
    valueType: 'money',
    width: 130,
  },
  {
    title: '收款金额',
    dataIndex: 'receiveAmount',
    valueType: 'money',
    width: 130,
  },
  {
    title: '收款时间',
    dataIndex: 'receiveTime',
    width: 180,
  },
  {
    title: '认领日期',
    dataIndex: 'claimTime',
    width: 180,
  },
  {
    title: '收款编号',
    dataIndex: 'collectNumber',
    width: 200,
  },
  {
    title: '收款方式',
    dataIndex: 'collectionWay',
    valueEnum: option2enum(PAYMENT),
    ellipsis: true,
    width: 110,
  },
  {
    title: '核算编号',
    dataIndex: 'perfNumber',
    width: 200,
  },
];

interface ReceiptPlanTableProps {
  value?: API.PerfReceiptsResp[];
  receivedAmountAll: string;
  onChangeSelect: (selectedRows: Record<string, any>[]) => void;
}

export interface ReceiptPlanTableRef {
  getCheckedKeys(): string[];
}

const ReceiptPlanTable: React.ForwardRefRenderFunction<
  ReceiptPlanTableRef,
  ReceiptPlanTableProps
> = ({ value, receivedAmountAll, onChangeSelect }, ref) => {
  const { isEditPage, isFinalDetailPage } = useContext(BaseContext);

  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);

  const tableHeaderData = useReactive({
    receivedAmount: 0,
    yearReceivedAmount: 0,
    yearTaxAmount: 0,
  });

  useImperativeHandle(ref, () => ({
    getCheckedKeys: () => checkedKeys,
  }));

  useEffect(() => {
    const selectedArr = value?.filter((i) => !!i?.currentPerformance)?.map((i) => i?.id);
    if (Array.isArray(selectedArr) && selectedArr.length) {
      setCheckedKeys(selectedArr as string[]);
    }
  }, [value]);

  useEffect(() => {
    const arr = value?.filter((i) => {
      return checkedKeys?.includes(i.id as string);
    });

    tableHeaderData.yearReceivedAmount =
      arr?.reduce((accumulator: number, current: API.PerfReceiptsResp) => {
        return accumulator + (current.receiveAmount || 0);
      }, 0) || 0;

    tableHeaderData.yearTaxAmount =
      arr?.reduce((accumulator: number, current: API.PerfReceiptsResp) => {
        return accumulator + (current.rateAmount || 0);
      }, 0) || 0;

    onChangeSelect(arr || []);
  }, [checkedKeys]);

  return (
    <ProTable<API.PerfReceiptsResp>
      {...defaultTableConfig}
      rowKey="id"
      rowSelection={{
        selectedRowKeys: checkedKeys,
        onSelectAll: (selected, selectedRows, changeRows) => {
          if (!selected) {
            setCheckedKeys((prevState) =>
              prevState.filter((item) => !changeRows.some((row) => row.id === item)),
            );
          } else {
            setCheckedKeys(
              (prevState) =>
                [
                  ...prevState,
                  ...selectedRows.filter((item) => item).map((item) => item.id),
                ] as string[],
            );
          }
        },
        onSelect: (record, selected) => {
          if (selected) {
            setCheckedKeys((prevState) => [...prevState, record.id] as string[]);
          } else {
            setCheckedKeys((prevState) => prevState.filter((item) => item !== record.id));
          }
        },
        getCheckboxProps: (record) => {
          return {
            disabled:
              !isEditPage ||
              record.performanceStatus === 'BEEN_ACCOUNTED' ||
              (record.performanceStatus === 'ACCOUNTING' && !record.currentPerformance),
          };
        },
      }}
      tableExtraRender={() => {
        return (
          <Card>
            <ProDescriptions size="small" column={3}>
              <ProDescriptions.Item valueType="money" label="已收款金额">
                {receivedAmountAll || 0}
              </ProDescriptions.Item>
              <ProDescriptions.Item valueType="money" label="年度收款金额">
                {tableHeaderData.yearReceivedAmount}
              </ProDescriptions.Item>
              <ProDescriptions.Item valueType="money" label="年度税额">
                {tableHeaderData.yearTaxAmount}
              </ProDescriptions.Item>
            </ProDescriptions>
          </Card>
        );
      }}
      scroll={{ x: '100%' }}
      dataSource={!isFinalDetailPage ? value : value?.filter((i) => !!i.currentPerformance)}
      options={false}
      className="inner-table"
      columns={columns}
    />
  );
};
export default forwardRef(ReceiptPlanTable);
