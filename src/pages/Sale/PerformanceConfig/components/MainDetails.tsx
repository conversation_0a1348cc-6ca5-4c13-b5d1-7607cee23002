import RKPageLoading from '@/components/RKPageLoading';
import BaseContext from '@/Context/BaseContext';
import PaymentPlanTable, {
  PaymentPlanTableRef,
} from '@/pages/Sale/PerformanceConfig/components/PaymentPlanTable';
import ProjectPaymentTable, {
  ProjectPaymentTableRef,
} from '@/pages/Sale/PerformanceConfig/components/ProjectPaymentTable';
import ReceiptPlanTable, {
  ReceiptPlanTableRef,
} from '@/pages/Sale/PerformanceConfig/components/ReceiptPlanTable';
import {
  performanceConf,
  performanceConfirm,
  performanceEnd,
  performanceSave,
  performanceStart,
} from '@/services/oa/sale';
import { onSuccessAndRefresh } from '@/utils';
import { useLocation } from '@@/exports';
import {
  FooterToolbar,
  PageContainer,
  PageHeader,
  ProForm,
  ProFormDependency,
  ProFormInstance,
} from '@ant-design/pro-components';
import { Access, useAccess, useModel, useRequest } from '@umijs/max';
import { Button, Collapse, message, Modal, Space } from 'antd';
import React, { useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import BaseInfo from './BaseInfo';
import PerformanceConfig from './PerformanceConfig';
import PerformanceDistribution from './PerformanceDistribution';
import ProjectTable, { ProjectTableRef } from './ProjectTable';

// 创建一个上下文
const CustomerDetails: React.FC = () => {
  const [formData, setFormData] = useState<API.PerformanceConfResp>();
  const { id, receiveYear } = useParams();
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  const {
    canEditPerformanceConfig = false,
    canStartPerformanceConfig = false,
    canEndPerformanceConfig = false,
    canConfirmPerformanceResult = false,
    canRejectPerformanceResult = false,
  } = useAccess();

  const receiptTableRef = useRef<ReceiptPlanTableRef>(null);
  const paymentTableRef = useRef<PaymentPlanTableRef>(null);
  const xsProjectTableRef = useRef<ProjectTableRef>(null);
  const sqProjectTableRef = useRef<ProjectTableRef>(null);
  const shProjectTableRef = useRef<ProjectTableRef>(null);
  const projectPaymentTableRef = useRef<ProjectPaymentTableRef>(null);

  const [selectedReceipts, setSelectedReceipts] = useState<Record<string, any>[]>([]);
  const [selectedPayments, setSelectedPayments] = useState<Record<string, any>[]>([]);
  const [selectedXsProjects, setSelectedXsProjects] = useState<Record<string, any>[]>([]);
  const [selectedSqProjects, setSelectedSqProjects] = useState<Record<string, any>[]>([]);
  const [selectedShProjects, setSelectedShProjects] = useState<Record<string, any>[]>([]);
  const [selectedProjectsPay, setSelectedProjectsPay] = useState<Record<string, any>[]>([]);

  //回款金额
  const totalReceiveAmount = selectedReceipts.reduce(
    (accumulator: number, current: API.PerfReceiptsResp) => {
      return accumulator + (current.receiveAmount || 0);
    },
    0,
  );
  //收款表的总税额
  const totalReceiveRateAmount = selectedReceipts.reduce(
    (accumulator: number, current: API.PerfReceiptsResp) => {
      return accumulator + (current.rateAmount || 0);
    },
    0,
  );
  //付款表的总税额
  const totalPaymentRateAmount = selectedPayments.reduce(
    (accumulator: number, current: API.PerfPayFlowResp) => {
      return accumulator + (current.rateAmount || 0);
    },
    0,
  );
  const finalRateAmount = totalReceiveRateAmount - totalPaymentRateAmount;
  //缴税成本
  const roundedFinalRateAmount = parseFloat(finalRateAmount.toFixed(2));
  //采购成本
  const totalPayAmount = selectedPayments.reduce(
    (accumulator: number, current: API.PerfPayFlowResp) => {
      return accumulator + (current.payAmount || 0);
    },
    0,
  );

  //费用报销
  const totalReimburseAmount =
    selectedSqProjects.reduce((accumulator: number, current: API.PerfProjectDetailsResp) => {
      return accumulator + (Number(current.actualMoney) || 0);
    }, 0) +
    selectedXsProjects.reduce((accumulator: number, current: API.PerfProjectDetailsResp) => {
      return accumulator + (Number(current.actualMoney) || 0);
    }, 0) +
    selectedShProjects.reduce((accumulator: number, current: API.PerfProjectDetailsResp) => {
      return accumulator + (Number(current.actualMoney) || 0);
    }, 0);

  //项目付款-付款金额之和
  const projectsPayAmount = selectedProjectsPay.reduce(
    (accumulator: number, current: API.ProjectPayResp) => {
      return accumulator + (Number(current.paymentAmount) || 0);
    },
    0,
  );

  //项目毛利
  const totalProfit = useMemo(
    () =>
      totalReceiveAmount -
      totalPayAmount -
      roundedFinalRateAmount -
      totalReimburseAmount -
      (formData?.performanceInfoResp?.consultingFee || 0) -
      (formData?.performanceInfoResp?.implementationCost || 0) -
      (formData?.performanceInfoResp?.fundCost || 0) -
      projectsPayAmount,
    [
      totalReceiveAmount,
      totalPayAmount,
      roundedFinalRateAmount,
      totalReimburseAmount,
      projectsPayAmount,
      formData,
    ],
  );

  //考核业绩
  const totalAchievement = useMemo(
    () => ((formData?.performanceInfoResp?.ratio || 0) / 100) * totalProfit,
    [formData?.performanceInfoResp?.ratio, totalProfit],
  );

  const { pathname } = useLocation();
  //初始表详情
  const isInitDetailPage = pathname.includes('initDetails');
  //终表详情
  const isFinalDetailPage = pathname.includes('finalDetails');
  //编辑页
  const isEditPage = pathname.includes('edit');

  const formRef = useRef<ProFormInstance>();

  const { refresh, loading: pageLoading } = useRequest(
    () =>
      performanceConf({
        id: id as string,
        receiveYear: receiveYear as string,
      }),
    {
      onSuccess: (res) => {
        const formattedRes = {
          ...res,
          performanceInfoResp: {
            ...res?.performanceInfoResp,
            ratio:
              res?.performanceInfoResp?.ratio || res?.performanceInfoResp?.ratio === 0
                ? res?.performanceInfoResp?.ratio
                : 100,
          },
        };
        setFormData({ ...formattedRes });
        setTimeout(() => formRef.current?.setFieldsValue({ ...formattedRes }), 100);
      },
    },
  );
  // 修改
  const { run: update, loading: editLoading } = useRequest(
    (value) =>
      performanceSave(
        {
          ...value,
          receiveYear: receiveYear as string,
        },
        {
          skipErrorHandler: true,
        },
      ),
    {
      manual: true,
      onSuccess: (res) => {
        // 有数据已经被其他合同配置过，需要提醒用户
        if (res?.code !== 200 && res?.data) {
          message.open({
            type: 'error',
            duration: 5,
            content: (
              <Space>
                <span>{res?.data}</span>
                <a
                  onClick={(e) => {
                    e.preventDefault();
                    navigator.clipboard.writeText(res?.data).then(() => {
                      message.success('复制成功');
                    });
                  }}
                >
                  复制
                </a>
              </Space>
            ),
          });
        } else if (res?.code !== 200 && res?.message) {
          message.error(res?.message || '请求失败！');
        } else {
          onSuccessAndRefresh(res, refresh);
        }
      },
      formatResult: (res) => res,
    },
  );

  //开始核算
  const { run: handleStart, loading: startLoading } = useRequest(
    () => {
      const { id = '', receiveYear = '' } = formData?.perMainInfoResp || {};
      return performanceStart({ id, receiveYear });
    },
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res) => {
        if (res?.code !== 200) return;
        message.success('操作成功');
        refresh();
      },
    },
  );

  // 结束核算
  const { run: handleEnd, loading: endLoading } = useRequest(
    () => {
      const { id = '', receiveYear = '' } = formData?.perMainInfoResp || {};
      return performanceEnd({ id, receiveYear });
    },
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res) => {
        if (res?.code !== 200) return;
        message.success('操作成功');
        refresh();
      },
    },
  );

  //确认核算
  const { run: handleConfirm, loading: confirmLoading } = useRequest(
    (id) =>
      performanceConfirm({
        id,
        performanceStatus: 'ACCOUNT_CONFIRMED',
        receiveYear: receiveYear as string,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('操作成功');
        refresh();
      },
      formatResult: (res) => res,
    },
  );

  //拒绝核算
  const { run: handleReject, loading: rejectLoading } = useRequest(
    (id) =>
      performanceConfirm({
        id,
        performanceStatus: 'ACCOUNT_REJECTED',
        receiveYear: receiveYear as string,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('操作成功');
        refresh();
      },
      formatResult: (res) => res,
    },
  );

  return (
    <PageContainer
      header={{
        title: true,
        breadcrumb: isFinalDetailPage
          ? {
              items: [
                {
                  href: '/sale',
                  title: '销售管理',
                },
                {
                  href: '/sale/performance-result',
                  title: '年度业绩考核表',
                },
                {
                  href: `/sale/performance-result/${receiveYear}`,
                  title: `${receiveYear}年度业绩考核明细表`,
                },
                {
                  title: '年度核算单',
                },
              ],
            }
          : {
              items: [
                {
                  href: '/sale',
                  title: '销售管理',
                },
                {
                  href: '/sale/performance-config',
                  title: '项目核算表',
                },
                {
                  title: '年度核算单',
                },
              ],
            },
      }}
      className="detail-container"
    >
      <RKPageLoading loading={pageLoading} />
      <ProForm<API.PerformanceConfResp | undefined>
        formRef={formRef}
        disabled={!canEditPerformanceConfig}
        onValuesChange={(_, allValues) => {
          setFormData(allValues);
        }}
        submitter={
          isEditPage
            ? {
                searchConfig: {
                  submitText: '保存',
                  resetText: '取消',
                },
                onReset: () => {
                  history.go(-1);
                },
                render: (props, doms) => {
                  return <FooterToolbar>{doms}</FooterToolbar>;
                },
                submitButtonProps: {
                  loading: editLoading,
                },
              }
            : false
        }
        onFinish={async (values) => {
          //这里的values拿不到salesList的数据，需要特殊处理
          const currentArray = formRef.current?.getFieldValue('performanceInfoResp')?.salesList;

          const form = {
            id,
            performanceInfo: { ...values?.performanceInfoResp, salesList: currentArray },
            perfProjectsSq: sqProjectTableRef?.current?.getCheckedKeys() || [],
            perfProjectsXs: xsProjectTableRef?.current?.getCheckedKeys() || [],
            perfProjectsSh: shProjectTableRef?.current?.getCheckedKeys() || [],
            perfReceipts: receiptTableRef?.current?.getCheckedKeys() || [],
            perfPayFlow: paymentTableRef?.current?.getCheckedKeys() || [],
            projectPays: projectPaymentTableRef?.current?.getCheckedKeys() || [],
          };

          update(form);
        }}
      >
        <BaseContext.Provider
          value={{
            isInitDetailPage,
            isFinalDetailPage,
            isEditPage,
            totalProfit,
            totalReceiveAmount,
            roundedFinalRateAmount,
            totalPayAmount,
            totalReimburseAmount,
            totalAchievement,
          }}
        >
          <PageHeader
            title={formData?.perMainInfoResp?.documentNumber}
            extra={
              <Space>
                <Access accessible={canStartPerformanceConfig}>
                  {['NOT_ACCOUNT', 'ACCOUNT_REJECTED'].includes(
                    formData?.perMainInfoResp?.performanceStatus || '',
                  ) && (
                    <Button
                      type="primary"
                      key="start"
                      loading={startLoading || endLoading}
                      onClick={() => handleStart()}
                    >
                      开始核算
                    </Button>
                  )}
                </Access>
                <Access accessible={canEndPerformanceConfig}>
                  {['NOT_ACCOUNT', 'ACCOUNTING', 'ACCOUNT_REJECTED'].includes(
                    formData?.perMainInfoResp?.performanceStatus || '',
                  ) && (
                    <Button
                      type="primary"
                      key="cancel"
                      loading={startLoading || endLoading}
                      onClick={() => handleEnd()}
                    >
                      结束核算
                    </Button>
                  )}
                </Access>
                {/*只有合同销售才能看到确认和拒绝*/}
                {currentUser?.id === formData?.perMainInfoResp?.salePersonId &&
                  formData?.perMainInfoResp?.performanceStatus === 'BEEN_ACCOUNTED' && (
                    <>
                      <Access accessible={canConfirmPerformanceResult}>
                        <Button
                          type="primary"
                          key="confirm"
                          loading={confirmLoading || rejectLoading}
                          onClick={() => {
                            Modal.confirm({
                              title: '确认核算',
                              content: `您要确认核算这条数据吗?`,
                              okText: '确认',
                              cancelText: '取消',
                              onOk: () => {
                                handleConfirm(id);
                              },
                            });
                          }}
                        >
                          确认核算
                        </Button>
                      </Access>
                      <Access accessible={canRejectPerformanceResult}>
                        <Button
                          type="primary"
                          danger
                          key="reject"
                          loading={confirmLoading || rejectLoading}
                          onClick={() => {
                            Modal.confirm({
                              title: '拒绝核算',
                              content: `您要拒绝核算这条数据吗?`,
                              okText: '确认',
                              cancelText: '取消',
                              onOk: () => {
                                handleReject(id);
                              },
                            });
                          }}
                        >
                          拒绝核算
                        </Button>
                      </Access>
                    </>
                  )}
              </Space>
            }
          ></PageHeader>
          <Collapse defaultActiveKey={['1', '2', '3', '4', '5', '6', '7', '8', '9']} ghost>
            <Collapse.Panel key="1" header="合同明细" collapsible="header">
              <ProFormDependency name={['perMainInfoResp', 'performanceInfoResp']}>
                {({ perMainInfoResp, performanceInfoResp }) => {
                  return (
                    <BaseInfo
                      performanceInfoResp={performanceInfoResp}
                      perMainInfoResp={perMainInfoResp}
                    />
                  );
                }}
              </ProFormDependency>
            </Collapse.Panel>
            <Collapse.Panel key="2" header="核算配置" collapsible="header">
              <PerformanceConfig formRef={formRef} setFormData={setFormData} />
            </Collapse.Panel>
            <Collapse.Panel key="3" header="比例分配表" collapsible="header">
              <ProFormDependency name={['performanceInfoResp']}>
                {({ performanceInfoResp }) => {
                  return <PerformanceDistribution salesList={performanceInfoResp?.salesList} />;
                }}
              </ProFormDependency>
            </Collapse.Panel>
            <Collapse.Panel key="4" header="收款计划">
              <ProForm.Item name="perfReceipts">
                <ReceiptPlanTable
                  ref={receiptTableRef}
                  receivedAmountAll={formRef.current?.getFieldValue('receivedAmountAll')}
                  onChangeSelect={(values) => {
                    setSelectedReceipts(values);
                  }}
                />
              </ProForm.Item>
            </Collapse.Panel>
            <Collapse.Panel key="5" header="关联采购合同的付款计划表">
              <ProForm.Item name="perfPayFlow">
                <PaymentPlanTable
                  ref={paymentTableRef}
                  paidAmountAll={formRef.current?.getFieldValue('paidAmountAll')}
                  onChangeSelect={(values) => {
                    setSelectedPayments(values);
                  }}
                />
              </ProForm.Item>
            </Collapse.Panel>
            <Collapse.Panel key="6" header="售前项目列表" collapsible="header">
              <ProForm.Item name="perfProjectsSq">
                <ProjectTable
                  ref={sqProjectTableRef}
                  onChangeSelect={(values) => {
                    setSelectedSqProjects(values);
                  }}
                />
              </ProForm.Item>
            </Collapse.Panel>
            <Collapse.Panel key="7" header="销售项目列表" collapsible="header">
              <ProForm.Item name="perfProjectsXs">
                <ProjectTable
                  ref={xsProjectTableRef}
                  onChangeSelect={(values) => {
                    setSelectedXsProjects(values);
                  }}
                />
              </ProForm.Item>
            </Collapse.Panel>
            <Collapse.Panel key="8" header="售后项目列表" collapsible="header">
              <ProForm.Item name="perfProjectsSh">
                <ProjectTable
                  ref={shProjectTableRef}
                  onChangeSelect={(values) => {
                    setSelectedShProjects(values);
                  }}
                />
              </ProForm.Item>
            </Collapse.Panel>
            <Collapse.Panel key="9" header="项目付款明细" collapsible="header">
              <ProForm.Item name="projectPayList">
                <ProjectPaymentTable
                  ref={projectPaymentTableRef}
                  onChangeSelect={(values) => {
                    setSelectedProjectsPay(values);
                  }}
                />
              </ProForm.Item>
            </Collapse.Panel>
          </Collapse>
        </BaseContext.Provider>
      </ProForm>
    </PageContainer>
  );
};

export default CustomerDetails;
