import BaseContext from '@/Context/BaseContext';
import { PERFORMANCE_STATUS, PROJECT_STATUS, PROJECT_TYPE } from '@/enums';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Card, Descriptions, Empty } from 'antd';
import dayjs from 'dayjs';
import React, { forwardRef, useContext, useEffect, useImperativeHandle, useState } from 'react';

const columns: ProColumns<API.PerfProjectDetailsResp>[] = [
  {
    title: '核算状态',
    dataIndex: 'performanceStatus',
    width: 100,
    tooltip: '状态为核算中且本条数据被标红则代表该条数据正在被其他合同核算，请注意',
    render: (_, record) => {
      const statusObj = option2enum(PERFORMANCE_STATUS)?.[record?.performanceStatus as string];
      return (
        <span
          style={{
            color:
              !record.currentPerformance && record.performanceStatus === 'ACCOUNTING'
                ? 'red'
                : undefined,
          }}
        >
          {statusObj.label}
        </span>
      );
    },
  },
  {
    title: '员工姓名',
    dataIndex: 'username',
    width: 120,
    ellipsis: true,
  },
  {
    title: '报销日期',
    dataIndex: 'finaDate',
    width: 120,
    ellipsis: true,
  },
  {
    title: '明细类型',
    dataIndex: 'type',
    width: 120,
    ellipsis: true,
  },
  {
    title: '关联报销编号',
    dataIndex: 'finaNumber',
    width: 200,
    ellipsis: true,
  },
  {
    title: '实际报销金额',
    dataIndex: 'actualMoney',
    valueType: 'money',
    width: 130,
    ellipsis: true,
  },
  {
    title: '实际报销类型',
    dataIndex: 'actualType',
    ellipsis: true,
    width: 130,
  },
  {
    title: '正当理由',
    dataIndex: 'reason',
    ellipsis: true,
    width: 180,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    ellipsis: true,
    width: 180,
  },
  {
    title: '核算编号',
    dataIndex: 'perfNumber',
    ellipsis: true,
    width: 200,
  },
];

interface ProjectTableProps {
  value?: API.PerfProjectResp[];
  onChangeSelect: (selectedRows: Record<string, any>[]) => void;
}

export interface ProjectTableRef {
  getCheckedKeys(): string[];
}

const ProjectTable: React.ForwardRefRenderFunction<ProjectTableRef, ProjectTableProps> = (
  { value, onChangeSelect },
  ref,
) => {
  const { isEditPage, isFinalDetailPage } = useContext(BaseContext);

  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
  useImperativeHandle(ref, () => ({
    getCheckedKeys: () => checkedKeys,
  }));

  useEffect(() => {
    let selectedArr: any[] = [];
    value?.forEach((item) => {
      if (item?.perfProjectDetails?.length) {
        const arr = item?.perfProjectDetails
          .filter((i) => !!i?.currentPerformance)
          .map((i) => i?.id);
        selectedArr = selectedArr.concat([...arr]);
      }
    });
    setCheckedKeys(selectedArr as string[]);
  }, [value]);

  useEffect(() => {
    const arr: any[] = [];
    value?.forEach((item) => {
      item?.perfProjectDetails?.forEach((i) => {
        if (checkedKeys?.includes(i?.id as string)) {
          arr.push(i);
        }
      });
    });
    onChangeSelect(arr || []);
  }, [checkedKeys]);

  return (
    <>
      {!value?.length && <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
      {value?.map((project, projectIndex) => (
        <ProTable<API.PerfProjectDetailsResp>
          {...defaultTableConfig}
          key={project?.id || projectIndex}
          style={{ marginBottom: 30 }}
          rowKey="id"
          rowSelection={{
            selectedRowKeys: checkedKeys,
            onSelectAll: (selected, selectedRows, changeRows) => {
              if (!selected) {
                setCheckedKeys((prevState) =>
                  prevState.filter((item) => !changeRows.some((row) => row.id === item)),
                );
              } else {
                setCheckedKeys(
                  (prevState) =>
                    [
                      ...prevState,
                      ...selectedRows.filter((item) => item).map((item) => item.id),
                    ] as string[],
                );
              }
            },
            onSelect: (record, selected) => {
              if (selected) {
                setCheckedKeys((prevState) => [...prevState, record.id] as string[]);
              } else {
                setCheckedKeys((prevState) => prevState.filter((item) => item !== record.id));
              }
            },
            getCheckboxProps: (record) => {
              return {
                disabled:
                  !isEditPage ||
                  record.performanceStatus === 'BEEN_ACCOUNTED' ||
                  (record.performanceStatus === 'ACCOUNTING' && !record.currentPerformance),
              };
            },
          }}
          tableExtraRender={() => {
            return (
              <Card>
                <Descriptions size="small" column={3}>
                  <Descriptions.Item label="项目编号">
                    <a
                      onClick={(e) => {
                        e.preventDefault();
                        switch (project?.projectClassify) {
                          case 'NB':
                            history.push(`/project/internal/edit/${project.id}`);
                            break;
                          case 'XS':
                            history.push(`/project/sales/edit/${project.id}`);
                            break;
                          case 'SQ':
                            history.push(`/project/pre-sales/edit/${project.id}`);
                            break;
                          case 'SH':
                            history.push(`/project/after-sales/edit/${project.id}`);
                            break;
                          default:
                            return;
                        }
                      }}
                    >
                      {project.projectNumber}
                    </a>
                  </Descriptions.Item>
                  <Descriptions.Item label="项目名称">{project.projectName}</Descriptions.Item>
                  <Descriptions.Item label="项目类型">
                    {option2enum(PROJECT_TYPE)?.[project?.projectClassify as string]['label']}
                  </Descriptions.Item>
                  <Descriptions.Item label="项目经理">{project.projectManger}</Descriptions.Item>
                  <Descriptions.Item label="成本估算">{project.costEstimate}</Descriptions.Item>
                  <Descriptions.Item label="预估工作量">
                    {project.estimatedWorkload}
                  </Descriptions.Item>
                  <Descriptions.Item label="创建时间">
                    {dayjs(project.createdTime).format('YYYY-MM-DD')}
                  </Descriptions.Item>
                  <Descriptions.Item label="项目状态">
                    {option2enum(PROJECT_STATUS)?.[project.status as string]['label']}
                  </Descriptions.Item>
                  <Descriptions.Item label="项目开销">
                    {'¥' + (project.spend || 0)}
                  </Descriptions.Item>
                  <Descriptions.Item label="已执行工作量">
                    {project.executedWorkload}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            );
          }}
          scroll={{ x: '100%' }}
          dataSource={
            (!isFinalDetailPage
              ? project?.perfProjectDetails
              : project?.perfProjectDetails?.filter((i) => !!i.currentPerformance)) || []
          }
          options={false}
          className="inner-table"
          columns={columns}
        />
      ))}
    </>
  );
};

export default forwardRef(ProjectTable);
