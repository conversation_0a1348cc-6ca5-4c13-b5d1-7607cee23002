import { ProDescriptions } from '@ant-design/pro-components';

import BaseContext from '@/Context/BaseContext';
import { PERFORMANCE_STATUS } from '@/enums';
import { option2enum } from '@/utils';
import { history } from '@@/core/history';
import React, { useContext } from 'react';

const BaseInfo: React.FC<{
  perMainInfoResp?: API.PerMainInfoResp;
  performanceInfoResp?: API.PerformanceInfoResp;
}> = ({ perMainInfoResp = {} }) => {
  const {
    isEditPage,
    totalProfit,
    totalReceiveAmount,
    roundedFinalRateAmount,
    totalPayAmount,
    totalReimburseAmount,
    totalAchievement,
  } = useContext(BaseContext);

  const {
    id,
    documentNumber,
    receiveYear,
    contractNumber,
    performanceStatus,
    customerName,
    customerAbbreviation,
    contractName,
    startTime,
    salePerson,
    contractAmount,
    endTime,
    confirmTime,
    updateTime,
  } = perMainInfoResp || {};

  return (
    <ProDescriptions column={3}>
      <ProDescriptions.Item valueType="text" ellipsis label="核算编号">
        {documentNumber || '-'}
      </ProDescriptions.Item>
      <ProDescriptions.Item valueType="text" ellipsis label="核算年度">
        {receiveYear || '-'}
      </ProDescriptions.Item>
      <ProDescriptions.Item valueType="text" ellipsis label="销售">
        {salePerson || '-'}
      </ProDescriptions.Item>
      <ProDescriptions.Item
        contentStyle={{
          maxWidth: '70%',
        }}
        valueType="text"
        ellipsis
        label="合同名称"
      >
        {contractName || '-'}
      </ProDescriptions.Item>
      <ProDescriptions.Item valueType="text" ellipsis label="合同编号">
        <a className="rk-a-span" onClick={() => history.push(`/contract/main/edit/${id}`)}>
          {contractNumber || '-'}
        </a>
      </ProDescriptions.Item>
      <ProDescriptions.Item label="核算状态" valueEnum={option2enum(PERFORMANCE_STATUS)}>
        {performanceStatus || '-'}
      </ProDescriptions.Item>
      <ProDescriptions.Item valueType="text" ellipsis label="客户名称">
        {customerName || '-'}
      </ProDescriptions.Item>
      <ProDescriptions.Item valueType="money" label="合同金额">
        {contractAmount || 0}
      </ProDescriptions.Item>
      <ProDescriptions.Item valueType="text" ellipsis label="客户简称">
        {customerAbbreviation || '-'}
      </ProDescriptions.Item>
      <ProDescriptions.Item valueType="text" ellipsis label="合同日期">
        {startTime || '-'}
      </ProDescriptions.Item>
      <ProDescriptions.Item valueType="money" label="回款金额">
        {totalReceiveAmount || 0}
      </ProDescriptions.Item>
      <ProDescriptions.Item valueType="money" label="缴税成本">
        {roundedFinalRateAmount || 0}
      </ProDescriptions.Item>
      <ProDescriptions.Item valueType="money" label="采购成本">
        {totalPayAmount || 0}
      </ProDescriptions.Item>
      <ProDescriptions.Item valueType="money" label="费用报销">
        {totalReimburseAmount || 0}
      </ProDescriptions.Item>
      <ProDescriptions.Item valueType="money" label="项目毛利">
        {totalProfit || 0}
      </ProDescriptions.Item>
      <ProDescriptions.Item valueType="money" label="考核业绩">
        {totalAchievement || 0}
      </ProDescriptions.Item>
      {!isEditPage && (
        <>
          <ProDescriptions.Item valueType="text" ellipsis label="提交时间">
            {endTime || '-'}
          </ProDescriptions.Item>
          <ProDescriptions.Item valueType="text" ellipsis label="确认时间">
            {confirmTime || '-'}
          </ProDescriptions.Item>
          <ProDescriptions.Item valueType="text" ellipsis label="更新时间">
            {updateTime || '-'}
          </ProDescriptions.Item>
        </>
      )}
    </ProDescriptions>
  );
};
export default BaseInfo;
