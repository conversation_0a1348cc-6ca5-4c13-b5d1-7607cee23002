import BaseContext from '@/Context/BaseContext';
import { CONTRACT_TYPE, PAYMENT_STATUS, PERFORMANCE_STATUS } from '@/enums';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { history } from '@@/core/history';
import { ProColumns, ProDescriptions, ProTable } from '@ant-design/pro-components';
import { useReactive } from 'ahooks';
import { Card, Space } from 'antd';
import React, { forwardRef, useContext, useEffect, useImperativeHandle, useState } from 'react';

const columns: ProColumns<API.PerfPayFlowResp>[] = [
  {
    title: '核算状态',
    dataIndex: 'performanceStatus',
    width: 100,
    tooltip: '状态为核算中且本条数据被标红则代表该条数据正在被其他合同核算，请注意',
    render: (_, record) => {
      const statusObj = option2enum(PERFORMANCE_STATUS)?.[record?.performanceStatus as string];
      return (
        <Space>
          <span
            style={{
              color:
                !record.currentPerformance && record.performanceStatus === 'ACCOUNTING'
                  ? 'red'
                  : undefined,
            }}
          >
            {statusObj.label}
          </span>
        </Space>
      );
    },
  },
  {
    title: '期次',
    dataIndex: 'period',
    width: 60,
  },
  {
    title: '计划付款时间',
    dataIndex: 'estimatePayTime',
    width: 120,
  },
  {
    title: '付款状态',
    dataIndex: 'payStatus',
    width: 110,
    valueEnum: option2enum(PAYMENT_STATUS),
  },
  {
    title: '税额',
    dataIndex: 'rateAmount',
    valueType: 'money',
    width: 130,
  },
  {
    title: '付款金额',
    dataIndex: 'payAmount',
    valueType: 'money',
    width: 130,
  },
  {
    title: '付款日期',
    dataIndex: 'payTime',
    width: 120,
  },
  {
    title: '合同类型',
    dataIndex: 'contractType',
    valueEnum: option2enum(CONTRACT_TYPE),
    width: 110,
  },
  {
    title: '合同编号',
    dataIndex: 'contractNumber',
    width: 200,
    copyable: true,
    render(dom, entity) {
      return (
        <a
          className="rk-a-span"
          onClick={() => history.push(`/contract/purchase/edit/${entity.contractId}`)}
        >
          {dom}
        </a>
      );
    },
  },
  {
    title: '对方开票日期',
    dataIndex: 'countInvoicedTime',
    width: 120,
  },
  {
    title: '收票金额',
    dataIndex: 'receiptAmount',
    valueType: 'money',
    width: 130,
  },
  {
    title: '核算编号',
    dataIndex: 'perfNumber',
    ellipsis: true,
    width: 200,
  },
];

interface PaymentPlanTableProps {
  value?: API.PerfPayFlowResp[];
  paidAmountAll: string;
  onChangeSelect: (selectedRows: Record<string, any>[]) => void;
}

export interface PaymentPlanTableRef {
  getCheckedKeys(): string[];
}

const PaymentPlanTable: React.ForwardRefRenderFunction<
  PaymentPlanTableRef,
  PaymentPlanTableProps
> = ({ value, paidAmountAll, onChangeSelect }, ref) => {
  const { isEditPage, isFinalDetailPage } = useContext(BaseContext);

  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);

  const tableHeaderData = useReactive({
    receivedAmount: 0,
    yearPayAmount: 0,
    yearTaxAmount: 0,
  });

  useImperativeHandle(ref, () => ({
    getCheckedKeys: () => checkedKeys,
  }));

  useEffect(() => {
    const selectedArr = value?.filter((i) => !!i?.currentPerformance)?.map((i) => i?.id);
    if (Array.isArray(selectedArr) && selectedArr.length) {
      setCheckedKeys(selectedArr as string[]);
    }
  }, [value]);

  useEffect(() => {
    const arr = value?.filter((i) => {
      return checkedKeys?.includes(i.id as string);
    });

    tableHeaderData.yearPayAmount =
      arr?.reduce((accumulator: number, current: API.PerfPayFlowResp) => {
        return accumulator + (current.payAmount || 0);
      }, 0) || 0;

    tableHeaderData.yearTaxAmount =
      arr?.reduce((accumulator: number, current: API.PerfPayFlowResp) => {
        return accumulator + (current.rateAmount || 0);
      }, 0) || 0;

    onChangeSelect(arr || []);
  }, [checkedKeys]);

  return (
    <ProTable
      {...defaultTableConfig}
      rowKey="id"
      rowSelection={{
        selectedRowKeys: checkedKeys,
        onSelectAll: (selected, selectedRows, changeRows) => {
          if (!selected) {
            setCheckedKeys((prevState) =>
              prevState.filter((item) => !changeRows.some((row) => row.id === item)),
            );
          } else {
            setCheckedKeys(
              (prevState) =>
                [
                  ...prevState,
                  ...selectedRows.filter((item) => item).map((item) => item.id),
                ] as string[],
            );
          }
        },
        onSelect: (record, selected) => {
          if (selected) {
            setCheckedKeys((prevState) => [...prevState, record.id] as string[]);
          } else {
            setCheckedKeys((prevState) => prevState.filter((item) => item !== record.id));
          }
        },
        getCheckboxProps: (record) => {
          return {
            disabled:
              !isEditPage ||
              record.performanceStatus === 'BEEN_ACCOUNTED' ||
              (record.performanceStatus === 'ACCOUNTING' && !record.currentPerformance),
          };
        },
      }}
      tableExtraRender={() => {
        return (
          <Card>
            <ProDescriptions size="small" column={3}>
              <ProDescriptions.Item valueType="money" label="已付款金额">
                {paidAmountAll || 0}
              </ProDescriptions.Item>
              <ProDescriptions.Item valueType="money" label="核算成本">
                {tableHeaderData.yearPayAmount}
              </ProDescriptions.Item>
              <ProDescriptions.Item valueType="money" label="核算税额">
                {tableHeaderData.yearTaxAmount}
              </ProDescriptions.Item>
            </ProDescriptions>
          </Card>
        );
      }}
      scroll={{ x: '100%' }}
      dataSource={!isFinalDetailPage ? value : value?.filter((i) => !!i.currentPerformance)}
      options={false}
      className="inner-table"
      columns={columns}
    />
  );
};

export default forwardRef(PaymentPlanTable);
