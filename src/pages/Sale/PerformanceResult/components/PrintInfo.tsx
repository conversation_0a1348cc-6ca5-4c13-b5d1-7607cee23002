import RKPageHeader from '@/components/RKPageHeader';
import RKPageLoading from '@/components/RKPageLoading';
import PrintTemplate from '@/pages/Sale/PerformanceResult/components/PrintTemplate';
import { printConfirm, printInfo, printSubmit } from '@/services/oa/sale';
import { useAccess, useModel, useParams, useRequest } from '@@/exports';
import {
  DrawerForm,
  PageContainer,
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { Access } from '@umijs/max';
import { Alert, Button, message, Modal } from 'antd';
import React, { useRef, useState } from 'react';

// 创建一个上下文
const PrintInfo: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const [open, setOpen] = useState(false);
  const { receiveYear = '', salePersonId = '' } = useParams();
  const { canEditPrintPerformanceResult = false } = useAccess();
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  const {
    data,
    refresh,
    loading: pageLoading,
  } = useRequest(
    () =>
      printInfo({
        receiveYear,
        salePersonId,
      }),
    {
      formatResult: (res) => {
        return { ...res.data, receiveYear };
      },
    },
  );

  // 确认
  const { run: handleConfirm } = useRequest(() => printConfirm({ receiveYear, salePersonId }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('确认成功');
      refresh();
    },
    formatResult: (res) => res,
  });

  return (
    <PageContainer
      header={{
        title: false,
        breadcrumb: {
          items: [
            {
              href: '/sale',
              title: '销售管理',
            },
            {
              href: '/sale/performance-result',
              title: '年度业绩考核表',
            },
            {
              href: `/sale/performance-result/${receiveYear}`,
              title: `${receiveYear}年度业绩考核明细表`,
            },
            {
              title: '年度业绩考核汇总表',
            },
          ],
        },
      }}
      footer={
        data?.perMainResultRespList?.length
          ? [
              <Access key="1" accessible={canEditPrintPerformanceResult}>
                <Button
                  type="primary"
                  onClick={() => {
                    setOpen(true);
                  }}
                >
                  提交
                </Button>
              </Access>,
              currentUser?.id === data?.salePersonId &&
                data?.performanceStatus !== 'PER_CONFIRM' && (
                  <Button
                    key="2"
                    type="primary"
                    onClick={() => {
                      Modal.confirm({
                        title: '确认核算',
                        content:
                          '确认后，您本年度的业绩考核将全部结束，不可再进行业绩核算以及任何修改！！',
                        okText: '确认',
                        cancelText: '取消',
                        onOk: async () => {
                          handleConfirm();
                        },
                      });
                    }}
                  >
                    确认
                  </Button>
                ),
            ]
          : undefined
      }
      className="detail-container"
    >
      <RKPageLoading loading={pageLoading} />
      <ProForm submitter={false}>
        <Alert
          style={{ marginBottom: '5px' }}
          message="提示"
          description="本表中仅包含核算状态为已确认的核算数据，请确认数据是否正确！"
          type="warning"
          showIcon
          banner
        />
        <RKPageHeader
          print={{
            printData: data,
            printType: 'performance',
          }}
        />
        <PrintTemplate data={data as API.PrintInfoResp & { receiveYear: string }} />
      </ProForm>
      <DrawerForm<API.PrintSubmitReq>
        width={753}
        title={'提交'}
        formRef={formRef}
        open={open}
        onOpenChange={(isOpen) => {
          setOpen(isOpen);
        }}
        initialValues={{
          performanceBaseline: data?.performanceBaseline,
          performanceTargets: data?.performanceTargets,
          commissionAmount: data?.commissionAmount,
        }}
        onFinish={async (value) => {
          const formData = {
            ...value,
            salePersonId,
            receiveYear,
          };

          const msg = await printSubmit(formData);
          const success = msg.code === 200;
          if (success) {
            message.success('提交成功!');
            refresh();
          }
          return success;
        }}
        autoFocusFirstInput
        drawerProps={{
          destroyOnClose: true,
        }}
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
        }}
      >
        <>
          <ProForm.Group>
            <ProFormText
              name="performanceTargets"
              label="业绩目标"
              fieldProps={{
                autoComplete: 'none',
              }}
              width="md"
            />
            <ProFormText
              name="performanceBaseline"
              label="业绩基线"
              fieldProps={{
                autoComplete: 'none',
              }}
              width="md"
            />
          </ProForm.Group>
          <ProForm.Group>
            <ProFormDigit
              name="commissionAmount"
              label="提成金额"
              width="md"
              fieldProps={{
                prefix: '￥',
                precision: 2,
              }}
            />
          </ProForm.Group>
        </>
      </DrawerForm>
    </PageContainer>
  );
};

export default PrintInfo;
