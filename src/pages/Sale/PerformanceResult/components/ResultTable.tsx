import { PERFORMANCE_PRINT_STATUS, PERFORMANCE_STATUS } from '@/enums';
import { userDepartmentList } from '@/services/oa/auth';
import { performanceConfirm, performanceResultTable } from '@/services/oa/sale';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { TableOutlined } from '@ant-design/icons';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProTable,
  StatisticCard,
} from '@ant-design/pro-components';
import {
  Access,
  history,
  useAccess,
  useLocation,
  useModel,
  useParams,
  useRequest,
} from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import React, { useEffect, useRef, useState } from 'react';

const ResultTable: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const currentSearchUser = useRef<string | null>('');
  const [statisticData, setStatisticData] = useState<Record<string, any>>();
  const {
    canConfirmPerformanceResult = false,
    canRejectPerformanceResult = false,
    canReadAllPerformanceResult = false,
  } = useAccess();
  const { year = '' } = useParams();
  const locations = useLocation();
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  useEffect(() => {
    if (locations.search.includes('salePersonId=')) {
      const salePersonId = locations.search.split('salePersonId=')[1].split('&')[0];
      currentSearchUser.current = salePersonId;
    }
  }, []);

  const { data: salesList, loading: salesListLoading } = useRequest(
    () => userDepartmentList({ id: '6' }),
    {
      onSuccess: (res) => {
        // 当前登录用户为销售(在销售人员列表中)
        if (
          res?.some((i: Record<string, any>) => {
            return i.id === currentUser?.id;
          })
        ) {
          //销售人员需要赋值默认搜索销售的id供打印功能使用
          currentSearchUser.current = currentUser?.id || null;
        }
      },
    },
  );

  const { run: handleConfirm, loading: confirmLoading } = useRequest(
    (id) => performanceConfirm({ id, performanceStatus: 'ACCOUNT_CONFIRMED', receiveYear: year }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('操作成功');
        tableRef.current?.reloadAndRest?.();
      },
      formatResult: (res) => res,
    },
  );

  const { run: handleReject, loading: rejectLoading } = useRequest(
    (id) => performanceConfirm({ id, performanceStatus: 'ACCOUNT_REJECTED', receiveYear: year }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('操作成功');
        tableRef.current?.reloadAndRest?.();
      },
      formatResult: (res) => res,
    },
  );

  const columns: ProColumns<API.PerMainResultResp>[] = [
    {
      title: '核算编号',
      dataIndex: 'documentNumber',
      copyable: true,
      width: 240,
      ellipsis: true,
    },
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      width: 150,
      copyable: true,
      search: false,
      render(dom, entity) {
        return (
          <a className="rk-a-span" onClick={() => history.push(`/contract/main/edit/${entity.id}`)}>
            {dom}
          </a>
        );
      },
    },
    {
      title: '销售',
      dataIndex: 'salePerson',
      width: 120,
      ellipsis: true,
      search: false,
    },
    {
      title: '销售',
      dataIndex: 'salePersonId',
      width: 120,
      hideInTable: true,
      hideInSearch: !canReadAllPerformanceResult,
      valueType: 'select',
      fieldProps: {
        options: salesList,
        loading: salesListLoading,
        showSearch: true,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        optionFilterProp: 'label',
        filterOption: true,
        onChange: (value: string | null) => {
          currentSearchUser.current = value;
        },
      },
    },
    {
      title: '核算状态',
      dataIndex: 'performanceStatus',
      width: 100,
      valueEnum: option2enum(PERFORMANCE_STATUS),
      search: false,
    },
    {
      title: '年度考核状态',
      dataIndex: 'printStatus',
      width: 120,
      hideInSearch: !canReadAllPerformanceResult,
      valueEnum: option2enum(PERFORMANCE_PRINT_STATUS),
    },
    {
      title: '客户简称',
      dataIndex: 'customerAbbreviation',
      width: 120,
      ellipsis: true,
      search: false,
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
      width: 200,
      search: false,
      ellipsis: true,
    },
    {
      title: '合同日期',
      dataIndex: 'startTime',
      width: 170,
      search: false,
    },
    {
      title: '合同额',
      valueType: 'money',
      dataIndex: 'mainConAmount',
      width: 130,
      search: false,
    },
    {
      title: '比例',
      valueType: 'percent',
      dataIndex: 'ratio',
      width: 100,
      search: false,
    },
    {
      title: '考核业绩',
      valueType: 'money',
      dataIndex: 'performanceEvaluation',
      width: 130,
      search: false,
    },
    {
      title: '回款金额',
      valueType: 'money',
      dataIndex: 'receiveAmount',
      width: 130,
      search: false,
    },
    {
      title: '咨询费用',
      valueType: 'money',
      dataIndex: 'consultingFee',
      width: 130,
      search: false,
    },
    {
      title: '缴税成本',
      valueType: 'money',
      dataIndex: 'taxCost',
      width: 130,
      search: false,
    },
    {
      title: '采购成本',
      valueType: 'money',
      dataIndex: 'purchaseCost',
      width: 130,
      search: false,
    },
    {
      title: '实施成本',
      valueType: 'money',
      dataIndex: 'implementationCost',
      width: 130,
      search: false,
    },
    {
      title: '资金成本',
      valueType: 'money',
      dataIndex: 'fundCost',
      width: 130,
      search: false,
    },
    {
      title: '费用报销',
      valueType: 'money',
      dataIndex: 'reimbursementFee',
      width: 130,
      search: false,
    },
    {
      title: '项目付款',
      valueType: 'money',
      dataIndex: 'projectCost',
      width: 130,
      search: false,
    },
    {
      title: '项目毛利',
      valueType: 'money',
      dataIndex: 'projectGrossProfit',
      width: 130,
      search: false,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 130,
      search: false,
      ellipsis: true,
    },
    {
      title: '操作',
      fixed: 'right',
      width: 210,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (_, record) => {
        const { id, performanceStatus, contractSalePersonId } = record;
        return (
          <Space>
            {/*超管和合同销售都能看到详情*/}
            {(canReadAllPerformanceResult || currentUser?.id === contractSalePersonId) && (
              <Button
                type="link"
                className="inner-table-link"
                style={{ color: '#2f54eb' }}
                key="details"
                onClick={() => {
                  history.push(`/sale/performance-result/finalDetails/${id}/${year}`);
                }}
              >
                核算详情
              </Button>
            )}
            {/*只有合同销售才能看到确认和拒绝*/}
            {currentUser?.id === contractSalePersonId && performanceStatus === 'BEEN_ACCOUNTED' && (
              <>
                <Access accessible={canConfirmPerformanceResult}>
                  <Button
                    type="link"
                    className="inner-table-link"
                    style={{ color: '#7cb305' }}
                    key="confirm"
                    loading={confirmLoading || rejectLoading}
                    onClick={() => {
                      Modal.confirm({
                        title: '确认核算',
                        content: `您要确认核算这条数据吗?`,
                        okText: '确认',
                        cancelText: '取消',
                        onOk: () => {
                          handleConfirm(id);
                        },
                      });
                    }}
                  >
                    确认核算
                  </Button>
                </Access>
                <Access accessible={canRejectPerformanceResult}>
                  <Button
                    type="link"
                    className="inner-table-link"
                    style={{ color: '#f5222d' }}
                    key="reject"
                    loading={confirmLoading || rejectLoading}
                    onClick={() => {
                      Modal.confirm({
                        title: '拒绝核算',
                        content: `您要拒绝核算这条数据吗?`,
                        okText: '确认',
                        cancelText: '取消',
                        onOk: () => {
                          handleReject(id);
                        },
                      });
                    }}
                  >
                    拒绝核算
                  </Button>
                </Access>
              </>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
        breadcrumb: {
          items: [
            {
              href: '/sale',
              title: '销售管理',
            },
            {
              href: '/sale/performance-result',
              title: '年度业绩考核表',
            },
            {
              title: `${year}年度业绩考核明细表`,
            },
          ],
        },
      }}
    >
      <StatisticCard.Group style={{ marginBottom: 16 }}>
        <StatisticCard
          statistic={{
            title: '项目毛利',
            value: statisticData?.projectGrossProfitCount,
            prefix: '￥',
            status: 'processing',
          }}
        />
        <StatisticCard
          statistic={{
            title: '考核业绩',
            value: statisticData?.performanceEvaluationCount,
            prefix: '￥',
            status: 'processing',
          }}
        />
      </StatisticCard.Group>
      <ProTable<API.PerMainResultResp>
        {...defaultTableConfig}
        rowKey="rowNum"
        headerTitle="年度业绩考核明细表"
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        request={async (params) => {
          const { current, pageSize, documentNumber, salePersonId, printStatus } = params;

          const msg = await performanceResultTable({
            pageNum: current,
            pageSize,
            extra: { startTime: year, documentNumber, salePersonId, printStatus },
          });

          const { projectGrossProfitCount, performanceEvaluationCount } =
            msg?.data as API.PerformancePagePerMainResultResp;
          setStatisticData({
            projectGrossProfitCount,
            performanceEvaluationCount,
          });
          return {
            ...msg?.data,
            data: msg?.data?.records || [],
            success: true,
            total: msg?.data?.total,
          };
        }}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
          labelWidth: 120,
        }}
        toolbar={{
          actions: [
            <Button
              key="table"
              onClick={() => {
                if (!currentSearchUser.current) {
                  message.error('请先在上方选择一个销售！');
                  return;
                }
                history.push(
                  `/sale/performance-result/printInfo/${year}/${currentSearchUser.current}`,
                );
              }}
              type="primary"
              icon={<TableOutlined />}
            >
              年度业绩考核汇总表
            </Button>,
          ],
        }}
      />
    </PageContainer>
  );
};

export default ResultTable;
