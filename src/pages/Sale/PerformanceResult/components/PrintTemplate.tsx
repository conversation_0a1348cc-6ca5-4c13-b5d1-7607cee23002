import dayjs from 'dayjs';
import React from 'react';
import './index.less';

export interface PrintTemplateProps {
  data: API.PrintInfoResp & { receiveYear: string };
}

const renderMoney = (value?: string | number) => {
  if (!value) return 0;
  const numValue = parseFloat(value as string);
  if (Number.isInteger(numValue)) {
    return `￥${numValue}`;
  }
  return `￥${numValue.toFixed(2)}`;
};

const formatTimeToDate = (time: string | undefined): string => {
  if (!time) return '-';
  return dayjs(time).format('YYYY-MM-DD');
};

const PrintTemplate = React.forwardRef<HTMLDivElement, PrintTemplateProps>(({ data }, ref) => {
  return (
    <div className="expense-account-container" ref={ref}>
      <div className="ea-header">
        <p className="ea-title">{data?.receiveYear}年度业绩考核汇总表</p>
      </div>
      <div className="ea-list">
        <div className="ea-row">
          <div className="ea-col-title">销售</div>
          <div className="ea-col-content">{data?.salePerson}</div>
          <div className="ea-col-title">部门</div>
          <div className="ea-col-content">{data?.department || '-'}</div>
        </div>
        <div className="ea-row">
          <div className="ea-col-title">子部门</div>
          <div className="ea-col-content">{data?.departmentBranch || '-'}</div>
          <div className="ea-col-title">业绩目标</div>
          <div className="ea-col-content">{data?.performanceTargets || '-'}</div>
        </div>
        <div className="ea-row">
          <div className="ea-col-title">业绩基线</div>
          <div className="ea-col-content">{data?.performanceBaseline || '-'}</div>
          <div className="ea-col-title">年度业绩</div>
          <div className="ea-col-content">{renderMoney(data?.performanceEvaluationCount)}</div>
        </div>
        <div className="ea-row">
          <div className="ea-col-title">提成金额</div>
          <div className="ea-col-content">{renderMoney(data?.commissionAmount)}</div>
          <div className="ea-col-title"></div>
          <div className="ea-col-content"></div>
        </div>
      </div>
      <div className="ea-table">
        <table>
          <tbody>
            <tr>
              <th>核算编号</th>
              <th>合同编号</th>
              <th>合同名称</th>
              <th>客户简称</th>
              <th>合同金额</th>
              <th>回款金额</th>
              <th>咨询费用</th>
              <th>缴税成本</th>
              <th>采购成本</th>
              <th>实施成本</th>
              <th>资金成本</th>
              <th>费用报销</th>
              <th>项目付款</th>
              <th>项目毛利</th>
              <th>比例</th>
              <th>考核业绩</th>
            </tr>
            {/* A4分辨率 794px */}
            {data?.perMainResultRespList?.map((item, index) => (
              <tr key={index}>
                <td width={100}>{item?.documentNumber}</td>
                <td width={100}>{item?.contractNumber}</td>
                <td width={180}>{item?.contractName}</td>
                <td width={80}>{item?.customerAbbreviation}</td>
                <td width={50}>{renderMoney(item?.mainConAmount)}</td>
                <td width={50}>{renderMoney(item?.receiveAmount)}</td>
                <td width={50}>{renderMoney(item?.consultingFee)}</td>
                <td width={50}>{renderMoney(item?.taxCost)}</td>
                <td width={50}>{renderMoney(item?.purchaseCost)}</td>
                <td width={50}>{renderMoney(item?.implementationCost)}</td>
                <td width={50}>{renderMoney(item?.fundCost)}</td>
                <td width={50}>{renderMoney(item?.reimbursementFee)}</td>
                <td width={50}>{renderMoney(item?.projectCost)}</td>
                <td width={50}>{renderMoney(item?.projectGrossProfit)}</td>
                <td width={50}>{item?.ratio}%</td>
                <td width={50}>{renderMoney(item?.performanceEvaluation)}</td>
              </tr>
            ))}
            <tr>
              <th colSpan={4}>合计金额</th>
              <th colSpan={1}>{renderMoney(data?.contractMoney)}</th>
              <th colSpan={1}>{renderMoney(data?.backMoney)}</th>
              <th colSpan={1}>{renderMoney(data?.consultationMoney)}</th>
              <th colSpan={1}>{renderMoney(data?.taxMoney)}</th>
              <th colSpan={1}>{renderMoney(data?.purchaseMoney)}</th>
              <th colSpan={1}>{renderMoney(data?.implementationMoney)}</th>
              <th colSpan={1}>{renderMoney(data?.capitalMoney)}</th>
              <th colSpan={1}>{renderMoney(data?.expenseMoney)}</th>
              <th colSpan={1}>{renderMoney(data?.projectMoney)}</th>
              <th colSpan={1}>{renderMoney(data?.projectGrossProfitCount)}</th>
              <th colSpan={1}>-</th>
              <th colSpan={1}>{renderMoney(data?.performanceEvaluationCount)}</th>
            </tr>
            <tr>
              <th colSpan={10}></th>
              <th colSpan={1}>提交日期</th>
              <th colSpan={2}>{formatTimeToDate(data?.submitTime)}</th>
              <th colSpan={1}>确认日期</th>
              <th colSpan={2}>{formatTimeToDate(data?.confirmTime)}</th>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
});

export default PrintTemplate;
