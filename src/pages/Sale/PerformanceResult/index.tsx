import RKPageLoading from '@/components/RKPageLoading';
import { performanceResultYear } from '@/services/oa/sale';
import { defaultTableConfig } from '@/utils/setting';
import { PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import React from 'react';

const PerformanceResult: React.FC = () => {
  const { data: years, loading: pageLoading } = useRequest(() => performanceResultYear(), {
    formatResult: (res) => {
      return res?.data?.map((i) => ({ year: i })) || [];
    },
  });

  // 表格
  const columns: ProColumns[] = [
    {
      title: '年份',
      dataIndex: 'year',
      valueType: 'dateYear',
      render: (dom, record) => (
        <a
          onClick={() =>
            history.push({
              pathname: `/sale/performance-result/${record?.year}`,
            })
          }
        >
          {dom}
        </a>
      ),
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <RKPageLoading loading={pageLoading} />
      <ProTable
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        rowKey="year"
        columns={columns}
        headerTitle="请选择统计年份"
        dataSource={years}
        options={false}
      />
    </PageContainer>
  );
};

export default PerformanceResult;
