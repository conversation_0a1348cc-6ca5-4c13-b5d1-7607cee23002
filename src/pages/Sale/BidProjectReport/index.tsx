import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { BIDDING_ALLOCATION_STATUS, BIDDING_STATUS } from '@/enums';
import { userDepartmentList } from '@/services/oa/auth';
import { bidProjectReportPage } from '@/services/oa/bid';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Link, useAccess, useRequest } from '@umijs/max';
import { message, Modal, Tag } from 'antd';
import React, { useRef, useState } from 'react';

const BidProjectReport: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const { canDeleteBidProjectReport = false } = useAccess();
  const { data: businessList, loading: businessListLoading } = useRequest(() =>
    userDepartmentList({ id: '1' }),
  );
  const { data: saleList, loading: saleListLoading } = useRequest(() =>
    userDepartmentList({ id: '6' }),
  );
  // 删除
  const handleDelete = async (rows: any[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.reportNumber!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除投标报备“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        // deleteRecord(ids); // 这里需要你实现删除逻辑
        message.success('删除成功');
        tableRef.current?.reloadAndRest?.();
      },
    });
  };

  // 表格
  const columns: ProColumns<API.BidProjectReportResp>[] = [
    {
      title: '投标报备号',
      dataIndex: 'documentNumber',
      width: 180,
      render(dom, entity) {
        return <Link to={`/sale/bid-project-report/edit/${entity.id}`}>{dom}</Link>;
      },
    },
    {
      title: '分配状态',
      dataIndex: 'tagStatus',
      width: 110,
      valueEnum: option2enum(BIDDING_ALLOCATION_STATUS),
      hideInTable: true,
    },
    {
      title: '分配状态',
      dataIndex: 'tagStatus',
      width: 110,
      render(_, entity) {
        const tagStatus = BIDDING_ALLOCATION_STATUS?.find(
          (item) => item.value === entity.tagStatus,
        );
        return <Tag color={tagStatus?.color}>{tagStatus?.label}</Tag>;
      },
      hideInSearch: true,
    },
    {
      title: '报备日期',
      dataIndex: 'reportDate',
      valueType: 'date',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '销售人员',
      dataIndex: 'salesPersonName',
      width: 110,
      hideInSearch: true,
    },
    {
      title: '销售人员',
      dataIndex: 'salesPersonId',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: saleList,
        fieldNames: {
          label: 'username',
          value: 'id',
        },
        loading: saleListLoading,
        showSearch: true,
      },
    },
    {
      title: '商务人员',
      dataIndex: 'businessName',
      width: 110,
      hideInSearch: true,
    },
    {
      title: '商务人员',
      dataIndex: 'businessId',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: businessList,
        fieldNames: {
          label: 'username',
          value: 'id',
        },
        loading: businessListLoading,
        showSearch: true,
      },
    },

    {
      title: '项目名称',
      dataIndex: 'bidProjectName',
      width: 180,
      ellipsis: true,
    },
    {
      title: '金额',
      dataIndex: 'amount',
      valueType: 'money',
      width: 120,
      hideInSearch: true,
    },

    {
      title: '保证金',
      dataIndex: 'deposit',
      valueType: 'money',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '报名截止日期',
      dataIndex: 'signupDeadline',
      valueType: 'date',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '投标日期',
      dataIndex: 'bidDate',
      valueType: 'date',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '投标状态',
      dataIndex: 'status',
      width: 100,
      valueEnum: option2enum(BIDDING_STATUS),
    },
  ];

  return (
    <PageContainer header={{ title: false }}>
      <ProTable<API.BidProjectReportResp>
        {...defaultTableConfig}
        actionRef={tableRef}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        rowSelection={
          canDeleteBidProjectReport && {
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRows(selectedRows);
            },
          }
        }
        columns={columns}
        headerTitle="投标项目报备表"
        request={async (params) => {
          const { type, bidMethod, status, tagStatus, salesPersonId, businessId, ...restParams } =
            params;
          return queryPagingTable(
            {
              filter: { type, bidMethod, status, tagStatus, salesPersonId, businessId },
              ...restParams,
            },
            bidProjectReportPage,
          );
        }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default BidProjectReport;
