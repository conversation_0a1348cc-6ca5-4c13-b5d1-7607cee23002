import RKCol from '@/components/RKCol';
import RK<PERSON>ageHeader from '@/components/RKPageHeader';
import { BIDDING_STATUS, BIDDING_TYPE, COOPERATION_WAYS } from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useVisibleUserList } from '@/hooks/useVisibleUserList';
import {
  bidProjectReportById,
  bidProjectReportSpendClose,
  bidProjectReportUpdate,
} from '@/services/oa/bid';
import { requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormInstance,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useAccess, useLocation, useModel, useRequest } from '@umijs/max';
import { message, Row, Space } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { useCallback, useEffect, useRef } from 'react';
import CloseModal from './CloseModal';
import ConfirmationModal from './ConfirmationModal';
import RemarksModal from './RemarksModal';

const BidProjectReportDetails: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  const formRef = useRef<ProFormInstance>();
  const { canEditBidProjectReport, canAssignBidProjectReport, canCloseBidProjectReport } =
    useAccess();
  const { approvalDetails } = useModel('useApprovalModel');

  const { visibleUserList, visibleUserLoading } = useVisibleUserList();

  // 判断是否为审批页面
  const { pathname } = useLocation();
  const isApprovalPage = pathname.includes('/approval/');
  const { run: getCount } = useRequest((id) => bidProjectReportSpendClose({ id }), {
    manual: true,
    onSuccess: (res) => {
      const count = res?.count;
      if (count) {
        message.open({
          type: 'warning',
          content: `该销售有${count} 投标项目报备未关闭！`,
          duration: 0,
        });
      }
    },
  });

  // 详情
  const { data, loading, refresh } = useRequest(() => bidProjectReportById({ id }), {
    ready: isEditPage && !isApprovalPage,
    onSuccess: (res) => {
      if (isApprovalPage) getCount(res?.salesPersonId);
    },
  });

  const canEditPage = canEditBidProjectReport && ['0', '2'].includes(data?.activiStatus || '');

  // 如果是审批页面 则数据从缓存里面取

  useEffect(() => {
    if (isApprovalPage) {
      formRef.current?.setFieldsValue?.(approvalDetails?.fromData);
    } else {
      formRef.current?.setFieldsValue?.(data);
    }
  }, [isApprovalPage, approvalDetails, data]);

  const codeRef = useRef(0);
  const saveIntervalRef = useRef<number | null>(null);

  // 更新

  const { run: updateBid, loading: updateLoading } = useRequest(bidProjectReportUpdate, {
    manual: true,
    formatResult: (res) => res,
    onSuccess: (res) => {
      codeRef.current = res.code;
      if (res.code === 200) {
        message.success('操作成功');
        refresh();
      }
    },
  });

  const clearSaveInterval = useCallback(() => {
    if (saveIntervalRef.current) {
      clearInterval(saveIntervalRef.current);
      saveIntervalRef.current = null;
    }
  }, []);

  const onSave = useCallback(async () => {
    try {
      // 验证表单
      await formRef.current?.validateFields();
      // 触发表单提交
      formRef.current?.submit();

      // 等待 updateBid 的结果
      return new Promise((resolve) => {
        const checkResult = () => {
          if (codeRef.current === 200) {
            resolve(200);
          } else if (codeRef.current !== 0) {
            resolve(500);
          } else {
            setTimeout(checkResult, 100);
          }
        };
        checkResult();
      });
    } catch (error) {
      console.error('表单验证失败:', error);
      return 500;
    }
  }, []);

  // 在组件内部定义统一的禁用规则
  const isFieldDisabled = useCallback((currentStatus: string = ''): boolean => {
    return ['1', '2'].includes(currentStatus || '');
  }, []);

  return (
    <PageContainer
      header={{ title: false }}
      className="detail-container"
      loading={loading}
      onBack={() => {
        if (saveIntervalRef.current) {
          clearSaveInterval();
        }
      }}
    >
      <ProForm<API.BidProjectReportResp>
        formRef={formRef}
        disabled={!canEditPage || isApprovalPage}
        submitter={
          canEditPage && !isApprovalPage && data?.activiStatus !== '2'
            ? {
                searchConfig: {
                  submitText: '保存',
                  resetText: '取消',
                },
                onReset: () => {
                  if (saveIntervalRef.current) {
                    clearSaveInterval();
                  }
                  history.go(-1);
                },

                render: (_, doms) => {
                  return <FooterToolbar>{doms}</FooterToolbar>;
                },
                submitButtonProps: {
                  loading: updateLoading,
                },
              }
            : false
        }
        onFinish={async (values) => {
          updateBid(values);
        }}
      >
        <div className="rk-none">
          <ProFormText name="id" />
          <ProFormText name="activiStatus" />
          <ProFormText name="salesPersonId" />
          <ProFormText name="customerId" />
          <ProFormText name="businessId" />
          <ProFormText name="documentNumber" disabled label="投标报备号" />
        </div>
        <ProFormDependency name={['documentNumber', 'activiStatus']}>
          {({ documentNumber, activiStatus }) => {
            const status = isApprovalPage
              ? (approvalDetails?.activiStatus as unknown as string) || '9'
              : activiStatus;
            return (
              <RKPageHeader
                id={id}
                status={status}
                title={documentNumber}
                approveType="BID_FILING_APPROVAL"
                onOperationCallback={() => {
                  refresh();
                }}
                onSave={onSave}
                saveLoading={updateLoading}
                extra={
                  <Space size={16}>
                    {activiStatus === '2' && canAssignBidProjectReport && (
                      <ConfirmationModal refresh={refresh} />
                    )}
                    {activiStatus === '2' && canCloseBidProjectReport && (
                      <CloseModal refresh={refresh} initialValues={{ status: data?.status }} />
                    )}
                    {activiStatus === '2' && (
                      <RemarksModal refresh={refresh} initialValues={{ remarks: data?.remarks }} />
                    )}
                  </Space>
                }
              />
            );
          }}
        </ProFormDependency>

        <Row gutter={24}>
          <RKCol>
            <ProFormDatePicker name="reportDate" label="报备日期" disabled rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormText name="salesPersonName" disabled label="销售" rules={[requiredRule]} />
          </RKCol>
          <ProFormDependency name={['businessId', 'businessName']}>
            {({ businessId }) => {
              const canVisible = visibleUserList?.some((item) => item.id === businessId);
              if (!canVisible)
                return (
                  <RKCol>
                    <ProFormText
                      name="businessName"
                      label="商务人员"
                      disabled={isFieldDisabled(data?.activiStatus)}
                    />
                  </RKCol>
                );
              return (
                <RKCol>
                  <ProFormSelect
                    name="businessId"
                    label="商务人员"
                    disabled={isFieldDisabled(data?.activiStatus)}
                    allowClear={false}
                    fieldProps={{
                      options: visibleUserList as DefaultOptionType[],
                      fieldNames: {
                        value: 'id',
                        label: 'username',
                      },
                      loading: visibleUserLoading,
                    }}
                  />
                </RKCol>
              );
            }}
          </ProFormDependency>

          <RKCol>
            <ProFormSelect name="customerName" label="客户名称" disabled rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormText
              name="bidProjectName"
              label="项目名称"
              rules={[requiredRule]}
              disabled={isFieldDisabled(data?.activiStatus)}
            />
          </RKCol>
          <RKCol>
            <ProFormMoney
              name="amount"
              label="金额"
              rules={[requiredRule]}
              disabled={isFieldDisabled(data?.activiStatus)}
            />
          </RKCol>
          <RKCol>
            <ProFormText
              name="linkRequired"
              label="链接"
              disabled={isFieldDisabled(data?.activiStatus)}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="bidType"
              label="类型"
              options={COOPERATION_WAYS}
              rules={[requiredRule]}
              disabled={isFieldDisabled(data?.activiStatus)}
            />
          </RKCol>
          <RKCol>
            <ProFormMoney
              name="deposit"
              label="保证金"
              rules={[requiredRule]}
              disabled={isFieldDisabled(data?.activiStatus)}
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker
              name="signupDeadline"
              label="报名截止日期"
              rules={[requiredRule]}
              disabled={isFieldDisabled(data?.activiStatus)}
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker
              name="bidDate"
              label="投标日期"
              rules={[requiredRule]}
              disabled={isFieldDisabled(data?.activiStatus)}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="bidMethod"
              label="投标方式"
              options={BIDDING_TYPE}
              rules={[requiredRule]}
              disabled={isFieldDisabled(data?.activiStatus)}
            />
          </RKCol>
          <ProFormDependency name={['status']}>
            {({ status }) => {
              if (!status) return <></>;
              return (
                <RKCol>
                  <ProFormSelect
                    name="status"
                    label="投标状态"
                    options={BIDDING_STATUS}
                    disabled={isFieldDisabled(data?.activiStatus)}
                  />
                </RKCol>
              );
            }}
          </ProFormDependency>
          <ProFormDependency name={['remarks']}>
            {({ remarks }) => {
              if (!remarks) return <></>;
              return (
                <RKCol lg={24} md={24} sm={24}>
                  <ProFormTextArea
                    name="remarks"
                    label="备注"
                    fieldProps={{
                      autoSize: {
                        minRows: 2,
                        maxRows: 3,
                      },
                    }}
                    disabled={isFieldDisabled(data?.activiStatus)}
                  />
                </RKCol>
              );
            }}
          </ProFormDependency>
        </Row>
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(BidProjectReportDetails);
