import { BIDDING_STATUS } from '@/enums';
import { bidProjectReportClose } from '@/services/oa/bid';
import { requiredRule } from '@/utils/setting';
import { ModalForm, ModalFormProps, ProFormSelect } from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import { Button, message } from 'antd';
import { FC } from 'react';

const CloseModal: FC<
  ModalFormProps & {
    refresh: () => void;
  }
> = ({ refresh, ...props }) => {
  const { id } = useParams();

  const { run, loading } = useRequest((params) => bidProjectReportClose(params), {
    manual: true,
    formatResult: (res) => res,
  });

  return (
    <ModalForm
      title="关闭投标项目报备"
      trigger={<Button type="primary">关闭</Button>}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
      }}
      onFinish={async (values) => {
        const res = await run({ id, ...values });
        const success = res.code === 200;
        if (success) {
          message.success('关闭成功');
          refresh();
        }
        return success;
      }}
      width={500}
      style={{
        height: 80,
      }}
      {...props}
      loading={loading}
    >
      <ProFormSelect
        label="投标状态"
        name="status"
        options={BIDDING_STATUS}
        rules={[requiredRule]}
      />
    </ModalForm>
  );
};

export default CloseModal;
