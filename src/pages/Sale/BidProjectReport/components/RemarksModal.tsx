import { bidProjectReportRemarks } from '@/services/oa/bid';
import { requiredRule } from '@/utils/setting';
import { ModalForm, ModalFormProps, ProFormTextArea } from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import { Button, message } from 'antd';
import { FC } from 'react';

const RemarksModal: FC<
  ModalFormProps & {
    refresh: () => void;
  }
> = ({ refresh, ...props }) => {
  const { id } = useParams();

  const { run, loading } = useRequest((params) => bidProjectReportRemarks(params), {
    manual: true,
    formatResult: (res) => res,
  });

  return (
    <ModalForm
      title="添加备注"
      trigger={<Button type="primary">备注</Button>}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
      }}
      onFinish={async (values) => {
        const res = await run({ id, ...values });
        const success = res.code === 200;
        if (success) {
          message.success('操作成功');
          refresh();
        }
        return success;
      }}
      width={700}
      style={{
        height: 100,
      }}
      {...props}
      loading={loading}
    >
      <ProFormTextArea
        name="remarks"
        fieldProps={{
          autoSize: {
            minRows: 3,
            maxRows: 3,
          },
        }}
        rules={[requiredRule]}
      />
    </ModalForm>
  );
};

export default RemarksModal;
