import { userDepartmentList } from '@/services/oa/auth';
import { bidProjectReportAssignment } from '@/services/oa/bid';
import { requiredRule } from '@/utils/setting';
import { ModalForm, ModalFormProps, ProFormSelect } from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import { Button, message } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { FC } from 'react';

const ConfirmationModal: FC<
  ModalFormProps & {
    refresh: () => void;
  }
> = ({ refresh, ...props }) => {
  const { data: businessList, loading: businessListLoading } = useRequest(() =>
    userDepartmentList({ id: '1' }),
  );

  const { id } = useParams();

  const { run, loading } = useRequest((params) => bidProjectReportAssignment(params), {
    manual: true,
    formatResult: (res) => res,
  });

  return (
    <ModalForm
      title="分配商务人员"
      trigger={<Button type="primary">分配</Button>}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
      }}
      onFinish={async (values) => {
        const res = await run({ id, ...values });
        const success = res.code === 200;
        if (success) {
          message.success('分配成功');
          refresh();
        }
        return success;
      }}
      width={500}
      style={{
        height: 80,
      }}
      {...props}
      loading={loading}
    >
      <ProFormSelect
        name="businessId"
        allowClear={false}
        fieldProps={{
          options: businessList as DefaultOptionType[],
          fieldNames: {
            value: 'id',
            label: 'username',
          },
          loading: businessListLoading,
        }}
        rules={[requiredRule]}
      />
    </ModalForm>
  );
};

export default ConfirmationModal;
