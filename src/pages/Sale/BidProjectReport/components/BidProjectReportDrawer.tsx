import { BIDDING_TYPE, COOPERATION_WAYS } from '@/enums';
import { bidProjectReportCreate } from '@/services/oa/bid';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProFormDatePicker,
  ProFormGroup,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import dayjs from 'dayjs';
import { FC } from 'react';

const BidProjectReportDrawer: FC<DrawerFormProps> = (props) => {
  const { initialValues, onFinish, ...restProps } = props;
  // const { visibleUserList, visibleUserLoading } = useVisibleUserList();
  // 创建
  const { run: create } = useRequest(bidProjectReportCreate, {
    formatResult: (res) => res,
    manual: true,
    onSuccess: (res) => {
      message.success('创建成功');
      onFinish?.(res);
    },
  });

  return (
    <DrawerForm
      width="auto"
      title="新建投标项目报备"
      initialValues={{
        reportDate: dayjs().format('YYYY-MM-DD'),
        salesPersonName: initialValues?.clientManger,
        salesPersonId: initialValues?.clientMangerId,
        customerName: initialValues?.clientName,
        customerId: initialValues?.id,
        amount: 0,
        deposit: 0,
      }}
      {...restProps}
      onFinish={async (values) => {
        if (!values?.id) {
          await create(values);
        }
      }}
    >
      <div className="rk-none">
        <ProFormText width="md" name="id" />
        <ProFormText width="md" name="activiStatus" />
        <ProFormText width="md" name="salesPersonId" />
        <ProFormText width="md" name="customerId" />
        <ProFormText width="md" name="documentNumber" disabled label="投标报备号" />
      </div>
      <ProFormGroup>
        <ProFormDatePicker
          width="md"
          name="reportDate"
          label="报备日期"
          disabled
          rules={[requiredRule]}
        />
        <ProFormText
          width="md"
          name="salesPersonName"
          disabled
          label="销售"
          rules={[requiredRule]}
        />
      </ProFormGroup>
      <ProFormGroup>
        <ProFormSelect
          width="md"
          name="customerName"
          label="客户名称"
          disabled
          rules={[requiredRule]}
        />
        <ProFormText width="md" name="bidProjectName" label="项目名称" rules={[requiredRule]} />
      </ProFormGroup>
      <ProFormGroup>
        <ProFormMoney width="md" name="amount" label="金额" rules={[requiredRule]} />
        <ProFormText width="md" name="linkRequired" label="链接" />
      </ProFormGroup>
      <ProFormGroup>
        <ProFormSelect
          width="md"
          name="bidType"
          label="类型"
          options={COOPERATION_WAYS}
          rules={[requiredRule]}
        />
        <ProFormMoney width="md" name="deposit" label="保证金" rules={[requiredRule]} />
      </ProFormGroup>
      <ProFormGroup>
        <ProFormDatePicker
          width="md"
          name="signupDeadline"
          label="报名截止日期"
          rules={[requiredRule]}
        />
        <ProFormDatePicker width="md" name="bidDate" label="投标日期" rules={[requiredRule]} />
      </ProFormGroup>
      <ProFormGroup>
        <ProFormSelect
          width="md"
          name="bidMethod"
          label="投标方式"
          options={BIDDING_TYPE}
          rules={[requiredRule]}
        />
      </ProFormGroup>
    </DrawerForm>
  );
};

export default BidProjectReportDrawer;
