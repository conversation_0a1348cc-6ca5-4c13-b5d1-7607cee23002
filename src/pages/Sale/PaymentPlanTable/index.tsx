import { CUSTOMER_LEVEL, PAY_STATUS } from '@/enums';
import { useDepartmentUserList } from '@/hooks/useDepartmentUserList';
import { sortMap } from '@/pages/ReportAnalysis/WorkloadStatistics';
import { purchasePaymentPlanSaleList } from '@/services/oa/sale';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProTable,
  StatisticCard,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';

const PaymentPlanTable: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [statisticData, setStatisticData] = useState<Record<string, any>>();
  const { userList, loading } = useDepartmentUserList('6');

  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '客户简称',
      dataIndex: 'clientAbbreviationEnd',
      hideInTable: true,
    },
    {
      title: '日期范围',
      dataIndex: 'month',
      valueType: 'dateMonthRange',
      hideInTable: true,
    },
    {
      title: '付款状态',
      dataIndex: 'payStatus',
      valueEnum: option2enum(PAY_STATUS),
      hideInTable: true,
    },
    {
      title: '客户简称',
      dataIndex: 'clientAbbreviationEnd',
      fixed: 'left',
      width: 120,
      ellipsis: true,
      copyable: true,
      search: false,
      render: (dom, entity) => {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/crm/customer/edit/${entity?.clientEndId}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '供应商简称',
      dataIndex: 'clientAbbreviation',
      width: 120,
      ellipsis: true,
      search: false,
      render: (dom, entity) => {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/crm/suppliers/edit/${entity?.partnerId}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      width: 120,
      search: false,
      ellipsis: true,
      render: (dom, entity) => {
        return (
          <a
            className="rk-a-span"
            onClick={() => {
              history.push(`/contract/purchase/edit/${entity.contractId}`);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
      width: 200,
      ellipsis: true,
      search: false,
    },
    {
      title: '期次',
      dataIndex: 'period',
      width: 60,
      ellipsis: true,
      search: false,
    },
    {
      title: '计划付款金额',
      valueType: 'money',
      dataIndex: 'estimatePayAmount',
      width: 130,
      ellipsis: true,
      search: false,
      sorter: true,
    },
    {
      title: '计划付款日期',
      valueType: 'date',
      dataIndex: 'estimatePayTime',
      width: 120,
      ellipsis: true,
      search: false,
      sorter: true,
    },
    {
      title: '对方开票日期',
      valueType: 'date',
      dataIndex: 'countInvoicedTime',
      width: 120,
      search: false,
      ellipsis: true,
      sorter: true,
    },
    {
      title: '付款状态',
      dataIndex: 'payStatus',
      valueEnum: option2enum(PAY_STATUS),
      ellipsis: true,
      width: 100,
      search: false,
    },
    {
      title: '客户等级',
      dataIndex: 'grade',
      width: 100,
      ellipsis: true,
      valueEnum: option2enum(CUSTOMER_LEVEL),
    },
    {
      title: '销售',
      dataIndex: 'salePerson',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '销售',
      dataIndex: 'salePersonId',
      valueType: 'select',
      fieldProps: {
        options: userList,
        loading,
        showSearch: true,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        filterOption: true,
        optionFilterProp: 'label',
      },
      hideInTable: true,
    },
  ];
  return (
    <PageContainer header={{ title: false }}>
      <StatisticCard.Group style={{ marginBottom: 16 }}>
        <StatisticCard
          statistic={{
            title: '计划付款总金额',
            value: statisticData?.paymentAmount || 0,
            prefix: '￥',
            status: 'processing',
          }}
        />
      </StatisticCard.Group>
      <ProTable
        {...defaultTableConfig}
        headerTitle="付款计划表"
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        search={{
          defaultCollapsed: false,
          filterType: 'query',
        }}
        request={async (params, sort) => {
          const {
            current,
            pageSize,
            clientAbbreviationEnd,
            month,
            payStatus,
            salePersonId,
            grade,
          } = params;
          const scope =
            month &&
            [
              {
                key: 'ge',
                name: 'estimatePayTime',
                val: dayjs(month[0]).startOf('month').format('YYYY-MM-DD'),
              },
              {
                key: 'le',
                name: 'estimatePayTime',
                val: dayjs(month[1]).endOf('month').format('YYYY-MM-DD'),
              },
            ].filter(Boolean);
          const extra = { clientAbbreviationEnd, payStatus };
          const filter = { salePersonId, grade };
          const sortEstimatePayAmount =
            sort && sort?.estimatePayAmount ? sortMap[sort.estimatePayAmount] : undefined;
          const sortEstimatePayTime =
            sort && sort?.estimatePayTime ? sortMap[sort.estimatePayTime] : undefined;
          const sortCountInvoicedTime =
            sort && sort?.countInvoicedTime ? sortMap[sort.countInvoicedTime] : undefined;

          const msg = await purchasePaymentPlanSaleList({
            pageNum: current,
            pageSize,
            scope,
            extra,
            filter,
            sortEstimatePayAmount,
            sortEstimatePayTime,
            sortCountInvoicedTime,
          });
          const { estimateReAmount, records, total } = (msg.data as Record<string, any>) || {};
          setStatisticData({
            paymentAmount: estimateReAmount,
          });
          return {
            data: records || [],
            success: true,
            total: total,
          };
        }}
      />
    </PageContainer>
  );
};

export default PaymentPlanTable;
