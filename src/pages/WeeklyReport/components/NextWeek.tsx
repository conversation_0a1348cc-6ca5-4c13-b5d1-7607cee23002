import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import RKSelectLabel from '@/components/RKSelectLabel';
import BaseListContext from '@/Context/BaseListContext';
import { WORK_TYPE } from '@/enums';
import { getRandomId, option2enum } from '@/utils';
import { ProColumns, ProForm } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { memo, useContext } from 'react';

// 设置中文本地化
dayjs.locale('zh-cn');

const NextWeek = () => {
  const { availableProjectList = [], availableProjectLoading, data } = useContext(BaseListContext);

  const columns: ProColumns<API.NextWeek>[] = [
    {
      title: '星期',
      width: 100,
      dataIndex: 'recordWeek',
      fieldProps: {
        disabled: true,
      },
      renderText: (text, { recordDate }) => {
        if (!recordDate) return '';
        return dayjs(recordDate).format('dddd');
      },
    },
    {
      title: '工作计划',
      dataIndex: 'workPlan',
      valueType: 'textarea',
      width: 200,
      fieldProps: {
        autoSize: {
          minRows: 1,
          maxRows: 2,
        },
      },
    },
    {
      title: '项目编号',
      dataIndex: 'project',
      valueType: 'select',
      fieldProps: (form, { rowKey }) => ({
        showSearch: true,
        loading: availableProjectLoading,
        options: availableProjectList?.map((item) => {
          return {
            value: item.projectNumber,
            label: <RKSelectLabel title={item.projectNumber} info={item.projectName} />,
            projectName: item.projectName,
            keywords: `${item.projectName}${item.projectNumber}`,
          };
        }),
        filterOption: (inputValue: string, option: Record<string, any>) => {
          return option?.keywords.indexOf(inputValue) >= 0;
        },
        onChange: (val: string, option: API.ProBaseInfoResp) => {
          const rowData = form.getFieldValue([rowKey || '']);
          const { projectName } = option;
          form.setFieldValue([rowKey || ''], {
            ...rowData,
            projectName: projectName,
          });
        },
      }),
      width: 180,
    },
    {
      title: '项目名称',
      width: 180,
      dataIndex: 'projectName',
      fieldProps: {
        disabled: true,
        placeholder: '',
      },
    },
    {
      title: '工作类型',
      width: 100,
      dataIndex: 'workType',
      valueEnum: option2enum(WORK_TYPE),
      fieldProps: {
        showSearch: true,
      },
    },
  ];
  return (
    <ProForm.Item
      name="nextWeeks"
      getValueProps={(val) => ({
        value: val?.map((item: Record<string, any>) => {
          const { projectNumber } = item;
          const rowData = availableProjectList?.find(
            (item) => item.projectNumber === projectNumber,
          );
          return {
            ...item,
            key_: item.key_ || getRandomId(),
            projectNumber: rowData?.projectNumber,
            projectId: rowData?.id,
          };
        }),
      })}
    >
      <RKFormEditableProTable
        headerTitle="下周工作计划"
        columns={columns}
        isDelete={false}
        recordCreatorProps={false}
        readonly={data?.status === 'SUBMITTED'}
        copy={false}
      />
    </ProForm.Item>
  );
};

export default memo(NextWeek);
