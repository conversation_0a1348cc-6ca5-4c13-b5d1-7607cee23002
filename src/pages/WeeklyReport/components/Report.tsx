import RKCol from '@/components/RKCol';
import BaseListContext from '@/Context/BaseListContext';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useAvailableProjectList } from '@/hooks/useAvailableProjectList';
import { updateWeek, weekInfo, weekStatus } from '@/services/oa/week';
import {
  FooterToolbar,
  PageContainer,
  PageHeader,
  ProForm,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal, Row } from 'antd';
import dayjs from 'dayjs';
import { produce } from 'immer';
import { useCallback, useRef } from 'react';
import NextWeek from './NextWeek';
import WeekReport from './WeekReport';
// 设置中文本地化
dayjs.locale('zh-cn');

const Report: React.FC<WithRouteEditingProps> = ({ id, isEditPage }) => {
  const { canEditWeeklyReport, canResetWeeklyReport } = useAccess();
  const formRef = useRef<ProFormInstance>();
  const { availableProjectList, loading: availableProjectLoading } = useAvailableProjectList();

  const handleUpdateData = (values: API.WeekUpdateReq) => {
    return produce(values || [], (draft) => {
      return {
        ...draft,
        weekDetailsList: draft?.weekDetailsList?.map((item) => {
          const { projectNumber } = item;
          const rowData = availableProjectList?.find(
            (item) => item.projectNumber === projectNumber,
          );
          return {
            ...item,
            projectName: rowData?.projectName,
            projectId: rowData?.id,
          };
        }),
      };
    });
  };

  // 获取详情
  const { data, loading, refresh } = useRequest(
    () =>
      weekInfo({
        req: { id },
      }),
    {
      ready: isEditPage,
      onSuccess: (res) => {
        setTimeout(() => {
          formRef.current?.setFieldsValue({ ...res, id: id });
        }, 500);
      },
    },
  );

  // 提交
  const { run: submit, loading: submitLoading } = useRequest(
    () =>
      weekStatus({
        id,
        status: 'SUBMITTED',
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        refresh();
      },
      formatResult: (res) => res,
    },
  );

  // 退回
  const { run: sendBack, loading: sendBackLoading } = useRequest(
    () =>
      weekStatus({
        id,
        status: 'DRAFT',
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('退回成功！');
        refresh();
      },
      formatResult: (res) => res,
    },
  );

  // 修改
  const { run: update, loading: editLoading } = useRequest((value) => updateWeek(value), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('保存成功！');
    },
    formatResult: (res) => res,
  });

  const handleSubmit = useCallback(() => {
    Modal.confirm({
      title: '确认提交',
      content: '请确认周报填写完整，提交后不可再次更改！',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const updateData = handleUpdateData(formRef.current?.getFieldsValue());
        await update(updateData);
        submit();
      },
    });
  }, [availableProjectList]);

  return (
    <PageContainer header={{ title: true }} className="detail-container" loading={loading}>
      <BaseListContext.Provider value={{ availableProjectList, availableProjectLoading, data }}>
        <ProForm
          formRef={formRef}
          submitter={
            data?.status === 'DRAFT' &&
            canEditWeeklyReport && {
              searchConfig: {
                submitText: '保存',
                resetText: '取消',
              },
              onReset: () => {
                history.go(-1);
              },

              render: (props, doms) => {
                return <FooterToolbar>{doms}</FooterToolbar>;
              },
              submitButtonProps: {
                loading: editLoading,
              },
            }
          }
          onValuesChange={(value) => {
            if (value?.weekDetailsList?.length) {
              const total = (value?.weekDetailsList as API.WeekDetailsInsertReq[])
                ?.map((item) => item.workTime)
                .filter(Boolean)
                ?.reduce((accumulator = 0, currentValue = 0) => {
                  return accumulator + currentValue;
                }, 0);

              formRef.current?.setFieldValue('countTime', total);
            }
          }}
          onFinish={async (values: API.WeekUpdateReq) => {
            const updateData = produce(values || [], (draft) => {
              return {
                ...draft,
                weekDetailsList: draft?.weekDetailsList?.map((item) => {
                  const { projectNumber } = item;
                  const rowData = availableProjectList?.find(
                    (item) => item.projectNumber === projectNumber,
                  );
                  return {
                    ...item,
                    projectName: rowData?.projectName,
                    projectId: rowData?.id,
                  };
                }),
              };
            });

            if (isEditPage) {
              update(updateData);
            }
          }}
        >
          <PageHeader
            extra={
              <>
                {canEditWeeklyReport && (
                  <Button
                    type="primary"
                    disabled={data?.status !== 'DRAFT'}
                    loading={submitLoading}
                    onClick={handleSubmit}
                  >
                    保存并提交
                  </Button>
                )}
                {['SUBMITTED', 'OVERDUE'].includes(data?.status || '') && canResetWeeklyReport && (
                  <Button type="primary" loading={sendBackLoading} onClick={sendBack}>
                    退回
                  </Button>
                )}
              </>
            }
          />

          <div className="rk-none">
            <ProFormText name="id" />
          </div>
          <Row gutter={24}>
            <RKCol>
              <ProFormText label="汇总人" name="employeeName" disabled />
            </RKCol>
            <RKCol>
              <ProFormText label="开始日期" name="startTime" disabled />
            </RKCol>
            <RKCol>
              <ProFormText label="结束日期" name="endTime" disabled />
            </RKCol>

            <RKCol>
              <ProFormText label="总工时" name="countTime" disabled placeholder="" />
            </RKCol>
          </Row>
          {/* 本周工作*/}
          <WeekReport />
          {/* 下周 */}
          <NextWeek />
        </ProForm>
      </BaseListContext.Provider>
    </PageContainer>
  );
};

export default withRouteEditing(Report);
