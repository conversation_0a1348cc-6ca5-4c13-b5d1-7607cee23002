import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import RKSelectLabel from '@/components/RKSelectLabel';
import BaseListContext from '@/Context/BaseListContext';
import { WORK_TYPE } from '@/enums';
import { getRandomId, option2enum } from '@/utils';
import { ProColumns, ProForm } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { useContext } from 'react';

// 设置中文本地化
dayjs.locale('zh-cn');

const WeekReport = () => {
  const { availableProjectList, availableProjectLoading, data } = useContext(BaseListContext);
  const columns: ProColumns<API.WeekDetailsInfoResp>[] = [
    {
      dataIndex: 'ifDefault',
      valueType: 'digit',
      width: 50,
      hideInTable: true,
    },
    {
      title: '日期',
      width: 150,
      dataIndex: 'recordDate',
      valueType: 'date',
      fieldProps: (form, { rowKey, entity }) => {
        return {
          allowClear: false,
          disabledDate: (current: dayjs.Dayjs | null) => {
            return (
              current && (current?.isBefore(data?.startTime) || current?.isAfter(data?.endTime))
            );
          },
          disabled: entity?.ifDefault === 0,
          onChange: (val: any) => {
            const rowData = form.getFieldValue([rowKey || '']);
            const recordWeek = val ? dayjs(val).format('dddd') : '';
            form.setFieldValue([rowKey || ''], {
              ...rowData,
              recordWeek,
            });
          },
        };
      },
    },
    {
      title: '星期',
      width: 100,
      dataIndex: 'recordWeek',
      fieldProps: {
        disabled: true,
      },
      renderText: (text, { recordDate }) => {
        if (!recordDate) return '';
        return dayjs(recordDate).format('dddd');
      },
    },
    {
      title: '工作记录',
      dataIndex: 'content',
      valueType: 'textarea',
      width: 200,
      fieldProps: {
        autoSize: {
          minRows: 1,
          maxRows: 2,
        },
        allowClear: true,
      },
    },
    {
      title: '项目编号',
      dataIndex: 'projectNumber',
      valueType: 'select',
      fieldProps: (form, { rowKey }) => ({
        showSearch: true,
        loading: availableProjectLoading,
        optionLabelProp: 'projectNumber',
        options: availableProjectList?.map((item) => {
          return {
            value: item.projectNumber,
            label: <RKSelectLabel title={item.projectNumber} info={item.projectName} />,
            projectName: item.projectName,
            keywords: `${item.projectName}${item.projectNumber}`,
          };
        }),
        filterOption: (inputValue: string, option: Record<string, any>) => {
          return option?.keywords.indexOf(inputValue) >= 0;
        },
        onChange: (val: string, option: API.ProBaseInfoResp = {}) => {
          const rowData = form.getFieldValue([rowKey || '']);
          const { projectName } = option;

          form.setFieldValue([rowKey || ''], {
            ...rowData,
            projectName: projectName,
          });
        },
      }),
      width: 180,
    },
    {
      title: '项目名称',
      width: 180,
      dataIndex: 'projectName',
      fieldProps: {
        disabled: true,
        placeholder: '',
      },
    },
    {
      title: '工作类型',
      width: 100,
      dataIndex: 'workType',
      valueEnum: option2enum(WORK_TYPE),
      fieldProps: {
        showSearch: true,
      },
    },
    {
      title: '工作时间',
      width: 80,
      dataIndex: 'workTime',
      valueType: 'digit',
      fieldProps: {
        min: 1,
        max: 24,
      },
    },
  ];

  return (
    <ProForm.Item
      name="weekDetailsList"
      getValueProps={(val) => {
        return {
          value: val?.map((item: Record<string, any>) => {
            const { projectNumber, ifDefault } = item;
            const rowData = availableProjectList?.find(
              (item) => item.projectNumber === projectNumber,
            );
            return {
              ...item,
              key_: item.key_ || getRandomId(),
              projectName: rowData?.projectName || item.projectName,
              projectId: rowData?.id,
              ifDefault: ifDefault === 0 ? 0 : 1,
              disabledDelete: ifDefault === 0,
            };
          }),
        };
      }}
    >
      <RKFormEditableProTable
        headerTitle="本周工作汇报"
        columns={columns}
        recordCreatorProps={false}
        readonly={data?.status === 'SUBMITTED'}
        operatorConfig={{
          copyType: 'next',
          ignoreField: ['ifDefault', 'workTime'],
        }}
      />
    </ProForm.Item>
  );
};

export default WeekReport;
