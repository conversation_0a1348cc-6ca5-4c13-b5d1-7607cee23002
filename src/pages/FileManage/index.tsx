import { createDirectory, getFiles } from '@/services/oa/nextcloud';
import { BASE_URL } from '@/utils/setting';
import { FolderAddOutlined, HomeFilled, UploadOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { request, useAccess, useRequest } from '@umijs/max';
import {
  Breadcrumb,
  Button,
  Card,
  Col,
  Empty,
  Input,
  message,
  Modal,
  Row,
  Spin,
  Upload,
} from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import File from './components/File';
import styles from './index.less';

const FileManage: React.FC = () => {
  const [currentPath, setCurrentPath] = useState<string>('');
  const [breadcrumbPaths, setBreadcrumbPaths] = useState<string[]>(['根目录']);
  const [createFolderVisible, setCreateFolderVisible] = useState(false);
  const [folderName, setFolderName] = useState('');
  const [uploadLoading, setUploadLoading] = useState(false);

  const { canCommonEditFileManage } = useAccess();

  // 获取文件列表
  const {
    data: fileData,
    run,
    loading,
  } = useRequest((path: string = '') => getFiles({ path }), {
    manual: true,
    onSuccess: (res: API.ResultListDavFileVO) => {
      if (res?.data && res.data.length > 0) {
        // 更新面包屑，第一项用于面包屑显示
        const firstItem = res.data[0];
        if (firstItem.path) {
          const pathSegments = firstItem.path.split('/').filter(Boolean);
          setBreadcrumbPaths(['根目录', ...pathSegments]);
        } else {
          setBreadcrumbPaths(['根目录']);
        }
      }
    },
    formatResult: (res) => res,
  });

  useEffect(() => {
    run(currentPath);
  }, [currentPath, run]);

  // 判断当前路径是否为公有文件夹
  const isPublicFolder = useCallback(() => {
    // 根据业务逻辑判断，这里假设公有文件夹路径包含"公有"或者是根目录下的特定文件夹
    // 您可能需要根据实际业务逻辑调整这个判断条件
    return currentPath.includes('公有') || currentPath.startsWith('公有/');
  }, [currentPath]);

  const shouldShowAddButton = canCommonEditFileManage && isPublicFolder();

  // 创建文件夹
  const handleCreateFolder = async () => {
    if (!folderName.trim()) {
      message.error('请输入文件夹名称');
      return;
    }

    try {
      const newPath = currentPath ? `${currentPath}${folderName}` : `${folderName}`;
      const res = await createDirectory({ path: newPath });
      if (res.code === 200) {
        message.success('文件夹创建成功');
        setCreateFolderVisible(false);
        setFolderName('');
        run(currentPath); // 刷新文件列表
      } else {
        message.error('文件夹创建失败');
      }
    } catch (error) {
      console.error('创建文件夹失败:', error);
      message.error('文件夹创建失败');
    }
  };

  // 文件上传配置
  const uploadProps = {
    name: 'file',
    action: `${BASE_URL}/api/nextcloud/upload/public`,
    headers: {
      Authorization: localStorage.getItem('RKLINK_OA_TOKEN')!,
    },
    data: {
      path: currentPath, // 当前路径
    },
    accept: '.xlsx,.xls,.docx,.doc,.pdf,.md,.txt',
    showUploadList: false,
    beforeUpload: (file: any) => {
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
        'application/msword', // .doc
        'application/pdf', // .pdf
        'text/markdown', // .md
        'text/plain', // .txt
      ];

      if (!allowedTypes.includes(file.type)) {
        message.error('只支持上传 Excel、Word、PDF、Markdown、文本文件！');
        return false;
      }

      setUploadLoading(true);
      return true;
    },
    onChange: (info: any) => {
      if (info.file.status === 'done') {
        setUploadLoading(false);
        if (info.file.response?.code === 200) {
          message.success('文件上传成功！');
          run(currentPath); // 刷新文件列表
        } else {
          message.error('文件上传失败！');
        }
      } else if (info.file.status === 'error') {
        setUploadLoading(false);
        message.error('文件上传失败！');
      }
    },
  };

  const handleClick = (file: API.DavFileVO) => {
    const { path = '', type } = file;
    if (type === 'directory') {
      setCurrentPath(path);
    }
  };

  // 获取文件扩展名
  const getFileExtension = (fileName: string): string => {
    return fileName.split('.').pop()?.toLowerCase() || '';
  };

  // 判断是否为支持的文件类型
  const isSupportedFileType = (fileName: string): boolean => {
    const extension = getFileExtension(fileName);
    const supportedTypes = ['xlsx', 'xls', 'docx', 'doc', 'pdf', 'md', 'markdown', 'txt'];
    return supportedTypes.includes(extension);
  };

  // 获取文件的MIME类型
  const getMimeType = (fileName: string, fileType: string): string => {
    if (fileType === 'directory') {
      return 'application/zip';
    }

    const extension = getFileExtension(fileName);
    const mimeTypes: Record<string, string> = {
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      xls: 'application/vnd.ms-excel',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      doc: 'application/msword',
      pdf: 'application/pdf',
      md: 'text/markdown',
      markdown: 'text/markdown',
      txt: 'text/plain',
    };

    return mimeTypes[extension] || 'application/octet-stream';
  };

  // 处理下载
  const handleDownload = async (path: string, type: string, name: string) => {
    try {
      // 对于文件类型，检查是否为支持的类型
      if (type === 'file' && !isSupportedFileType(name)) {
        message.warning('暂不支持该文件类型的下载');
        return;
      }

      const res = await request('/api/nextcloud/download', {
        method: 'GET',
        params: {
          path,
          type,
        },
        responseType: 'blob',
        getResponse: true,
        skipErrorHandler: true,
      });

      if (res.status === 200) {
        const mimeType = getMimeType(name, type);
        const url = URL.createObjectURL(new Blob([res.data], { type: mimeType }));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;

        // 设置下载文件名，文件夹添加.zip后缀
        const downloadFileName = type === 'directory' ? `${name}.zip` : name;
        link.setAttribute('download', downloadFileName);

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        message.success('下载成功');
      } else {
        message.error('下载失败');
      }
    } catch (error) {
      console.error('下载失败:', error);
      message.error('下载失败');
    }
  };

  // 获取实际显示的文件列表（从第二项开始）
  const displayFiles = fileData?.data?.slice(1) || [];

  return (
    <PageContainer header={{ title: false }}>
      <Card
        bordered={false}
        title={
          <div className={styles['breadcrumb']}>
            <Breadcrumb>
              <Breadcrumb.Item
                onClick={() => {
                  setCurrentPath('');
                  setBreadcrumbPaths(['根目录']);
                }}
                className={styles['breadcrumb-home']}
              >
                <HomeFilled className={styles.icon} />
              </Breadcrumb.Item>
              {breadcrumbPaths.slice(1).map((item, index) => (
                <Breadcrumb.Item
                  key={index}
                  onClick={() => {
                    // 点击面包屑时，构建对应的路径
                    const targetPath = breadcrumbPaths.slice(1, index + 2).join('/') + '/';
                    setCurrentPath(targetPath);
                  }}
                >
                  {item}
                </Breadcrumb.Item>
              ))}
            </Breadcrumb>
            {shouldShowAddButton && (
              <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
                <Upload {...uploadProps}>
                  <Button type="default" icon={<UploadOutlined />} loading={uploadLoading}>
                    上传文件
                  </Button>
                </Upload>
                <Button
                  type="primary"
                  icon={<FolderAddOutlined />}
                  onClick={() => setCreateFolderVisible(true)}
                >
                  新建文件夹
                </Button>
              </div>
            )}
          </div>
        }
        className={styles.card}
      >
        {!displayFiles.length && !loading && (
          <div className={styles.empty}>
            <Empty description="空目录" />
          </div>
        )}
        <Spin spinning={loading}>
          <Row gutter={[16, 24]}>
            {displayFiles.map((item) => (
              <Col lg={4} md={6} sm={8} key={item.href}>
                <File
                  fileName={item.name || ''}
                  fileType={item.type || 'file'}
                  filePath={item.path}
                  clickable={item.type === 'directory'}
                  onClick={() => handleClick(item)}
                  onDownload={handleDownload}
                />
              </Col>
            ))}
          </Row>
        </Spin>
      </Card>

      {/* 新建文件夹弹窗 */}
      <Modal
        title="新建文件夹"
        open={createFolderVisible}
        onOk={handleCreateFolder}
        onCancel={() => {
          setCreateFolderVisible(false);
          setFolderName('');
        }}
        okText="确定"
        cancelText="取消"
      >
        <Input
          placeholder="请输入文件夹名称"
          value={folderName}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFolderName(e.target.value)}
          onPressEnter={handleCreateFolder}
        />
      </Modal>
    </PageContainer>
  );
};

export default FileManage;
