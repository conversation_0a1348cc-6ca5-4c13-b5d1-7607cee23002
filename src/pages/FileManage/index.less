.list-item {
  align-items: center !important;
  :global {
    .ant-list-item-meta-title {
      margin: 0 !important;
    }
  }
}

.breadcrumb {
  .icon {
    color: var(--primary-color);
    font-size: 18px;
    cursor: pointer;
  }
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  &-home {
    color: @colorTextBase;
  }
  .more {
    font-size: 26px;
  }
}

.card {
  min-height: calc(100vh - 210px);
  :global {
    .ant-card-body {
      min-height: 100%;
    }
  }
  .empty {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
}
