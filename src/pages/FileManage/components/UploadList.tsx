// import { UNZIP_STATUS } from '@/enums';
// import { getFileTransPageUsingGET } from '@/services/http/settingController';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef } from 'react';

// 表格
const columns: ProColumns<any>[] = [
  {
    title: '文件名',
    dataIndex: 'fileName',
    width: 200,
  },
  {
    title: '上传时间',
    dataIndex: 'uploadTime',
    width: 150,
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    width: 150,
  },
  // {
  //   title: '解压状态',
  //   dataIndex: 'unzipStatus',
  //   width: 150,
  //   valueEnum: option2enum(UNZIP_STATUS),
  // },
];
const UploadList: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<any, AntTableParams>
        {...defaultTableConfig}
        options={false}
        actionRef={tableRef}
        columns={columns}
        headerTitle="上传列表"
        polling={(dataSource) =>
          dataSource.find((item) => item.unzipStatus === '解压中') ? 5000 : 0
        }
        // request={async (params) => queryPagingTable(params, getFileTransPageUsingGET)}
      />
    </PageContainer>
  );
};

export default UploadList;
