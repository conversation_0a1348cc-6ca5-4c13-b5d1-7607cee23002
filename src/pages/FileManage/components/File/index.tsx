import { Avatar, Dropdown, MenuProps, Typography } from 'antd';
import classNames from 'classnames/bind';
import styles from './index.less';
const cx = classNames.bind(styles);

const File: React.FC<{
  onClick?: () => void;
  clickable?: boolean;
  fileName: string;
  fileType: string;
  filePath?: string;
  onDownload?: (path: string, type: string, name: string) => void;
}> = ({ fileName, fileType, onClick, clickable = true, filePath, onDownload }) => {
  const isClickable = fileType === 'directory' && clickable;
  let avatarSrc;
  if (fileType === 'directory') {
    avatarSrc = '/images/folder.svg';
  } else {
    // 根据文件扩展名判断文件类型
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'xlsx':
      case 'xls':
        avatarSrc = '/images/excel.svg';
        break;
      case 'docx':
      case 'doc':
        avatarSrc = '/images/word.svg';
        break;
      case 'pdf':
        avatarSrc = '/images/pdf.svg';
        break;
      case 'md':
      case 'markdown':
        avatarSrc = '/images/markdown.svg';
        break;
      default:
        avatarSrc = '/images/file.svg';
        break;
    }
  }

  const handleDownload = () => {
    if (onDownload && filePath) {
      onDownload(filePath, fileType, fileName);
    }
  };

  const menuItems: MenuProps['items'] = [
    {
      key: 'download',
      label: '下载',
      onClick: handleDownload,
    },
  ];

  return (
    <Dropdown menu={{ items: menuItems }} trigger={['contextMenu']}>
      <div className={cx('file', { folder: isClickable })} onClick={onClick}>
        <Avatar shape="square" size={44} src={<img src={avatarSrc} alt="avatar" />} />
        <Typography.Text type="secondary" ellipsis>
          {fileName}
        </Typography.Text>
      </div>
    </Dropdown>
  );
};

export default File;
