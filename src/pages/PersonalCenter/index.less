.card {
  margin-bottom: 20px !important;
}

.verticalCenter {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.carousels {
  // height: 175.3px;
  height: 200px;
  margin-bottom: 20px !important;

  :global {
    .ant-carousel .slick-dots li.slick-active button {
      background: #13c2c2;
      opacity: 1;
    }

    .ant-carousel .slick-dots li button {
      background: #515050;
    }

    .ant-carousel .slick-dots-bottom {
      bottom: -15px;
      margin: 0;
    }
  }
}

.announcement {
  // height: 175.3px;
  height: 200px;
  margin-bottom: 20px !important;
  :global {
    .ant-list-pagination {
      margin: 0;
    }
  }
}

.scheduleCard {
  height: 177.2px;
  margin-bottom: 20px !important;
}

.scheduleAvatar {
  vertical-align: 'middle';
  background-color: #36cfc9;
}

.mouse {
  cursor: pointer;
}

.listRow {
  :global {
    .ant-pro-list-row-header {
      width: 40%;
    }
    .ant-pro-list-row-content {
      width: 50%;
    }
  }
}
