import { commonlyFunctions } from '@/services/oa/home';
import {
  <PERSON><PERSON>ard,
  DrawerForm,
  DrawerFormProps,
  ProForm,
  ProFormInstance,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Avatar, message } from 'antd';
import { useRef } from 'react';
import { abilityList } from './abilityList';
export const avatars = (name?: string) => {
  return (
    <Avatar style={{ backgroundColor: '#36cfc9', verticalAlign: 'middle' }} size="large">
      {name}
    </Avatar>
  );
};

const AbilityDrawerForm: React.FC<DrawerFormProps> = ({
  initialValues,
  open,
  onOpenChange,
  onFinish,
}) => {
  const formRef = useRef<ProFormInstance>();
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const pagePermissions = currentUser?.pagePermissions;

  return (
    <DrawerForm
      width="754"
      title="常用功能"
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      initialValues={initialValues}
      onFinish={async (value) => {
        const commonly = Object.entries(value)
          .map((item) => item[1])
          .flat() as string[];
        const msg = await commonlyFunctions({ commonlyFunctions: commonly });
        const success = msg.code === 200;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
      drawerProps={{
        destroyOnClose: true,
      }}
    >
      {Object.entries(abilityList).map(([key, value]) => {
        const option = value
          .filter(
            (item) =>
              !['LEAVE-APPLICATION-VIEW', 'EXPENSE-REIMBURSEMENT-LIST-VIEW'].includes(item?.value),
          )
          .filter((item) => pagePermissions?.includes(item?.value))
          .map((item) => ({ ...item, avatar: avatars(item?.abbreviation) }));
        return (
          <ProForm.Item name={key} key={key} label={key}>
            <CheckCard.Group multiple size="small" options={option} />
          </ProForm.Item>
        );
      })}
    </DrawerForm>
  );
};

export default AbilityDrawerForm;
