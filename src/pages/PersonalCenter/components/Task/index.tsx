import { ProCard, StatisticCard } from '@ant-design/pro-components';
import { history, useModel } from '@umijs/max';
import { Typography } from 'antd';
import styles from '../../index.less';

/**
 * 任务管理入口
 *
 */
const { Divider } = StatisticCard;
const { Title } = Typography;
const level = 5;
const Task: React.FC<{
  value?: API.TaskCountResp;
  onChange?: (val: any) => void;
}> = ({ value = {} }) => {
  const {
    not_started,
    in_progress,
    terminated,
    submitted,
    completed_on_time,
    completed_overdue,
    i_initiated,
  } = value || {};
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  return (
    <ProCard
      className={styles.card}
      title={<Title level={level}>我的任务</Title>}
      layout="center"
      bordered
      wrap
      direction="column"
    >
      <ProCard>
        <StatisticCard.Group className="rklink-statistic">
          <StatisticCard
            className={styles.mouse}
            statistic={{
              title: '我发起',
              value: i_initiated || 0,
            }}
            onClick={() => {
              history.push(`/task/list?current=1&pageSize=10&launchUserId=${currentUser?.id}`);
            }}
          />
          <Divider type="vertical" />
          <StatisticCard
            className={styles.mouse}
            statistic={{
              title: '未开始',
              value: not_started || 0,
              status: 'default',
            }}
            onClick={() =>
              history.push(
                `/task/list?current=1&pageSize=10&taskStatus=NOT_STARTED&executeUserId=${currentUser?.id}`,
              )
            }
          />
          <StatisticCard
            className={styles.mouse}
            statistic={{
              title: '执行中',
              value: in_progress || 0,
              status: 'processing',
            }}
            onClick={() =>
              history.push(
                `/task/list?current=1&pageSize=10&taskStatus=IN_PROGRESS&executeUserId=${currentUser?.id}`,
              )
            }
          />
          <StatisticCard
            className={styles.mouse}
            statistic={{
              title: '已终止',
              value: terminated || 0,
              status: 'default',
            }}
            onClick={() =>
              history.push(
                `/task/list?current=1&pageSize=10&taskStatus=TERMINATED&executeUserId=${currentUser?.id}`,
              )
            }
          />
          <StatisticCard
            className={styles.mouse}
            statistic={{
              title: '已提交',
              value: submitted || 0,
              status: 'warning',
            }}
            onClick={() =>
              history.push(
                `/task/list?current=1&pageSize=10&taskStatus=SUBMITTED&executeUserId=${currentUser?.id}`,
              )
            }
          />
          <StatisticCard
            className={styles.mouse}
            statistic={{
              title: '按期完成',
              value: completed_on_time || 0,
              status: 'success',
            }}
            onClick={() =>
              history.push(
                `/task/list?current=1&pageSize=10&taskStatus=COMPLETED_ON_TIME&executeUserId=${currentUser?.id}`,
              )
            }
          />
          <StatisticCard
            className={styles.mouse}
            statistic={{
              title: '逾期完成',
              value: completed_overdue || 0,
              status: 'error',
            }}
            onClick={() =>
              history.push(
                `/task/list?current=1&pageSize=10&taskStatus=COMPLETED_OVERDUE&executeUserId=${currentUser?.id}`,
              )
            }
          />
        </StatisticCard.Group>
      </ProCard>
    </ProCard>
  );
};

export default Task;
