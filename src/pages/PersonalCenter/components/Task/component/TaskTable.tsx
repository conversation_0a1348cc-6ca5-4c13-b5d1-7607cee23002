import { TASK_STATUS } from '@/enums';
import { useUserList } from '@/hooks/useUserList';
import { getDepartmentWeeklyTree } from '@/services/oa/department';
import { taskDashboard } from '@/services/oa/task';
import { defaultTableConfig } from '@/utils/setting';
import { PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useLocation, useRequest } from '@umijs/max';
import { Progress } from 'antd';
import { useState } from 'react';

const TaskTable = () => {
  // 解析URL查询参数
  const { search } = useLocation();
  const queryParams = new URLSearchParams(search);
  const currentKey = queryParams.get('taskStatus') || 'NOT_STARTED';
  const [activeKey, setActiveKey] = useState<string>(currentKey);
  const { data: departmentTreeList = [], loading: departmentTreeLoading } = useRequest(
    () => getDepartmentWeeklyTree(),
    {
      formatResult: (res) =>
        res?.data?.map((i) => ({
          children: i.child?.map((chi) => ({
            value: chi.id,
            label: chi.departmentName,
          })),
          value: i.id,
          label: i.departmentName,
        })) || [],
    },
  );
  const { userList, loading } = useUserList();

  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '任务名称',
      dataIndex: 'taskName',
      width: 180,
      copyable: true,
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => {
              history.push(`/task/details/${entity?.id}`);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '执行人',
      dataIndex: 'executeUserId',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        options: userList,
        loading,
        fieldNames: {
          label: 'username',
          value: 'id',
        },
        filterOption: true,
        optionFilterProp: 'label',
      },
    },
    {
      title: '发起人',
      dataIndex: 'launchUserName',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '执行人',
      dataIndex: 'executeUserName',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '执行部门',
      dataIndex: 'executeDepartmentId',
      width: 120,
      valueType: 'treeSelect',
      fieldProps: {
        loading: departmentTreeLoading,
        options: departmentTreeList,
      },
    },
    {
      title: '完成进度',
      dataIndex: 'progress',
      render(dom, entity) {
        return (
          <Progress
            percent={entity.progress || 0}
            strokeColor="#36cfc9"
            status="active"
            style={{ paddingRight: 20 }}
          />
        );
      },
      width: 300,
      hideInSearch: true,
    },
    {
      title: '开始日期',
      dataIndex: 'startTime',
      valueType: 'date',
      width: 150,
      hideInSearch: true,
      hideInTable: activeKey === 'NOT_STARTED',
    },
    {
      title: '计划完成日期',
      dataIndex: 'planEndTime',
      valueType: 'date',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '结束日期',
      dataIndex: 'endTime',
      valueType: 'date',
      width: 150,
      hideInSearch: true,
      hideInTable: !['SUBMITTED', 'COMPLETED_ON_TIME', 'COMPLETED_OVERDUE'].includes(activeKey),
    },
    {
      title: '关闭日期',
      dataIndex: 'closeTime',
      valueType: 'date',
      width: 150,
      hideInSearch: true,
      hideInTable: !['COMPLETED_ON_TIME', 'COMPLETED_OVERDUE'].includes(activeKey),
    },
  ];

  return (
    <PageContainer header={{ title: false }}>
      <ProTable<Record<string, any>>
        {...defaultTableConfig}
        rowKey="id"
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        columns={columns}
        params={{
          taskStatus: activeKey,
        }}
        form={{
          syncToUrl: true,
          syncToInitialValues: false,
          extraUrlParams: {
            taskStatus: activeKey,
          },
        }}
        toolbar={{
          menu: {
            type: 'tab',
            activeKey: activeKey,
            items: TASK_STATUS?.map((i) => ({ ...i, key: i.value })),
            onChange: (key) => setActiveKey(key as string),
          },
        }}
        request={async (params) => {
          const { taskName, executeUserId, executeDepartmentId, current, pageSize, taskStatus } =
            params;
          const search = { taskName };
          const filter = { executeDepartmentId, taskStatus, executeUserId };
          const msg = await taskDashboard({ pageNum: current, pageSize: pageSize, search, filter });
          return {
            data: msg.data?.records || [],
            success: true,
            total: msg.data?.total || 0,
          };
        }}
      />
    </PageContainer>
  );
};
export default TaskTable;
