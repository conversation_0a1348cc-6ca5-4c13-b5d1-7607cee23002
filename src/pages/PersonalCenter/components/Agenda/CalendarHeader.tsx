import { But<PERSON>, Col, Row, Select } from 'antd';
import dayjs, { Dayjs } from 'dayjs';

import { InsertRowAboveOutlined } from '@ant-design/icons';
import React from 'react';

interface CalendarHeaderProps {
  value: Dayjs;
  onChange: (value: Dayjs) => void;
  selectedDate: () => void;
}

const CalendarHeader: React.FC<CalendarHeaderProps> = ({ value, onChange, selectedDate }) => {
  const generateOptions = (start: number, end: number, suffix: string) =>
    Array.from({ length: end - start + 1 }, (_, i) => ({
      label: `${i + start}${suffix}`,
      value: i + start,
    }));

  const yearOptions = generateOptions(value.year() - 10, value.year() + 10, '年');
  const monthOptions = generateOptions(1, 12, '月');

  return (
    <Row justify="space-between" gutter={8} style={{ padding: 8 }}>
      <Col>
        <Select
          size="small"
          value={value.year()}
          options={yearOptions}
          onChange={(newYear) => onChange(value.clone().year(newYear))}
          style={{ marginRight: '5px' }}
        />
        <Select
          size="small"
          value={value.month() + 1} // month is 0-indexed
          options={monthOptions}
          onChange={(newMonth) => onChange(value.clone().month(newMonth - 1))}
          style={{ marginRight: '15px' }}
        />
        <Button size="small" type="primary" onClick={selectedDate}>
          <InsertRowAboveOutlined />
          新建日程
        </Button>
      </Col>
      <Col>
        <Button
          size="small"
          onClick={() => onChange(dayjs())} // Reset to current date
        >
          今
        </Button>
      </Col>
    </Row>
  );
};

export default CalendarHeader;
