import Avatar from '@/components/Avatar';
import { SCHEDULE_REMINDS } from '@/enums';
import { useUserList } from '@/hooks/useUserList';
import {
  createUserSchedule,
  deleteUserSchedule,
  operationsSchedule,
  pageUserSchedule,
  updateUserSchedule,
} from '@/services/oa/userSchedule';
import { requiredRule } from '@/utils/setting';
import { useRequest } from '@@/exports';
import {
  CalendarOutlined,
  CheckCircleOutlined,
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  QuestionCircleOutlined,
  StopOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import {
  ActionType,
  ModalForm,
  ProCard,
  ProFormCheckbox,
  ProFormDateTimePicker,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import {
  Button,
  Calendar,
  CalendarProps,
  Divider,
  Dropdown,
  MenuProps,
  message,
  Modal,
  Popover,
  SelectProps,
  Space,
  Tag,
  Typography,
} from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useRef, useState } from 'react';

import { useEmotionCss } from '@ant-design/use-emotion-css';
import { useModel } from '@umijs/max';
import AllScheduleDrawer from '../AllScheduleDrawer';
import CalendarHeader from './CalendarHeader';
import './index.less';

const { Title } = Typography;
type TagRender = SelectProps['tagRender'];

const tagRender: TagRender = (props) => {
  const { label, closable, onClose } = props;
  const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };
  return (
    <Tag
      color={'#F4F5F5'}
      onMouseDown={onPreventMouseDown}
      closable={closable}
      closeIcon={<CloseOutlined style={{ color: 'black', fontSize: 11 }} />}
      onClose={onClose}
      style={{
        margin: 4,
        height: '40px',
        display: 'flex',
        alignItems: 'center',
        color: '#555',
      }}
    >
      <Space style={{ marginRight: '10px' }}>
        <Avatar name={(label as string) || '未知'} />
        <span>{label}</span>
      </Space>
    </Tag>
  );
};

function getChineseWeekday(dateString: string): string {
  const date = new Date(dateString);
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  return weekdays[date.getDay()];
}

function getRelativeDate(dateString: string): string {
  const inputDate = new Date(dateString);
  const today = new Date();

  // 计算日期差（单位：毫秒）
  const diffMs = Math.abs(inputDate.getTime() - today.getTime());
  const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24)); // 转换为天数

  if (diffDays === 0) {
    return '今天';
  } else if (diffDays === 1) {
    return inputDate < today ? '昨天' : '明天';
  } else {
    return `${diffDays}天${inputDate < today ? '前' : '后'}`;
  }
}

function getChineseWeekdayAndRelativeDate(dateString: string) {
  const weekday = getChineseWeekday(dateString);
  const relativeDate = getRelativeDate(dateString);
  return { weekday, relativeDate };
}

const Agenda: React.FC = () => {
  const actionRef = useRef<ActionType | undefined>();
  const [isEdit, setIsEdit] = useState(false);
  const [formVisible, setFormVisible] = useState(false);
  // 控制当前显示的popover，存储当前打开的日程id
  const [currentOpenPop, setCurrentOpenPop] = useState('');
  const currentDateRef = useRef('');
  const formRef = useRef<ProFormInstance>();
  // 获取用户基本信息
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  const cardLine = useEmotionCss(() => {
    return {
      display: 'flex',
      alignItems: 'baseline',
    };
  });
  const cardTag = useEmotionCss(() => {
    return {
      margin: 4,
      height: '40px',
      display: 'flex',
      alignItems: 'center',
    };
  });
  const cardPersons = useEmotionCss(() => {
    return {
      display: 'flex',
      alignItems: 'center',
      flexWrap: 'wrap',
      maxHeight: 200,
      overflow: 'auto',
    };
  });
  const cardButtons = useEmotionCss(() => {
    return {
      height: 60,
      background: '#F6F5F7',
      margin: '10px -12px -12px -12px',
      padding: '0 5px',
      display: 'flex',
      justifyContent: 'flex-start',
      alignItems: 'center',
    };
  });

  const items: MenuProps['items'] = [
    {
      label: '新建日程',
      key: '1',
      onClick: () => {
        setIsEdit(false);
        setTimeout(() => {
          setFormVisible(true);
        }, 0);
      },
    },
  ];

  const { userList = [], loading: userLoading } = useUserList(true);

  const { run: getSchedule, data: scheduleList } = useRequest(
    () =>
      pageUserSchedule({
        pageNum: 1,
        pageSize: 9999,
      }),
    {
      manual: true,
      formatResult: (res) => res?.data?.records || [],
    },
  );

  const { run: getCount, data: count } = useRequest(
    () =>
      pageUserSchedule({
        extra: { status: 'WAIT_CONFIRMED' },
        pageNum: 1,
        pageSize: 1,
      }),
    {
      manual: true,
      formatResult: (res) => res?.data?.total || 0,
    },
  );

  const { run: acceptSchedule, loading: acceptScheduleLoading } = useRequest(
    (id) =>
      operationsSchedule({
        id,
        operation: true,
      }),
    {
      manual: true,
      onSuccess: () => {
        message.success('日程已接受');
        getSchedule();
        getCount();
      },
    },
  );

  const { run: rejectSchedule, loading: rejectScheduleLoading } = useRequest(
    (id) =>
      operationsSchedule({
        id,
        operation: false,
      }),
    {
      manual: true,
      onSuccess: () => {
        message.success('日程已拒绝');
        setCurrentOpenPop('');
        getSchedule();
        getCount();
      },
    },
  );

  const cardContent = (item: API.UserScheduleResp) => {
    const createUser = userList.find((i) => i.id === item.userId);
    const partUsers = userList.filter((i) => item.scheduleUserIds?.includes(i.id!));
    const refuseUsers = userList.filter((i) => item.refuseUserIds?.includes(i.id!));
    const remindListString = SCHEDULE_REMINDS.filter((i) => item.remindTime?.includes(i.value))
      ?.map((i) => i.label)
      ?.join('，');
    //当前是未确认状态
    const isUnConfirmed =
      (item?.scheduleUserIds?.includes(currentUser?.id as string) ||
        (item?.whetherFull && item?.whetherFull?.length > 0)) &&
      !item?.confirmUserIds?.includes(currentUser?.id as string) &&
      !item?.refuseUserIds?.includes(currentUser?.id as string);
    const { weekday, relativeDate } = getChineseWeekdayAndRelativeDate(item.startTime!);
    //当前是全选状态
    const isAllSelected = item.whetherFull && item.whetherFull?.length > 0;
    return (
      <div
        style={{
          width: 430,
        }}
      >
        <div style={{ fontSize: '14px' }}>
          {item.startTime}（{weekday} {relativeDate}）
        </div>
        <Divider />
        <div className={cardLine}>
          创建人：
          <Tag color={'#F4F5F5'} className={cardTag}>
            <Space style={{ marginRight: '10px' }}>
              <Avatar name={(createUser?.username as string) || '未知'} />
              <span style={{ color: '#555' }}>{createUser?.username || '未知'}</span>
            </Space>
          </Tag>
        </div>
        <div className={cardLine}>
          <div style={{ whiteSpace: 'nowrap' }}>参与人：</div>
          <div className={cardPersons}>
            {isAllSelected ? (
              <Tag color={'#F4F5F5'} className={cardTag}>
                <Space style={{ marginRight: '10px' }}>
                  <Avatar name="全员" />
                  <span style={{ color: '#555' }}>{'全员'}</span>
                </Space>
              </Tag>
            ) : (
              partUsers.map((i) => {
                return (
                  <Tag key={i.id} color={'#F4F5F5'} className={cardTag}>
                    <Space style={{ marginRight: '10px' }}>
                      <Avatar name={(i.username as string) || '未知'} />
                      <span style={{ color: '#555' }}>{i.username || '未知'}</span>
                    </Space>
                  </Tag>
                );
              })
            )}
          </div>
        </div>
        <div className={cardLine}>
          <div style={{ whiteSpace: 'nowrap' }}>已拒绝：</div>
          <div className={cardPersons}>
            {refuseUsers.map((i) => {
              return (
                <Tag key={i.id} color={'#F4F5F5'} className={cardTag}>
                  <Space style={{ marginRight: '10px' }}>
                    <Avatar name={(i.username as string) || '未知'} />
                    <span style={{ color: '#555' }}>{i.username || '未知'}</span>
                  </Space>
                </Tag>
              );
            })}
          </div>
        </div>
        <Divider />
        <div className={cardLine}>
          <div style={{ marginRight: 15 }}>提醒：</div>
          {remindListString || '没有提醒'}
        </div>
        <div className={cardButtons}>
          {currentUser?.id === createUser?.id && (
            <>
              <Button
                icon={<EditOutlined />}
                type="text"
                onClick={(event) => {
                  event.stopPropagation();
                  setCurrentOpenPop('');
                  setIsEdit(true);
                  setFormVisible(true);
                  setTimeout(() => {
                    formRef.current?.setFieldsValue({ ...item });
                  }, 100);
                }}
              >
                编辑
              </Button>
              <Button
                icon={<DeleteOutlined />}
                type="text"
                danger
                onClick={(event) => {
                  event.stopPropagation();
                  Modal.confirm({
                    title: '确认删除',
                    content: `您确定要删除此日程吗?`,
                    okText: '确认',
                    cancelText: '取消',
                    onOk: async () => {
                      const msg = await deleteUserSchedule({
                        ids: [item.id as string],
                      });
                      if (msg.code === 200) {
                        message.success('删除成功!');
                        setCurrentOpenPop('');
                        getSchedule();
                        getCount();
                      }
                    },
                  });
                }}
              >
                删除
              </Button>
            </>
          )}
          {isUnConfirmed && (
            <>
              <Button
                icon={<CheckCircleOutlined />}
                type="text"
                loading={acceptScheduleLoading || rejectScheduleLoading}
                onClick={async (event) => {
                  event.stopPropagation();
                  acceptSchedule(item.id);
                }}
              >
                接受
              </Button>
              <Button
                icon={<StopOutlined />}
                type="text"
                loading={acceptScheduleLoading || rejectScheduleLoading}
                onClick={async (event) => {
                  event.stopPropagation();
                  rejectSchedule(item.id);
                }}
              >
                拒绝
              </Button>
            </>
          )}
          {item?.confirmUserIds?.includes(currentUser?.id as string) && (
            <Button
              icon={<CheckCircleOutlined />}
              type="text"
              disabled
              style={{ background: '#DAF4E2', color: '#1EBC42' }}
            >
              已接受
            </Button>
          )}
        </div>
      </div>
    );
  };

  const getListData = (value: Dayjs) => {
    let listData = [];
    const formatedDate = value.format('YYYY-MM-DD');

    if (scheduleList?.length) {
      for (const schedule of scheduleList!) {
        const formatedStartTime = dayjs(schedule.startTime)?.format('YYYY-MM-DD');
        //当前是未确认状态
        const isUnConfirmed =
          (schedule?.scheduleUserIds?.includes(currentUser?.id as string) ||
            schedule?.whetherFull === 'SELECT_ALL') &&
          !schedule?.confirmUserIds?.includes(currentUser?.id as string) &&
          !schedule?.refuseUserIds?.includes(currentUser?.id as string);

        if (formatedStartTime === formatedDate) {
          listData.push({
            ...schedule,
            remindTime: schedule.remindTime || [],
            whetherFull: schedule.whetherFull === 'SELECT_ALL' ? ['全选'] : [],
            type: isUnConfirmed ? 'done' : schedule.scheduleType,
            content: schedule.topic,
          });
        }
      }
    }
    return listData;
  };

  const dateCellRender = (value: Dayjs) => {
    //获取详细的日期下的日程列表
    const listData = getListData(value);
    return (
      <Dropdown menu={{ items }} trigger={['contextMenu']}>
        <ul
          className="events"
          onClick={() => {
            setCurrentOpenPop('');
          }}
        >
          {listData.map((item: any, index) => (
            <Popover
              content={() => cardContent(item)}
              title={<div style={{ fontSize: '18px' }}>{item.topic}</div>}
              trigger="click"
              key={index}
              placement="left"
              open={currentOpenPop === item.id}
              onOpenChange={(newOpen) => {
                if (newOpen === false) {
                  setCurrentOpenPop('');
                }
              }}
            >
              <li
                onClick={(event) => {
                  event.stopPropagation();
                  setCurrentOpenPop(item.id);
                }}
                className={`events-li ${
                  item.type === 'SCHEDULE' ? 'schedule' : item.type === 'MEET' ? 'meeting' : 'done'
                }`}
              >
                {item.type === 'SCHEDULE' ? (
                  <CalendarOutlined />
                ) : item.type === 'MEET' ? (
                  <TeamOutlined />
                ) : (
                  <QuestionCircleOutlined />
                )}
                &nbsp;
                {item.content}
              </li>
            </Popover>
          ))}
        </ul>
      </Dropdown>
    );
  };

  const cellRender: CalendarProps<Dayjs>['cellRender'] = (current, info) => {
    if (info.type === 'date') return dateCellRender(current);
    return info.originNode;
  };

  useEffect(() => {
    getSchedule();
    getCount();
  }, []);

  return (
    <ProCard
      className={'calendar'}
      wrap
      bordered
      title={<Title level={5}>我的日程</Title>}
      extra={
        <AllScheduleDrawer
          myUserId={currentUser?.id as string}
          proListActionRef={actionRef}
          refreshCalendar={() => {
            getSchedule();
            getCount();
          }}
          closePop={() => {
            setCurrentOpenPop('');
          }}
          count={count as number}
        />
      }
    >
      <Calendar
        cellRender={cellRender}
        headerRender={({ value, onChange }) =>
          CalendarHeader({
            value,
            onChange,
            selectedDate: () => {
              currentDateRef.current = '';
              setIsEdit(false);
              setFormVisible(true);
            },
          })
        }
        onSelect={(date) => {
          currentDateRef.current = date.format('YYYY-MM-DD HH:mm:ss');
        }}
      />
      <ModalForm
        title={isEdit ? '编辑日程' : '新建日程'}
        formRef={formRef}
        open={formVisible}
        onOpenChange={(open) => setFormVisible(open)}
        autoFocusFirstInput
        modalProps={{
          width: 660,
          destroyOnClose: true,
        }}
        submitTimeout={2000}
        onFinish={async (values) => {
          const form = {
            ...values,
            scheduleType: 'SCHEDULE',
            whetherFull: values.whetherFull?.length > 0 ? 'SELECT_ALL' : 'NOT_SELECT_ALL',
          };
          const msg = isEdit ? await updateUserSchedule(form) : await createUserSchedule(form);
          const success = msg.code === 200;
          if (success) {
            message.success('保存成功!');
            getSchedule();
            getCount();
          }
          return success;
        }}
      >
        <div style={{ height: '60vh', overflow: 'auto' }}>
          <div className="rk-none">
            <ProFormText name="id" />
          </div>
          <ProFormText name="topic" label="日程主题" placeholder="请输入" rules={[requiredRule]} />
          <div className="participant">
            <ProFormDependency name={['whetherFull']}>
              {({ whetherFull }) => {
                return whetherFull?.length > 0 ? (
                  <ProFormText
                    fieldProps={{ className: 'participant-input' }}
                    disabled
                    placeholder="全员"
                    label="参与人"
                  />
                ) : (
                  <ProFormSelect
                    name="scheduleUserIds"
                    label="参与人"
                    mode="multiple"
                    fieldProps={{
                      className: 'participant-selected',
                      tagRender: tagRender,
                      showSearch: true,
                      loading: userLoading,
                      disabled: whetherFull?.length > 0,
                      fieldNames: {
                        value: 'id',
                        label: 'username',
                      },
                      onChange: (e) => {
                        if (e?.length) {
                          formRef.current?.setFieldValue('whetherFull', []);
                        }
                      },
                    }}
                    options={userList as DefaultOptionType[]}
                    transform={(value, namePath) => {
                      return {
                        [namePath]: value,
                      };
                    }}
                  />
                );
              }}
            </ProFormDependency>
            <ProFormCheckbox.Group
              name="whetherFull"
              options={['全选']}
              fieldProps={{
                onChange: (e) => {
                  if (e?.length) {
                    formRef.current?.setFieldValue('scheduleUserIds', []);
                  }
                },
              }}
            />
          </div>
          <ProFormDateTimePicker
            name="startTime"
            label="开始时间"
            rules={[requiredRule]}
            initialValue={currentDateRef.current || dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')}
          />
          <ProFormDateTimePicker name="endTime" label="结束时间" />
          <ProFormTextArea
            name="description"
            label="描述"
            fieldProps={{
              autoSize: {
                minRows: 1,
                maxRows: 3,
              },
            }}
          />
          <ProFormSelect
            name="remindTime"
            label="提醒"
            options={SCHEDULE_REMINDS}
            mode="multiple"
          />
        </div>
      </ModalForm>
    </ProCard>
  );
};

export default Agenda;
