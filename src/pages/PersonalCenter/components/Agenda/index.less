.calendar {
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background-color: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 15px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #888;
    border: 4px solid rgba(0, 0, 0, 0);
    border-radius: 5px;
    box-shadow: none;
  }

  .ant-picker-calendar-date-content {
    overflow: hidden;
  }

  .ant-checkbox-group {
    position: relative !important;
    top: 30px !important;
  }

  .events {
    height: 95%;
    margin: 0;
    padding: 0 5px 0 0;
    overflow: auto;

    .schedule {
      color: #52c31a;
      background: #f6feed;
      border: 1px solid #b7ea8e;
    }

    .meeting {
      color: #fa4c4e;
      background: #fef2f0;
      border: 1px solid #fccbc6;
    }

    .done {
      color: #202020;
      background: #f9f9f9;
      border: 1px solid #d9d9d9;
    }

    &-li {
      margin-bottom: 8px;
      padding-left: 10px;
      overflow: hidden;
      color: #63666b;
      white-space: nowrap;
      text-overflow: ellipsis;
      border-radius: 5px;
    }
  }
}

.participant {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.participant-input {
  width: 530px;
}

.participant-selected {
  .ant-select-selection-overflow {
    width: 500px;
    max-height: 200px;
    overflow: auto;
  }
}
