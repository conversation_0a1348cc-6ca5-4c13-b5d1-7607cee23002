import {
  createUserSchedule,
  deleteUserSchedule,
  updateUserSchedule,
} from '@/services/oa/userSchedule';
import { ActionType } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';

interface UseScheduleActionsProps {
  actionRef: React.MutableRefObject<ActionType | undefined>;
}

export const useScheduleActions = ({ actionRef }: UseScheduleActionsProps) => {
  const { run: handleSave } = useRequest(createUserSchedule, {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      actionRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const { run: handleUpdate } = useRequest(updateUserSchedule, {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      actionRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const { run: handleDelete } = useRequest(deleteUserSchedule, {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      actionRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  return { handleSave, handleUpdate, handleDelete };
};
