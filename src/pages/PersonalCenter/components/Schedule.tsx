import { agent } from '@/services/oa/home';
import { ProCard } from '@ant-design/pro-components';
import { history, useModel, useRequest } from '@umijs/max';
import { Avatar, Col, Empty, Row, Typography } from 'antd';
import styles from '../index.less';

const colSpan = { xs: 12, sm: 12, md: 12, lg: 8, xl: 8 };
const { Text, Title } = Typography;
const level = 5;

const Schedule: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const pagePermissions = currentUser?.pagePermissions;

  const { data = {} } = useRequest(() => agent());
  const { weekCount, disposeCount, approvalCount } = data || {};
  const weekPermissions = pagePermissions?.includes('WEEKLY-REPORT-VIEW') === true;
  const approvalPermissions = pagePermissions?.includes('APPROVAL') === true;

  return (
    <>
      <ProCard
        wrap
        className={styles.scheduleCard}
        layout="center"
        bordered
        title={<Title level={level}>我的待办</Title>}
      >
        {weekPermissions || disposeCount || approvalPermissions ? (
          <>
            {weekPermissions && (
              <ProCard
                colSpan={colSpan}
                onClick={() => {
                  history.push('/weekly-report/list');
                }}
                className={styles.mouse}
                bodyStyle={{ padding: 0 }}
              >
                <Row gutter={16}>
                  <Col>
                    <Avatar className={styles.scheduleAvatar} size="large">
                      周
                    </Avatar>
                  </Col>
                  <Col className={styles.verticalCenter}>
                    <Title level={level}>{weekCount}</Title>
                    <Text type="secondary">周报</Text>
                  </Col>
                </Row>
              </ProCard>
            )}
            {approvalPermissions && (
              <ProCard
                colSpan={colSpan}
                onClick={() => {
                  history.push('/approval/pending-approval');
                }}
                className={styles.mouse}
                bodyStyle={{ padding: 0 }}
              >
                <Row gutter={16}>
                  <Col>
                    <Avatar className={styles.scheduleAvatar} size="large">
                      审
                    </Avatar>
                  </Col>
                  <Col className={styles.verticalCenter}>
                    <Title level={5}>{approvalCount}</Title>
                    <Text type="secondary">待审批</Text>
                  </Col>
                </Row>
              </ProCard>
            )}
            <ProCard
              colSpan={colSpan}
              onClick={() => {
                history.push(
                  '/personal-center/pending-dispose?pendingDisposeType=PENDING_CONFIRMED_RETURN',
                );
              }}
              className={styles.mouse}
              bodyStyle={{ padding: 0 }}
            >
              <Row gutter={16}>
                <Col>
                  <Avatar className={styles.scheduleAvatar} size="large">
                    处
                  </Avatar>
                </Col>
                <Col className={styles.verticalCenter}>
                  <Title level={level}>{disposeCount}</Title>
                  <Text type="secondary">待处理</Text>
                </Col>
              </Row>
            </ProCard>
          </>
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
      </ProCard>
    </>
  );
};

export default Schedule;
