import { billingInformationData, billingInformationEdit } from '@/services/oa/dictionary';
import { ProCard, ProDescriptions } from '@ant-design/pro-components';
import { useAccess, useRequest } from '@umijs/max';
import { message, Typography } from 'antd';
import { Key, useState } from 'react';

const BillingInformation: React.FC = () => {
  const [editableKeys, setEditableKeys] = useState<Key[]>([]);
  const [billingInfo, setBillingInfo] = useState<API.BillingInformationDTO>({});
  const { canEditBillingInformation = false } = useAccess();

  // 获取开票信息数据
  const { loading, refresh } = useRequest(() => billingInformationData(), {
    onSuccess: (res) => {
      if (res && res.code === 200 && res.data) {
        setBillingInfo(res.data);
      }
    },
    formatResult: (res) => res,
  });

  // 编辑开票信息
  const { run: updateBillingInfo } = useRequest(
    (values: API.BillingInformationDTO) => billingInformationEdit(values),
    {
      manual: true,
      onSuccess: (res) => {
        if (res && res.code === 200) {
          message.success('更新成功');
          refresh();
        } else {
          message.error(res?.message || '更新失败');
        }
      },
      formatResult: (res) => res,
    },
  );

  return (
    <>
      <ProCard
        title={<Typography.Title level={5}>开票信息</Typography.Title>}
        bordered
        style={{ marginBottom: 24 }}
        loading={loading}
      >
        <ProDescriptions<API.BillingInformationDTO>
          column={2}
          title={false}
          dataSource={billingInfo}
          editable={{
            type: 'multiple',
            editableKeys,
            onChange: (keys) => setEditableKeys(keys),
            actionRender: (_, __, defaultDoms) => [defaultDoms.save, defaultDoms.cancel],
            onSave: async (key, record) => {
              // 只更新当前编辑的字段
              const fieldName = key as string;
              const fieldValue = (record as any)[fieldName];

              const result = await updateBillingInfo({
                [fieldName]: fieldValue,
              });

              // 如果提交成功，退出编辑状态
              if (result && result.code === 200) {
                setEditableKeys([]);
              }
            },
          }}
          columns={[
            {
              title: '更新日期',
              dataIndex: 'updateDate',
              editable: false,
              copyable: true,
            },
            {
              title: '公司邮寄地址',
              dataIndex: 'mailingAddress',
              copyable: true,
              editable: canEditBillingInformation ? () => true : false,
            },
            {
              title: '发票抬头',
              dataIndex: 'invoiceTitle',
              copyable: true,
              editable: false,
            },
            {
              title: '纳税人识别号',
              dataIndex: 'taxpayerId',
              copyable: true,
              editable: false,
            },
            {
              title: '注册地址',
              dataIndex: 'registeredAddress',
              copyable: true,
              editable: canEditBillingInformation ? () => true : false,
            },
            {
              title: '电话',
              dataIndex: 'phoneNumber',
              copyable: true,
              editable: canEditBillingInformation ? () => true : false,
            },
            {
              title: '开户名',
              dataIndex: 'accountName',
              copyable: true,
              editable: false,
            },
            {
              title: '开户行',
              dataIndex: 'bankName',
              copyable: true,
              editable: false,
            },
            {
              title: '账号',
              dataIndex: 'bankAccount',
              copyable: true,
              editable: false,
            },
          ]}
        />
      </ProCard>
    </>
  );
};

export default BillingInformation;
