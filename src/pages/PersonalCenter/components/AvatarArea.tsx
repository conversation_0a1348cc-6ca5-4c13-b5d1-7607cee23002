import { Avatar, Col, Row, Typography } from 'antd';
import { memo } from 'react';
import styles from '../index.less';

const { Text } = Typography;

const messages = [
  '愿你在新的一周里充满活力和动力 💪😃',
  '今天要加油哦，别忘了给自己一个微笑 😊',
  '愿你心情愉快，工作顺利！😃👌',
  '坚持就是胜利，加油！💪',
  '一天的努力，一定会有所收获的！💪🌱',
  '周末快到了，继续加油！🎉💪',
  '今天是个休息的好日子，放松一下吧！🌞☕️',
];

const AvatarArea: React.FC = () => {
  const dayOfWeek = new Date().getDay();
  const todayMessage = messages[dayOfWeek];

  return (
    <Row className={styles.verticalCenter}>
      <Col>
        <Avatar size={32} src="/images/user.svg" alt="avatar" />
      </Col>
      <Col style={{ marginTop: 10 }}>
        <Text>{todayMessage}</Text>
      </Col>
    </Row>
  );
};

export default memo(AvatarArea);
