import { announcementHomePage } from '@/services/oa/announcement';
import { ProCard } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Button, Empty, List, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from '../../index.less';
import AnnouncementModal from './AnnouncementModal';

const { Title, Text } = Typography;
const Announcement: React.FC = () => {
  const [modalVisit, setModalVisit] = useState(false);
  const [initialValues, setInitialValues] = useState<API.AnnouncementPageResp>({});
  const {
    run,
    loading,
    data = {},
  } = useRequest((val) => announcementHomePage(val), {
    manual: true,
  });
  const { records = [], total } = data;

  useEffect(() => {
    run({ pageNum: 1, pageSize: 3 });
  }, []);

  return (
    <ProCard
      // layout="center"
      wrap
      bodyStyle={{ padding: '0 25px 0 8px' }}
      className={styles.carousels}
      title={<Title level={5}>公告信息</Title>}
      extra={
        <Button
          type="link"
          onClick={() => {
            history.push('/human-resources/announcement');
          }}
        >
          更多
        </Button>
      }
    >
      <List<API.AnnouncementPageResp>
        rowKey="id"
        loading={loading}
        split={false}
        size="small"
        style={{ width: '100%' }}
        header={false}
        footer={false}
        dataSource={records}
        renderItem={(item) => (
          <List.Item key={item?.id}>
            <a
              onClick={() => {
                setModalVisit(true);
                setInitialValues(item);
              }}
              style={{ position: 'relative' }}
            >
              <span>{item?.title}</span>
              <img
                src="/images/new.png"
                width={28}
                style={{
                  display: item.hasNew ? 'inline' : 'none',
                  position: 'absolute',
                  bottom: -2.5,
                  right: -40,
                }}
              ></img>
            </a>
            <Text>{item?.releasedTime}</Text>
          </List.Item>
        )}
        locale={{
          emptyText: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无公告" />,
        }}
        pagination={{
          style: { position: 'relative', bottom: 20, right: 8 },
          size: 'small',
          hideOnSinglePage: true,
          defaultPageSize: 3,
          total: total || 0,
          onChange: (page, pageSize) => {
            run({ pageNum: page, pageSize });
          },
        }}
      />
      <AnnouncementModal
        open={modalVisit}
        onOpenChange={setModalVisit}
        initialValues={initialValues}
      />
    </ProCard>
  );
};

export default Announcement;
