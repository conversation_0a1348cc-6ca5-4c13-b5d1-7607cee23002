import {
  ModalForm,
  ModalFormProps,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import React, { useRef } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

const AnnouncementModal: React.FC<ModalFormProps> = ({ initialValues, open, onOpenChange }) => {
  const formRef = useRef<ProFormInstance>();

  return (
    <ModalForm
      width="40%"
      title={initialValues?.title}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      modalProps={{
        destroyOnClose: true,
        forceRender: true,
        centered: true,
        closable: false,
        bodyStyle: {
          maxHeight: 'calc(100vh - 200px)',
          overflow: 'auto',
        },
      }}
      submitter={false}
      initialValues={initialValues}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
      </div>
      <div style={{ height: 'auto' }}>
        <ReactQuill
          value={initialValues?.content}
          readOnly={true}
          theme="snow"
          modules={{
            toolbar: false,
          }}
          style={{
            height: 'auto',
          }}
        />
      </div>
    </ModalForm>
  );
};

export default AnnouncementModal;
