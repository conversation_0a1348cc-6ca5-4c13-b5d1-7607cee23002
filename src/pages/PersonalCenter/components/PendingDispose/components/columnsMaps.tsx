import { PAYMENT_TYPE } from '@/enums';
import { option2enum } from '@/utils';
import { history } from '@umijs/max';

//资源使用申请-待确认归还
const resourceApplicationColumns = [
  {
    title: '编号',
    dataIndex: 'documentNumber',
    with: 200,
    ellipsis: true,
    render: (text: string, record: Record<string, any>) => (
      <a
        className="rk-a-span"
        onClick={() =>
          history.push(
            `/human-resources/resource-application?current=1&pageSize=10&documentNumber=${record.documentNumber}`,
          )
        }
      >
        {text}
      </a>
    ),
  },
  {
    title: '申请人',
    dataIndex: 'employeeName',
    with: 150,
    ellipsis: true,
  },
  {
    title: '申请内容',
    dataIndex: 'content',
    with: 150,
    ellipsis: true,
  },
  {
    title: '预计归还时间',
    dataIndex: 'stillTime',
    valueType: 'dateTime',
    with: 150,
    ellipsis: true,
  },
  {
    title: '逾期原因',
    dataIndex: 'lateCause',
    with: 150,
    ellipsis: true,
  },
];
//主合同-合同待开票申请、待收款
const mainContractColumns = [
  {
    title: '合同编号',
    dataIndex: 'contractNumber',
    with: 150,
    ellipsis: true,
    render(text?: string, record?: Record<string, any>) {
      return (
        <a
          className="rk-a-span"
          onClick={() => history.push(`/contract/main/edit/${record?.contractId}`)}
        >
          {text}
        </a>
      );
    },
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    with: 200,
    ellipsis: true,
  },
  {
    title: '计划收款金额',
    dataIndex: 'estimateReAmount',
    valueType: 'money',
    with: 100,
    ellipsis: true,
  },
  {
    title: '计划收款日期',
    dataIndex: 'estimateReTime',
    valueType: 'date',
    with: 100,
    ellipsis: true,
  },
];
//内部、采购合同-合同待收票、待付款
//合同待付款 所有提出付款申请但未通过的合同
//合同待收票---所有未收票的数据，不单独筛选合同是否付款但未收票的情况，合同可能存在先收票再付款也可能先付款再收票
const internalAndPurchaseColumns = [
  {
    title: '合同编号',
    dataIndex: 'contractNumber',
    with: 150,
    ellipsis: true,
    render: (text: string, record: Record<string, any>) => {
      const contractType = record.contractType === 'IC' ? 'internal' : 'purchase';
      return (
        <a
          className="rk-a-span"
          onClick={() => history.push(`/contract/${contractType}/edit/${record.contractId}`)}
        >
          {text}
        </a>
      );
    },
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    with: 200,
    ellipsis: true,
  },
  {
    title: '合同类型',
    dataIndex: 'contractType',
    renderText: (text: string) => {
      return text === 'IC' ? '内部合同' : '采购合同';
    },
    with: 150,
    ellipsis: true,
  },
  {
    title: '计划付款金额',
    dataIndex: 'estimatePayAmount',
    valueType: 'money',
    with: 150,
    ellipsis: true,
  },
  {
    title: '计划付款日期',
    dataIndex: 'estimatePayTime',
    valueType: 'date',
    with: 150,
    ellipsis: true,
  },
];
//收款记录-待认领
const receiptColumns = [
  {
    title: '流水号',
    dataIndex: 'collectNumber',
    render(text?: string, record?: Record<string, any>) {
      return (
        <a
          className="rk-a-span"
          onClick={() => history.push(`/finance/JR/receipt-list/details/${record?.id}`)}
        >
          {text}
        </a>
      );
    },
    with: 200,
    ellipsis: true,
  },
  {
    title: '收款日期',
    dataIndex: 'receiveTime',
    valueType: 'date',
    with: 150,
    ellipsis: true,
  },
  {
    title: '收款金额',
    dataIndex: 'receiveAmount',
    valueType: 'money',
    with: 150,
    ellipsis: true,
  },
  {
    title: '对方单位(账户名)',
    dataIndex: 'institutionName',
    with: 200,
    ellipsis: true,
  },
];
//待付款记录-待付款
const outstandingPaymentColumns = [
  {
    title: '付款编号',
    dataIndex: 'payNumber',
    render(text: string, record: Record<string, any>) {
      return (
        <a
          className="rk-a-span"
          onClick={() => history.push(`/finance/JR/outstanding-payment/${record.id}`)}
        >
          {text}
        </a>
      );
    },
    with: 200,
    ellipsis: true,
  },
  {
    title: '付款类型',
    dataIndex: 'paymentType',
    valueEnum: option2enum(PAYMENT_TYPE),
    with: 150,
    ellipsis: true,
  },
  {
    title: '计划日期',
    dataIndex: 'estimatePayTime',
    valueType: 'date',
    with: 150,
    ellipsis: true,
  },
  {
    title: '付款金额',
    dataIndex: 'payAmount',
    valueType: 'money',
    with: 150,
    ellipsis: true,
  },
  {
    title: '单位名称(账户名)',
    dataIndex: 'institutionName',
    with: 200,
    ellipsis: true,
  },
];
//付款记录-待允许付款
const paymentColumns = [
  {
    title: '付款编号',
    dataIndex: 'payNumber',
    render(text: string, record: Record<string, any>) {
      return (
        <a
          className="rk-a-span"
          onClick={() =>
            history.push(
              `/finance/JR/payment-list?current=1&pageSize=10&payNumber=${record.payNumber}`,
            )
          }
        >
          {text}
        </a>
      );
    },
    with: 200,
    ellipsis: true,
  },
  {
    title: '付款类型',
    dataIndex: 'paymentType',
    valueEnum: option2enum(PAYMENT_TYPE),
    with: 150,
    ellipsis: true,
  },
  {
    title: '计划付款日期',
    dataIndex: 'estimatePayTime',
    valueType: 'date',
    with: 150,
    ellipsis: true,
  },
  {
    title: '付款金额',
    dataIndex: 'payAmount',
    valueType: 'money',
    with: 150,
    ellipsis: true,
  },
];
//销账-待销账
const receivableColumns = [
  {
    title: '预退款编号',
    dataIndex: 'payNumber',
    render(text: string, record: Record<string, any>) {
      return (
        <a
          className="rk-a-span"
          onClick={() =>
            history.push(`/finance/JR/refund?current=1&pageSize=10&payNumber=${record.payNumber}`)
          }
        >
          {text}
        </a>
      );
    },
    with: 200,
    ellipsis: true,
  },
  {
    title: '预退款金额',
    dataIndex: 'payMoney',
    valueType: 'money',
    with: 150,
    ellipsis: true,
  },
  {
    title: '单位名称(账户名)',
    dataIndex: 'institutionName',
    with: 200,
    ellipsis: true,
  },
  {
    title: '资金类别',
    dataIndex: 'fundingCategory',
    valueEnum: option2enum(PAYMENT_TYPE),
    with: 150,
    ellipsis: true,
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
    with: 150,
    ellipsis: true,
  },
  {
    title: '计划退款日期',
    dataIndex: 'expectedReturnTime',
    valueType: 'date',
    with: 150,
    ellipsis: true,
  },
  {
    title: '申请人',
    dataIndex: 'applicationUser',
    with: 150,
    ellipsis: true,
  },
];
//开票记录-待开票
const invoiceColumns = [
  {
    title: '开票记录号',
    dataIndex: 'invoicingNumber',
    render(text?: string, record?: Record<string, any>) {
      return (
        <a
          className="rk-a-span"
          onClick={() => history.push(`/finance/invoice/invoicing-list/${record?.id}`)}
        >
          {text}
        </a>
      );
    },
    with: 200,
    ellipsis: true,
  },
  {
    title: '开票金额',
    dataIndex: 'ticketAmount',
    valueType: 'money',
    with: 150,
    ellipsis: true,
  },
  {
    title: '单位名称',
    dataIndex: 'institutionName',
    with: 200,
    ellipsis: true,
  },
  {
    title: '开票申请日期',
    dataIndex: 'applicationTime',
    valueType: 'date',
    with: 150,
    ellipsis: true,
  },
];

export const columnsMaps: Record<string, any> = {
  PENDING_CONFIRMED_RETURN: resourceApplicationColumns,
  PENDING_CONTRACT_INVOICE: mainContractColumns,
  PENDING_CONTRACT_COLLECTION: mainContractColumns,
  PENDING_CONTRACT_PAYMENT: internalAndPurchaseColumns,
  PENDING_COLLECT_INVOICE: internalAndPurchaseColumns,
  PENDING_CLAIM: receiptColumns,
  PENDING_PAYMENT: outstandingPaymentColumns,
  PENDING_ALLOW_PAYMENT: paymentColumns,
  PENDING_WRITE_OFFS: receivableColumns,
  PENDING_INVOICE: invoiceColumns,
};

export const tabItems = [
  {
    tab: '待确认归还',
    path: '/human-resources/resource-application',
    key: 'PENDING_CONFIRMED_RETURN',
  },
  {
    tab: '合同待开票',
    path: '/contract/main',
    key: 'PENDING_CONTRACT_INVOICE',
  },
  {
    tab: '合同待收款',
    path: '/contract/main',
    key: 'PENDING_CONTRACT_COLLECTION',
  },
  {
    tab: '合同待付款',
    path: '/contract',
    key: 'PENDING_CONTRACT_PAYMENT',
  },
  {
    tab: '合同待收票',
    path: '/contract',
    key: 'PENDING_COLLECT_INVOICE',
  },
  {
    tab: '待认领',
    path: '/finance/JR/receipt-list',
    key: 'PENDING_CLAIM',
  },
  {
    tab: '待付款',
    path: '/finance/JR/outstanding-payment',
    key: 'PENDING_PAYMENT',
  },
  {
    tab: '待允许付款',
    path: '/finance/JR/payment-list',
    key: 'PENDING_ALLOW_PAYMENT',
  },
  {
    tab: '待销账',
    path: '/finance/JR/refund',
    key: 'PENDING_WRITE_OFFS',
  },
  {
    tab: '财务待开票',
    path: '/finance/invoice/invoicing-list',
    key: 'PENDING_INVOICE',
  },
];
