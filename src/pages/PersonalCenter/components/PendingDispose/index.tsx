import { pendingDispose, pendingTips } from '@/services/oa/home';
import { defaultTableConfig } from '@/utils/setting';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { history, useLocation, useRequest } from '@umijs/max';
import { Badge } from 'antd';
import { useEffect, useState } from 'react';
import { columnsMaps, tabItems } from './components/columnsMaps';

const PendingDispose: React.FC = () => {
  const { search, pathname } = useLocation();
  const queryParams = new URLSearchParams(search);
  const currentKey = queryParams.get('pendingDisposeType') || 'PENDING_CONFIRMED_RETURN';
  const [activeKey, setActiveKey] = useState(currentKey);

  const { data: awaitDisposeList = [] } = useRequest(() => pendingTips());

  useEffect(() => {
    history.push(pathname + '?pendingDisposeType=' + activeKey);
  }, [activeKey]);

  return (
    <PageContainer header={{ title: false }}>
      <ProTable
        {...defaultTableConfig}
        rowKey="id"
        search={false}
        columns={columnsMaps[activeKey]}
        headerTitle="待处理事项"
        params={{
          pendingDisposeType: activeKey,
        }}
        form={{
          syncToUrl: true,
          syncToInitialValues: false,
          extraUrlParams: {
            pendingDisposeType: activeKey,
          },
        }}
        toolbar={{
          multipleLine: true,
          tabs: {
            activeKey,
            onChange: (key) => setActiveKey(key as string),
            items: tabItems.map((item) => ({
              ...item,
              tab: awaitDisposeList?.includes(item.key) ? (
                <Badge size="small" dot>
                  {item.tab}
                </Badge>
              ) : (
                <>{item.tab}</>
              ),
            })),
          },
        }}
        request={async (params) => {
          const { pageSize, current, pendingDisposeType } = params;
          const { data } = await pendingDispose({
            pageNum: current,
            pageSize,
            filter: { pendingDisposeType },
          });
          return {
            data: data?.records || [],
            success: true,
            total: data?.total || 0,
          };
        }}
      />
    </PageContainer>
  );
};
export default PendingDispose;
