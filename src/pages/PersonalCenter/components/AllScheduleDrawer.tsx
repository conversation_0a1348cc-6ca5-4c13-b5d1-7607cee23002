import { SCHEDULE_FILTER } from '@/enums';
import {
  deleteUserSchedule,
  operationsSchedule,
  pageUserSchedule,
} from '@/services/oa/userSchedule';
import { queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, DrawerForm, ProColumns, ProTable } from '@ant-design/pro-components';
import { Badge, Button, message, Modal } from 'antd';
import { FC, memo, useRef, useState } from 'react';

const AllScheduleDrawer: FC<{
  onRefresh?: () => void;
  proListActionRef: React.MutableRefObject<ActionType | undefined>; //刷新ProList
  refreshCalendar: () => void;
  closePop: () => void;
  myUserId: string;
  count: number;
}> = ({ refreshCalendar, myUserId, closePop, count }) => {
  const [activeKey, setActiveKey] = useState<React.Key>('WAIT_CONFIRMED');
  const actionRef = useRef<ActionType | undefined>();

  const columns: ProColumns[] = [
    {
      width: 175,
      title: '开始日期',
      dataIndex: 'startTime',
    },
    {
      width: 175,
      title: '结束日期',
      dataIndex: 'endTime',
    },
    {
      width: 170,
      ellipsis: true,
      title: '主题',
      dataIndex: 'topic',
    },
    {
      ellipsis: true,
      title: '描述',
      dataIndex: 'description',
    },
    {
      width: 160,
      fixed: 'right',
      align: 'center',
      title: '操作',
      render(_, entity) {
        //当前是未确认状态
        const isUnConfirmed =
          (entity?.scheduleUserIds?.includes(myUserId) || entity?.whetherFull === 'SELECT_ALL') &&
          !entity?.confirmUserIds?.includes(myUserId) &&
          !entity?.refuseUserIds?.includes(myUserId);

        const canDelete = entity?.userId === myUserId;

        return (
          <>
            {isUnConfirmed && (
              <>
                <Button
                  type="link"
                  onClick={async () => {
                    const msg = await operationsSchedule({
                      id: entity.id,
                      operation: true,
                    });
                    if (msg.code === 200) {
                      message.success('日程已接受');
                      actionRef.current?.reloadAndRest?.();
                    }
                  }}
                >
                  接受
                </Button>
                <Button
                  type="link"
                  onClick={async () => {
                    const msg = await operationsSchedule({
                      id: entity.id,
                      operation: false,
                    });
                    if (msg.code === 200) {
                      message.success('日程已拒绝');
                      actionRef.current?.reloadAndRest?.();
                    }
                  }}
                >
                  拒绝
                </Button>
              </>
            )}
            {canDelete && (
              <Button
                type="link"
                onClick={() => {
                  Modal.confirm({
                    title: '确认删除',
                    content: `您确定要删除此日程吗?`,
                    okText: '确认',
                    cancelText: '取消',
                    onOk: async () => {
                      const msg = await deleteUserSchedule({
                        ids: [entity.id],
                      });
                      if (msg.code === 200) {
                        message.success('删除成功!');
                        actionRef.current?.reloadAndRest?.();
                      }
                    },
                  });
                }}
              >
                删除
              </Button>
            )}
          </>
        );
      },
    },
  ];
  return (
    <DrawerForm
      className="inner-table"
      width={'40%'}
      title="所有日程"
      trigger={
        <Badge count={count} dot offset={[-10, 8]}>
          <Button type="link">所有日程</Button>
        </Badge>
      }
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      onOpenChange={(open) => {
        if (!open) {
          refreshCalendar();
        } else {
          closePop();
        }
      }}
      size="small"
      submitter={false}
    >
      <ProTable<API.UserScheduleReq>
        actionRef={actionRef}
        {...defaultTableConfig}
        scroll={{
          x: '100%',
        }}
        toolbar={{
          menu: {
            type: 'tab',
            activeKey: activeKey,
            items: SCHEDULE_FILTER,
            onChange: (key) => {
              setActiveKey(key as string);
            },
          },
        }}
        options={false}
        columns={columns}
        params={{ activeKey }}
        request={async ({ activeKey, ...params }) => {
          let filterObj = {};
          switch (activeKey) {
            case 'WAIT_CONFIRMED':
              filterObj = { extra: { status: 'WAIT_CONFIRMED' } };
              break;
            case 'CONFIRMED':
              filterObj = { extra: { status: 'CONFIRMED' } };
              break;
            case 'CREATED':
              filterObj = { filter: { userId: myUserId } };
              break;
            default:
              filterObj = {};
          }
          return queryPagingTable({ ...params, ...filterObj }, pageUserSchedule);
        }}
      />
    </DrawerForm>
  );
};

export default memo(AllScheduleDrawer);
