import { APPROVAL_STATUS, APPROVAL_TYPE } from '@/enums';
import { typeMap } from '@/pages/Approval/PendingApproval';

import { treatSubmit } from '@/services/oa/home';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useLocation } from '@umijs/max';
import { Tag } from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';

//表格的菜单枚举
const menuStatus = [...APPROVAL_STATUS.map((item) => ({ ...item, key: item.value }))];

const PendingSubmit: React.FC = () => {
  // 解析URL查询参数
  const { search } = useLocation();
  const queryParams = new URLSearchParams(search);
  const currentKey = queryParams.get('activiStatus') || '0';
  const [activeKey, setActiveKey] = useState<string>(currentKey);

  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '编号',
      dataIndex: 'DOCUMENT_NUMBER',
      width: 150,
      copyable: true,
      render(dom, entity) {
        const { ID, ACTIVI_STATUS, SOURCE_TABLE, DOCUMENT_NUMBER } = entity;
        const app = APPROVAL_TYPE.find((item) => item.value === SOURCE_TABLE);
        let url: string;
        if (ACTIVI_STATUS === '0') {
          if (SOURCE_TABLE === 'BUSINESS_PARTNER') {
            const prefix = DOCUMENT_NUMBER?.split('-')[0];
            url = `${app?.editorPath}/${typeMap[prefix]}/edit/${ID}`;
          } else if (
            [
              'LEAVE_APPROVAL_PROCESS',
              'REVOCATION_APPROVAL',
              'RESOURCE_APPLICATION',
              'TRAINING_REIMBURSEMENT_APPROVAL',
              'ANNOUNCEMENT_APPROVAL',
            ].includes(SOURCE_TABLE)
          ) {
            url = `${app?.editorPath}?current=1&pageSize=10&activiStatus=${ACTIVI_STATUS}`;
          } else {
            url = `${app?.editorPath}/${ID}`;
          }
        } else {
          //跳转审批页
          if (SOURCE_TABLE === 'BUSINESS_PARTNER') {
            const prefix = DOCUMENT_NUMBER?.split('-')[0];
            url = `${app?.path}/${typeMap[prefix]}-details/${ID}}`;
          } else {
            url = `${app?.path}/details/${ID}`;
          }
        }
        return (
          <a className="rk-a-span" onClick={() => history.push(url)}>
            {dom}
          </a>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'CREATED_TIME',
      width: 120,
      render: (dom, record) => {
        const { CREATED_TIME } = record;
        if (!CREATED_TIME) return null;
        return dayjs(CREATED_TIME).format('YYYY-MM-DD');
      },
      hideInSearch: true,
    },
    {
      title: '来源',
      dataIndex: 'SOURCE_TABLE',
      width: 120,
      hideInSearch: true,
      render: (dom, entity) => {
        const { SOURCE_TABLE } = entity;
        const types = option2enum(APPROVAL_TYPE);
        const val = types[SOURCE_TABLE!];
        return val ? <Tag>{val.text}</Tag> : dom;
      },
    },
    {
      title: '来源',
      dataIndex: 'SOURCE_TABLE',
      valueType: 'select',
      fieldProps: () => ({
        showSearch: true,
      }),
      valueEnum: option2enum(APPROVAL_TYPE),
      hideInTable: true,
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      hideInTable: true,
      search: false,
    },
  ];

  return (
    <PageContainer header={{ title: false }}>
      <ProTable<Record<string, any>>
        {...defaultTableConfig}
        rowKey="ID"
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        columns={columns}
        params={{
          activiStatus: activeKey,
        }}
        form={{
          syncToUrl: true,
          syncToInitialValues: false,
          extraUrlParams: {
            activiStatus: activeKey,
          },
        }}
        toolbar={{
          menu: {
            type: 'tab',
            activeKey: activeKey,
            items: menuStatus,
            onChange: (key) => {
              setActiveKey(key as string);
            },
          },
        }}
        request={async (params) => {
          const { SOURCE_TABLE, DOCUMENT_NUMBER, pageSize, current, activiStatus } = params;
          const msg = await treatSubmit({
            searchNumber: DOCUMENT_NUMBER,
            searchType: SOURCE_TABLE,
            pageNum: current,
            pageSize: pageSize,
            activiStatus: activiStatus,
          });

          return {
            data: msg?.data?.treatSubmitRespList || [],
            success: true,
            total: msg?.data?.count,
          };
        }}
      />
    </PageContainer>
  );
};

export default PendingSubmit;
