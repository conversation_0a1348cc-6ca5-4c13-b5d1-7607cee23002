import { ProCard, StatisticCard } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Typography } from 'antd';
import styles from '../index.less';

const { Title } = Typography;
const ApprovalStatistics: React.FC<{
  value?: any;
  onChange?: (val: any) => void;
}> = ({ value = {} }) => {
  const { beSubmittedCount, passCount, refuseCount, underReviewCount, rejectCount } = value || {};

  return (
    <ProCard
      className={styles.card}
      layout="center"
      bordered
      title={<Title level={5}>我的申请</Title>}
    >
      <StatisticCard
        className={styles.mouse}
        onClick={() =>
          history.push('/personal-center/pending-submit?current=1&pageSize=10&activiStatus=0')
        }
        bodyStyle={{
          paddingRight: '10px',
        }}
        statistic={{
          title: '待提交',
          value: beSubmittedCount || 0,
          status: 'default',
        }}
      />
      <StatisticCard
        className={styles.mouse}
        bodyStyle={{
          paddingRight: '10px',
        }}
        statistic={{
          title: '审核中',
          value: underReviewCount || 0,
          status: 'processing',
        }}
        onClick={() =>
          history.push('/personal-center/pending-submit?current=1&pageSize=10&activiStatus=1')
        }
      />
      <StatisticCard
        className={styles.mouse}
        bodyStyle={{
          paddingRight: '10px',
        }}
        statistic={{
          title: '通过',
          value: passCount || 0,
          status: 'success',
        }}
        onClick={() =>
          history.push('/personal-center/pending-submit?current=1&pageSize=10&activiStatus=2')
        }
      />
      <StatisticCard
        className={styles.mouse}
        bodyStyle={{
          paddingRight: '10px',
        }}
        statistic={{
          title: '拒绝',
          value: refuseCount || 0,
          status: 'error',
        }}
        onClick={() =>
          history.push('/personal-center/pending-submit?current=1&pageSize=10&activiStatus=3')
        }
      />
      <StatisticCard
        className={styles.mouse}
        statistic={{
          title: '驳回',
          value: rejectCount || 0,
          status: 'warning',
        }}
        onClick={() =>
          history.push('/personal-center/pending-submit?current=1&pageSize=10&activiStatus=4')
        }
      />
    </ProCard>
  );
};

export default ApprovalStatistics;
