import { useDepartment } from '@/hooks/useDepartment';
import { RightOutlined } from '@ant-design/icons';
import { ProDescriptions } from '@ant-design/pro-components';
import { Link, useModel } from '@umijs/max';
import styles from '../index.less';

const UserInfo: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const { departmentList } = useDepartment();

  return (
    <ProDescriptions>
      <ProDescriptions.Item label="姓名">{currentUser?.username}</ProDescriptions.Item>
      <ProDescriptions.Item label="联系电话">{currentUser?.phone}</ProDescriptions.Item>
      <ProDescriptions.Item label="邮箱">{currentUser?.email}</ProDescriptions.Item>
      <ProDescriptions.Item
        label="部门"
        valueType="select"
        valueEnum={Object.fromEntries(
          departmentList?.map((item) => [item.id, item.departmentName]),
        )}
      >
        {currentUser?.department}
      </ProDescriptions.Item>
      <ProDescriptions.Item span={1}>
        <Link to={`/human-resources/employees/edit/${currentUser?.id}`} className={styles.mouse}>
          更多
          <RightOutlined />
        </Link>
      </ProDescriptions.Item>
    </ProDescriptions>
  );
};

export default UserInfo;
