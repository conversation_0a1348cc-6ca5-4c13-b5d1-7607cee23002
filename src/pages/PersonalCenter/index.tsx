import RKCol from '@/components/RKCol';
import { activiCount } from '@/services/oa/home';
import { taskCount } from '@/services/oa/task';
import { SettingOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useModel, useRequest } from '@umijs/max';
import { Button, Carousel, Col, Row, Space, Typography } from 'antd';
import { useEffect, useState } from 'react';
import AbilityDrawerForm, { avatars } from './components/AbilityDrawerForm';
import { abilityList } from './components/abilityList';
import Agenda from './components/Agenda';
import Announcement from './components/Announcement';
import ApprovalStatistics from './components/ApprovalStatistics';
import AvatarArea from './components/AvatarArea';
import BillingInformation from './components/BillingInformation';
import Schedule from './components/Schedule';
import Task from './components/Task';
import UserInfo from './components/UserInfo';
import styles from './index.less';

const { Text, Title } = Typography;

const cardContent = (arr: Record<string, any>[]) => {
  return (
    <ProCard ghost>
      {arr?.map((item) => {
        return (
          <ProCard
            key={item.value}
            onClick={() => history.push(`${item.url}`)}
            layout="center"
            wrap
            className={styles.mouse}
            colSpan={6}
            bodyStyle={{ paddingLeft: 0, paddingRight: 0 }}
          >
            <Row gutter={16} className={styles.verticalCenter}>
              <Col>
                <Space>{item.avatar}</Space>
              </Col>
              <Col style={{ marginTop: '5px' }}>
                <Text type="secondary">{item.title}</Text>
              </Col>
            </Row>
          </ProCard>
        );
      })}
    </ProCard>
  );
};

const PersonalCenter: React.FC = () => {
  const [modalVisit, setModalVisit] = useState(false);
  const [initialValues, setInitialValues] = useState({});
  const { initialState, refresh } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const commonlyFunctions = currentUser?.commonlyFunctions;

  //监听登录用户的页面权限
  useEffect(() => {
    if (commonlyFunctions) {
      const checkedList = Object?.entries(abilityList).map(([key, value]) => {
        const option = value
          ?.filter((item) => commonlyFunctions?.includes(item?.value))
          .map((item) => item.value);
        return [key, option];
      });
      const checked = Object.fromEntries(checkedList);
      setInitialValues(checked);
    }
  }, [commonlyFunctions]);
  //筛选出router中符合的数据
  const filterArr = (router: any) => {
    const result = Object?.entries(router)
      .map((item) => item[1])
      .flat()
      .filter((item: any) => commonlyFunctions?.includes(item?.value))
      .map((item: any) => ({ ...item, avatar: avatars(item?.abbreviation) }));
    //分组
    return result.reduce((acc, curr, index) => {
      if (index % 4 === 0) acc.push([]);
      acc[Math.floor(index / 4)].push(curr);
      return acc;
    }, []);
  };

  const { data: approvalStaticData = {} } = useRequest(() => activiCount());
  const { data: taskStaticData = {} } = useRequest(() => taskCount());

  return (
    <>
      <ProCard split="vertical" layout="center" bordered gutter={24} className={styles.card}>
        <ProCard colSpan="20%" layout="center">
          <AvatarArea />
        </ProCard>
        <ProCard layout="center">
          <UserInfo />
        </ProCard>
      </ProCard>
      <Row gutter={24}>
        <RKCol lg={12} md={12} sm={12} xs={12}>
          <Schedule />
        </RKCol>
        <RKCol lg={12} md={12} sm={12} xs={12}>
          <ApprovalStatistics value={approvalStaticData} />
        </RKCol>
      </Row>
      <Row gutter={24}>
        <RKCol lg={12} md={12} sm={12} xs={12}>
          <Announcement />
        </RKCol>
        <RKCol lg={12} md={12} sm={12} xs={12}>
          <ProCard
            wrap
            className={styles.carousels}
            bordered
            title={<Title level={5}>常用功能</Title>}
            extra={
              <Button
                type="link"
                onClick={() => {
                  setModalVisit(true);
                }}
                icon={<SettingOutlined />}
              >
                自定义
              </Button>
            }
          >
            <Carousel>
              {filterArr(abilityList)?.map((item: [], index: any) => {
                return <div key={index}>{cardContent(item)}</div>;
              })}
            </Carousel>
          </ProCard>
        </RKCol>
      </Row>

      <Task value={taskStaticData} />

      <BillingInformation />

      <Agenda />

      <AbilityDrawerForm
        initialValues={initialValues}
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          refresh();
        }}
      />
    </>
  );
};

export default PersonalCenter;
