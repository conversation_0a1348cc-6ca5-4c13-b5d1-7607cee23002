import { STATUS } from '@/enums';
import { useUserList } from '@/hooks/useUserList';
import { roleDel, rolePage, roleUpdateStatus } from '@/services/oa/auth';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import { useRef, useState } from 'react';
import AssignUserDrawerForm from './components/AssignUserDrawerForm';
const Permission = () => {
  const [drawerVisit, setDrawerVisit] = useState(false);
  const actionRef = useRef<ActionType | undefined>();
  const currentRoleRef = useRef<API.RoleInfoResp>();

  // 获取用户列表
  const { userList } = useUserList();
  // 更新状态
  const { run: updateState, fetches } = useRequest((value) => roleUpdateStatus(value), {
    manual: true,
    fetchKey: (params) => params.id,
    formatResult: (res) => res,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('操作成功！');
      actionRef.current?.reloadAndRest?.();
    },
  });

  // 删除
  const { run: deleteRecord } = useRequest((id) => roleDel({ id: id }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      actionRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const handleDelete = async (row: API.RoleInfoResp) => {
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${row.name}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(row.id);
      },
    });
  };

  const columns: ProColumns[] = [
    {
      title: '角色',
      dataIndex: 'name',
      render(dom, entity) {
        return (
          <a className="rk-a-span" onClick={() => history.push(`/permission/details/${entity.id}`)}>
            {dom}
          </a>
        );
      },
      width: 180,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: option2enum(STATUS),
      width: 100,
    },
    {
      title: '成员',
      dataIndex: 'userIds',
      hideInSearch: true,
      valueType: 'select',
      fieldProps: {
        options: userList,
        fieldNames: {
          value: 'id',
          label: 'employeeName',
        },
      },
      width: 400,
      render(dom, entity) {
        if (entity.id === '1721358108087914498') return '全员';
        return dom;
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      hideInSearch: true,
      width: 200,
    },
    {
      title: '操作',
      width: 200,
      fixed: 'right',
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        const { status, id } = record;
        const disabled = id === '1721358108087914498';
        return (
          <Space>
            <Button
              type="link"
              className="inner-table-link"
              key="operator"
              loading={fetches?.[id]?.loading}
              onClick={() => {
                updateState({
                  id,
                  status: status === '1' ? '0' : '1',
                });
              }}
            >
              {status === '1' ? '禁用' : '启用'}
            </Button>
            <Button
              type="link"
              className="inner-table-link"
              key="assign"
              disabled={disabled}
              onClick={() => {
                history.push(`/permission/assign-user/${id}`);
              }}
            >
              分配至用户
            </Button>
            <Button
              type="link"
              className="inner-table-link"
              key="delete"
              onClick={() => handleDelete(record)}
              disabled={disabled}
            >
              删除
            </Button>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        pagination={{
          defaultPageSize: 20,
          showSizeChanger: true,
        }}
        actionRef={actionRef}
        rowKey="id"
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        columns={columns}
        headerTitle="角色列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => history.push('/permission/add')}
            >
              新建角色
            </Button>,
          ],
        }}
        request={async (params) => queryPagingTable(params, rolePage)}
      />
      <AssignUserDrawerForm
        open={drawerVisit}
        onOpenChange={setDrawerVisit}
        initialValues={currentRoleRef.current}
        onFinish={async () => {
          actionRef.current?.reload?.();
        }}
      />
    </PageContainer>
  );
};

export default Permission;
