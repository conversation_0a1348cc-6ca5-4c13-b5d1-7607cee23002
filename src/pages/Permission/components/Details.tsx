import RKCol from '@/components/RKCol';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { info, roleCreate, roleUpdate } from '@/services/oa/auth';
import { onSuccessAndGoBack, queryFormData } from '@/utils';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message, Row, Tabs, TabsProps } from 'antd';
import { FC } from 'react';
import dataPermissions from './dataPermissions';
import menuPermissions from './menuPermissions';
import RoleTree from './RoleTree';

const PermissionDetails: FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  // 新建
  const { run: add, loading: addLoading } = useRequest((value) => roleCreate(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 修改
  const { run: update, loading: updateLoading } = useRequest((value) => roleUpdate(value), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('操作成功！');
    },
    formatResult: (res) => res,
  });
  const items: TabsProps['items'] = [
    {
      key: 'page',
      label: '页面权限配置',
      children: (
        <ProForm.Item name="pagePermissions">
          <RoleTree treeData={menuPermissions} />
        </ProForm.Item>
      ),
    },
    {
      key: 'data',
      label: '数据权限配置',
      children: (
        <ProForm.Item name="dataPermissions">
          <RoleTree treeData={dataPermissions} />
        </ProForm.Item>
      ),
    },
  ];

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          onReset: () => {
            history.go(-1);
          },
          render: (props, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
          submitButtonProps: {
            loading: addLoading || updateLoading,
          },
        }}
        request={async () =>
          queryFormData(
            {
              id,
            },
            isEditPage,
            info,
          )
        }
        onFinish={async (values) => {
          if (isEditPage) {
            update(values);
          } else {
            add(values);
          }
        }}
      >
        <div className="rk-none">
          <ProFormText name="id" label="id" />
          <ProFormText name="pagePermissions" />
          <ProFormText name="dataPermissions" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText name="name" label="角色" />
          </RKCol>
          <RKCol lg={8} md={8} sm={12}>
            <ProFormTextArea
              name="description"
              label="描述"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 3,
                },
              }}
            />
          </RKCol>

          <RKCol lg={24} md={24} sm={24}>
            <Tabs defaultActiveKey="1" items={items} />
          </RKCol>
        </Row>
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(PermissionDetails);
