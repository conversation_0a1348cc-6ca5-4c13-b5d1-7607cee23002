import RKCol from '@/components/RKCol';
import UserContext from '@/Context/UserContext';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useUserList } from '@/hooks/useUserList';
import { info, roleAssignment2User } from '@/services/oa/auth';
import { queryFormData } from '@/utils';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message, Row } from 'antd';
import { FC } from 'react';
import UserTable from './UserTable';

const PermissionDetails: FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  // 获取用户列表
  const { userList = [] } = useUserList();
  // 修改
  const { run: update, loading: updateLoading } = useRequest(
    (value) => roleAssignment2User(value),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('操作成功！');
      },
      formatResult: (res) => res,
    },
  );

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          onReset: () => {
            history.go(-1);
          },
          render: (props, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
          submitButtonProps: {
            loading: updateLoading,
          },
        }}
        request={async () =>
          queryFormData(
            {
              id,
            },
            isEditPage,
            info,
          )
        }
        onFinish={async (values) => {
          const { roleId, userIds } = values;
          update({ roleId, userIds });
        }}
      >
        <div className="rk-none">
          <ProFormText
            name="id"
            label="id"
            transform={(value) => {
              return { roleId: value };
            }}
          />
        </div>
        <UserContext.Provider value={{ userList }}>
          <Row gutter={24}>
            <RKCol>
              <ProFormText name="name" label="角色" disabled />
            </RKCol>
            <RKCol lg={8} md={8} sm={12}>
              <ProFormTextArea
                name="description"
                label="描述"
                disabled
                fieldProps={{
                  autoSize: {
                    minRows: 1,
                    maxRows: 3,
                  },
                }}
              />
            </RKCol>
            <RKCol lg={24} md={24} sm={24}>
              <ProForm.Item name="userIds">
                <UserTable />
              </ProForm.Item>
            </RKCol>
          </Row>
        </UserContext.Provider>
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(PermissionDetails);
