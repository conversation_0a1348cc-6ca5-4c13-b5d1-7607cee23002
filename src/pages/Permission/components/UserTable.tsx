import UserContext from '@/Context/UserContext';
import { usersByDepartmentId } from '@/services/oa/auth';
import { getDepartmentTree } from '@/services/oa/department';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ModalForm, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, Col, Row, Tree } from 'antd';
import { DataNode } from 'antd/es/tree';
import React, { useContext, useMemo, useState } from 'react';
const transformData = (data: API.DepartmentTreeResp[] = []) => {
  return data.map((item) => {
    const newItem: DataNode = {
      ...item,
      title: item.departmentName!,
      key: item.id!,
      children: [],
      disabled: item.status !== '1',
    };

    if (item?.child && item.child.length > 0) {
      newItem.children = transformData(item.child);
    }

    return newItem;
  });
};
const UserTable: React.FC<{
  value?: string[];
  onChange?: (val: string[]) => void;
}> = ({ value = [], onChange }) => {
  const { userList = [] } = useContext(UserContext);
  const [modalVisit, setModalVisit] = useState(false);
  // 树节点选中的key
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  // 部门树
  const { data = [] } = useRequest(getDepartmentTree);
  const [treeNode, setTreeNode] = useState<API.DepartmentTreeResp>();
  const dataSource = useMemo(
    () => value?.map((item: string) => userList?.find((user) => user.id === item)),
    [userList, value],
  );
  // 已勾选人员
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);

  const columns: ProColumns[] = [
    {
      title: '编号',
      dataIndex: 'employeeNumber',
      width: 150,
    },
    {
      title: '姓名',
      dataIndex: 'employeeName',
      valueType: 'select',
      fieldProps: {
        options: userList,
      },
    },
    {
      title: '邮箱',
      dataIndex: 'email',
    },
  ];
  return (
    <>
      <ProTable
        className="inner-table"
        {...defaultTableConfig}
        options={{
          search: {
            allowClear: true,
          },
          reload: false,
          density: false,
          setting: false,
        }}
        request={async ({ keyword = '' }) => {
          const data = dataSource.filter(Boolean);
          return {
            data: data.filter((item) =>
              item?.employeeName ? item.employeeName?.indexOf(keyword) >= 0 : false,
            ),
            success: true,
          };
        }}
        search={false}
        params={dataSource}
        columns={[
          ...columns,
          {
            title: '操作',
            width: 100,
            fixed: 'right',
            key: 'option',
            valueType: 'option',
            align: 'center',
            render: (text, record) => {
              const { id } = record;
              return (
                <Button
                  type="link"
                  onClick={() => {
                    onChange?.(value?.filter((item) => item !== id) || []);
                  }}
                >
                  移除
                </Button>
              );
            },
          },
        ]}
        headerTitle="已分配用户"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setModalVisit(true)}
            >
              添加成员
            </Button>,
          ],
        }}
      />
      <ModalForm
        title="添加成员"
        open={modalVisit}
        onFinish={async () => {
          onChange?.([...value, ...checkedKeys]);
          setModalVisit(false);
        }}
        onOpenChange={(visible) => {
          setModalVisit(visible);
          setCheckedKeys([]);
        }}
      >
        <Row gutter={24}>
          <Col
            span={8}
            style={{ borderRight: 'solid 1px rgba(5, 5, 5, 0.06)', height: 500, overflow: 'auto' }}
          >
            <Tree
              onClick={(_, info) => {
                setTreeNode(info as API.DepartmentTreeResp);
                setSelectedKeys([info.key as string]);
              }}
              selectedKeys={selectedKeys}
              icon={false}
              treeData={transformData(data) || []}
            />
          </Col>
          <Col span={16} style={{ height: 500, overflow: 'auto' }}>
            <ProTable
              className="inner-table"
              {...defaultTableConfig}
              options={{
                search: {
                  allowClear: true,
                },
                reload: false,
                density: false,
                setting: false,
              }}
              params={treeNode}
              rowSelection={{
                selectedRowKeys: checkedKeys,
                onSelectAll: (selected, selectedRows, changeRows) => {
                  if (!selected) {
                    setCheckedKeys((prevState) =>
                      prevState.filter((item) => !changeRows.map((row) => row.id).includes(item)),
                    );
                  } else {
                    setCheckedKeys((prevState) => [
                      ...prevState,
                      ...selectedRows?.filter((item) => item).map((item) => item?.id),
                    ]);
                  }
                },

                onSelect: (record, selected) => {
                  if (selected) {
                    setCheckedKeys((prevState) => [...prevState, record.id]);
                  } else {
                    setCheckedKeys((prevState) => prevState.filter((item) => item !== record.id));
                  }
                },

                getCheckboxProps: (record) => ({
                  disabled: value.includes(record.id),
                }),
              }}
              request={async (params) => {
                const { keyword = '', id, parentId } = params;
                const res = await usersByDepartmentId({
                  departmentId: id,
                  departmentGrade: parentId === '0' ? '1' : '2',
                });
                return {
                  data:
                    res?.data?.filter(
                      (item) => item.employeeName && item.employeeName?.indexOf(keyword) >= 0,
                    ) || [],
                  success: true,
                };
              }}
              search={false}
              columns={columns}
              headerTitle="用户"
            />
          </Col>
        </Row>
      </ModalForm>
    </>
  );
};

export default UserTable;
