import RKCol from '@/components/RKCol';
import { Button, Space, Tree, TreeProps } from 'antd';
import { DataNode } from 'antd/es/tree';
import { Key, useEffect, useMemo, useState } from 'react';
// 递归函数，用于获取所有节点的 key
const getAllNodeKeys = (nodes: DataNode[]) => {
  return nodes.reduce((keys: DataNode['key'][], node: DataNode) => {
    keys.push(node.key);
    if (node.children) {
      keys.push(...getAllNodeKeys(node.children));
    }
    return keys;
  }, []);
};

export function findParentNodeKey(treeData: DataNode[], targetKeys: Key[]): Key[] {
  const ancestorKeys: Key[] = [];

  function searchAncestors(nodes: DataNode[], ancestors: Key[]) {
    nodes.forEach((node) => {
      if (targetKeys.includes(node.key)) {
        // Add all current ancestors when a target key is found
        ancestorKeys.push(...ancestors);
      }
      if (node?.children?.length) {
        // Extend the list of ancestors for the next level
        searchAncestors(node.children, [...ancestors, node.key]);
      }
    });
  }

  searchAncestors(treeData, []);
  return [...new Set(ancestorKeys)];
}

const RoleTree: React.FC<{
  treeData: DataNode[];
  onChange?: (ids: Key[]) => void;
  value?: Key[];
}> = ({ treeData, value, onChange }) => {
  const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
  // 获取所有树节点
  const allKeys = useMemo(() => getAllNodeKeys(treeData), [treeData]);

  // 如果所有树节点跟勾选的节点一致，则全选，否则移除没有的节点的父节点
  useEffect(() => {
    if (allKeys.length === value?.length) {
      setCheckedKeys(value || []);
    } else {
      const filterKeys = allKeys.filter((key) => !value?.includes(key));
      const parentNodeKey = findParentNodeKey(treeData, filterKeys);
      const newValue = value?.filter((item) => !parentNodeKey.includes(item));
      setCheckedKeys(newValue || []);
    }
  }, [allKeys, value, treeData]);

  const onCheck: TreeProps['onCheck'] = (checkedKeysValue) => {
    setCheckedKeys(checkedKeysValue as React.Key[]);
    onChange?.(checkedKeysValue as React.Key[]);
  };

  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };

  // 点击按钮时触发，展开全部节点
  const handleExpandAll = () => {
    // 获取所有节点的 key，可以根据你的数据结构来生成
    setExpandedKeys(allKeys);
  };

  return (
    <>
      <RKCol lg={24} md={24} sm={24}>
        <div style={{ display: 'flex', justifyContent: 'end', height: 36 }}>
          <Space>
            <Button type="link" className="inner-table-link" onClick={handleExpandAll}>
              全部展开
            </Button>
            <Button type="link" className="inner-table-link" onClick={() => setExpandedKeys([])}>
              全部收起
            </Button>
          </Space>
        </div>
      </RKCol>
      <Tree
        checkable
        defaultExpandAll
        defaultExpandParent
        treeData={treeData}
        blockNode
        checkedKeys={checkedKeys}
        onCheck={onCheck}
        expandedKeys={expandedKeys}
        autoExpandParent={autoExpandParent}
        onExpand={onExpand}
      />
    </>
  );
};

export default RoleTree;
