import RKSelectLabel from '@/components/RKSelectLabel';
import { useUserList } from '@/hooks/useUserList';
import { roleAssignment2User } from '@/services/oa/auth';
import {
  DrawerForm,
  ModalFormProps,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';
import React, { memo, useEffect, useRef } from 'react';

const AssignUserDrawerForm: React.FC<ModalFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  const formRef = useRef<ProFormInstance>();

  // 获取用户列表
  const { userList, loading: userLoading } = useUserList();
  useEffect(() => {
    const userNames = userList
      .filter((item) => initialValues?.userIds.includes(item.id))
      .map((item) => item.employeeName);
    formRef.current?.setFieldsValue({ ...initialValues, userIds: userNames });
  }, [initialValues, userList]);

  return (
    <DrawerForm<API.RoleAssignment2UserReq>
      width={460}
      title="分配用户"
      open={open}
      onOpenChange={onOpenChange}
      autoFocusFirstInput
      initialValues={{}}
      formRef={formRef}
      onFinish={async (values) => {
        const res = await roleAssignment2User(values);
        const success = res?.code === 200;
        if (success) {
          message.success('操作成功!');
          onFinish?.(values);
        }
        return success;
      }}
      drawerProps={{}}
    >
      <div className="rk-none">
        <ProFormText
          name="id"
          transform={(value) => {
            return { roleId: value };
          }}
        />
      </div>
      <ProFormText name="name" label="角色名" width="lg" disabled />
      <ProFormSelect
        name="userIds"
        label="用户"
        width="lg"
        mode="multiple"
        fieldProps={{
          showSearch: true,
          loading: userLoading,
          optionLabelProp: 'employeeName',
        }}
        options={userList.map((item) => {
          const { employeeName, employeeNumber } = item;
          return {
            value: employeeName,
            label: <RKSelectLabel title={employeeName} info={employeeNumber} />,
          };
        })}
        transform={(value) => ({
          userIds: userList
            .filter((item) => value.includes(item.employeeName))
            .map((item) => item.id),
        })}
      />
    </DrawerForm>
  );
};

export default memo(AssignUserDrawerForm);
