import DepartmentEmployee from '@/components/DepartmentEmployee';
import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { TASK_STATUS } from '@/enums';
import { useUserList } from '@/hooks/useUserList';
import {
  accept,
  assign,
  backTask,
  closed,
  finish,
  taskDelete,
  taskPage,
  terminated,
} from '@/services/oa/task';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { useModel } from '@@/exports';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Button, message, Modal, Progress, Space, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import ChildTaskDrawerForm from './components/ChildTaskDrawerForm';

const WorkOrder: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.TaskPageResp[]>([]);
  const [assignModalShow, setAssignModalShow] = useState(false);
  const [currentAssignTask, setCurrentAssignTask] = useState('');

  const { userList, loading: userLoading } = useUserList();

  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  const [initialValues, setInitialValues] = useState<Record<string, any>>();
  const [drawerVisit, setDrawerVisit] = useState<boolean>(false);

  // 删除
  const { run: deleteRecord } = useRequest((ids) => taskDelete({ ids: ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  // 指派
  const { run: assignRecord } = useRequest((params) => assign(params), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('指派成功');
      setAssignModalShow(false);
      setCurrentAssignTask('');
      tableRef.current?.reload?.();
    },
    formatResult: (res) => res,
  });

  const handleDelete = async (rows: API.TaskPageResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.documentNumber!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除任务“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  const handleAssign = (record: API.TaskPageResp) => {
    setCurrentAssignTask(record?.id || '');
    setAssignModalShow(true);
  };

  //接受
  const { run: acceptRecord } = useRequest((record) => accept({ id: record.id }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('接受成功');
      tableRef.current?.reload?.();
    },
    formatResult: (res) => res,
  });

  //完成
  const { run: finishRecord } = useRequest((record) => finish({ id: record.id }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('完成成功');
      tableRef.current?.reload?.();
    },
    formatResult: (res) => res,
  });

  //关闭
  const { run: closeRecord } = useRequest((record) => closed({ id: record.id }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('关闭成功');
      tableRef.current?.reload?.();
    },
    formatResult: (res) => res,
  });

  //退回
  const { run: backRecord } = useRequest((record) => backTask({ id: record.id }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('退回成功');
      tableRef.current?.reload?.();
    },
    formatResult: (res) => res,
  });

  //终止
  const { run: terminateRecord } = useRequest((record) => terminated({ id: record.id }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('终止成功');
      tableRef.current?.reload?.();
    },
    formatResult: (res) => res,
  });

  // 表格
  const columns: ProColumns<API.TaskPageResp & { children?: Record<string, any>[] }>[] = [
    {
      title: '任务编号',
      dataIndex: 'documentNumber',
      copyable: true,
      ellipsis: true,
      width: 180,
      render(dom, record) {
        const { taskStatus = '', executeUserId = '', launchUserId = '', id, parentId } = record;
        /**
         * 任务未开始或者执行中，创建人可编辑页面，当初处理人可编辑
         * 11.06 冯总说任务结束了创建者都可编辑
         */

        const canEdit =
          (['NOT_STARTED', 'IN_PROGRESS'].includes(taskStatus) &&
            currentUser?.id === executeUserId) ||
          currentUser?.id === launchUserId;
        return (
          <a
            className="rk-a-span"
            onClick={() => {
              if (parentId !== '0') {
                setDrawerVisit(true);
                setInitialValues(record);
              } else {
                history.push(`/task/${canEdit ? 'edit' : 'details'}/` + id);
              }
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      width: 150,
    },
    {
      title: '任务状态',
      dataIndex: 'taskStatus',
      valueEnum: option2enum(TASK_STATUS),
      width: 110,
    },
    {
      title: '是否逾期',
      dataIndex: 'hasOverdue',
      search: false,
      width: 100,
      render: (text) => {
        if (text) {
          return <Tag color="red">逾期</Tag>;
        } else {
          return <Tag color="green">未逾期</Tag>;
        }
      },
    },
    {
      title: '完成进度',
      width: 150,
      search: false,
      render: (_, record) => {
        return <Progress percent={record?.progress || 0} size="small" />;
      },
    },
    {
      title: '发起人',
      dataIndex: 'launchUserName',
      search: false,
      width: 100,
    },
    {
      title: '发起人',
      dataIndex: 'launchUserId',
      valueType: 'select',
      fieldProps: {
        options: userList,
        loading: userLoading,
        showSearch: true,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        filterOption: true,
        optionFilterProp: 'label',
      },
      hideInTable: true,
    },
    {
      title: '执行人',
      dataIndex: 'executeUserName',
      search: false,
      width: 100,
    },
    {
      title: '执行人',
      dataIndex: 'executeUserId',
      valueType: 'select',
      fieldProps: {
        options: userList,
        loading: userLoading,
        showSearch: true,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        filterOption: true,
        optionFilterProp: 'label',
      },
      hideInTable: true,
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      valueType: 'date',
      search: false,
      width: 130,
    },
    {
      title: '计划完成时间',
      dataIndex: 'planEndTime',
      valueType: 'date',
      width: 130,
      search: false,
    },
    {
      title: '完成时间',
      dataIndex: 'endTime',
      valueType: 'date',
      width: 130,
      search: false,
    },
    {
      title: '关闭时间',
      dataIndex: 'closeTime',
      valueType: 'date',
      width: 130,
      search: false,
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (text, record) => {
        const { taskStatus = '', launchUserId = '', executeUserId = '', parentId } = record;

        return (
          <Space>
            {/* 任务未开始，创建者可删除 */}
            {taskStatus === 'NOT_STARTED' && currentUser?.id === launchUserId && (
              <Button
                type="link"
                className="inner-table-link"
                key="delete"
                onClick={() => handleDelete([record])}
              >
                删除
              </Button>
            )}
            {/* 任务未开始或者执行中，创建者或者当前执行人，父级任务可指派   */}
            {['NOT_STARTED', 'IN_PROGRESS'].includes(taskStatus) &&
              (currentUser?.id === launchUserId || currentUser?.id === executeUserId) &&
              parentId === '0' && (
                <Button
                  type="link"
                  className="inner-table-link"
                  key="assign"
                  onClick={() => handleAssign(record)}
                >
                  指派
                </Button>
              )}
            {/* 任务未开始，当前执行人可接受 */}
            {taskStatus === 'NOT_STARTED' && currentUser?.id === executeUserId && (
              <Button
                type="link"
                className="inner-table-link"
                key="accept"
                onClick={() => {
                  Modal.confirm({
                    title: '确认操作',
                    content: `您确定要接受此任务吗?`,
                    okText: '确认',
                    cancelText: '取消',
                    onOk: async () => {
                      acceptRecord(record);
                    },
                  });
                }}
              >
                接受
              </Button>
            )}
            {/* 任务执行中，当前执行人可完成 */}
            {taskStatus === 'IN_PROGRESS' && currentUser?.id === executeUserId && (
              <Button
                type="link"
                className="inner-table-link"
                key="finish"
                onClick={() => {
                  //存在子任务，且子任务的任务状态存在未开始、执行中，则提升存在尚未完成 */
                  const isWarnSubmit =
                    record.children &&
                    record.children.length > 0 &&
                    !record.children?.every(
                      (item) => !['NOT_STARTED', 'IN_PROGRESS'].includes(item.taskStatus!),
                    );

                  Modal.confirm({
                    title: '确认操作',
                    content: `${
                      isWarnSubmit ? '此任务有尚未完成的子任务!' : '您确定要完成此任务吗?'
                    }`,
                    okText: '确认',
                    cancelText: '取消',
                    onOk: async () => {
                      finishRecord(record);
                    },
                  });
                }}
              >
                完成
              </Button>
            )}
            {/* 任务已提交，创建者可关闭 */}
            {taskStatus === 'SUBMITTED' && currentUser?.id === launchUserId && (
              <Button
                type="link"
                className="inner-table-link"
                key="close"
                onClick={() => {
                  //存在子任务，且子任务的任务状态都不是未开始、执行中、提交中，则可关闭 */
                  const isWarnClose =
                    record.children &&
                    record.children.length > 0 &&
                    !record.children?.every(
                      (item) =>
                        !['NOT_STARTED', 'IN_PROGRESS', 'SUBMITTED'].includes(item.taskStatus!),
                    );

                  Modal.confirm({
                    title: '确认操作',
                    content: `${
                      isWarnClose ? '此任务有尚未关闭的子任务!' : '您确定要关闭此任务吗?'
                    }`,
                    okText: '确认',
                    cancelText: '取消',
                    onOk: async () => {
                      closeRecord(record);
                    },
                  });
                }}
              >
                关闭
              </Button>
            )}
            {/* 任务已提交，创建者可退回 */}
            {taskStatus === 'SUBMITTED' && currentUser?.id === launchUserId && (
              <Button
                type="link"
                className="inner-table-link"
                key="return"
                onClick={() => {
                  Modal.confirm({
                    title: '确认操作',
                    content: `您确定要退回此任务吗?`,
                    okText: '确认',
                    cancelText: '取消',
                    onOk: async () => {
                      //todo 操作涉及退回父任务时是否需要将子任务的id同时传递过去，将子任务一同退回
                      // if (record.children && record.children.length > 0) {
                      //   const ids = record.children.map((i) => i.id).push(record.id);
                      // }
                      backRecord(record);
                    },
                  });
                }}
              >
                退回
              </Button>
            )}
            {/* 任务未开始或者进行中，创建者可终止 */}
            {['NOT_STARTED', 'IN_PROGRESS'].includes(taskStatus) &&
              currentUser?.id === launchUserId && (
                <Button
                  type="link"
                  className="inner-table-link"
                  key="terminate"
                  onClick={() => {
                    const isWarnClose =
                      record.children &&
                      !record.children?.every(
                        (item) =>
                          !['NOT_STARTED', 'IN_PROGRESS', 'SUBMITTED'].includes(item.taskStatus!),
                      );
                    Modal.confirm({
                      title: '确认操作',
                      content: `${
                        isWarnClose ? '此任务有尚未终止的子任务!' : '您确定要终止此任务吗?'
                      }`,
                      okText: '确认',
                      cancelText: '取消',
                      onOk: async () => {
                        //todo 操作涉及终止父任务时是否需要将子任务的id同时传递过去，一同终止子任务
                        // if (record.children && record.children.length > 0) {
                        //   const ids = record.children.map((i) => i.id).push(record.id);
                        // }
                        terminateRecord(record);
                      },
                    });
                  }}
                >
                  终止
                </Button>
              )}
            {/* 父级任务进行中，当前执行人可分解子任务 */}
            {taskStatus === 'IN_PROGRESS' &&
              currentUser?.id === executeUserId &&
              parentId === '0' && (
                <Button
                  type="link"
                  className="inner-table-link"
                  key="decompose"
                  onClick={() => {
                    setInitialValues(record);
                    setDrawerVisit(true);
                  }}
                >
                  分解
                </Button>
              )}
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.TaskPageResp>
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          getCheckboxProps: (record) => ({
            disabled:
              currentUser?.id !== record?.launchUserId || record?.taskStatus !== 'NOT_STARTED',
          }),
        }}
        columns={columns}
        headerTitle="任务列表"
        toolbar={{
          actions: [
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                history.push('/task/add');
              }}
              key="add"
            >
              新建任务
            </Button>,
          ],
        }}
        polling={5000}
        request={async (params) => {
          const {
            documentNumber,
            taskName,
            taskStatus,
            current,
            pageSize,
            executeUserId,
            launchUserId,
          } = params;
          const search = { documentNumber, taskName };
          const filter = { taskStatus, executeUserId, launchUserId };
          return queryPagingTable<API.PageReq>({ current, pageSize, search, filter }, taskPage);
        }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
      <DepartmentEmployee
        title={'请选择任务执行人'}
        open={assignModalShow}
        onFinish={(selectedRows) => {
          if (!selectedRows?.length) {
            message.error('请先选择一个执行人');
            return;
          }
          const params = {
            id: currentAssignTask,
            executeUserId: selectedRows[0]?.id,
          };
          assignRecord(params);
        }}
        close={() => {
          setAssignModalShow(false);
        }}
      />
      <ChildTaskDrawerForm
        initialValues={initialValues}
        open={drawerVisit}
        onOpenChange={(visible) => setDrawerVisit(visible)}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default WorkOrder;
