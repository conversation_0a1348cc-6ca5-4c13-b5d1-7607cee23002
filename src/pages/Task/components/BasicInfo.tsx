import RKCol from '@/components/RKCol';
import BaseContext from '@/Context/BaseContext';
import { TASK_STATUS } from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useDepartment } from '@/hooks/useDepartment';
import { useUserList } from '@/hooks/useUserList';
import { getDepartmentAndUserTree } from '@/services/oa/department';
import { requiredRule } from '@/utils/setting';
import { useModel } from '@@/exports';
import {
  ProFormCheckbox,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Row } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import dayjs from 'dayjs';
import { useContext } from 'react';

const BasicInfo: React.FC<WithRouteEditingProps> = ({}) => {
  const { isAddPage, isEditPage, isDetailPage, formRef } = useContext(BaseContext);

  const { userList, loading: userLoading } = useUserList();
  const { departmentList, loading: departmentLoading } = useDepartment();
  const { data: treeData = [], loading: treeLoading } = useRequest(() =>
    getDepartmentAndUserTree(),
  );

  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  return (
    <>
      <div className="rk-none">
        <ProFormText name="id" />
      </div>
      <Row gutter={24}>
        <RKCol>
          <ProFormText
            name="taskName"
            label="任务名称"
            rules={[requiredRule]}
            disabled={!isAddPage}
          />
        </RKCol>
        {!isAddPage && (
          <>
            <RKCol>
              <ProFormText name="documentNumber" label="任务编号" disabled />
            </RKCol>

            <RKCol>
              <ProFormSelect name="taskStatus" label="任务状态" disabled options={TASK_STATUS} />
            </RKCol>
            <RKCol>
              <ProFormSelect
                name="executeDepartmentId"
                label="执行部门"
                fieldProps={{
                  loading: departmentLoading,
                  fieldNames: {
                    value: 'id',
                    label: 'departmentName',
                  },
                }}
                disabled
                options={departmentList as DefaultOptionType[]}
              />
            </RKCol>
            <RKCol>
              <ProFormSelect
                name="executeUserId"
                label="执行人"
                fieldProps={{
                  loading: userLoading,
                  fieldNames: {
                    value: 'id',
                    label: 'username',
                  },
                }}
                disabled
                options={userList as DefaultOptionType[]}
              />
            </RKCol>
            <RKCol>
              <ProFormDatePicker name="launchTime" label="发起日期" disabled />
            </RKCol>
            <RKCol>
              <ProFormDatePicker name="startTime" label="开始日期" disabled />
            </RKCol>
            <RKCol>
              <ProFormDatePicker name="endTime" label="结束日期" disabled />
            </RKCol>
            <RKCol>
              <ProFormDatePicker name="closeTime" label="关闭日期" disabled />
            </RKCol>
            <RKCol>
              <ProFormDigit
                name="progress"
                label="完成进度"
                disabled
                fieldProps={{ addonAfter: '%' }}
              />
            </RKCol>
          </>
        )}

        <RKCol>
          <ProFormSelect
            name="launchDepartmentId"
            label="发起部门"
            fieldProps={{
              loading: departmentLoading,
              fieldNames: {
                value: 'id',
                label: 'departmentName',
              },
            }}
            disabled
            initialValue={currentUser?.department}
            options={departmentList as DefaultOptionType[]}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name="launchUserId"
            label="发起人"
            fieldProps={{
              loading: userLoading,
              fieldNames: {
                value: 'id',
                label: 'username',
              },
            }}
            disabled
            initialValue={currentUser?.id}
            options={userList as DefaultOptionType[]}
          />
        </RKCol>
        <ProFormDependency name={['taskStatus', 'launchUserId']}>
          {({ taskStatus, launchUserId }) => {
            return (
              <RKCol>
                <ProFormDatePicker
                  name="planEndTime"
                  label="计划完成日期"
                  disabled={
                    currentUser?.id !== launchUserId ||
                    (isEditPage && !['NOT_STARTED', 'IN_PROGRESS'].includes(taskStatus)) ||
                    isDetailPage
                  }
                  rules={[requiredRule]}
                  fieldProps={{
                    disabledDate: (current) =>
                      current && current <= dayjs().subtract(1, 'days').endOf('day'),
                  }}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>
        <ProFormDependency name={['launchUserId']}>
          {({ launchUserId }) => {
            return (
              <RKCol lg={24} md={24} sm={24}>
                <ProFormTextArea
                  name="taskDesc"
                  label="任务描述"
                  disabled={currentUser?.id !== launchUserId}
                  fieldProps={{
                    autoSize: {
                      minRows: 2,
                      maxRows: 5,
                    },
                  }}
                  rules={[requiredRule]}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>

        <RKCol lg={24} md={24} sm={24} style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ flex: 1 }}>
            <ProFormDependency name={['carbonCopyScope', 'launchUserId']}>
              {({ carbonCopyScope, launchUserId }) => {
                return carbonCopyScope?.length > 0 ? (
                  <ProFormText
                    fieldProps={{ className: 'participant-input' }}
                    disabled
                    placeholder="全员"
                    label="公开范围"
                  />
                ) : (
                  <ProFormTreeSelect
                    name="carbonCopyIds"
                    label="公开范围"
                    disabled={currentUser?.id !== launchUserId}
                    fieldProps={{
                      treeData: treeData,
                      multiple: true,
                      filterTreeNode: (input, node) => {
                        return node.name?.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      },
                      showSearch: true,
                      autoClearSearchValue: true,
                      treeNodeFilterProp: 'name',
                      loading: treeLoading,
                      fieldNames: {
                        value: 'id',
                        label: 'name',
                        children: 'child',
                      },
                      treeCheckable: true,
                      showCheckedStrategy: 'SHOW_PARENT',
                      maxTagCount: 'responsive',
                      treeCheckStrictly: false,
                    }}
                  />
                );
              }}
            </ProFormDependency>
          </div>
          <div style={{ marginLeft: 8, marginTop: 28 }}>
            <ProFormDependency name={['launchUserId']}>
              {({ launchUserId }) => {
                return (
                  <ProFormCheckbox.Group
                    disabled={currentUser?.id !== launchUserId}
                    name="carbonCopyScope"
                    options={[{ label: '全选', value: 'PUBLIC' }]}
                    fieldProps={{
                      onChange: (e) => {
                        if (e?.length) {
                          formRef.current?.setFieldValue('carbonCopyIds', []);
                        }
                      },
                    }}
                  />
                );
              }}
            </ProFormDependency>
          </div>
        </RKCol>
      </Row>
    </>
  );
};

export default withRouteEditing(BasicInfo);
