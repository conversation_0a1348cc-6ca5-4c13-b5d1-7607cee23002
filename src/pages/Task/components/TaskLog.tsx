import BaseContext from '@/Context/BaseContext';
import { taskLogList } from '@/services/oa/task';
import { useParams, useRequest } from '@umijs/max';
import { Table, TableProps } from 'antd';
import React, { forwardRef, useContext, useImperativeHandle } from 'react';

export interface taskRefProp {
  logRefresh: () => void;
}

const TaskLog: React.ForwardRefRenderFunction<taskRefProp> = (_, ref) => {
  const { id = '' } = useParams();
  const { isAddPage } = useContext(BaseContext);

  //获取详情
  const {
    data,
    loading,
    refresh: logRefresh,
  } = useRequest(() => taskLogList({ id }), {
    ready: !isAddPage,
  });

  useImperativeHandle(ref, () => ({
    logRefresh,
  }));
  // 表格
  const columns: TableProps<API.TaskLogListResp>['columns'] = [
    {
      title: '操作时间',
      dataIndex: 'createdTime',
      defaultSortOrder: 'descend',
      sorter: (a, b) => {
        return new Date(a.createdTime || '') > new Date(b.createdTime || '') ? 1 : -1;
      },
      width: 200,
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      width: 200,
    },
    {
      title: '操作内容',
      dataIndex: 'content',
    },
  ];
  if (!data?.length) return <></>;
  return (
    <Table
      className="inner-table"
      pagination={{
        disabled: false,
      }}
      size="small"
      scroll={{ x: '100%' }}
      loading={loading}
      columns={columns}
      dataSource={data}
    />
  );
};

export default forwardRef(TaskLog);
