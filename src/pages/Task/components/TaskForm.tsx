import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import RKPageLoading from '@/components/RKPageLoading';
import BaseContext from '@/Context/BaseContext';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useUserList } from '@/hooks/useUserList';
import TaskLog, { taskRefProp } from '@/pages/Task/components/TaskLog';
import { publishTask, taskInfo, updateTask } from '@/services/oa/task';
import { getRandomId, onSuccessAndGoBack } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProColumns,
  ProForm,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { useLocation, useRequest } from '@umijs/max';
import { Collapse, message } from 'antd';
import React, { useRef } from 'react';
import BasicInfo from './BasicInfo';

const TaskForm: React.FC<WithRouteEditingProps> = ({ id }) => {
  //判断是否为编辑页面,工单状态为通过时整个表单不可编辑
  const formRef = useRef<ProFormInstance>();
  const logRef = useRef<taskRefProp>(null);

  const { pathname } = useLocation();
  const isDetailPage = pathname.includes('/task/details/');
  const isEditPage = pathname.includes('/task/edit/');
  const isAddPage = pathname.includes('/task/add');
  const { userList, loading: userLoading } = useUserList();

  //获取详情
  const { loading, refresh } = useRequest(() => taskInfo({ id }), {
    ready: !isAddPage,
    onSuccess: (res) => {
      formRef?.current?.setFieldsValue({
        ...res,
        carbonCopyScope: res?.carbonCopyScope ? [res.carbonCopyScope] : null,
      });
    },
  });

  // 新建
  const { run: add, loading: addLoading } = useRequest((value) => publishTask(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 修改
  const { run: edit, loading: editLoading } = useRequest((value) => updateTask(value), {
    manual: true,
    onSuccess: (res) => {
      if (res.code === 200) {
        message.success('保存成功！');
        logRef?.current?.logRefresh();
        refresh();
      }
    },
    formatResult: (res) => res,
  });

  const taskDescColumns: ProColumns<API.TaskDescribeReq>[] = [
    {
      title: '日期',
      dataIndex: 'taskDate',
      valueType: 'date',
      width: 200,
      fieldProps: {
        format: 'YYYY-MM-DD',
      },
    },
    {
      title: '描述',
      dataIndex: 'content',
    },
  ];

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <BaseContext.Provider
        value={{
          isDetailPage,
          isEditPage,
          isAddPage,
          formRef,
        }}
      >
        <RKPageLoading loading={loading} />
        <ProForm
          formRef={formRef}
          disabled={isDetailPage}
          submitter={
            isDetailPage
              ? false
              : {
                  searchConfig: {
                    submitText: '保存',
                    resetText: '取消',
                  },
                  onReset: () => {
                    history.go(-1);
                  },

                  submitButtonProps: {
                    loading: addLoading || editLoading,
                  },
                  render: (props, doms) => {
                    return <FooterToolbar>{doms}</FooterToolbar>;
                  },
                }
          }
          onFinish={async (value) => {
            let form = { ...value };
            if (value.carbonCopyScope?.length) {
              form = {
                ...value,
                carbonCopyScope: value.carbonCopyScope[0],
              };
            } else if (value.carbonCopyScope && value.carbonCopyScope.length === 0) {
              delete form.carbonCopyScope;
            }
            if (isEditPage) {
              edit(form);
            } else {
              add(form);
            }
          }}
        >
          <Collapse defaultActiveKey={['1', '2', '3']} ghost>
            <Collapse.Panel key="1" header="任务明细" collapsible="header">
              <BasicInfo />
            </Collapse.Panel>
            {!isAddPage && (
              <Collapse.Panel key="2" header="子任务信息" collapsible="header">
                <ProForm.Item name="children">
                  <ProTable
                    {...defaultTableConfig}
                    scroll={{ x: '100%' }}
                    columns={[
                      { dataIndex: 'taskName', title: '任务名称', width: 150, ellipsis: true },
                      {
                        dataIndex: 'planEndTime',
                        title: '计划完成日期',
                        width: 150,
                        valueType: 'date',
                        ellipsis: true,
                      },
                      {
                        dataIndex: 'executeUserId',
                        title: '任务执行人',
                        width: 150,
                        ellipsis: true,
                        valueType: 'select',
                        fieldProps: {
                          options: userList,
                          loading: userLoading,
                          showSearch: true,
                          fieldNames: {
                            value: 'id',
                            label: 'username',
                          },
                          filterOption: true,
                          optionFilterProp: 'label',
                        },
                      },
                      { dataIndex: 'taskDesc', title: '任务描述', width: 150, ellipsis: true },
                    ]}
                    search={false}
                    options={false}
                    pagination={false}
                    headerTitle={false}
                    cardProps={false}
                    dataSource={formRef.current?.getFieldValue('children') || []}
                  />
                </ProForm.Item>
              </Collapse.Panel>
            )}
            {!isAddPage && (
              <Collapse.Panel key="3" header="任务情况描述" collapsible="header">
                <ProForm.Item
                  name="taskDescribeList"
                  getValueProps={(val) => ({
                    value: val?.map((item: Record<string, any>) => ({
                      ...item,
                      key_: item.key_ || getRandomId(),
                    })),
                  })}
                >
                  <RKFormEditableProTable
                    columns={taskDescColumns}
                    copy={false}
                    readonly={isDetailPage || isAddPage}
                    disabled={isDetailPage || isAddPage}
                  />
                </ProForm.Item>
              </Collapse.Panel>
            )}
            {!isAddPage && (
              <Collapse.Panel key="4" header="任务日志" collapsible="header">
                <TaskLog ref={logRef} />
              </Collapse.Panel>
            )}
          </Collapse>
        </ProForm>
      </BaseContext.Provider>
    </PageContainer>
  );
};

export default withRouteEditing(TaskForm);
