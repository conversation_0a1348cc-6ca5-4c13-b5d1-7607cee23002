import { useUserList } from '@/hooks/useUserList';
import { splitTask, updateChildren } from '@/services/oa/task';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProCard,
  ProFormDatePicker,
  ProFormGroup,
  ProFormInstance,
  ProFormList,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { message, Space } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import dayjs from 'dayjs';
import React, { useRef } from 'react';

const ChildTaskDrawerForm: React.FC<DrawerFormProps> = ({
  initialValues,
  open,
  onOpenChange,
  onFinish,
}) => {
  const formRef = useRef<ProFormInstance>();
  const isEdit = initialValues?.id && initialValues?.parentId !== '0';
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const { userList, loading: userLoading } = useUserList();
  const canEdit =
    (['NOT_STARTED', 'IN_PROGRESS'].includes(initialValues?.taskStatus) &&
      currentUser?.id === initialValues?.executeUserId) ||
    currentUser?.id === initialValues?.launchUserId;
  const GroupComponent = () => (
    <Space>
      <div className="rk-none">
        <ProFormText name="parentId" width="md" initialValue={initialValues?.id} />
      </div>
      <ProFormGroup>
        <ProFormText
          name="taskName"
          width="md"
          label="任务名称"
          rules={[requiredRule]}
          disabled={isEdit}
        />
        <ProFormDatePicker
          width="md"
          name="planEndTime"
          label="计划完成日期"
          rules={[requiredRule]}
          fieldProps={{
            disabledDate: (current) =>
              current && current <= dayjs().subtract(1, 'days').endOf('day'),
          }}
        />
      </ProFormGroup>
      <ProFormGroup>
        <ProFormSelect
          width="md"
          name="executeUserId"
          label="任务执行人"
          placeholder="请输入"
          options={userList as DefaultOptionType[]}
          rules={[requiredRule]}
          fieldProps={{
            loading: userLoading,
            showSearch: true,
            fieldNames: {
              value: 'id',
              label: 'username',
            },
            filterOption: true,
            optionFilterProp: 'label',
          }}
        />
        <ProFormTextArea
          width="md"
          name="taskDesc"
          label="任务描述"
          fieldProps={{
            autoSize: {
              minRows: 1,
              maxRows: 2,
            },
          }}
          rules={[requiredRule]}
        />
      </ProFormGroup>
    </Space>
  );
  return (
    <DrawerForm
      width={800}
      title={`${canEdit ? `${isEdit ? '编辑' : '新建'}子任务` : '子任务详情'}`}
      formRef={formRef}
      open={open}
      disabled={!canEdit}
      initialValues={isEdit ? initialValues : void 0}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const msg = isEdit ? await updateChildren(value) : await splitTask(value.children);
        const success = msg.code === 200;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
      submitter={
        !canEdit
          ? false
          : {
              searchConfig: {
                submitText: '保存',
              },
            }
      }
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
    >
      <div className="rk-none">
        <ProFormText name="id" />
      </div>
      {isEdit || !canEdit ? (
        <GroupComponent />
      ) : (
        <ProFormList
          name="children"
          initialValue={[{ parentId: initialValues?.id }]}
          itemRender={({ listDom, action }, { index }) => (
            <ProCard
              bordered
              style={{ marginBlockEnd: 8 }}
              title={`子任务${index + 1}`}
              extra={action}
              direction="row"
              bodyStyle={{ paddingBlockEnd: 0 }}
            >
              {listDom}
            </ProCard>
          )}
        >
          <GroupComponent />
        </ProFormList>
      )}
    </DrawerForm>
  );
};

export default ChildTaskDrawerForm;
