import RKCol from '@/components/RKCol';
import RK<PERSON>ageHeader from '@/components/RKPageHeader';
import RKPageLoading from '@/components/RKPageLoading';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useDepartment } from '@/hooks/useDepartment';
import { useGradeList } from '@/hooks/useGradeList';
import { usePositionList } from '@/hooks/usePositionList';
import { createRecruit, recruitInfo, updateRecruit } from '@/services/oa/recruit';
import { onSuccessAndGoBack } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useAccess, useLocation, useModel, useRequest } from '@umijs/max';
import { message, Row, Typography } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import dayjs from 'dayjs';
import { useCallback, useEffect, useRef } from 'react';
import { GENDER, TOP_EDUCATION } from '../../Employees/components/EmployeesEnums';

const RecruitmentApplicationDetail: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  const formRef = useRef<ProFormInstance>();
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const { departmentList, loading: departmentLoading } = useDepartment();
  const { gradeList, loading: gradeLoading } = useGradeList();
  const { canAddRecruitmentApplication, canEditRecruitmentApplication } = useAccess();
  const { approvalDetails } = useModel('useApprovalModel');
  const codeRef = useRef(0);
  const { positionList, loading: positionLoading } = usePositionList();

  // 判断是否为审批页面
  const { pathname = '' } = useLocation();
  const isApprovalPage = pathname.includes('/approval/');

  // 新建
  const { run: add, loading: addLoading } = useRequest((value) => createRecruit(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 修改
  const { run: update, loading: editLoading } = useRequest((value) => updateRecruit(value), {
    manual: true,
    formatResult: (res) => res,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      codeRef.current = res.code;
      message.success('保存成功！');
    },
  });

  const onSave = useCallback(() => {
    return new Promise((resolve) => {
      codeRef.current = 0;
      formRef.current?.submit();

      const checkFormInterval = setInterval(() => {
        formRef.current
          ?.validateFields()
          .then(() => {
            const isFormSubmitted = codeRef.current === 200;
            if (isFormSubmitted) {
              clearInterval(checkFormInterval); // 清除轮询
              resolve(200); // 表单提交成功，resolve Promise
            }
          })
          .catch(() => {
            resolve(500);
            clearInterval(checkFormInterval); // 清除轮询
          });
      }, 1000); // 每秒检查一次表单是否提交成功
    });
  }, []);

  // 获取详情
  const {
    data = {},
    loading,
    refresh,
  } = useRequest(() => recruitInfo({ id }), {
    ready: isEditPage && !isApprovalPage,
    onSuccess: (res) => {
      setTimeout(() => {
        formRef.current?.setFieldsValue(res);
      }, 500);
    },
  });

  useEffect(() => {
    if (isApprovalPage) formRef.current?.setFieldsValue(approvalDetails?.fromData);
  }, [isApprovalPage, approvalDetails]);
  // 是否提交审批
  const isSubmit = (data?.activiStatus && data?.activiStatus !== '0') || false;

  const canEdit =
    (canEditRecruitmentApplication && isEditPage) || (!isEditPage && canAddRecruitmentApplication);

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <RKPageLoading loading={loading} />
      <ProForm<API.RecruitInfoResp>
        formRef={formRef}
        initialValues={{
          applicantName: currentUser?.username,
          applicantId: currentUser?.id,
          applicationTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        }}
        disabled={isSubmit || isApprovalPage || !canEdit}
        submitter={
          !isSubmit &&
          !isApprovalPage &&
          canEdit && {
            searchConfig: {
              submitText: '保存',
              resetText: '取消',
            },
            onReset: () => {
              history.go(-1);
            },

            render: (props, doms) => {
              return <FooterToolbar>{doms}</FooterToolbar>;
            },
            submitButtonProps: {
              loading: addLoading || editLoading,
            },
            resetButtonProps: {
              disabled: false,
            },
          }
        }
        onFinish={async (values) => {
          if (isEditPage) {
            update(values);
          } else {
            add(values);
          }
        }}
      >
        {isEditPage && (
          <ProFormDependency name={['documentNumber', 'activiStatus']}>
            {({ activiStatus, documentNumber }) => {
              return (
                <RKPageHeader
                  id={id}
                  status={
                    isApprovalPage
                      ? (approvalDetails?.activiStatus as unknown as string) || '9'
                      : activiStatus
                  }
                  title={documentNumber}
                  approveType="RECRUITMENT_APPLICATION"
                  onOperationCallback={refresh}
                  onSave={onSave}
                  saveDisabled={!canEditRecruitmentApplication}
                />
              );
            }}
          </ProFormDependency>
        )}
        <div className="rk-none">
          <ProFormText name="id" />
          <ProFormText name="applicantId" />
          <ProFormText name="documentNumber" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText disabled name="applicantName" label="申请人" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormDatePicker
              disabled
              name="applicationTime"
              label="申请日期"
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker
              name="expectationTime"
              label="期望到岗日期"
              rules={[requiredRule]}
              transform={(value, namePath) => {
                return {
                  [namePath]: dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
                };
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="positionNameId"
              label="需求岗位名称"
              rules={[requiredRule]}
              fieldProps={{
                loading: positionLoading,
                fieldNames: {
                  value: 'id',
                  label: 'positionName',
                },
                showSearch: true,
                onChange: (val, option) => {
                  const { departmentId } = option as { departmentId?: string };
                  formRef.current?.setFieldValue('affiliationDepartmentId', departmentId);
                },
              }}
              options={positionList as DefaultOptionType[]}
            />
          </RKCol>
          <RKCol>
            <ProFormDigit
              name="positionNum"
              label="需求人数"
              min={0}
              fieldProps={{
                autoComplete: 'none',
                addonAfter: '位',
              }}
              rules={[requiredRule]}
              transform={(val, namePath) => ({ [namePath]: String(val) })}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              disabled
              name="affiliationDepartmentId"
              label="隶属部门"
              rules={[requiredRule]}
              fieldProps={{
                loading: departmentLoading,
                fieldNames: {
                  value: 'id',
                  label: 'departmentName',
                },
                showSearch: true,
              }}
              options={departmentList as DefaultOptionType[]}
            />
          </RKCol>

          <RKCol>
            <ProFormSelect
              name="demandGrade"
              label="需求岗位等级"
              rules={[requiredRule]}
              fieldProps={{
                loading: gradeLoading,
                fieldNames: {
                  value: 'id',
                  label: 'grade',
                },
                showSearch: true,
              }}
              options={gradeList as DefaultOptionType[]}
            />
          </RKCol>
          <RKCol>
            <ProFormText name="territory" label="工作属地" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="applicationCause"
              label="申请原因"
              rules={[requiredRule]}
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 3,
                },
              }}
            />
          </RKCol>
        </Row>
        <Typography.Title level={5} style={{ marginBlock: 16 }}>
          职位要求
        </Typography.Title>
        <Row gutter={24}>
          <RKCol>
            <ProFormSelect name="gender" label="性别" placeholder="请输入" options={GENDER} />
          </RKCol>
          <RKCol>
            <ProFormText name="age" label="年龄" placeholder="请输入" />
          </RKCol>
          <RKCol>
            <ProFormText name="wages" label="薪酬" placeholder="请输入" />
          </RKCol>
          <RKCol>
            <ProFormText name="industryBack" label="行业背景" placeholder="请输入" />
          </RKCol>
          <RKCol>
            <ProFormText name="workYears" label="工作年限" placeholder="请输入" />
          </RKCol>
          <RKCol>
            <ProFormText name="frequency" label="跳槽频率" placeholder="请输入" />
          </RKCol>
          <RKCol>
            <ProFormSelect name="degree" label="学历" options={TOP_EDUCATION} />
          </RKCol>
          <RKCol>
            <ProFormText name="specialized" label="专业" placeholder="请输入" />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="priorityCondition"
              label="优先条件"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 3,
                },
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="chenCheng"
              label="其他补充要求"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 3,
                },
              }}
            />
          </RKCol>
        </Row>
        <Typography.Title level={5} style={{ marginBlock: 16 }}>
          招聘需求
        </Typography.Title>
        <ProFormTextArea
          name="recruitDemand"
          label="招聘需求"
          rules={[requiredRule]}
          fieldProps={{
            autoSize: {
              minRows: 3,
              maxRows: 8,
            },
          }}
        />
        <Typography.Title level={5} style={{ marginBlock: 16 }}>
          岗位职责
        </Typography.Title>
        <ProFormTextArea
          name="duty"
          label="岗位职责"
          fieldProps={{
            autoSize: {
              minRows: 3,
              maxRows: 8,
            },
          }}
          rules={[requiredRule]}
        />
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(RecruitmentApplicationDetail);
