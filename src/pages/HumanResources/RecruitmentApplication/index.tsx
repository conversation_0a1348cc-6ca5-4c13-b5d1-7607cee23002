import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { APPROVAL_STATUS } from '@/enums';
import { useDepartment } from '@/hooks/useDepartment';
import { useGradeList } from '@/hooks/useGradeList';
import { usePositionList } from '@/hooks/usePositionList';
import { recruitDel, recruitPage } from '@/services/oa/recruit';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, history, useAccess, useLocation, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import React, { useRef, useState } from 'react';

const RecruitmentApplication: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const { state } = useLocation();
  const [selectedRows, setSelectedRows] = useState<API.RecruitInfoResp[]>([]);
  const { canAddRecruitmentApplication = false, canDeleteRecruitmentApplication = false } =
    useAccess();
  const { gradeList, loading: gradeLoading } = useGradeList();
  const { departmentList, loading: departmentLoading } = useDepartment();
  const { positionList, loading: positionLoading } = usePositionList();
  //删除
  const { run: deleteRecord } = useRequest((ids) => recruitDel({ ids: ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: any[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(String(item.id)!);
      names.push(item.documentNumber!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.RecruitInfoResp>[] = [
    {
      title: '编号',
      dataIndex: 'documentNumber',
      width: 180,
      copyable: true,
      initialValue: state,
      fixed: 'left',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() =>
              history.push(`/human-resources/recruitment-application/details/${entity.id}`)
            }
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '申请人',
      dataIndex: 'applicantName',
      width: 100,
    },
    {
      title: '申请日期',
      valueType: 'date',
      dataIndex: 'applicationTime',
      search: false,
      width: 100,
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      width: 100,
      hideInSearch: true,
      valueEnum: option2enum(APPROVAL_STATUS),
    },
    {
      title: '期望到岗日期',
      valueType: 'date',
      dataIndex: 'expectationTime',
      search: false,
      width: 100,
    },
    {
      title: '需求岗位名称',
      dataIndex: 'positionNameId',
      width: 100,
      ellipsis: true,
      search: false,
      valueType: 'select',
      fieldProps: {
        loading: positionLoading,
        fieldNames: {
          value: 'id',
          label: 'positionName',
        },
        showSearch: true,
        options: positionList as DefaultOptionType[],
      },
    },
    {
      title: '需求人数',
      dataIndex: 'positionNum',
      search: false,
      width: 80,
    },
    {
      title: '隶属部门',
      width: 100,
      dataIndex: 'affiliationDepartmentId',
      search: false,
      valueType: 'select',
      fieldProps: {
        loading: departmentLoading,
        fieldNames: {
          value: 'id',
          label: 'departmentName',
        },
        showSearch: true,
        options: departmentList as DefaultOptionType[],
      },
    },
    {
      title: '需求岗位等级',
      width: 100,
      dataIndex: 'demandGrade',
      search: false,
      valueType: 'select',
      fieldProps: {
        loading: gradeLoading,
        fieldNames: {
          value: 'id',
          label: 'grade',
        },
        showSearch: true,
        options: gradeList as DefaultOptionType[],
      },
    },
    {
      width: 100,
      title: '工作属地',
      dataIndex: 'territory',
      search: false,
    },
    {
      title: '申请原因',
      width: 150,
      ellipsis: true,
      dataIndex: 'applicationCause',
      search: false,
    },

    {
      title: '操作',
      fixed: 'right',
      width: 100,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !canDeleteRecruitmentApplication,
      render: (text, record) => {
        const { activiStatus } = record;
        return (
          <Access accessible={canDeleteRecruitmentApplication}>
            {activiStatus === '0' && <a onClick={() => handleDelete([record])}>删除</a>}
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.RecruitInfoResp>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="招聘申请列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddRecruitmentApplication}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  history.push('/human-resources/recruitment-application/add');
                }}
              >
                新建招聘申请
              </Button>
            </Access>,
          ],
        }}
        rowSelection={
          canDeleteRecruitmentApplication && {
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRows(selectedRows);
            },
            getCheckboxProps: (record) => ({
              disabled: record?.activiStatus !== '0',
            }),
          }
        }
        polling={5000}
        request={async (params) => queryPagingTable(params, recruitPage)}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default RecruitmentApplication;
