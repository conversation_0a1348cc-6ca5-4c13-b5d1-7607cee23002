import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { usePositionList } from '@/hooks/usePositionList';
import { talentDel, talentPage } from '@/services/oa/talent';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, history, useAccess, useLocation, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import React, { useRef, useState } from 'react';
import { TOP_EDUCATION } from '../Employees/components/EmployeesEnums';

const TalentPool: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const { state } = useLocation();
  const [selectedRows, setSelectedRows] = useState<API.TalentInfoResp[]>([]);
  const { canAddTalentPool = false, canDeleteTalentPool = false } = useAccess();
  const { positionList, loading: positionLoading } = usePositionList();

  //删除
  const { run: deleteRecord } = useRequest((ids) => talentDel({ ids: ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: API.TalentInfoResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(String(item.id)!);
      names.push(item.username!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.TalentInfoResp>[] = [
    {
      title: '姓名',
      dataIndex: 'username',
      width: 120,
      copyable: true,
      initialValue: state,
      fixed: 'left',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/human-resources/talent-pool/details/${entity.id}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '主要技能',
      dataIndex: 'keySkill',
      width: 180,
      ellipsis: true,
    },
    {
      title: '应聘职位',
      dataIndex: 'positionId',
      search: false,
      valueType: 'select',
      fieldProps: {
        loading: positionLoading,
        fieldNames: {
          value: 'id',
          label: 'positionName',
        },
        showSearch: true,
        options: positionList as DefaultOptionType[],
      },
      width: 120,
    },
    {
      title: '联系方式',
      dataIndex: 'contactNumber',
      search: false,
      width: 110,
      ellipsis: true,
    },
    {
      title: '学历',
      dataIndex: 'degree',
      valueEnum: option2enum(TOP_EDUCATION),
      ellipsis: true,
      search: false,
      width: 80,
    },
    {
      title: '证书',
      dataIndex: 'certificate',
      search: false,
      width: 120,
      ellipsis: true,
    },
    {
      title: '专业',
      dataIndex: 'specialized',
      search: false,
      width: 120,
      ellipsis: true,
    },
    {
      title: '毕业院校',
      dataIndex: 'graduateSchool',
      search: false,
      ellipsis: true,
      width: 120,
    },
    {
      title: '简历筛选结果',
      dataIndex: 'filterTheResults',
      search: false,
      ellipsis: true,
      width: 120,
    },
    {
      title: '初面结果',
      dataIndex: 'introductoryOutcome',
      search: false,
      ellipsis: true,
      width: 120,
    },
    {
      title: '笔试结果',
      dataIndex: 'writtenOutcome',
      search: false,
      ellipsis: true,
      width: 120,
    },

    {
      title: '操作',
      fixed: 'right',
      width: 100,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !canDeleteTalentPool,
      render: (text, record) => {
        return (
          <Access accessible={canDeleteTalentPool}>
            <a onClick={() => handleDelete([record])}>删除</a>
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.TalentInfoResp>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="人才库列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddTalentPool}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  history.push('/human-resources/talent-pool/add');
                }}
              >
                新建人才信息
              </Button>
            </Access>,
          ],
        }}
        rowSelection={
          canDeleteTalentPool && {
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRows(selectedRows);
            },
          }
        }
        polling={5000}
        request={async (params) => queryPagingTable(params, talentPage)}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default TalentPool;
