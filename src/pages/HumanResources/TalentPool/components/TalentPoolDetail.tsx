import BasicUpload, { UploaderRef } from '@/components/BasicUpload';
import RKCol from '@/components/RKCol';
import RKPageLoading from '@/components/RKPageLoading';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { usePositionList } from '@/hooks/usePositionList';
import { createTalent, talentInfo, updateTalent } from '@/services/oa/talent';
import { getRandomId, onSuccessAndGoBack } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDatePicker,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useAccess, useRequest } from '@umijs/max';
import { message, Row } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { UploadFileStatus } from 'antd/es/upload/interface';
import dayjs from 'dayjs';
import { useRef } from 'react';
import { GENDER, TOP_EDUCATION } from '../../Employees/components/EmployeesEnums';

const TalentPoolDetail: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  const formRef = useRef<ProFormInstance>();
  const { canAddTalentPool, canEditTalentPool } = useAccess();
  const codeRef = useRef(0);
  const { positionList, loading: positionLoading } = usePositionList();
  const uploadRef = useRef<UploaderRef>(null);

  // 新建
  const { run: add, loading: addLoading } = useRequest((value) => createTalent(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 修改
  const { run: update, loading: editLoading } = useRequest((value) => updateTalent(value), {
    manual: true,
    formatResult: (res) => res,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      codeRef.current = res.code;
      message.success('保存成功！');
    },
  });

  // 获取详情
  const { loading } = useRequest(
    () =>
      talentInfo({
        id,
      }),
    {
      ready: isEditPage,
      onSuccess: (res) => {
        const { resumeAnnex } = res as API.TalentInfoResp;
        if (resumeAnnex?.length !== 0) {
          const file = {
            uid: getRandomId(),
            name: '',
            url: res?.resumeAnnex,
            thumbUrl: res?.resumeAnnex,
            status: 'done' as UploadFileStatus,
          };
          uploadRef.current?.fileList.push(file);
        }

        setTimeout(() => {
          formRef.current?.setFieldsValue({
            ...res,
            age: dayjs(dayjs().format('YYYY-MM-DD')).diff(res?.dateOfBirth, 'year').toString(),
          });
        }, 500);
      },
    },
  );

  const canEdit = (canEditTalentPool && isEditPage) || (!isEditPage && canAddTalentPool);

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <RKPageLoading loading={loading} />
      <ProForm<API.PaymentApplicationInsertReq>
        formRef={formRef}
        disabled={!canEdit}
        submitter={
          canEdit && {
            searchConfig: {
              submitText: '保存',
              resetText: '取消',
            },
            onReset: () => {
              history.go(-1);
            },

            render: (props, doms) => {
              return <FooterToolbar>{doms}</FooterToolbar>;
            },
            submitButtonProps: {
              loading: addLoading || editLoading,
            },
            resetButtonProps: {
              disabled: false,
            },
          }
        }
        onFinish={async (values) => {
          const formData = {
            ...values,
            resumeAnnex: uploadRef.current?.fileList?.at(0)?.thumbUrl ?? '',
          };
          if (isEditPage) {
            update(formData);
          } else {
            add(formData);
          }
        }}
        onValuesChange={(val) => {
          if (val.dateOfBirth) {
            formRef.current?.setFieldValue(
              'age',
              dayjs(dayjs().format('YYYY-MM-DD')).diff(val.dateOfBirth, 'year').toString(),
            );
          }
        }}
      >
        <div className="rk-none">
          <ProFormText name="id" />
          <ProFormText name="userId" />
          <ProFormText name="documentNumber" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText name="username" label="姓名" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormSelect name="gender" label="性别" options={GENDER} rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormDatePicker
              name="dateOfBirth"
              label="出生年月"
              transform={(value, namePath) => ({
                [namePath]: dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
              })}
            />
          </RKCol>
          <RKCol>
            <ProFormText disabled name="age" label="年龄" />
          </RKCol>
          <RKCol>
            <ProFormText name="contactNumber" label="联系方式" />
          </RKCol>
          <RKCol>
            <ProFormText
              name="email"
              label="邮箱"
              rules={[
                {
                  type: 'email',
                  message: '格式不正确',
                },
              ]}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="degree"
              label="学历"
              options={TOP_EDUCATION}
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="keySkill"
              label="主要技能"
              rules={[requiredRule]}
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 2,
                },
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormText name="certificate" label="证书" />
          </RKCol>
          <RKCol>
            <ProFormText name="specialized" label="专业" />
          </RKCol>
          <RKCol>
            <ProFormText name="graduateSchool" label="毕业院校" />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="positionId"
              label="应聘职位"
              fieldProps={{
                loading: positionLoading,
                fieldNames: {
                  value: 'id',
                  label: 'positionName',
                },
                showSearch: true,
                onChange: (val, option) => {
                  const { departmentId } = option as { departmentId?: string };
                  formRef.current?.setFieldValue('affiliationDepartmentId', departmentId);
                },
              }}
              options={positionList as DefaultOptionType[]}
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker
              name="deliveryDate"
              label="简历投递日期"
              transform={(value, namePath) => ({
                [namePath]: dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
              })}
            />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="filterTheResults"
              label="简历筛选结果"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 2,
                },
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker
              name="introductoryTime"
              label="初面时间"
              transform={(value, namePath) => ({
                [namePath]: dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
              })}
            />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="introductoryOutcome"
              label="初面结果"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 2,
                },
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker
              name="writtenTime"
              label="笔试时间"
              transform={(value, namePath) => ({
                [namePath]: dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
              })}
            />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="writtenOutcome"
              label="笔试结果"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 2,
                },
              }}
            />
          </RKCol>
        </Row>
        <ProForm.Item name="resumeAnnex" label="简历附件(目前仅支持图片和pdf格式的文件)">
          <BasicUpload multiple={false} fileCount={1} ref={uploadRef} />
        </ProForm.Item>
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(TalentPoolDetail);
