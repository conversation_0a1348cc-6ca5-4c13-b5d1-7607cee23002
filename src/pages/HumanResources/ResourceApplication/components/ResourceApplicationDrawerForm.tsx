import RKPageHeader from '@/components/RKPageHeader';
import {
  createFirmResource,
  firmResourceInfo,
  updateFirmResource,
} from '@/services/oa/firmResource';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProForm,
  ProFormDateTimePicker,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useAccess, useModel, useRequest } from '@umijs/max';
import { message } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';

const ResourceApplicationDrawerForm: React.FC<
  DrawerFormProps & { treeData: API.DepartmentTreeResp[] }
> = ({ open, onOpenChange, onFinish, initialValues, treeData }) => {
  const isEditPage = !!initialValues?.id;
  const { canEditResourceApplication, canAddResourceApplication } = useAccess();
  const canEdit =
    (canEditResourceApplication && isEditPage) || (!isEditPage && canAddResourceApplication);
  const formRef = useRef<ProFormInstance>();

  // 防止数据错乱
  const [data, setData] = useState<API.LeaveAppInfoResp>(); // 初始化 data 为 null

  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  // 获取详情
  const { run } = useRequest(() => firmResourceInfo({ id: initialValues?.id }), {
    manual: true,
    onSuccess: (res) => {
      setTimeout(() => formRef.current?.setFieldsValue(res), 500);
      setData(res);
    },
    cacheKey: Date.now(),
  });

  useEffect(() => {
    if (isEditPage && open) {
      run();
    }
  }, [isEditPage, open]);

  // 重置数据
  useEffect(() => {
    if (open === false) {
      setData({});
    }
  }, [open]);

  // 页面是否禁止编辑
  const isDisabled = (isEditPage && data?.activiStatus !== '0') || !canEdit;
  const codeRef = useRef(0);
  const onSave = useCallback(() => {
    return new Promise((resolve) => {
      codeRef.current = 0;
      formRef.current?.submit();

      const checkFormInterval = setInterval(() => {
        formRef.current
          ?.validateFields()
          .then(() => {
            const isFormSubmitted = codeRef.current === 200;
            if (isFormSubmitted) {
              clearInterval(checkFormInterval); // 清除轮询
              resolve(200); // 表单提交成功，resolve Promise
            }
          })
          .catch(() => {
            resolve(500);
            clearInterval(checkFormInterval); // 清除轮询
          });
      }, 1000); // 每秒检查一次表单是否提交成功
    });
  }, []);

  return (
    <DrawerForm<API.FirmResourceInsertReq & { id?: string }>
      width={753}
      title={isEditPage ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      initialValues={{
        employee: currentUser?.employeeNumber,
        employeeName: currentUser?.username,
      }}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const { id } = value;
        const formData = {
          ...value,
          id,
        };

        const msg = isEditPage
          ? await updateFirmResource(formData)
          : await createFirmResource(formData);
        const success = msg.code === 200;
        codeRef.current = msg.code!;
        if (success) {
          message.success('保存成功!');
          onFinish?.(value);
        }
        return isEditPage ? false : success;
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      disabled={isDisabled}
      submitter={
        !isDisabled
          ? {
              searchConfig: {
                submitText: '保存',
                resetText: '取消',
              },
            }
          : false
      }
    >
      <div className="rk-none">
        <ProFormText name="id" />
      </div>
      {isEditPage && (
        <RKPageHeader
          id={initialValues?.id}
          status={data?.activiStatus}
          title={data?.documentNumber}
          pageHeaderType="drawer"
          approveType="RESOURCE_APPLICATION"
          onOperationCallback={() => {
            run();
          }}
          onSave={onSave}
          saveDisabled={!canEditResourceApplication}
        />
      )}
      <ProForm.Group>
        <ProFormText
          disabled
          name="employee"
          label="员工编号"
          fieldProps={{
            autoComplete: 'none',
          }}
          width="md"
        />
        <ProFormText
          disabled
          name="employeeName"
          label="员工姓名"
          fieldProps={{
            autoComplete: 'none',
          }}
          rules={[requiredRule]}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText
          name="content"
          label="申请内容"
          fieldProps={{
            autoComplete: 'none',
          }}
          rules={[requiredRule]}
          width="md"
        />
        <ProFormSelect
          name="resourceDepartment"
          label="资源所属部门"
          rules={[requiredRule]}
          width="md"
          options={treeData
            ?.filter((item) => item.status === '1')
            ?.map((item) => ({
              label: item.departmentName,
              value: item.id,
            }))}
          fieldProps={{
            showSearch: true,
          }}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDateTimePicker
          name="useTime"
          label="预计使用时间"
          rules={[requiredRule]}
          fieldProps={{
            format: (value) => value.format('YYYY-MM-DD HH:mm:ss'),
          }}
          width="md"
        />
        <ProFormDateTimePicker
          name="stillTime"
          label="预计归还时间"
          rules={[requiredRule]}
          fieldProps={{
            format: (value) => value.format('YYYY-MM-DD HH:mm:ss'),
          }}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormTextArea
          name="useCause"
          label="使用原因"
          placeholder="请输入使用原因"
          width="md"
          fieldProps={{
            autoSize: {
              minRows: 1,
              maxRows: 6,
            },
          }}
        />
      </ProForm.Group>
    </DrawerForm>
  );
};
export default ResourceApplicationDrawerForm;
