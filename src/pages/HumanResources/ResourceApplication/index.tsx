import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { APPROVAL_STATUS, BORROW_STATUS } from '@/enums';
import { getDepartmentTree } from '@/services/oa/department';
import { confirmFirmResource, firmResourceDel, firmResourcePage } from '@/services/oa/firmResource';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  ModalForm,
  PageContainer,
  ProColumns,
  ProFormDateTimePicker,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { Access, useAccess, useModel, useRequest } from '@umijs/max';
import { Button, Form, message, Modal, Space } from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';
import ResourceApplicationDrawerForm from './components/ResourceApplicationDrawerForm';

function compareDateString(dateStr1: string, dateStr2: string): boolean {
  const date1 = dayjs(dateStr1);
  const date2 = dayjs(dateStr2);

  if (date1.isValid() && date2.isValid()) {
    // 比较两个 Day.js 实例
    if (date1.isBefore(date2)) {
      return true; // date1 在 date2 之前
    } else {
      return false;
    }
  } else {
    return false;
  }
}

const ResourceApplication: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.FirmResourceInfoResp[]>([]);
  const [modalVisit, setModalVisit] = useState(false);
  const [showReason, setShowReason] = useState(false);
  const id = useRef<string>();
  const [form] = Form.useForm();
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const { canAddResourceApplication = false, canDeleteResourceApplication = false } = useAccess();
  const { data: treeData = [] } = useRequest(() => getDepartmentTree());
  const { run: deleteRecord } = useRequest((ids) => firmResourceDel({ ids: ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: API.FirmResourceInfoResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(String(item.id)!);
      names.push(item.documentNumber!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  const { run: returnResource } = useRequest(
    (record, values) =>
      confirmFirmResource({
        ...values,
        id: record.id,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('归还成功');
        tableRef.current?.reloadAndRest?.();
      },
      formatResult: (res) => res,
    },
  );

  // 表格
  const columns: ProColumns<API.FirmResourceInfoResp>[] = [
    {
      title: '编号',
      dataIndex: 'documentNumber',
      copyable: true,
      ellipsis: true,
      width: 180,
      render: (dom, entity) => (
        <a
          className="rk-a-span"
          onClick={() => {
            {
              setModalVisit(true);
              id.current = entity.id!;
            }
          }}
        >
          {dom}
        </a>
      ),
    },
    {
      title: '申请人',
      dataIndex: 'employeeName',
    },
    {
      title: '申请内容',
      dataIndex: 'content',
    },
    {
      title: '资源所属部门',
      dataIndex: 'resourceDepartmentName',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '资源所属部门',
      dataIndex: 'resourceDepartment',
      valueType: 'select',
      hideInTable: true,
      fieldProps: () => ({
        options: treeData
          ?.filter((item: API.DepartmentTreeResp) => item.status === '1')
          ?.map((item: API.DepartmentTreeResp) => ({
            label: item.departmentName,
            value: item.id,
          })),
        showSearch: true,
        optionFilterProp: 'label',
        filterOption: true,
      }),
    },
    {
      title: '预计使用时间',
      dataIndex: 'useTime',
      hideInSearch: true,
    },
    {
      title: '预计归还时间',
      dataIndex: 'stillTime',
      hideInSearch: true,
    },
    {
      title: '使用原因',
      dataIndex: 'useCause',
      hideInSearch: true,
    },
    {
      title: '最终归还时间',
      dataIndex: 'ultimatelyStillTime',
      hideInSearch: true,
    },
    {
      title: '逾期原因',
      dataIndex: 'lateCause',
      hideInSearch: true,
    },
    {
      title: '借用状态',
      dataIndex: 'causeStatus',
      valueEnum: option2enum(BORROW_STATUS),
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
    },
    {
      title: '操作',
      width: 140,
      fixed: 'right',
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        const { activiStatus, causeStatus } = record;
        return (
          <Space>
            <Access accessible={canDeleteResourceApplication}>
              {activiStatus === '0' && (
                <Button
                  type="link"
                  className="inner-table-link"
                  key="delete"
                  onClick={() => handleDelete([record])}
                >
                  删除
                </Button>
              )}
            </Access>
            {causeStatus === 'BORROWING' &&
              treeData
                .find((i) => i.id === record.resourceDepartment)
                ?.leaders?.some((i) => i.id === currentUser?.id) && (
                <ModalForm<{
                  ultimatelyStillTime: string;
                  lateCause: string;
                }>
                  title="确认归还"
                  trigger={
                    <Button type="link" className="inner-table-link" key="return">
                      确认归还
                    </Button>
                  }
                  form={form}
                  autoFocusFirstInput
                  modalProps={{
                    destroyOnClose: true,
                  }}
                  submitTimeout={2000}
                  onFinish={async (values) => {
                    returnResource(record, values);
                    return true;
                  }}
                >
                  <ProFormDateTimePicker
                    name="ultimatelyStillTime"
                    label="最终归还时间"
                    rules={[requiredRule]}
                    fieldProps={{
                      format: (value) => value.format('YYYY-MM-DD HH:mm:ss'),
                      onChange: (_, dateString) => {
                        if (dateString) {
                          const reasonBool = compareDateString(
                            record.stillTime as string,
                            dateString,
                          );
                          setShowReason(reasonBool);
                        }
                      },
                    }}
                  />
                  {showReason && (
                    <ProFormTextArea
                      className="ant-picker"
                      name="lateCause"
                      label="逾期原因"
                      placeholder="请输入逾期原因"
                    />
                  )}
                </ModalForm>
              )}
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.FirmResourceInfoResp>
        {...defaultTableConfig}
        actionRef={tableRef}
        search={{
          labelWidth: 100,
          filterType: 'query',
          defaultCollapsed: false,
        }}
        rowSelection={
          canDeleteResourceApplication
            ? {
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
                getCheckboxProps: (record) => ({
                  disabled: record?.activiStatus !== '0',
                }),
              }
            : false
        }
        columns={columns}
        headerTitle="资源使用申请列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddResourceApplication}>
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  id.current = '';
                  setModalVisit(true);
                }}
              >
                新建资源使用申请
              </Button>
            </Access>,
          ],
        }}
        polling={5000}
        request={async (params) => {
          const {
            documentNumber,
            employeeName,
            content,
            activiStatus,
            causeStatus,
            resourceDepartment,
            current,
            pageSize,
          } = params;
          const search = { documentNumber, employeeName, content };
          const filter = { resourceDepartment, activiStatus, causeStatus };
          return queryPagingTable<API.PageReq>(
            { current, pageSize, search, filter },
            firmResourcePage,
          );
        }}
      />
      <ResourceApplicationDrawerForm
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
        initialValues={{ id: id.current }}
        treeData={treeData}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default ResourceApplication;
