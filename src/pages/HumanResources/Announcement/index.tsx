import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { APPROVAL_STATUS } from '@/enums';
import AnnouncementDrawerForm from '@/pages/HumanResources/Announcement/components/AnnouncementDrawerForm';
import {
  announcementInfo,
  announcementPage,
  deleteAnnouncement,
  publishAnnouncement,
} from '@/services/oa/announcement';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import React, { useRef, useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

const TalentPool: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.AnnouncementPageResp[]>([]);
  const [modalVisit, setModalVisit] = useState(false);
  const [checkModalVisit, setCheckModalVisit] = useState(false);
  const [previewContent, setPreviewContent] = useState('');
  const id = useRef<string>();
  const {
    canAddAnnouncement = false,
    canEditAnnouncement = false,
    canDeleteAnnouncement = false,
    canPublishAnnouncement = false,
  } = useAccess();

  //删除
  const { run: deleteRecord } = useRequest((ids) => deleteAnnouncement({ ids: ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  //发布
  const { run: handlePublic, fetches } = useRequest((id) => publishAnnouncement({ id }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('发布成功');
      tableRef.current?.reload?.();
    },
    formatResult: (res) => res,
    fetchKey: (params) => params,
  });
  const handleDelete = async (rows: API.AnnouncementPageResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(String(item.id)!);
      names.push(item.title!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };
  // 获取详情
  const { run: getInfo } = useRequest((id) => announcementInfo({ id }), {
    manual: true,
    onSuccess: (res) => {
      setPreviewContent(res?.content || '');
      setCheckModalVisit(true);
    },
    cacheKey: Date.now(),
  });

  // 表格
  const columns: ProColumns<API.AnnouncementPageResp>[] = [
    {
      title: '标题',
      dataIndex: 'title',
      fixed: 'left',
      render(dom, entity) {
        return canEditAnnouncement ? (
          <a
            className="rk-a-span"
            onClick={() => {
              id.current = entity.id!;
              setModalVisit(true);
            }}
          >
            {dom}
          </a>
        ) : (
          <span>{dom}</span>
        );
      },
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      width: 120,
      valueEnum: option2enum(APPROVAL_STATUS),
    },
    {
      title: '发布时间',
      dataIndex: 'releasedTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      fixed: 'right',
      width: 150,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (_, record) => {
        const { id } = record;
        return (
          <Space>
            <a
              className="rk-a-span"
              onClick={() => {
                getInfo(record?.id);
              }}
            >
              查看内容
            </a>
            <Access accessible={canDeleteAnnouncement}>
              {record?.activiStatus !== '1' && <a onClick={() => handleDelete([record])}>删除</a>}
            </Access>
            <Access accessible={canPublishAnnouncement}>
              {record?.activiStatus === '2' && !record?.releasedTime && (
                <Button
                  type="link"
                  className="inner-table-link"
                  loading={fetches?.[id!]?.loading}
                  onClick={() => handlePublic(record.id)}
                >
                  发布
                </Button>
              )}
            </Access>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.AnnouncementPageResp>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="公告列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddAnnouncement}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  id.current = '';
                  setModalVisit(true);
                }}
              >
                新建公告
              </Button>
            </Access>,
          ],
        }}
        rowSelection={
          canDeleteAnnouncement && {
            onChange: (_, selectedRows) => {
              setSelectedRows(selectedRows);
            },
          }
        }
        polling={5000}
        request={async (params) => queryPagingTable(params, announcementPage)}
      />
      <AnnouncementDrawerForm
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
        initialValues={{ id: id.current }}
      />
      <Modal
        width="40%"
        title="公告内容"
        open={checkModalVisit}
        onCancel={() => {
          setPreviewContent('');
          setCheckModalVisit(false);
        }}
        footer={null}
        destroyOnClose
        forceRender
        centered
        bodyStyle={{
          maxHeight: 'calc(100vh - 200px)',
          overflow: 'auto',
        }}
      >
        <div style={{ height: 'auto' }}>
          <ReactQuill
            value={previewContent}
            readOnly={true}
            theme="snow"
            modules={{
              toolbar: false,
            }}
            style={{
              height: 'auto',
            }}
          />
        </div>
      </Modal>
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default TalentPool;
