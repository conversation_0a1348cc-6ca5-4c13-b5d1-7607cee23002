import RKPageHeader from '@/components/RKPageHeader';
import {
  announcementInfo,
  createAnnouncement,
  updateAnnouncement,
} from '@/services/oa/announcement';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProForm,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { useAccess, useRequest } from '@umijs/max';
import { message } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

const AnnouncementDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  onFinish,
  initialValues,
}) => {
  const isEditPage = !!initialValues?.id;
  const { canEditResourceApplication, canAddResourceApplication } = useAccess();
  const canEdit =
    (canEditResourceApplication && isEditPage) || (!isEditPage && canAddResourceApplication);
  const formRef = useRef<ProFormInstance>();
  const quillRef = useRef<ReactQuill>(null);

  // 防止数据错乱
  const [data, setData] = useState<API.AnnouncementPageResp>(); // 初始化 data 为 null

  // 获取详情
  const { run } = useRequest(() => announcementInfo({ id: initialValues?.id }), {
    manual: true,
    onSuccess: (res) => {
      setTimeout(() => formRef.current?.setFieldsValue(res), 500);
      setData(res);
    },
    cacheKey: Date.now(),
  });

  useEffect(() => {
    if (isEditPage && open) {
      run();
    }
  }, [isEditPage, open]);

  // 重置数据
  useEffect(() => {
    if (open === false) {
      setData({});
    }
  }, [open]);

  // 页面是否禁止编辑
  const isDisabled = (isEditPage && data?.activiStatus !== '0') || !canEdit;
  const codeRef = useRef(0);
  const onSave = useCallback(() => {
    return new Promise((resolve) => {
      codeRef.current = 0;
      formRef.current?.submit();

      const checkFormInterval = setInterval(() => {
        formRef.current
          ?.validateFields()
          .then(() => {
            const isFormSubmitted = codeRef.current === 200;
            if (isFormSubmitted) {
              clearInterval(checkFormInterval); // 清除轮询
              resolve(200); // 表单提交成功，resolve Promise
            }
          })
          .catch(() => {
            resolve(500);
            clearInterval(checkFormInterval); // 清除轮询
          });
      }, 1000); // 每秒检查一次表单是否提交成功
    });
  }, []);

  // Handle image paste
  useEffect(() => {
    if (quillRef.current) {
      const quill = quillRef.current.getEditor();

      const handlePaste = (event: ClipboardEvent) => {
        const clipboardData = event.clipboardData;
        if (!clipboardData) return;

        // 检查是否有图片
        const hasImage = Array.from(clipboardData.items).some(
          (item) => item.type.indexOf('image') !== -1,
        );

        if (hasImage) {
          event.preventDefault();
          // 只处理第一个图片
          const imageItem = Array.from(clipboardData.items).find(
            (item) => item.type.indexOf('image') !== -1,
          );

          if (imageItem) {
            const file = imageItem.getAsFile();
            if (file) {
              const reader = new FileReader();
              reader.onload = (e) => {
                // 创建图片对象来获取尺寸
                const img = new Image();
                img.onload = () => {
                  let finalImage = e.target?.result as string;

                  // 如果图片尺寸超过限制，进行压缩
                  if (img.width > 1920 || img.height > 1080) {
                    // 计算压缩后的尺寸
                    let newWidth = img.width;
                    let newHeight = img.height;
                    const ratio = img.width / img.height;

                    if (img.width > 1920) {
                      newWidth = 1920;
                      newHeight = Math.round(newWidth / ratio);
                    }
                    if (newHeight > 1080) {
                      newHeight = 1080;
                      newWidth = Math.round(newHeight * ratio);
                    }

                    // 使用canvas进行压缩
                    const canvas = document.createElement('canvas');
                    canvas.width = newWidth;
                    canvas.height = newHeight;
                    const ctx = canvas.getContext('2d');
                    if (ctx) {
                      ctx.drawImage(img, 0, 0, newWidth, newHeight);
                      finalImage = canvas.toDataURL(file.type || 'image/jpeg', 0.8);
                    }
                  }

                  // 插入图片到编辑器
                  const range = quill.getSelection(true);
                  quill.insertEmbed(range.index, 'image', finalImage, 'user');
                };
                img.src = e.target?.result as string;
              };
              reader.readAsDataURL(file);
            }
          }
        }
      };

      quill.root.addEventListener('paste', handlePaste);

      // 清理事件监听
      return () => {
        quill.root.removeEventListener('paste', handlePaste);
      };
    }
  }, [quillRef.current]);

  return (
    <DrawerForm<API.AnnouncementUpdateReq>
      width={753}
      title={isEditPage ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const { id } = value;
        const formData = {
          ...value,
          id,
        };
        const msg = isEditPage
          ? await updateAnnouncement(formData)
          : await createAnnouncement(formData);
        const success = msg.code === 200;
        codeRef.current = msg.code!;
        if (success) {
          message.success('保存成功!');
          onFinish?.(value);
        }
        return isEditPage ? false : success;
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      disabled={isDisabled}
      submitter={
        !isDisabled
          ? {
              searchConfig: {
                submitText: '保存',
                resetText: '取消',
              },
            }
          : false
      }
    >
      <div className="rk-none">
        <ProFormText name="id" />
      </div>
      {isEditPage && (
        <RKPageHeader
          id={initialValues?.id}
          status={data?.activiStatus}
          title={data?.title}
          pageHeaderType="drawer"
          approveType="ANNOUNCEMENT_APPROVAL"
          onOperationCallback={() => {
            run();
          }}
          onSave={onSave}
          saveDisabled={!canEditResourceApplication}
        />
      )}
      <ProFormText
        name="title"
        label="标题"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
      />
      <ProForm.Item name="content" label="公告内容" rules={[requiredRule]}>
        <ReactQuill
          ref={quillRef}
          theme="snow"
          readOnly={isDisabled}
          style={{ height: '400px', marginBottom: '50px' }}
          modules={{
            toolbar: [
              [{ header: [1, 2, 3, 4, 5, 6, false] }],
              ['bold', 'italic', 'underline', 'strike'],
              [{ list: 'ordered' }, { list: 'bullet' }],
              [{ color: [] }, { background: [] }],
              [{ align: [] }],
              ['link', 'image'],
              ['clean'],
            ],
            clipboard: {
              matchVisual: false,
            },
          }}
          formats={[
            'header',
            'bold',
            'italic',
            'underline',
            'strike',
            'list',
            'bullet',
            'color',
            'background',
            'align',
            'link',
            'image',
          ]}
        />
      </ProForm.Item>
    </DrawerForm>
  );
};
export default AnnouncementDrawerForm;
