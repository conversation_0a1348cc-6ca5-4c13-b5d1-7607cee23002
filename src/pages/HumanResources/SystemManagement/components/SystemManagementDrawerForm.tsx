import BasicUpload, { UploaderRef } from '@/components/BasicUpload';
import { createRules, modifyRules, selectRulesById } from '@/services/oa/rules';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProFormDatePicker,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { useAccess, useRequest } from '@umijs/max';
import { Form, message } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useRef } from 'react';

const SystemManagementDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  onFinish,
  initialValues,
}) => {
  const isEditPage = !!initialValues?.id;
  const { canEditSystemManagement, canAddSystemManagement } = useAccess();
  const canEdit =
    (canEditSystemManagement && isEditPage) || (!isEditPage && canAddSystemManagement);

  const formRef = useRef<ProFormInstance>();
  const uploader = useRef<UploaderRef>(null);

  // 获取详情
  const { run } = useRequest(() => selectRulesById({ idReq: { id: initialValues?.id } }), {
    manual: true,
    onSuccess: (res) => {
      setTimeout(() => formRef.current?.setFieldsValue(res?.rulesResp), 500);
    },
  });

  useEffect(() => {
    if (isEditPage && open) {
      run();
    }
  }, [isEditPage, open]);

  return (
    <DrawerForm<API.RulesReq & { id?: string }>
      title={isEditPage ? '编辑' : '新建'}
      width="auto"
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const { id, startTime, endTime } = value;
        const formData = isEditPage
          ? { id, startTime, endTime }
          : {
              ...value,
              id,
              filePath: uploader.current?.fileList?.[0]?.thumbUrl ?? '',
            };
        const msg = isEditPage ? await modifyRules(formData) : await createRules(formData);
        const success = msg.code === 200;
        if (success) {
          message.success('保存成功!');
          onFinish?.(value);
        }
        return isEditPage ? false : success;
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      disabled={!canEdit}
      submitter={
        canEdit
          ? {
              searchConfig: {
                submitText: '保存',
                resetText: '取消',
              },
            }
          : false
      }
    >
      <div className="rk-none">
        <ProFormText name="id" />
      </div>
      {!isEditPage && (
        <ProFormText
          name="rulesName"
          label="制度名称"
          fieldProps={{
            autoComplete: 'none',
          }}
          rules={[requiredRule]}
          width="lg"
        />
      )}
      <ProFormDatePicker
        className="ant-picker"
        name="startTime"
        label="开始日期"
        tooltip="到期后制度自动生效"
        rules={[requiredRule]}
        transform={(value, namePath) => {
          return {
            [namePath]: dayjs(value).format('YYYY-MM-DD'),
          };
        }}
        width="lg"
      />
      <ProFormDatePicker
        className="ant-picker"
        name="endTime"
        label="结束日期"
        tooltip="到期后制度自动失效"
        rules={[requiredRule]}
        transform={(value, namePath) => {
          return {
            [namePath]: dayjs(value).format('YYYY-MM-DD'),
          };
        }}
        width="lg"
      />
      {!isEditPage && (
        <Form.Item name="switch" label="制度文件(只支持pdf格式的文件)">
          <BasicUpload
            ref={uploader}
            fileType=".pdf"
            fileSize={40 * 1024 * 1024}
            fileCount={1}
            multiple={false}
          />
        </Form.Item>
      )}
    </DrawerForm>
  );
};
export default SystemManagementDrawerForm;
