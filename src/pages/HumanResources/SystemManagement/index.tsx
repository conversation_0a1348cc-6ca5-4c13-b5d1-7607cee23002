import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import PDFPreviewModal from '@/components/PDFPreviewModal';
import TableMoreActions from '@/components/TableMoreActions';
import { SYSTEM_STATUS } from '@/enums';
import { userDropDownList } from '@/services/oa/auth';
import {
  assignUsers,
  cancelTop,
  deleteRulesByIds,
  modifyRules,
  pageRules,
  preViewAndModify,
  selectRulesById,
  top,
} from '@/services/oa/rules';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  ModalForm,
  PageContainer,
  ProColumns,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { Access, useAccess, useRequest } from '@umijs/max';
import { Al<PERSON>, Button, Drawer, Form, message, Modal, Space, Table, TableProps, Tag } from 'antd';
import React, { useMemo, useRef, useState } from 'react';
import SystemManagementDrawerForm from './components/SystemManagementDrawerForm';

const SystemManagement: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.RulesResp[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [modalVisit, setModalVisit] = useState(false);
  const [collectOpen, setCollectOpen] = useState(false);
  const [collectData, setCollectData] = useState<API.UserViewRulesResp[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);

  const id = useRef<string>();

  const { data: allUserList, loading: userLoading } = useRequest(() => userDropDownList({}));
  const userList = useMemo(
    () => allUserList?.filter((item) => item?.activation === 1) || [],
    [allUserList],
  );

  const [form] = Form.useForm();

  const {
    canAddSystemManagement = false,
    canDeleteSystemManagement = false,
    canReadCollect = false,
    canEditSystemManagement = false,
  } = useAccess();

  const { run: deleteRecord } = useRequest((ids) => deleteRulesByIds({ ids: ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const { run: confirmRecord } = useRequest(
    (record) =>
      preViewAndModify({ rulesId: record.id, rulesName: record.rulesName, confirmBool: true }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('确认成功');
        tableRef.current?.reloadAndRest?.();
      },
      formatResult: (res) => res,
    },
  );

  const { run: denyRecord } = useRequest(
    (record, opinion) =>
      preViewAndModify({
        rulesId: record.id,
        rulesName: record.rulesName,
        opinion,
        confirmBool: false,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('提交成功');
        tableRef.current?.reloadAndRest?.();
      },
      formatResult: (res) => res,
    },
  );

  //分配至用户
  const { run: assignUser, loading: assignUserLoading } = useRequest(
    (record: API.RulesResp) =>
      assignUsers({
        rulesId: record.id,
        userIdList: selectedUsers,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('分配成功');
        tableRef.current?.reloadAndRest?.();
      },
      formatResult: (res) => res,
    },
  );

  const { run: cancelRecord } = useRequest(
    (id) =>
      modifyRules({
        id,
        status: 'error',
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('作废成功');
        tableRef.current?.reloadAndRest?.();
      },
      formatResult: (res) => res,
    },
  );

  //置顶
  const { run: topRecord } = useRequest((id) => top({ id }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('置顶成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  //取消置顶
  const { run: cancelTopRecord } = useRequest((id) => cancelTop({ id }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('取消置顶成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  // 获取详情
  const { run: recordDetail } = useRequest(
    (record) => selectRulesById({ idReq: { id: record?.id } }),
    {
      manual: true,
      onSuccess: (res) => {
        setCollectData((res?.viewRulesRespList as API.UserViewRulesResp[]) || []);
        setCollectOpen(true);
      },
    },
  );

  // 获取分配用户详情
  const { run: recordUserDetail, loading: recordUserLoading } = useRequest(
    (record) => selectRulesById({ idReq: { id: record?.id } }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.noticeUserRespList?.length) {
          setSelectedUsers(res?.noticeUserRespList.map((i: API.NoticeUserResp) => i.userId!));
        } else {
          setSelectedUsers([]);
        }
      },
    },
  );

  const handleDelete = async (rows: API.RulesResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(String(item.id)!);
      names.push(item.rulesName!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };
  const handleCancel = async (record: API.RulesResp) => {
    Modal.confirm({
      title: '确认作废',
      content: `您确定要作废“${record.rulesName}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        cancelRecord(record.id);
      },
    });
  };
  const handleTop = async (record: API.RulesResp) => {
    Modal.confirm({
      title: '确认置顶',
      content: `您确定要置顶“${record.rulesName}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        topRecord(record.id);
      },
    });
  };
  const handleCancelTop = async (record: API.RulesResp) => {
    Modal.confirm({
      title: '取消置顶',
      content: `您确定要取消置顶“${record.rulesName}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        cancelTopRecord(record.id);
      },
    });
  };
  const handleConfirm = async (record: API.RulesResp) => {
    Modal.confirm({
      title: '确认操作',
      content: `您确定要确认此制度吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        confirmRecord(record);
      },
    });
  };
  //收集表格
  const collectColumns: TableProps<API.UserViewRulesResp>['columns'] = [
    {
      title: '用户名称',
      dataIndex: 'userName',
    },
    {
      title: '确认结果',
      dataIndex: 'opinionBool',
      render: (value, record: API.UserViewRulesResp) => {
        let label = '';
        let color = '';
        if (record.confirmBool) {
          label = '同意';
          color = 'green';
        } else if (record.opinionBool) {
          label = '有异议';
          color = 'volcano';
        } else {
          label = '已查看';
          color = 'blue';
        }
        return <Tag color={color}>{label}</Tag>;
      },
    },
    {
      title: '异议内容',
      dataIndex: 'opinion',
      render: (val) => {
        return val || '-';
      },
    },
    {
      title: '首次查看时间',
      dataIndex: 'viewTime',
    },
  ];
  //用户表格
  const userCol: ProColumns[] = [
    {
      title: '编号',
      dataIndex: 'employeeNumber',
    },
    {
      title: '姓名',
      dataIndex: 'username',
      render: (_, record) => {
        return `${record?.username}(${record?.department})` || '-';
      },
    },
  ];

  const getActions = (record: API.RulesResp) => {
    return [
      // 定义所有可能的操作，包括已有的链接、按钮等组件
      {
        key: 'download',
        isShow: !!record.filePath,
        component: (
          <>
            {!!record.filePath && (
              <Button
                type="link"
                className="inner-table-link"
                key="download"
                onClick={() => {
                  window.open(record.filePath);
                }}
              >
                下载
              </Button>
            )}
          </>
        ),
      },
      {
        key: 'edit',
        isShow: canEditSystemManagement && record?.status === 'processing',
        component: (
          <Access accessible={canEditSystemManagement}>
            {record?.status === 'processing' && (
              <Button
                type="link"
                className="inner-table-link"
                key="edit"
                onClick={() => {
                  {
                    setModalVisit(true);
                    id.current = record.id!;
                  }
                }}
              >
                编辑
              </Button>
            )}
          </Access>
        ),
      },
      {
        key: 'assignUser',
        isShow:
          canAddSystemManagement &&
          (record?.status === 'processing' || record?.status === 'success'),
        component: (
          <Access accessible={canAddSystemManagement}>
            {(record?.status === 'processing' || record?.status === 'success') && (
              <ModalForm
                title="请选择要分配的用户"
                submitter={{
                  submitButtonProps: {
                    loading: userLoading || recordUserLoading || assignUserLoading,
                  },
                }}
                trigger={
                  <Button type="link" className="inner-table-link" key="changeuser">
                    分配至用户
                  </Button>
                }
                modalProps={{
                  destroyOnClose: true,
                }}
                onOpenChange={(visible) => {
                  if (visible) {
                    recordUserDetail(record);
                  }
                }}
                onFinish={async () => {
                  assignUser(record);
                }}
              >
                <ProTable
                  {...defaultTableConfig}
                  className="inner-table"
                  scroll={{
                    x: '100%',
                    y: '40vh',
                  }}
                  options={{
                    reload: false,
                    density: false,
                    setting: false,
                    search: true,
                  }}
                  loading={userLoading || recordUserLoading}
                  request={async ({ keyword = '' }) => {
                    return {
                      success: true,
                      data: userList?.filter(
                        (item: API.UserPageResp) =>
                          `${item?.username}(${item?.department})`!.indexOf(keyword) >= 0,
                      ),
                    };
                  }}
                  columns={userCol}
                  tableAlertRender={({ selectedRowKeys }) => {
                    return (
                      <Space size={24}>
                        <span>已选 {selectedRowKeys.length} 项</span>
                      </Space>
                    );
                  }}
                  tableAlertOptionRender={() => {
                    return (
                      <Space>
                        <a
                          style={{ marginInlineStart: 8 }}
                          onClick={() => {
                            setSelectedUsers((userList.map((i) => i.id) as string[]) || []);
                          }}
                        >
                          全部选择
                        </a>
                        <a
                          style={{ marginInlineStart: 8 }}
                          onClick={() => {
                            setSelectedUsers([]);
                          }}
                        >
                          取消选择
                        </a>
                      </Space>
                    );
                  }}
                  rowSelection={{
                    selectedRowKeys: selectedUsers,
                    alwaysShowAlert: true,
                    onSelectAll(selected, selectedRows, changeRows) {
                      let arr = [];
                      if (selected) {
                        // 如果选中,加入selectedRowKeys
                        arr = Array.from(
                          new Set([...selectedUsers, ...changeRows.map((item) => item.id)]),
                        );
                      } else {
                        //如果未选中，则在已选择的数组中清除这些未选中的数据
                        arr = selectedUsers.filter((item) => {
                          return changeRows.map((item) => item.id).indexOf(item) < 0;
                        });
                      }
                      setSelectedUsers(arr);
                    },
                    onSelect(record, selected) {
                      let arr = [];
                      if (selected) {
                        // 如果选中,加入selectedRowKeys
                        arr = Array.from(new Set([...selectedUsers, record.id]));
                      } else {
                        // 如果未选中,删除这一项数据
                        arr = selectedUsers.filter((item) => {
                          return item !== record.id;
                        });
                      }
                      setSelectedUsers(arr);
                    },
                  }}
                />
              </ModalForm>
            )}
          </Access>
        ),
      },
      {
        key: 'confirm',
        isShow: !record?.currentUserState && record?.status === 'processing',
        component: (
          <>
            {!record?.currentUserState && record?.status === 'processing' && (
              <Button
                type="link"
                className="inner-table-link"
                key="confirm"
                onClick={() => handleConfirm(record)}
              >
                确认
              </Button>
            )}
          </>
        ),
      },
      {
        key: 'negative',
        isShow: !record?.currentUserState && record?.status === 'processing',
        component: (
          <>
            {!record?.currentUserState && record?.status === 'processing' && (
              <ModalForm
                width={600}
                title="请输入建议内容"
                trigger={
                  <Button type="link" className="inner-table-link" key="negative">
                    有异议
                  </Button>
                }
                form={form}
                autoFocusFirstInput
                modalProps={{
                  destroyOnClose: true,
                }}
                submitTimeout={2000}
                onFinish={async (values) => {
                  denyRecord(record, values.name);
                  return true;
                }}
              >
                <Alert
                  message="建议内容提交后不可更改，且每个公示制度每人仅能提交一次建议！"
                  type="error"
                  showIcon
                  style={{
                    marginBottom: 16,
                    fontWeight: 700,
                  }}
                />
                <ProFormTextArea
                  className="ant-picker"
                  name="name"
                  rules={[requiredRule]}
                  fieldProps={{
                    autoSize: { minRows: 3, maxRows: 5 },
                  }}
                />
              </ModalForm>
            )}
          </>
        ),
      },
      {
        key: 'collect',
        isShow: canReadCollect,
        component: (
          <Access accessible={canReadCollect}>
            <Button
              type="link"
              className="inner-table-link"
              key="collect"
              onClick={() => recordDetail(record)}
            >
              收集表
            </Button>
          </Access>
        ),
      },
      {
        key: 'del',
        isShow: canDeleteSystemManagement,
        component: (
          <Access accessible={canDeleteSystemManagement}>
            <Button
              type="link"
              className="inner-table-link"
              key="delete"
              onClick={() => handleDelete([record])}
            >
              删除
            </Button>
          </Access>
        ),
      },
      {
        key: 'cancel',
        isShow: canEditSystemManagement && record.status !== 'error',
        component: (
          <Access accessible={canEditSystemManagement}>
            {record.status !== 'error' && (
              <Button
                type="link"
                className="inner-table-link"
                key="delete"
                onClick={() => handleCancel(record)}
              >
                作废
              </Button>
            )}
          </Access>
        ),
      },
      {
        key: 'top',
        isShow: canEditSystemManagement && record.hasTop === 0,
        component: (
          <Access accessible={canEditSystemManagement}>
            {record.hasTop === 0 && (
              <Button
                type="link"
                className="inner-table-link"
                key="top"
                onClick={() => handleTop(record)}
              >
                置顶
              </Button>
            )}
          </Access>
        ),
      },
      {
        key: 'cancelTop',
        isShow: canEditSystemManagement && record.hasTop === 1,
        component: (
          <Access accessible={canEditSystemManagement}>
            {record.hasTop === 1 && (
              <Button
                type="link"
                className="inner-table-link"
                key="cancelTop"
                onClick={() => handleCancelTop(record)}
              >
                取消置顶
              </Button>
            )}
          </Access>
        ),
      },
    ];
  };

  // 表格
  const columns: ProColumns<API.RulesResp>[] = [
    {
      title: '制度名称',
      dataIndex: 'rulesName',
      copyable: true,
      ellipsis: true,
      width: 120,
      fixed: 'left',
      render: (dom, entity) => (
        <a
          className="rk-a-span"
          onClick={() => {
            {
              preViewAndModify({ rulesId: entity.id });
              setPreviewOpen(true);
              setPreviewImage(entity?.filePath || '');
              tableRef.current?.reloadAndRest?.();
            }
          }}
        >
          {entity.rulesName + (entity?.hasTop === 1 ? '(置顶)' : '')}
        </a>
      ),
    },
    {
      title: '制度状态',
      dataIndex: 'status',
      valueEnum: option2enum(SYSTEM_STATUS),
    },
    {
      title: '开始日期',
      dataIndex: 'startTime',
      valueType: 'date',
    },
    {
      title: '结束日期',
      dataIndex: 'endTime',
      valueType: 'date',
    },
    {
      title: '上传时间',
      dataIndex: 'uploadTime',
      hideInSearch: true,
    },
    {
      title: '已分配人数',
      dataIndex: 'assignUserCount',
      hideInSearch: true,
    },
    {
      title: '已查看人数',
      dataIndex: 'hasView',
      hideInSearch: true,
    },
    {
      title: '已确认人数',
      dataIndex: 'hasConfirm',
      hideInSearch: true,
    },
    {
      title: '有异议人数',
      dataIndex: 'hasOpinion',
      hideInSearch: true,
    },
    {
      title: '操作',
      width: 140,
      fixed: 'right',
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (_, record) => <TableMoreActions allActions={() => getActions(record)} />,
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.RulesResp>
        {...defaultTableConfig}
        actionRef={tableRef}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        rowSelection={
          canDeleteSystemManagement
            ? {
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
              }
            : false
        }
        columns={columns}
        headerTitle="制度管理列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddSystemManagement}>
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  id.current = '';
                  setModalVisit(true);
                }}
              >
                新建制度
              </Button>
            </Access>,
          ],
        }}
        request={async ({ startTime, endTime, ...params }) => {
          const { rulesName, status, current, pageSize } = params;
          const search = { rulesName };
          const filter = { status };
          return queryPagingTable<API.PageReq>(
            {
              current,
              pageSize,
              scope: [
                startTime && {
                  name: 'startTime',
                  key: 'ge',
                  val: startTime,
                },
                endTime && {
                  name: 'endTime',
                  key: 'le',
                  val: endTime,
                },
              ].filter(Boolean),
              search,
              filter,
            },
            pageRules,
          );
        }}
      />
      <Drawer
        title="收集表信息"
        width={660}
        placement="right"
        size="large"
        onClose={() => setCollectOpen(false)}
        open={collectOpen}
      >
        <Table
          size="small"
          bordered
          columns={collectColumns}
          dataSource={collectData}
          rowKey="id"
        />
      </Drawer>
      <SystemManagementDrawerForm
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
        initialValues={{ id: id.current }}
      />
      <PDFPreviewModal
        setPreviewOpen={setPreviewOpen}
        previewOpen={previewOpen}
        previewUrl={previewImage}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default SystemManagement;
