import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { APPROVAL_STATUS } from '@/enums';
import { useUserList } from '@/hooks/useUserList';
import { delLeaveSell, leaveSellPage } from '@/services/oa/leaveSell';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, history, useAccess, useLocation, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import { useRef, useState } from 'react';
import LeaveSellDrawerForm from './components/LeaveSellDrawerForm';

const LeaveSell: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [id, setId] = useState<any | undefined>();
  const [drawerVisit, setDrawerVisit] = useState(false);
  const [selectedRows, setSelectedRows] = useState<API.LeaveSellInfoResp[]>([]);
  const { state } = useLocation();
  const { userList, loading: userLoading } = useUserList();
  const { canAddRevokeLeaveApplication = false, canDeleteRevokeLeaveApplication = false } =
    useAccess();
  const onEdit = (record: API.LeaveSellInfoResp) => {
    const { id } = record;
    setDrawerVisit(true);
    setId(id);
  };

  //删除
  const { run: deleteRecord } = useRequest((ids) => delLeaveSell({ ids: ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: API.LeaveSellInfoResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(String(item.id)!);
      names.push(item.documentNumber!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.LeaveSellInfoResp>[] = [
    {
      title: '编号',
      dataIndex: 'documentNumber',
      copyable: true,
      ellipsis: true,
      width: 200,
      initialValue: state,
      render: (text, record) => (
        <a className="rk-a-span" onClick={() => onEdit(record)}>
          {text}
        </a>
      ),
      fixed: 'left',
    },
    {
      title: '申请人',
      dataIndex: 'created_by',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: userList,
        loading: userLoading,
        showSearch: true,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        optionFilterProp: 'label',
        filterOption: true,
      },
    },
    {
      title: '申请人',
      dataIndex: 'username',
      hideInSearch: true,
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
    },
    {
      title: '休假申请',
      dataIndex: 'leaveAppNumber',
      hideInTable: true,
    },
    {
      title: '休假申请编号',
      dataIndex: 'leaveAppNumber',
      render: (text, record) => (
        <a
          className="rk-a-span"
          onClick={() =>
            history.push({
              pathname: '/human-resources/leave-application',
              search: `documentNumber=${record.leaveAppNumber}`,
            })
          }
        >
          {text}
        </a>
      ),
      copyable: true,
      hideInSearch: true,
    },
    {
      title: '销假时间',
      dataIndex: 'sellDate',
      search: false,
    },

    {
      title: '操作',
      width: 100,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      hideInTable: !canDeleteRevokeLeaveApplication,
      render: (text, record) => {
        const { activiStatus } = record;
        return (
          <Access accessible={canDeleteRevokeLeaveApplication}>
            {activiStatus === '0' && (
              <Button
                type="link"
                className="inner-table-link"
                key="delete"
                onClick={() => handleDelete([record])}
              >
                删除
              </Button>
            )}
          </Access>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.LeaveSellInfoResp>
        {...defaultTableConfig}
        actionRef={tableRef}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        rowSelection={
          canDeleteRevokeLeaveApplication
            ? {
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
                getCheckboxProps: (record) => ({
                  disabled: record?.activiStatus !== '0',
                }),
              }
            : false
        }
        columns={columns}
        headerTitle="销假申请列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddRevokeLeaveApplication}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setDrawerVisit(true);
                  setId(undefined);
                }}
              >
                新建销假申请
              </Button>
            </Access>,
          ],
        }}
        polling={5000}
        request={async (params) => queryPagingTable<API.PageReq>(params, leaveSellPage)}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
      <LeaveSellDrawerForm
        id={id}
        open={drawerVisit}
        onOpenChange={(visible) => {
          setDrawerVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default LeaveSell;
