import RK<PERSON>ageHeader from '@/components/RKPageHeader';
import R<PERSON>electLabel from '@/components/RKSelectLabel';
import { leaveAppBalance, leaveAppPage } from '@/services/oa/leaveApp';
import { createLeaveSell, leaveSellInfo, updateLeaveSell } from '@/services/oa/leaveSell';
import { queryFormData } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useAccess, useRequest } from '@umijs/max';
import { useCounter } from 'ahooks';
import { message } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useRef, useState } from 'react';

const LeaveSellDrawerForm: React.FC<DrawerFormProps> = ({ open, onOpenChange, id, onFinish }) => {
  const formRef = useRef<ProFormInstance>();
  const isEditPage = !!id;
  const [status, setStatus] = useState<string>(); //审批状态
  const { canEditRevokeLeaveApplication, canAddRevokeLeaveApplication } = useAccess();
  const canEdit =
    (canEditRevokeLeaveApplication && isEditPage) || (!isEditPage && canAddRevokeLeaveApplication);

  const [current, { inc }] = useCounter(0);

  //新建销假
  const { run: add } = useRequest((value) => createLeaveSell(value), {
    manual: true,
    formatResult: (res) => res,
  });
  //修改销假
  const { run: edit, loading: editLoading } = useRequest((value) => updateLeaveSell(value), {
    manual: true,
    formatResult: (res) => res,
  });

  // 页面是否禁止编辑
  const isDisabled = (isEditPage && ((status && Number(status) > 0) || !canEdit)) || false;

  const leaveApplicationRef = useRef<API.LeaveAppPageResp[]>([]);
  //所有休假申请数据
  const { run: runLeaveAppPage, loading: leaveApplicationLoading } = useRequest(
    () =>
      leaveAppPage({
        pageNum: 1,
        pageSize: 10000,
      }),
    {
      manual: true,
      formatResult: (res) => {
        const { data } = res;
        //筛选审核通过的
        const List = ((data as API.PageLeaveAppPageResp).records as API.LeaveAppPageResp[]).filter(
          (item) => item.activiStatus === '2',
        );
        leaveApplicationRef.current = List;
      },
    },
  );

  useEffect(() => {
    if (open === true) {
      runLeaveAppPage();
    }
  }, [open]);

  //假期类型
  const { data: leaveList = [] } = useRequest(() => leaveAppBalance());

  //销假日期的范围选择
  const getDisabledDate: RangePickerProps['disabledDate'] = (current) => {
    const startDay = formRef.current?.getFieldValue('fromDate');
    const endDay = formRef.current?.getFieldValue('toDate');
    return (
      current < dayjs(new Date(startDay)).startOf('day') ||
      current > dayjs(new Date(endDay))?.endOf('day')
    );
  };
  const filterOptions = (list: API.LeaveAppPageResp[]) => {
    return list.map((item) => ({
      keywords: `${item.documentNumber} ${item.leaveTypeName}`,
      value: item.id,
      label: <RKSelectLabel title={item.documentNumber} info={item.leaveTypeName} />,
      documentNumber: item.documentNumber,
      leaveType: item.leaveTypeName,
      fromDate: item.fromDate,
      toDate: item.toDate,
      totalLeaveDays: item.totalLeaveDays,
    }));
  };
  const codeRef = useRef(0);
  const onSave = useCallback(() => {
    return new Promise((resolve) => {
      codeRef.current = 0;
      formRef.current?.submit();

      const checkFormInterval = setInterval(() => {
        formRef.current
          ?.validateFields()
          .then(() => {
            const isFormSubmitted = codeRef.current === 200;
            if (isFormSubmitted) {
              clearInterval(checkFormInterval); // 清除轮询
              resolve(200); // 表单提交成功，resolve Promise
            }
          })
          .catch(() => {
            resolve(500);
            clearInterval(checkFormInterval); // 清除轮询
          });
      }, 1000); // 每秒检查一次表单是否提交成功
    });
  }, []);

  return (
    <DrawerForm<API.LeaveSellInfoResp>
      title={isEditPage ? '编辑' : '新建'}
      width={753}
      formRef={formRef}
      open={open}
      onOpenChange={(visible) => {
        onOpenChange?.(visible);
      }}
      disabled={isDisabled}
      submitter={
        isDisabled
          ? false
          : {
              searchConfig: {
                submitText: '保存',
              },
            }
      }
      onFinish={async (value) => {
        const msg = isEditPage ? await edit({ ...value, id: id }) : await add(value);
        const success = msg.code === 200;
        codeRef.current = msg.code!;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return isEditPage ? false : success;
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      params={{ refresh: current }}
      request={async () => {
        const res = await queryFormData(
          {
            req: { id },
          },
          isEditPage,
          leaveSellInfo,
        );
        setStatus(res?.activiStatus);
        return res;
      }}
      initialValues={{ half: 0 }}
    >
      {id && (
        <ProFormDependency name={['activiStatus', 'documentNumber']}>
          {({ activiStatus, documentNumber }) => {
            return (
              <RKPageHeader
                id={id}
                status={activiStatus}
                title={documentNumber}
                approveType="REVOCATION_APPROVAL"
                onOperationCallback={inc}
                pageHeaderType="drawer"
                onSave={onSave}
                saveLoading={editLoading}
                saveDisabled={!canEditRevokeLeaveApplication}
              />
            );
          }}
        </ProFormDependency>
      )}
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" />
      </div>
      <ProForm.Group>
        <ProFormSelect
          disabled={isEditPage}
          width="md"
          name="leaveAppId"
          label="休假申请"
          fieldProps={{
            filterOption: (inputValue, option) => {
              return option?.keywords.indexOf(inputValue) >= 0;
            },
            showSearch: true,
            loading: leaveApplicationLoading,
            optionLabelProp: 'documentNumber',
            onChange: (value, option) => {
              const opt = option as {
                leaveType?: string;
                fromDate?: string;
                toDate?: string;
                totalLeaveDays?: string;
              };
              formRef.current?.setFieldsValue({
                leaveType: opt?.leaveType,
                fromDate: opt?.fromDate,
                toDate: opt?.toDate,
                totalLeaveDays: opt?.totalLeaveDays,
              });
            },
          }}
          options={
            isEditPage
              ? filterOptions(leaveApplicationRef.current)
              : filterOptions(
                  leaveApplicationRef.current.filter(
                    (item) => new Date(item.fromDate!) <= new Date(item.toDate!),
                  ),
                )
          }
          rules={[requiredRule]}
          transform={(value, namePath) => ({
            [namePath]: value,
            leaveAppNumber: leaveApplicationRef.current?.find((item) => item.id === value)
              ?.documentNumber,
          })}
        />
        <ProFormSelect
          disabled
          name="leaveType"
          label="休假类型"
          placeholder="请输入"
          options={leaveList.map((item) => ({
            value: item.id,
            label: item.leaveTypeName,
          }))}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDatePicker disabled width="md" name="fromDate" label="休假开始时间" />
        <ProFormDatePicker disabled width="md" name="toDate" label="休假结束时间" />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDigit
          disabled
          name="totalLeaveDays"
          label="总休假天数"
          placeholder="请输入"
          min={0}
          fieldProps={{
            autoComplete: 'none',
            addonAfter: '天',
          }}
          width="md"
        />
        <ProFormDatePicker
          width="md"
          name="sellDate"
          label="销假日期"
          tooltip="选择销假日期代表从此时至休假结束时间的时间段选择销假"
          rules={[requiredRule]}
          fieldProps={{
            disabledDate: getDisabledDate,
          }}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormSelect
          name="half"
          label="是否销假半天"
          placeholder="请输入"
          options={[
            { value: 0, label: '否' },
            { value: 1, label: '是' },
          ]}
          width="md"
        />
      </ProForm.Group>
    </DrawerForm>
  );
};

export default LeaveSellDrawerForm;
