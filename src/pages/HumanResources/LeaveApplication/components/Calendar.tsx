import { attendanceCalendar } from '@/services/oa/attendance';
import { holidayMonth } from '@/services/oa/holiday';
import { groupedData } from '@/utils';
import { PageContainer } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Calendar, CalendarProps, Space, Tag } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import React, { useEffect, useMemo } from 'react';
const getListData = (value: Dayjs, data: Record<string, any>[] = []) => {
  const arr = groupedData(data, 'date');
  const date = value.format('YYYY-MM-DD');
  return arr?.[date] || [];
};

const CalendarPage: React.FC = () => {
  // 获取请假数据
  const { data, run } = useRequest(
    (calendarMonth) =>
      attendanceCalendar({
        calendarMonth,
      }),
    {
      manual: true,
    },
  );

  // 获取假期列表
  const { data: holiday, run: getHoliday } = useRequest(
    (calendarMonth) =>
      holidayMonth({
        calendarMonth,
      }),
    {
      manual: true,
    },
  );

  const holidayData = useMemo(() => {
    const leaveData =
      data?.map((item: Record<string, any>) => ({
        content: item?.employeeName,
        date: item?.attendanceDate,
        color: 'geekblue',
        ...item,
      })) || [];
    const holidayData =
      holiday?.map((item: Record<string, any>) => ({
        content: item?.describe,
        date: item?.holidays,
        color: 'volcano',
        ...item,
      })) || [];
    return [...holidayData, ...leaveData];
  }, [data, holiday]);

  useEffect(() => {
    const date = dayjs().format('YYYY-MM-DD HH:mm:ss');
    run(date);
    getHoliday(date);
  }, []);

  const dateCellRender = (value: Dayjs) => {
    const listData = getListData(value, holidayData);
    return (
      <Space direction="vertical" size={2}>
        {listData.map((item, index) => (
          <Tag key={index} color={item.color} bordered={false} style={{ width: '100%' }}>
            {item.content}
          </Tag>
        ))}
      </Space>
    );
  };

  const cellRender: CalendarProps<Dayjs>['cellRender'] = (current, info) => {
    if (info.type === 'date') return dateCellRender(current);
    if (info.type === 'month') return null;
    return info.originNode;
  };

  const onPanelChange = (value: Dayjs, mode: CalendarProps<Dayjs>['mode']) => {
    if (mode === 'month') {
      const date = value.format('YYYY-MM-DD HH:mm:ss');

      run(date);
      getHoliday(date);
    }
  };

  return (
    <PageContainer header={{ title: false }}>
      <Calendar cellRender={cellRender} style={{ padding: 24 }} onPanelChange={onPanelChange} />
    </PageContainer>
  );
};

export default CalendarPage;
