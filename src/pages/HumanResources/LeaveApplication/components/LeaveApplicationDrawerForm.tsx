import RKPageHeader from '@/components/RKPageHeader';
import { useDepartment } from '@/hooks/useDepartment';
import { checkSectionHoliday } from '@/services/oa/holiday';
import {
  createLeaveApp,
  leaveAppBalance,
  leaveAppInfo,
  updateLeaveApp,
} from '@/services/oa/leaveApp';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useAccess, useModel, useRequest } from '@umijs/max';
import { message } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import { DefaultOptionType } from 'antd/es/select';
import dayjs from 'dayjs';
import { useCallback, useEffect, useRef, useState } from 'react';

const LeaveApplicationDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  onFinish,
  initialValues,
}) => {
  const isEditPage = !!initialValues?.id;
  const { canEditLeaveApplication, canAddLeaveApplication } = useAccess();
  const canEdit =
    (canEditLeaveApplication && isEditPage) || (!isEditPage && canAddLeaveApplication);

  const formRef = useRef<ProFormInstance>();
  const [startDay, setStartDay] = useState<string>();
  const [endDay, setEndDay] = useState<string>();
  const [halfDay, setHalfDay] = useState<string>();
  const [leaveTypes, setLeaveTypes] = useState<string>();
  const { departmentList, loading: departmentLoading } = useDepartment();

  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  // 防止数据错乱
  const [data, setData] = useState<API.LeaveAppInfoResp>(); // 初始化 data 为 null
  // 获取详情
  const { run } = useRequest(() => leaveAppInfo({ req: { id: initialValues?.id } }), {
    manual: true,
    onSuccess: (res) => {
      setTimeout(() => formRef.current?.setFieldsValue(res), 500);
      setData(res);

      const { fromDate, toDate, halfDate, leaveType } = res as API.LeaveAppInfoResp;
      setStartDay(fromDate);
      setEndDay(toDate);
      setHalfDay(halfDate);
      setLeaveTypes(leaveType);
    },
    cacheKey: Date.now(),
  });

  const leaveListRef = useRef<API.LeaveBalanceListResp[]>([]);
  const { run: runBalance, loading } = useRequest(() => leaveAppBalance(), {
    manual: true,
    onSuccess: (data) => {
      leaveListRef.current = data?.filter(
        (item) => !['工伤假', '育儿假', '丧假'].includes(item?.leaveTypeName || ''),
      ) as API.LeaveBalanceListResp[];
    },
  });
  //抽屉打开请求获取剩余假期
  useEffect(() => {
    if (open === true) {
      runBalance();
    }
  }, [open]);

  useEffect(() => {
    if (isEditPage && open) {
      run();
    }
  }, [isEditPage, open]);

  // 重置数据
  useEffect(() => {
    if (open === false) {
      setStartDay(undefined);
      setEndDay(undefined);
      setHalfDay(undefined);
      setData({});
    }
  }, [open]);

  const { run: getTotalLeaveDays } = useRequest((value) => checkSectionHoliday(value), {
    manual: true,
    onSuccess: (data) => {
      formRef.current?.setFieldValue('totalLeaveDays', data);
      if (halfDay) {
        formRef.current?.setFieldValue('totalLeaveDays', Number(data) - 0.5);
      }
    },
  });
  //监听
  //年假、事假、育儿假算工作日(即不包含周末与法定节假日)
  useEffect(() => {
    if (startDay && endDay) {
      if (['年假', '育儿假', '事假']?.includes(leaveTypes!)) {
        getTotalLeaveDays({
          startHolidays: startDay,
          endHolidays: endDay,
        });
      } else {
        //开始日期不能大于结束日期
        if (new Date(startDay) > new Date(endDay)) {
          formRef.current?.setFieldValue('halfDate', undefined);
          formRef.current?.setFieldValue('toDate', undefined);
          formRef.current?.setFieldValue('totalLeaveDays', undefined);
        }
        const days = dayjs(endDay).diff(startDay, 'day') + 1;
        formRef.current?.setFieldValue('totalLeaveDays', days);
        if (halfDay) {
          //上半天时间不能小于开始时间,上半天日期不能大于结束时间
          if (new Date(halfDay) < new Date(startDay) || new Date(halfDay) > new Date(endDay)) {
            formRef.current?.setFieldValue('halfDate', undefined);
          } else {
            formRef.current?.setFieldValue('totalLeaveDays', Number(days) - 0.5);
          }
        }
      }
    }
    if (!startDay || !endDay) {
      formRef.current?.setFieldValue('halfDate', undefined);
    }
  }, [startDay, endDay, halfDay, leaveTypes]);

  //上半天班的时间范围选择
  const getDisabledDate: RangePickerProps['disabledDate'] = (current) => {
    const startDay = formRef.current?.getFieldValue('fromDate');
    const endDay = formRef.current?.getFieldValue('toDate');
    const startDate = isEditPage ? dayjs(new Date(startDay)) : startDay;
    const endDate = isEditPage ? dayjs(new Date(endDay)) : endDay;
    const exist = startDay && endDay;
    return exist && (current < startDate.startOf('day') || current > endDate?.endOf('day'));
  };

  // 页面是否禁止编辑
  const isDisabled = (isEditPage && data?.activiStatus !== '0') || !canEdit;
  const codeRef = useRef(0);
  const onSave = useCallback(() => {
    return new Promise((resolve) => {
      codeRef.current = 0;
      formRef.current?.submit();

      const checkFormInterval = setInterval(() => {
        formRef.current
          ?.validateFields()
          .then(() => {
            const isFormSubmitted = codeRef.current === 200;
            if (isFormSubmitted) {
              clearInterval(checkFormInterval); // 清除轮询
              resolve(200); // 表单提交成功，resolve Promise
            }
          })
          .catch(() => {
            resolve(500);
            clearInterval(checkFormInterval); // 清除轮询
          });
      }, 1000); // 每秒检查一次表单是否提交成功
    });
  }, []);

  return (
    <DrawerForm<API.LeaveAppInfoResp & { leaveBalance: number; halfDate?: string; id?: string }>
      width={753}
      title={isEditPage ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      initialValues={{
        employee: currentUser?.employeeNumber,
        employeeName: currentUser?.username,
        department: currentUser?.department,
      }}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const { leaveBalance, totalLeaveDays, fromDate, toDate, halfDate, id } = value;

        if (totalLeaveDays! > leaveBalance) {
          message.warning('总休假时间不能大于可用时间');
        } else if (totalLeaveDays! <= 0) {
          message.warning('总休假时间不能小于或等于零');
        } else {
          const formDate = (value: string | undefined) => dayjs(value).format('YYYY-MM-DD');
          const formData = {
            ...value,
            fromDate: formDate(fromDate),
            toDate: formDate(toDate),
            halfDate: halfDate && formDate(halfDate),
            id,
          };
          const msg = isEditPage ? await updateLeaveApp(formData) : await createLeaveApp(formData);
          const success = msg.code === 200;
          codeRef.current = msg.code!;
          if (success) {
            message.success('保存成功!');
            onFinish?.(value);
          }
          return isEditPage ? false : success;
        }
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      disabled={isDisabled}
      submitter={
        !isDisabled
          ? {
              searchConfig: {
                submitText: '保存',
                resetText: '取消',
              },
            }
          : false
      }
    >
      <div className="rk-none">
        <ProFormText name="id" />
      </div>
      {isEditPage && (
        <RKPageHeader
          id={initialValues?.id}
          status={data?.activiStatus}
          title={data?.documentNumber}
          pageHeaderType="drawer"
          approveType="LEAVE_APPROVAL_PROCESS"
          onOperationCallback={() => {
            run();
          }}
          onSave={onSave}
          saveDisabled={!canEditLeaveApplication}
        />
      )}
      <ProForm.Group>
        <ProFormText
          disabled
          name="employee"
          label="员工编号"
          fieldProps={{
            autoComplete: 'none',
          }}
          width="md"
        />
        <ProFormText
          disabled
          name="employeeName"
          label="员工姓名"
          fieldProps={{
            autoComplete: 'none',
          }}
          rules={[requiredRule]}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormSelect
          disabled
          name="department"
          label="部门"
          fieldProps={{
            loading: departmentLoading,
            fieldNames: {
              value: 'id',
              label: 'departmentName',
            },
            showSearch: true,
          }}
          options={departmentList as DefaultOptionType[]}
          rules={[requiredRule]}
          width="md"
        />

        <ProFormSelect
          name="leaveType"
          label="休假类型"
          options={leaveListRef.current as DefaultOptionType[]}
          rules={[requiredRule]}
          fieldProps={{
            loading,
            fieldNames: {
              value: 'id',
              label: 'leaveTypeName',
            },
            showSearch: true,
            onChange: (values, option) => {
              const maxDay = leaveListRef.current.find((item) => item.id === values)?.leaveBalance;
              formRef.current?.setFieldValue('leaveBalance', maxDay);
              setLeaveTypes((option as { label: string; value: string }).label);
            },
            allowClear: false,
          }}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDigit
          disabled
          name="leaveBalance"
          label="可用天数"
          min={0}
          fieldProps={{
            autoComplete: 'none',
            addonAfter: '天',
          }}
          rules={[requiredRule]}
          width="md"
        />

        <ProFormDatePicker
          className="ant-picker"
          name="fromDate"
          label="开始日期"
          rules={[requiredRule]}
          fieldProps={{
            onChange: (value, valueString) => {
              setStartDay(valueString);
            },
            getPopupContainer: () => document.body,
          }}
          transform={(value, namePath) => {
            return {
              [namePath]: dayjs(value).format('YYYY-MM-DD'),
            };
          }}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDatePicker
          className="ant-picker"
          name="toDate"
          label="结束日期"
          rules={[requiredRule]}
          transform={(value, namePath) => {
            return {
              [namePath]: dayjs(value).format('YYYY-MM-DD'),
            };
          }}
          fieldProps={{
            onChange: async (value, valueString) => {
              setEndDay(valueString);
            },
            getPopupContainer: () => document.body,
          }}
          width="md"
        />
        <ProFormDependency name={['fromDate', 'toDate']}>
          {({ fromDate, toDate }) => {
            return (
              <ProFormDatePicker
                className="ant-picker"
                name="halfDate"
                label="上半天班日期"
                fieldProps={{
                  disabledDate: getDisabledDate,
                  onChange: (value, valueString) => {
                    setHalfDay(valueString);
                  },
                }}
                transform={(value, namePath) => {
                  return {
                    [namePath]: dayjs(value).format('YYYY-MM-DD'),
                  };
                }}
                width="md"
                disabled={!fromDate || !toDate || isDisabled}
              />
            );
          }}
        </ProFormDependency>
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDigit
          disabled
          name="totalLeaveDays"
          label="总休假天数"
          min={0}
          fieldProps={{
            autoComplete: 'none',
            addonAfter: '天',
          }}
          width="md"
        />
        {data?.activiStatus === '2' && (
          <ProFormDigit
            disabled
            name="actualLeaveDays"
            label="实际休假天数"
            min={0}
            fieldProps={{
              autoComplete: 'none',
              addonAfter: '天',
            }}
            width="md"
          />
        )}
        <ProFormTextArea
          name="description"
          label="原因"
          fieldProps={{
            autoSize: {
              minRows: 1,
              maxRows: 6,
            },
          }}
          width="md"
        />
      </ProForm.Group>
    </DrawerForm>
  );
};
export default LeaveApplicationDrawerForm;
