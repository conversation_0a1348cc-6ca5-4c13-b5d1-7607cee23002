import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { APPROVAL_STATUS } from '@/enums';
import { rangePresets } from '@/pages/ReportAnalysis/components/DimensionSetting';
import { sortMap } from '@/pages/ReportAnalysis/WorkloadStatistics';
import { delLeaveApp, leaveAppPage } from '@/services/oa/leaveApp';
import { leaveTypeList } from '@/services/oa/leaveType';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { CalendarOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal, Tooltip } from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';
import LeaveApplicationDrawerForm from './components/LeaveApplicationDrawerForm';

const LeaveApplication: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.LeaveAppPageResp[]>([]);
  const [modalVisit, setModalVisit] = useState(false);
  const id = useRef<string>();
  const formRef = useRef<ProFormInstance>();

  const { canAddLeaveApplication = false, canDeleteLeaveApplication = false } = useAccess();

  //删除
  const { run: deleteRecord } = useRequest((ids) => delLeaveApp({ ids: ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: API.LeaveAppPageResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(String(item.id)!);
      names.push(item.documentNumber!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.LeaveAppPageResp>[] = [
    {
      title: '编号',
      dataIndex: 'documentNumber',
      copyable: true,
      ellipsis: true,
      width: 180,
      render: (dom, entity) => (
        <a
          className="rk-a-span"
          onClick={() => {
            {
              setModalVisit(true);
              id.current = entity.id!;
            }
          }}
        >
          {dom}
        </a>
      ),
    },
    {
      title: '员工姓名',
      dataIndex: 'employeeName',
    },

    {
      title: '休假类型',
      dataIndex: 'leaveType',
      valueType: 'select',
      request: async () => {
        const res = await leaveTypeList();
        const data = res?.data || [];
        return data
          ?.filter((item) => !['工伤假', '育儿假', '丧假'].includes(item?.leaveTypeName || ''))
          ?.map((item) => ({
            label: item.leaveTypeName,
            value: item.id,
          }));
      },
      hideInTable: true,
    },
    {
      title: '休假类型',
      dataIndex: 'leaveTypeName',
      search: false,
    },
    {
      title: '部门',
      dataIndex: 'department',
      search: false,
    },
    {
      title: '开始日期',
      dataIndex: 'fromDate',
      search: false,
    },
    {
      title: '结束日期',
      dataIndex: 'toDate',
      search: false,
    },
    {
      title: '休假天数',
      dataIndex: 'totalLeaveDays',
      sorter: true,
      search: false,
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
    },
    {
      title: '日期范围',
      valueType: 'dateRange',
      dataIndex: 'days',
      fieldProps: {
        presets: rangePresets,
      },
      hideInTable: true,
    },
    {
      title: '操作',
      fixed: 'right',
      width: 100,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !canDeleteLeaveApplication,
      render: (text, record) => {
        const { activiStatus } = record;
        return (
          <Access accessible={canDeleteLeaveApplication}>
            {activiStatus === '0' && <a onClick={() => handleDelete([record])}>删除</a>}
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.LeaveAppPageResp>
        {...defaultTableConfig}
        rowKey="id"
        actionRef={tableRef}
        rowSelection={
          canDeleteLeaveApplication
            ? {
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
                getCheckboxProps: (record) => ({
                  disabled: record.activiStatus !== '0',
                }),
              }
            : false
        }
        formRef={formRef}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        columns={columns}
        headerTitle="休假申请列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddLeaveApplication}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  id.current = '';
                  setModalVisit(true);
                }}
              >
                新建休假申请
              </Button>
            </Access>,
            <Tooltip key="calender" title="日历">
              <Button
                type="link"
                icon={<CalendarOutlined />}
                onClick={() => history.push('/human-resources/calendar')}
              ></Button>
            </Tooltip>,
          ],
        }}
        polling={10000}
        request={async (params, sort) => {
          const { documentNumber, employeeName, leaveType, activiStatus, current, pageSize, days } =
            params;

          const sortTotalLeaveDays =
            sort && sort?.totalLeaveDays ? sortMap[sort.totalLeaveDays] : undefined;

          const scope =
            days &&
            [
              {
                key: 'ge',
                name: 'fromDate',
                val: dayjs(days[0]).format('YYYY-MM-DD'),
              },
              {
                key: 'le',
                name: 'fromDate',
                val: dayjs(days[1]).format('YYYY-MM-DD'),
              },
            ].filter(Boolean);
          const search = { documentNumber, employeeName };
          const filter = { leaveType, activiStatus };
          for (const [key, value] of Object.entries(search)) {
            if (value === undefined || value === '' || value === null) {
              Reflect.deleteProperty(search, key);
            }
          }
          const msg = await leaveAppPage({
            pageNum: current,
            pageSize,
            search,
            filter,
            scope,
            sortTotalLeaveDays,
          });
          return {
            data: msg.data?.records || [],
            success: msg.code === 200,
            total: msg.data?.total,
          };
        }}
      />
      <LeaveApplicationDrawerForm
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
        initialValues={{ id: id.current }}
      />

      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default LeaveApplication;
