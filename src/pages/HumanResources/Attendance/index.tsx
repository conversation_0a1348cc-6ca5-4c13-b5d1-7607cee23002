import { attendancePage } from '@/services/oa/attendance';
import { queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history, request } from '@umijs/max';
import { message, Modal, Space } from 'antd';
import React, { useRef } from 'react';

const Attendance: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();

  //导出
  const handleDownExcel = (row: any) => {
    const { recordData } = row;
    Modal.confirm({
      title: '确认导出',
      content: `您确定要导出“${recordData}”的考勤数据吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        request('/api/employee/attendance/attendance-export', {
          params: { month: recordData },
          method: 'get',
          responseType: 'blob',
          getResponse: true,
          skipErrorHandler: true,
        })
          .then((response) => {
            const url = URL.createObjectURL(
              new Blob([response.data], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              }),
            );
            const link = document.createElement('a');
            link.style.display = 'none';
            link.href = url;
            link.setAttribute('download', `${recordData!}考勤`); // 指定下载的文件名和类型
            document.body.appendChild(link);
            link.click();
          })
          .catch(() => {
            message.error('导出失败');
          });
      },
    });
  };

  // 表格
  const columns: ProColumns<API.AttRecordListResp>[] = [
    {
      title: '月份',
      dataIndex: 'recordData',
      valueType: 'dateMonth',
      render: (dom, record) => (
        <a
          onClick={() =>
            history.push({
              pathname: `/human-resources/attendance/attendance-detail/${record?.id}`,
              search: `${record.recordData}`,
            })
          }
        >
          {dom}
        </a>
      ),
      width: 200,
    },
    {
      title: '操作',
      fixed: 'right',
      width: 100,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <a onClick={() => handleDownExcel(record)}>导出</a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.AttRecordListResp>
        {...defaultTableConfig}
        actionRef={tableRef}
        columns={columns}
        headerTitle="考勤列表"
        request={async (params) => {
          const { documentNumber, employeeName, status, attendanceDate, current, pageSize } =
            params;
          const search = {
            documentNumber,
            employeeName,
            attendanceDate,
          };
          const filter = {
            status,
          };
          return queryPagingTable<API.PageReq>(
            { current, pageSize, filter, search },
            attendancePage,
          );
        }}
      />
    </PageContainer>
  );
};

export default Attendance;
