import { attendanceMonth } from '@/services/oa/attendance';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProForm,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { request, useLocation, useRequest } from '@umijs/max';
import { Button, message } from 'antd';
import React, { useRef } from 'react';
import {
  attendanceRecordColumns,
  newEmployeeColumns,
  probationaryColumns,
  resignatedColumns,
} from './attendanceColumns';

const AttendanceDetail: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const formRef = useRef<ProFormInstance>();
  const { search } = useLocation();

  const { data = {} } = useRequest(() => attendanceMonth({ month: search.substring(1) }));

  const downExcel = () => {
    request('/api/employee/attendance/attendance-export', {
      params: { month: search.substring(1) },
      method: 'get',
      responseType: 'blob',
      getResponse: true,
      skipErrorHandler: true,
    })
      .then((response) => {
        const url = URL.createObjectURL(
          new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        );
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', `${search.substring(1)!}考勤`); // 指定下载的文件名和类型
        document.body.appendChild(link);
        link.click();
      })
      .catch(() => {
        message.error('导出失败');
      });
  };

  return (
    <PageContainer
      header={{
        title: false,
      }}
      className="detail-container"
    >
      <ProForm formRef={formRef} submitter={false}>
        <Button
          type="primary"
          style={{ float: 'right', marginTop: '24px' }}
          onClick={() => downExcel()}
        >
          导出
        </Button>
        <ProTable<API.AttAdmission>
          className="inner-table"
          {...defaultTableConfig}
          pagination={false}
          options={false}
          actionRef={tableRef}
          columns={newEmployeeColumns}
          headerTitle="新员工入职列表"
          dataSource={data.attAdmissionList}
        />
        <ProTable<API.AttChangeJust>
          className="inner-table"
          {...defaultTableConfig}
          pagination={false}
          options={false}
          actionRef={tableRef}
          columns={probationaryColumns}
          headerTitle="转正人员列表"
          dataSource={data.attChangeJustList}
        />
        <ProTable<API.AttDimission>
          className="inner-table"
          {...defaultTableConfig}
          pagination={false}
          options={false}
          actionRef={tableRef}
          columns={resignatedColumns}
          headerTitle="离职人员列表"
          dataSource={data.attDimissionList}
        />
        <ProTable<API.AttLeave>
          className="inner-table"
          {...defaultTableConfig}
          pagination={false}
          options={false}
          actionRef={tableRef}
          columns={attendanceRecordColumns}
          headerTitle="考勤记录表"
          dataSource={data.attLeaveList}
        />
      </ProForm>
    </PageContainer>
  );
};

export default AttendanceDetail;
