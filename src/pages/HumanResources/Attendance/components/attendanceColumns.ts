import { ProColumns } from '@ant-design/pro-components';

// 表格
//新员工入职
export const newEmployeeColumns: ProColumns<API.AttAdmission>[] = [
  {
    title: '姓名',
    dataIndex: 'username',
    width: '150',
  },
  {
    title: '性别',
    dataIndex: 'gender',
  },
  {
    title: '身份证号',
    dataIndex: 'personId',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
  },
  {
    title: '银行',
    dataIndex: 'bank',
  },
  {
    title: '银行卡号',
    dataIndex: 'bankAcNo',
  },
  {
    title: '入职日期',
    dataIndex: 'dateOfJoining',
  },
  {
    title: '拟转正日期',
    dataIndex: 'simulationImportantTime',
  },
  {
    title: '试用期薪资',
    dataIndex: 'probationSalary',
    valueType: 'money',
  },
  {
    title: '转正薪资',
    dataIndex: 'changeJustSalary',
    valueType: 'money',
  },
  {
    title: '补贴',
    dataIndex: 'allowance',
    valueType: 'money',
  },
];
//转正人员
export const probationaryColumns: ProColumns<API.AttChangeJust>[] = [
  {
    title: '姓名',
    dataIndex: 'username',
    width: '150',
  },
  {
    title: '性别',
    dataIndex: 'gender',
  },
  {
    title: '身份证号',
    dataIndex: 'personId',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
  },
  {
    title: '转正日期',
    dataIndex: 'importantTime',
    valueType: 'date',
  },
  {
    title: '转正薪资',
    dataIndex: 'changeJustSalary',
    valueType: 'money',
  },
  {
    title: '补贴',
    dataIndex: 'allowance',
    valueType: 'money',
  },
];
//离职人员
export const resignatedColumns: ProColumns<API.AttDimission>[] = [
  {
    title: '姓名',
    dataIndex: 'username',
    width: '150',
  },
  {
    title: '性别',
    dataIndex: 'gender',
  },
  {
    title: '身份证号',
    dataIndex: 'personId',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
  },
  {
    title: '离职日期',
    dataIndex: 'relievingDate',
    valueType: 'date',
  },
  {
    title: '止薪日期',
    dataIndex: 'salaryEndDate',
    valueType: 'date',
  },
];
//考勤记录
export const attendanceRecordColumns: ProColumns<API.AttLeave>[] = [
  {
    title: '姓名',
    dataIndex: 'username',
    width: '150',
  },
  {
    title: '性别',
    dataIndex: 'gender',
  },
  {
    title: '休假类型',
    dataIndex: 'leaveTypeName',
  },
  {
    title: '开始日期',
    dataIndex: 'fromDate',
    valueType: 'date',
  },
  {
    title: '结束日期',
    dataIndex: 'toDate',
    valueType: 'date',
  },
  {
    title: '总休假天数',
    dataIndex: 'actualLeaveDays',
  },
];
