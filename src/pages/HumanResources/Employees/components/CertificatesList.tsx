import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import BaseContext from '@/Context/BaseContext';
import { useCertificateTypeList } from '@/hooks/useCertificateTypeList';
import { getRandomId } from '@/utils';
import { ProColumns, ProForm } from '@ant-design/pro-components';
import { useContext } from 'react';

const CertificatesList = () => {
  const { canSuperEdit: isAuthority, id, userName } = useContext(BaseContext);
  const { certificateTypeList = [], loading: certificateTypeLoading } = useCertificateTypeList();

  const columns: ProColumns<any>[] = [
    {
      title: '证书编号',
      dataIndex: 'cerNumber',
      width: 150,
    },
    {
      title: '证书名称',
      dataIndex: 'cerName',
      width: 150,
    },
    {
      title: '证书类别',
      dataIndex: 'type',
      valueType: 'select',
      fieldProps: () => ({
        options: certificateTypeList,
        showSearch: true,
        loading: certificateTypeLoading,
        fieldNames: {
          value: 'id',
          label: 'type',
        },
      }),
      width: 100,
    },
    {
      title: '厂商名称',
      dataIndex: 'companyName',
      width: 150,
    },
    {
      title: '证书等级',
      dataIndex: 'grade',
      width: 100,
    },
    {
      title: '取证时间',
      dataIndex: 'receiveTime',
      valueType: 'date',
      fieldProps: () => ({
        format: 'YYYY-MM-DD',
      }),
      width: 110,
    },
    {
      title: '有效期',
      dataIndex: 'valTime',
      valueType: 'date',
      fieldProps: () => ({
        format: 'YYYY-MM-DD',
      }),
      width: 110,
    },
  ];
  return (
    <>
      <ProForm.Item
        name="certificates"
        getValueProps={(val) => ({
          value: val?.map((item: Record<string, any>) => ({
            ...item,
            key_: item.key_ || getRandomId(),
            readonly: !isAuthority && item.save,
            userId: id,
            userName: userName,
          })),
        })}
        rules={[
          () => ({
            validator(_, values = []) {
              const resolve = values.every(
                (item: Record<string, any>) =>
                  item.cerNumber && item.cerName && item.type && item.companyName,
              );
              if (resolve) {
                return Promise.resolve();
              }
              return Promise.reject('请填写完整！');
            },
          }),
        ]}
      >
        <RKFormEditableProTable
          columns={columns}
          headerTitle="证书简历列表"
          operatorConfig={{
            copyType: 'next',
            ignoreField: ['save', 'id'],
          }}
        />
      </ProForm.Item>
    </>
  );
};
export default CertificatesList;
