import RKCol from '@/components/RKCol';
import BaseContext from '@/Context/BaseContext';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { ProFormDatePicker, ProFormText } from '@ant-design/pro-components';
import { Row } from 'antd';
import dayjs from 'dayjs';
import { useContext } from 'react';

const ArchivalInfo: React.FC<WithRouteEditingProps> = ({ isEditPage }) => {
  const { canSuperEdit: isAuthority } = useContext(BaseContext);

  return (
    <Row gutter={24}>
      <RKCol>
        <ProFormText
          disabled={!isAuthority}
          name="contractNum"
          label="劳动合同编号"
          placeholder="请输入"
          rules={[
            {
              required: true,
              message: '劳动合同编号为必填项',
            },
          ]}
        />
      </RKCol>
      <RKCol>
        <ProFormDatePicker
          disabled={!isAuthority}
          className="ant-picker"
          name="contractStartDate"
          label="合同开始时间"
          placeholder="请输入"
          rules={[
            {
              required: true,
              message: '合同开始时间为必填项',
            },
          ]}
          transform={(value, namePath) => {
            return {
              [namePath]: dayjs(value).format('YYYY-MM-DD'),
            };
          }}
        />
      </RKCol>
      <RKCol>
        <ProFormDatePicker
          disabled={!isAuthority}
          className="ant-picker"
          name="contractEndDate"
          label="合同结束时间"
          placeholder="请输入"
          rules={[
            {
              required: true,
              message: '合同结束时间为必填项',
            },
          ]}
          transform={(value, namePath) => {
            return {
              [namePath]: dayjs(value).format('YYYY-MM-DD'),
            };
          }}
        />
      </RKCol>
      <RKCol>
        <ProFormText
          disabled={!isAuthority}
          name="company"
          label="公司"
          placeholder="请输入"
          rules={[
            {
              required: true,
              message: '公司为必填项',
            },
          ]}
        />
      </RKCol>
      {isEditPage && (
        <>
          <RKCol>
            <ProFormText
              disabled={!isAuthority}
              name="totalWorkYears"
              label="参加工作年限"
              placeholder="请输入"
            />
          </RKCol>
          <RKCol>
            <ProFormText
              disabled={!isAuthority}
              name="rkWorkYears"
              label="公司在职年限"
              placeholder="请输入"
            />
          </RKCol>
        </>
      )}
    </Row>
  );
};

export default withRouteEditing(ArchivalInfo);
