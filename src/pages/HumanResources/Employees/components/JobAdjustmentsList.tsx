import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import BaseContext from '@/Context/BaseContext';
import { getRandomId } from '@/utils';
import { ProColumns, ProForm } from '@ant-design/pro-components';
import { useContext } from 'react';

const JobAdjustmentsList = () => {
  const { canSuperEdit: isAuthority } = useContext(BaseContext);

  const columns: ProColumns<any>[] = [
    {
      title: '变更日期',
      dataIndex: 'changeTime',
      valueType: 'date',
      fieldProps: () => ({
        format: 'YYYY-MM-DD',
      }),
    },
    {
      title: '调整类型',
      dataIndex: 'changeType',
      valueType: 'select',
      fieldProps: () => ({
        options: [
          { label: '调薪', value: '0' },
          { label: '调级', value: '1' },
          { label: '调工作岗位', value: '2' },
        ],
      }),
    },
    {
      title: '变更内容',
      dataIndex: 'changeContent',
    },
  ];
  return (
    <>
      <ProForm.Item
        name="jobAdjustments"
        getValueProps={(val) => ({
          value: val?.map((item: Record<string, any>) => ({
            ...item,
            key_: item.key_ || getRandomId(),
            readonly: !isAuthority && item.save,
          })),
        })}
      >
        <RKFormEditableProTable
          columns={columns}
          headerTitle="工作岗位调整记录表"
          operatorConfig={{
            copyType: 'next',
            ignoreField: ['save'],
          }}
        />
      </ProForm.Item>
    </>
  );
};
export default JobAdjustmentsList;
