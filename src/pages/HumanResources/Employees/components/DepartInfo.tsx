import RKCol from '@/components/RKCol';
import BaseContext from '@/Context/BaseContext';
import { ProFormDatePicker, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { Row } from 'antd';
import dayjs from 'dayjs';
import { useContext } from 'react';

const DepartInfo = () => {
  const { canSuperEdit: isAuthority } = useContext(BaseContext);

  return (
    <Row gutter={24}>
      <RKCol>
        <ProFormDatePicker
          disabled={!isAuthority}
          className="ant-picker"
          name="dimissionApplyDate"
          label="辞职申请日期"
          placeholder="请输入"
          transform={(value, namePath) => {
            return {
              [namePath]: dayjs(value).format('YYYY-MM-DD'),
            };
          }}
        />
      </RKCol>
      <RKCol>
        <ProFormDatePicker
          disabled={!isAuthority}
          className="ant-picker"
          name="relievingDate"
          label="离职日期"
          placeholder="请输入"
          transform={(value, namePath) => {
            return {
              [namePath]: dayjs(value).format('YYYY-MM-DD'),
            };
          }}
        />
      </RKCol>
      <RKCol>
        <ProFormDatePicker
          disabled={!isAuthority}
          className="ant-picker"
          name="salaryEndDate"
          label="止薪日期"
          placeholder="请输入"
          transform={(value, namePath) => {
            return {
              [namePath]: dayjs(value).format('YYYY-MM-DD'),
            };
          }}
        />
      </RKCol>
      <RKCol>
        <ProFormText name="newWorkAddress" label="新工作地点" disabled={!isAuthority} />
      </RKCol>
      <RKCol lg={12} md={16} sm={24}>
        <ProFormTextArea
          disabled={!isAuthority}
          name="dimissionReason"
          label="离职原因"
          fieldProps={{
            autoSize: {
              minRows: 1,
              maxRows: 3,
            },
          }}
        />
      </RKCol>
    </Row>
  );
};

export default DepartInfo;
