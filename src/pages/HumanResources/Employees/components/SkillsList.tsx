import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import BaseContext from '@/Context/BaseContext';
import { getRandomId } from '@/utils';
import { ProColumns, ProForm } from '@ant-design/pro-components';
import { useContext } from 'react';

const SkillsList = () => {
  const { canSuperEdit: isAuthority } = useContext(BaseContext);

  const columns: ProColumns<any>[] = [
    {
      title: '技能',
      dataIndex: 'skillName',
    },
  ];
  return (
    <>
      <ProForm.Item
        name="skills"
        getValueProps={(val) => ({
          value: val?.map((item: Record<string, any>) => ({
            ...item,
            key_: item.key_ || getRandomId(),
            readonly: !isAuthority && item.save,
          })),
        })}
      >
        <RKFormEditableProTable
          columns={columns}
          headerTitle="员工技能"
          operatorConfig={{
            copyType: 'next',
            ignoreField: ['save'],
          }}
        />
      </ProForm.Item>
    </>
  );
};
export default SkillsList;
