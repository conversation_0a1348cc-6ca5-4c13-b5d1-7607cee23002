import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import BaseContext from '@/Context/BaseContext';
import { getRandomId } from '@/utils';
import { ProColumns, ProForm } from '@ant-design/pro-components';
import { useContext } from 'react';

const TransList = () => {
  const { canSuperEdit: isAuthority } = useContext(BaseContext);

  const columns: ProColumns<any>[] = [
    {
      title: '起始时间',
      dataIndex: 'startTime',
      valueType: 'date',
      fieldProps: () => ({
        format: 'YYYY-MM-DD',
      }),
    },
    {
      title: '终止时间',
      dataIndex: 'endTime',
      valueType: 'date',
      fieldProps: () => ({
        format: 'YYYY-MM-DD',
      }),
    },
    {
      title: '培训名称',
      dataIndex: 'trainingName',
    },
    {
      title: '是否结业',
      dataIndex: 'isGraduation',
      valueType: 'select',
      fieldProps: () => ({
        options: [
          { label: '否', value: '0' },
          { label: '是', value: '1' },
        ],
      }),
    },
  ];
  return (
    <>
      <ProForm.Item
        name="trains"
        getValueProps={(val) => ({
          value: val?.map((item: Record<string, any>) => ({
            ...item,
            key_: item.key_ || getRandomId(),
            readonly: !isAuthority && item.save,
          })),
        })}
      >
        <RKFormEditableProTable
          columns={columns}
          headerTitle="培训简历"
          operatorConfig={{
            copyType: 'next',
            ignoreField: ['save'],
          }}
        />
      </ProForm.Item>
    </>
  );
};
export default TransList;
