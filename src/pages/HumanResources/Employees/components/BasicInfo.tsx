import RKCol from '@/components/RKCol';
import RKSelectLabel from '@/components/RKSelectLabel';
import BaseContext from '@/Context/BaseContext';
import { EMPLOYEE_STATUS } from '@/enums';
import { useDesignationList } from '@/hooks/useDesignationList';
import { useGradeList } from '@/hooks/useGradeList';
import { useUserList } from '@/hooks/useUserList';
import { getDepartmentTree } from '@/services/oa/department';
import { getEmploymentTypeList } from '@/services/oa/employmentType';
import { option2enum } from '@/utils';
import { requiredRule } from '@/utils/setting';
import { IdCard, phoneReg } from '@/utils/validator';
import {
  ProFormDatePicker,
  ProFormDependency,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Row } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import dayjs from 'dayjs';
import { useContext } from 'react';
import {
  GENDER,
  MARITAL_STATUS,
  NATION,
  POLITICS,
  TOP_EDUCATION,
  WORK_STATUS,
} from './EmployeesEnums';

const BasicInfo: React.FC<{ isEdit: boolean }> = ({ isEdit }) => {
  const { canSuperEdit: isAuthority } = useContext(BaseContext);
  const { userList, loading } = useUserList();
  const { data: treeData = [] } = useRequest(() => getDepartmentTree());

  //员工类别
  const { data: EmploymentTypeList = [], loading: EmploymentTypeListLoading } = useRequest(() =>
    getEmploymentTypeList(),
  );
  const { designationList, loading: designationListLoading } = useDesignationList();
  const { gradeList, loading: gradeLoading } = useGradeList();

  return (
    <Row gutter={24}>
      <RKCol>
        <ProFormText
          disabled={!isAuthority}
          name="employeeNumber"
          label="员工编号"
          placeholder="请输入"
          rules={[requiredRule]}
        />
      </RKCol>
      <RKCol>
        <ProFormText
          disabled={!isAuthority}
          name="username"
          label="姓名"
          placeholder="请输入"
          rules={[requiredRule]}
        />
      </RKCol>

      {!isEdit && (
        <RKCol>
          <ProFormText name="password" label="密码" placeholder="请输入" rules={[requiredRule]} />
        </RKCol>
      )}

      <RKCol>
        <ProFormSelect
          disabled={!isAuthority}
          name="gender"
          label="性别"
          placeholder="请输入"
          options={GENDER}
          rules={[requiredRule]}
        />
      </RKCol>

      <RKCol>
        <ProFormSelect
          disabled={!isAuthority}
          name="activation"
          label="激活状态"
          valueEnum={option2enum(EMPLOYEE_STATUS)}
          rules={[requiredRule]}
        />
      </RKCol>

      <RKCol>
        <ProFormSelect
          disabled={!isAuthority}
          name="employmentType"
          label="员工类别"
          fieldProps={{
            loading: EmploymentTypeListLoading,
            fieldNames: {
              value: 'id',
              label: 'employeeTypeName',
            },
            showSearch: true,
          }}
          options={(EmploymentTypeList as DefaultOptionType[]) || []}
          rules={[requiredRule]}
        />
      </RKCol>

      <RKCol>
        <ProFormDatePicker
          disabled={!isAuthority}
          className="ant-picker"
          name="dateOfJoining"
          label="入职日期"
          placeholder="请输入"
          rules={[requiredRule]}
          transform={(value, namePath) => {
            return {
              [namePath]: dayjs(value).format('YYYY-MM-DD'),
            };
          }}
        />
      </RKCol>
      <ProFormDependency name={['employmentType', 'practiceEndDate']}>
        {({ employmentType, practiceEndDate }) => {
          if (employmentType === '2' || practiceEndDate) {
            return (
              <RKCol>
                <ProFormDatePicker
                  disabled={!isAuthority}
                  className="ant-picker"
                  name="practiceEndDate"
                  label="实习结束日期"
                  placeholder="请输入"
                  rules={[requiredRule]}
                  transform={(value, namePath) => {
                    return {
                      [namePath]: dayjs(value).format('YYYY-MM-DD'),
                    };
                  }}
                />
              </RKCol>
            );
          }
        }}
      </ProFormDependency>

      <RKCol>
        <ProFormDatePicker
          disabled={!isAuthority}
          className="ant-picker"
          name="importantTime"
          label="转正日期"
          placeholder="请输入"
          rules={[requiredRule]}
          transform={(value, namePath) => {
            return {
              [namePath]: dayjs(value).format('YYYY-MM-DD'),
            };
          }}
        />
      </RKCol>
      <RKCol>
        <ProFormSelect
          disabled={!isAuthority}
          name="workStatus"
          label="岗位状态"
          options={WORK_STATUS}
          rules={[requiredRule]}
          fieldProps={{ showSearch: true }}
        />
      </RKCol>

      <RKCol>
        <ProFormText
          disabled={!isAuthority}
          name="personId"
          label="身份证号"
          placeholder="请输入"
          rules={[
            requiredRule,
            {
              pattern: IdCard,
              message: '格式不正确',
            },
          ]}
        />
      </RKCol>

      <RKCol>
        <ProFormDatePicker
          disabled
          className="ant-picker"
          name="dateOfBirth"
          label="出生日期"
          placeholder="请输入"
          rules={[requiredRule]}
          transform={(value, namePath) => {
            return {
              [namePath]: dayjs(value).format('YYYY-MM-DD'),
            };
          }}
        />
      </RKCol>
      <RKCol>
        <ProFormText disabled name="age" label="年龄" placeholder="请输入" rules={[requiredRule]} />
      </RKCol>
      <RKCol>
        <ProFormText
          disabled={!isAuthority}
          name="phone"
          label="电话"
          placeholder="请输入"
          rules={[
            requiredRule,
            {
              pattern: phoneReg,
              message: '格式不正确',
            },
          ]}
        />
      </RKCol>

      <RKCol>
        <ProFormSelect
          disabled={!isAuthority}
          name="department"
          label="部门"
          rules={[requiredRule]}
          options={treeData
            ?.filter((item) => item.status === '1')
            ?.map((item) => ({
              label: item.departmentName,
              value: item.id,
            }))}
          fieldProps={{
            showSearch: true,
          }}
        />
      </RKCol>
      <ProFormDependency name={['department']}>
        {({ department }) => {
          const departmentBranch = treeData?.find((item) => item.id === department)?.child || [];
          return (
            <>
              <RKCol>
                <ProFormSelect
                  disabled={!isAuthority}
                  name="departmentBranch"
                  label="子部门"
                  options={departmentBranch
                    ?.filter((item) => item.status === '1')
                    ?.map((item) => ({
                      label: item.departmentName,
                      value: item.id,
                    }))}
                  fieldProps={{
                    showSearch: true,
                  }}
                />
              </RKCol>
              <RKCol>
                <ProFormSelect
                  disabled={!isAuthority}
                  name="designation"
                  label="职位"
                  fieldProps={{
                    loading: designationListLoading,
                    fieldNames: {
                      value: 'id',
                      label: 'positionName',
                    },
                    showSearch: true,
                  }}
                  options={
                    designationList.filter(
                      (item) => item.departmentId === department,
                    ) as DefaultOptionType[]
                  }
                  rules={[requiredRule]}
                />
              </RKCol>
            </>
          );
        }}
      </ProFormDependency>

      <RKCol>
        <ProFormSelect
          name="reportsToName"
          label="直属上级"
          placeholder="请输入"
          disabled={!isAuthority}
          fieldProps={{
            loading,
            showSearch: true,
            optionLabelProp: 'username',
          }}
          options={userList.map((item) => ({
            value: item.username,
            label: <RKSelectLabel title={item.username} info={item.employeeNumber} />,
            username: item.username,
          }))}
          transform={(value) => {
            return {
              reportsToName: value,
              reportsToNumber: userList.find((item) => item.username === value)?.employeeNumber,
            };
          }}
          rules={[requiredRule]}
        />
      </RKCol>

      <RKCol>
        <ProFormText
          disabled={!isAuthority}
          name="workPlace"
          label="工作地点"
          placeholder="请输入"
          rules={[requiredRule]}
        />
      </RKCol>
      <RKCol>
        <ProFormDatePicker
          disabled={!isAuthority}
          className="ant-picker"
          name="firstWorkTime"
          label="初始工作时间"
          placeholder="请输入"
          rules={[requiredRule]}
          transform={(value, namePath) => {
            return {
              [namePath]: dayjs(value).format('YYYY-MM-DD'),
            };
          }}
        />
      </RKCol>
      <RKCol>
        <ProFormText
          disabled={!isAuthority}
          name="socialSecurity"
          label="社保缴纳地"
          placeholder="请输入"
          rules={[requiredRule]}
        />
      </RKCol>

      <RKCol>
        <ProFormSelect
          disabled={!isAuthority}
          name="nation"
          label="民族"
          placeholder="请输入"
          options={NATION}
          rules={[requiredRule]}
          fieldProps={{
            showSearch: true,
          }}
        />
      </RKCol>
      <RKCol>
        <ProFormText name="nativePlace" label="籍贯" placeholder="请输入" disabled={!isAuthority} />
      </RKCol>
      <RKCol>
        <ProFormText
          name="currentAddress"
          label="居住地址"
          placeholder="请输入"
          rules={[requiredRule]}
        />
      </RKCol>
      <RKCol>
        <ProFormSelect
          disabled={!isAuthority}
          name="maritalStatus"
          label="婚姻状况"
          options={MARITAL_STATUS}
          rules={[requiredRule]}
        />
      </RKCol>

      <RKCol>
        <ProFormSelect
          disabled={!isAuthority}
          name="topEducation"
          label="最高学历"
          options={TOP_EDUCATION}
          rules={[requiredRule]}
        />
      </RKCol>
      <RKCol>
        <ProFormText
          disabled={!isAuthority}
          name="graduationSchool"
          label="毕业院校"
          placeholder="请输入"
          rules={[requiredRule]}
        />
      </RKCol>
      <RKCol>
        <ProFormDatePicker
          disabled={!isAuthority}
          className="ant-picker"
          name="graduationDate"
          label="毕业时间"
          placeholder="请输入"
          rules={[requiredRule]}
          transform={(value, namePath) => {
            return {
              [namePath]: dayjs(value).format('YYYY-MM-DD'),
            };
          }}
        />
      </RKCol>
      <RKCol>
        <ProFormText
          disabled={!isAuthority}
          name="major"
          label="专业"
          placeholder="请输入"
          rules={[requiredRule]}
        />
      </RKCol>
      <RKCol>
        <ProFormSelect
          disabled={!isAuthority}
          name="politics"
          label="政治面貌"
          options={POLITICS}
          rules={[requiredRule]}
        />
      </RKCol>
      <RKCol>
        <ProFormSelect
          disabled={!isAuthority}
          name="isStu"
          label="是否应届毕业生"
          options={[
            {
              label: '是',
              value: '1',
            },
            {
              label: '否',
              value: '0',
            },
          ]}
        />
      </RKCol>

      <RKCol>
        <ProFormText
          disabled={!isAuthority}
          name="personToBeContacted"
          label="紧急联系人"
          placeholder="请输入"
          rules={[requiredRule]}
        />
      </RKCol>
      <RKCol>
        <ProFormText
          disabled={!isAuthority}
          name="relation"
          label="联系人关系"
          placeholder="请输入"
          rules={[requiredRule]}
        />
      </RKCol>
      <RKCol>
        <ProFormText
          disabled={!isAuthority}
          name="emergencyPhoneNumber"
          label="紧急联系人方式1"
          placeholder="请输入"
          rules={[
            requiredRule,
            {
              pattern: phoneReg,
              message: '格式不正确',
            },
          ]}
        />
      </RKCol>
      <RKCol>
        <ProFormText
          name="rgencyPhoneNumber2"
          label="紧急联系人方式2"
          placeholder="请输入"
          disabled={!isAuthority}
        />
      </RKCol>

      <RKCol>
        <ProFormText
          disabled={!isAuthority}
          name="email"
          label="公司邮箱"
          placeholder="请输入"
          rules={[
            requiredRule,
            {
              type: 'email',
              message: '格式不正确',
            },
          ]}
        />
      </RKCol>
      <RKCol>
        <ProFormText
          name="cellNumber"
          label="联系电话"
          placeholder="请输入"
          rules={[
            requiredRule,
            {
              pattern: phoneReg,
              message: '格式不正确',
            },
          ]}
        />
      </RKCol>
      <RKCol>
        <ProFormMoney
          disabled={!isAuthority}
          name="basicSalary"
          label="基本工资"
          placeholder="请输入"
          rules={[requiredRule]}
          min={0}
          locale="zh-CN"
          fieldProps={{ precision: 3 }}
          transform={(value, namePath) => {
            return { [namePath]: String(value) };
          }}
        />
      </RKCol>
      <RKCol>
        <ProFormSelect
          disabled={!isAuthority}
          name="grade"
          label="福利等级"
          rules={[requiredRule]}
          fieldProps={{
            loading: gradeLoading,
            fieldNames: {
              value: 'id',
              label: 'grade',
            },
            showSearch: true,
          }}
          options={gradeList as DefaultOptionType[]}
        />
      </RKCol>
      <RKCol>
        <ProFormText name="technicalExpertise" label="技术特长" placeholder="请输入" />
      </RKCol>
      <RKCol>
        <ProFormText
          name="accountOpening"
          label="开户行"
          placeholder="请输入"
          disabled={!isAuthority}
        />
      </RKCol>
      <RKCol>
        <ProFormText
          name="bankAcNo"
          label="银行账号"
          placeholder="请输入"
          disabled={!isAuthority}
        />
      </RKCol>
    </Row>
  );
};

export default BasicInfo;
