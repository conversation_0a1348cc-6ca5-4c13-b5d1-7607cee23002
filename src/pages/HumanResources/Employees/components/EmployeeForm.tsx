import ChangeLog from '@/components/ChangeLog';
import BaseContext from '@/Context/BaseContext';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { archives, updateUser, userCreate, userInfo } from '@/services/oa/auth';
import { onSuccessAndGoBack } from '@/utils';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { useAccess, useRequest } from '@umijs/max';
import { Collapse, message } from 'antd';
import dayjs from 'dayjs';
import { useRef } from 'react';
import ArchivalInfo from './ArchivalInfo';
import BasicInfo from './BasicInfo';
import CertificatesList from './CertificatesList';
import DepartInfo from './DepartInfo';
import EducationsList from './EducationsList';
import JobAdjustmentsList from './JobAdjustmentsList';
import ProjectsList from './ProjectsList';
import SkillsList from './SkillsList';
import TransList from './TransList';
import WorksList from './WorksList';

const EmployeeForm: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  //判断是否为编辑页面
  const formRef = useRef<ProFormInstance>();
  const { canEditEmployee, canAddEmployee, canSuperEditEmployee } = useAccess();
  const canEdit = (canEditEmployee && isEditPage) || (!isEditPage && canAddEmployee);
  // 超管权限
  const canSuperEdit = (canSuperEditEmployee && isEditPage) || (!isEditPage && canAddEmployee);
  const certificateUserName = useRef<string>('');

  const { refresh, loading: pageLoading } = useRequest(() => userInfo({ req: { id: id! } }), {
    ready: isEditPage,
    onSuccess: (res) => {
      //每次获取详情时根据当前年月日更新年龄
      const backData = res as API.UserDetailResp;
      //note: 考虑到人事操作变更证书
      certificateUserName.current = backData?.username as string;
      const data = {
        ...backData,
        age: Math.trunc(
          Math.abs(
            dayjs(dayjs(new Date()).format('YYYY-MM-DD')).diff(backData?.dateOfBirth, 'month'),
          ) / 12,
        ).toString(),
      } as API.UserDetailResp;
      const listObj = Object.fromEntries(
        Object.entries(data).filter((item) => Array.isArray(item[1])),
      );
      const formData = {
        ...Object.fromEntries(Object.entries(data).filter((item) => !Array.isArray(item[1]))),
        permissions: listObj.permissions,
      };
      delete listObj.permissions;
      const obj = Object.fromEntries(
        Object.entries(listObj).map((item) => {
          const arr = (item[1] as { [key: string]: string }[]).map((item) => {
            //判断是否存在save字段，存在则禁用删除按钮，不存在则可删除
            const isDel = !!item?.save ?? false;
            //判断权限，超级管理员则可删除，不是超级管理员则依据是否存在save字段来判断，若是复制了已保存的数据则联系管理员删除
            const disabledDelete = canSuperEdit ? false : isDel;
            return {
              ...item,
              disabledDelete,
            };
          });
          return [item[0], [...arr]];
        }),
      );
      const data1 = { ...formData, ...obj };
      setTimeout(() => formRef.current?.setFieldsValue(data1), 100);
    },
  });

  // 新建
  const { run: addBaseInfo, loading: addBaseInfoLoading } = useRequest(
    (value) => userCreate(value),
    {
      manual: true,
      onSuccess: onSuccessAndGoBack,
      formatResult: (res) => res,
    },
  );
  const { run: addArchives, loading: addDetailLoading } = useRequest((value) => archives(value), {
    manual: true,
    formatResult: (res) => res,
  });
  // 修改
  const { run: editBaseInfo, loading: editBaseInfoLoading } = useRequest(
    (value) => updateUser(value),
    {
      manual: true,
      formatResult: (res) => res,
    },
  );

  return (
    <PageContainer header={{ title: false }} className="detail-container" loading={pageLoading}>
      <ProForm
        formRef={formRef}
        disabled={!canEdit}
        submitter={
          canEdit && {
            searchConfig: {
              submitText: '保存',
              resetText: '取消',
            },
            onReset: () => {
              history.go(-1);
            },
            submitButtonProps: {
              loading: addBaseInfoLoading || editBaseInfoLoading || addDetailLoading,
            },
            render: (props, doms) => {
              return <FooterToolbar>{doms}</FooterToolbar>;
            },
          }
        }
        onFinish={async (value) => {
          const { permissions } = value;
          const formData = {
            ...Object.fromEntries(Object.entries(value).filter((item) => !Array.isArray(item[1]))),
            permissions: permissions,
          };

          const { works, projects, trains, certificates, jobAdjustments, skills, educations } =
            value;
          const archives = {
            works,
            projects,
            trains,
            certificates,
            jobAdjustments,
            skills,
            educations,
          };
          const baseInfoRes = isEditPage
            ? await Promise.all([
                editBaseInfo({ ...formData, id: id }),
                addArchives({ ...archives, id: id }),
              ])
            : [await addBaseInfo(formData)];
          const success = baseInfoRes
            .map((item) => item.code)
            .every((currentValue) => currentValue === 200);
          if (success) {
            message.success('操作成功！');
            refresh();
          }
          return success;
        }}
        initialValues={{ activation: '1' }}
        onValuesChange={(val) => {
          //当身份证变更时更新出生日期与年龄
          if (val.personId) {
            const len = val.personId?.length;
            let birthday;
            if (len <= 15) {
              birthday = `19${val.personId.substring(6, 8)}-${val.personId.substring(
                8,
                10,
              )}-${val.personId.substring(10, 12)}`;
            } else {
              birthday = `${val.personId?.substring(6, 10)}-${val.personId?.substring(
                10,
                12,
              )}-${val.personId?.substring(12, 14)}`;
            }
            const age = Math.trunc(
              Math.abs(dayjs(dayjs(new Date()).format('YYYY-MM-DD')).diff(birthday, 'month')) / 12,
            ).toString();
            formRef.current?.setFieldsValue({ dateOfBirth: birthday, age: age });
          }
          //部门改变时清空子部门
          if (val.department) {
            formRef.current?.setFieldValue('designation', undefined);
            formRef.current?.setFieldValue('departmentBranch', undefined);
          }
        }}
      >
        {/* 不需要展示，只是为了form传值 */}
        <div style={{ display: 'none' }}>
          <ProFormText name="id" label="id" placeholder="请输入" />
        </div>

        <BaseContext.Provider value={{ canSuperEdit, id, userName: certificateUserName.current }}>
          <Collapse defaultActiveKey={['1', '2', '3', '4']} ghost collapsible="header">
            <Collapse.Panel key="1" header="员工基础信息 ">
              <BasicInfo isEdit={isEditPage} />
            </Collapse.Panel>
            <Collapse.Panel key="2" header="员工档案信息 ">
              <ArchivalInfo />
            </Collapse.Panel>
            {isEditPage && (
              <Collapse.Panel key="3" header="档案详细信息">
                <WorksList />
                <ProjectsList />
                <TransList />
                <CertificatesList />
                <JobAdjustmentsList />
                <SkillsList />
                <EducationsList />
              </Collapse.Panel>
            )}
            <Collapse.Panel key="4" header="离职详细信息 ">
              <DepartInfo />
            </Collapse.Panel>
          </Collapse>
        </BaseContext.Provider>
        <ChangeLog />
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(EmployeeForm);
