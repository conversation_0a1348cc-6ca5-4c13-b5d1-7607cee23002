import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import BaseContext from '@/Context/BaseContext';
import { getRandomId } from '@/utils';
import { ProColumns, ProForm } from '@ant-design/pro-components';
import { useContext } from 'react';

const ProjectsList = () => {
  const { canSuperEdit: isAuthority } = useContext(BaseContext);

  const columns: ProColumns<any>[] = [
    {
      title: '起始时间',
      dataIndex: 'startTime',
      valueType: 'date',
      fieldProps: () => ({
        format: 'YYYY-MM-DD',
      }),
    },
    {
      title: '终止时间',
      dataIndex: 'endTime',
      valueType: 'date',
      fieldProps: () => ({
        format: 'YYYY-MM-DD',
      }),
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
    },
    {
      title: '担任岗位',
      dataIndex: 'position',
    },
    {
      title: '工作内容',
      dataIndex: 'content',
      valueType: 'textarea',
      fieldProps: () => ({
        autoSize: { minRows: 1, maxRows: 4 },
      }),
      width: 350,
    },
  ];
  return (
    <>
      <ProForm.Item
        name="projects"
        getValueProps={(val) => ({
          value: val?.map((item: Record<string, any>) => ({
            ...item,
            key_: item.key_ || getRandomId(),
            readonly: !isAuthority && item.save,
          })),
        })}
      >
        <RKFormEditableProTable
          columns={columns}
          headerTitle="项目简历"
          operatorConfig={{
            copyType: 'next',
            ignoreField: ['save'],
          }}
        />
      </ProForm.Item>
    </>
  );
};
export default ProjectsList;
