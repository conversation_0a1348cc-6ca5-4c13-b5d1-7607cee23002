import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import BaseContext from '@/Context/BaseContext';
import { getRandomId, option2enum } from '@/utils';
import { ProColumns, ProForm } from '@ant-design/pro-components';
import { useContext } from 'react';
import { TOP_EDUCATION } from './EmployeesEnums';

const EducationsList = () => {
  const { canSuperEdit: isAuthority } = useContext(BaseContext);

  const columns: ProColumns<any>[] = [
    {
      title: '学校',
      dataIndex: 'schoolName',
    },
    {
      title: '学历',
      dataIndex: 'education',
      valueType: 'select',
      valueEnum: option2enum(TOP_EDUCATION),
    },
    {
      title: '专业',
      dataIndex: 'majorName',
    },
    {
      title: '毕业年份',
      dataIndex: 'graduateYear',
      valueType: 'dateYear',
    },
    {
      title: '主/选修科目',
      dataIndex: 'subjectName',
    },
  ];
  return (
    <>
      <ProForm.Item
        name="educations"
        getValueProps={(val) => ({
          value: val?.map((item: Record<string, any>) => ({
            ...item,
            key_: item.key_ || getRandomId(),
            readonly: !isAuthority && item.save,
          })),
        })}
      >
        <RKFormEditableProTable
          columns={columns}
          headerTitle="教育培训列表"
          operatorConfig={{
            copyType: 'next',
            ignoreField: ['save'],
          }}
        />
      </ProForm.Item>
    </>
  );
};
export default EducationsList;
