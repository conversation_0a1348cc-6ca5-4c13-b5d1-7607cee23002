import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { EMPLOYEE_STATUS } from '@/enums';
import { resetPassword, updateActivation, userPage, usersByDepartmentId } from '@/services/oa/auth';
import { getDepartmentWeeklyTree } from '@/services/oa/department';
import { option2enum, queryPagingTable } from '@/utils';
import { BASE_URL, defaultTableConfig } from '@/utils/setting';
import { phoneReg } from '@/utils/validator';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal, Space, Upload, UploadProps } from 'antd';
import React, { useRef, useState } from 'react';

//上传下载多选权限为超级权限才可以操作
const fileSize = 20 * 1024 * 1024;
const fileType =
  '.xlsx,.xls,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel';
const props: UploadProps = {
  name: 'file',
  action: `${BASE_URL}/api/user/users-import`,
  accept: fileType,
  headers: { Authorization: localStorage.getItem('RKLINK_OA_TOKEN')! },
  method: 'post',
  maxCount: 1,
  showUploadList: false,
  beforeUpload: (file) => {
    const { size, type } = file;
    const isExcel = fileType.includes(type);
    const isOverSize = size > fileSize;
    if (!isExcel) {
      message.error('请上传excel格式的文件');
    } else if (isOverSize) {
      message.error(`上传文件大小不能超过（最大${fileSize / 1024 / 1024}MB）`);
    }
    return isExcel && !isOverSize;
  },
  onChange: (info) => {
    const { status } = info?.file;
    if (status === 'done') {
      const { code, message: msg } = info.file.response;
      if (code !== 200) {
        message.error(msg);
      } else {
        message.success(`${info.file.name}上传成功!`);
      }
    }
  },
};

const Employees: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [rowId, setRowId] = useState<string>();
  const [selectedRows, setSelectedRows] = useState<API.UserPageResp[]>([]);
  const { canResetEmployee, canAddEmployee, canSuperEditEmployee } = useAccess();
  const { data: departmentTreeList = [], loading: departmentTreeLoading } = useRequest(
    () => getDepartmentWeeklyTree(),
    {
      formatResult: (res) =>
        res?.data?.map((i) => ({
          children: i.child?.map((chi) => ({
            value: chi.id,
            label: chi.departmentName,
          })),
          value: i.id,
          label: i.departmentName,
        })) || [],
    },
  );

  const { run: disabledRecord, loading } = useRequest((obj) => updateActivation(obj), {
    manual: true,
    onSuccess: (res, params) => {
      if (res.code !== 200) return;
      const text = params.at(0).status === 0 ? '启用' : '禁用';
      message.success(`${text}成功`);
      setRowId(undefined);
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDisabled = (row: API.UserPageResp) => {
    const text = row.activation === 1 ? '禁用' : '启用';
    Modal.confirm({
      title: `确认“${text}”`,
      content: `您确定要“${text}”员工“${row.username}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        setRowId(row.id);
        const status = row.activation === 0 ? 1 : 0;
        const obj = { id: row.id, activation: status };
        disabledRecord(obj);
      },
    });
  };

  const { run: resetPwd } = useRequest((obj) => resetPassword(obj), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('操作成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const onReset = (row: API.UserPageResp) => {
    const obj = { id: row.id!, password: '123456' };
    Modal.confirm({
      title: '确认重置',
      content: `您确定要重置“${row.username}”密码吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        resetPwd(obj);
      },
    });
  };
  //批量导出
  const { run: exportRecord } = useRequest(
    (ids) =>
      usersByDepartmentId(ids, {
        responseType: 'blob',
        getResponse: true,
        skipErrorHandler: true,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        const url = URL.createObjectURL(
          new Blob([res.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        );
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', `员工简历`);
        document.body.appendChild(link);
        link.click();
        tableRef.current?.reloadAndRest?.();
      },
      formatResult: (res) => res,
    },
  );
  const handleExport = async (rows: API.UserPageResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(String(item.id)!);
      names.push(item.username!);
    });
    Modal.confirm({
      title: '确认导出',
      content: `您确定要导出员工“${names.join('、')}”简历吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        exportRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.UserPageResp>[] = [
    {
      title: '编号',
      dataIndex: 'employeeNumber',
      copyable: true,
      ellipsis: true,
      width: 100,
      render: (dom, entity) => (
        <a
          className="rk-a-span"
          onClick={() => history.push(`/human-resources/employees/edit/${entity?.id}`)}
        >
          {dom}
        </a>
      ),
    },
    {
      title: '姓名',
      dataIndex: 'employeeName',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      formItemProps: {
        rules: [{ type: 'email', message: '格式不正确' }],
      },
    },
    {
      title: '激活状态',
      dataIndex: 'activation',
      valueType: 'select',
      valueEnum: option2enum(EMPLOYEE_STATUS),
    },
    {
      title: '部门',
      dataIndex: 'department',
      valueType: 'treeSelect',
      fieldProps: {
        loading: departmentTreeLoading,
        options: departmentTreeList,
      },
    },
    {
      title: '直属上级姓名',
      dataIndex: 'reportsToName',
      search: false,
    },
    {
      title: '联系电话',
      dataIndex: 'cellNumber',
      formItemProps: {
        rules: [{ pattern: phoneReg, message: '格式不正确' }],
      },
    },
    {
      title: '工作地点',
      dataIndex: 'workPlace',
      search: false,
    },
    {
      title: '操作',
      width: 200,
      fixed: 'right',
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            {canResetEmployee && (
              <>
                <a key="reset" onClick={() => onReset(record)}>
                  重置密码
                </a>
                <Button
                  style={{ margin: 0, padding: 0 }}
                  type="link"
                  key="disabled"
                  loading={loading && rowId === record.id}
                  onClick={() => handleDisabled(record)}
                >
                  {record.activation === 1 ? '禁用' : '启用'}
                </Button>
              </>
            )}
            {canSuperEditEmployee && (
              <Button
                style={{ margin: 0, padding: 0 }}
                key="export"
                type="link"
                onClick={() => handleExport([record])}
              >
                导出
              </Button>
            )}
          </Space>
        );
      },
      hideInTable: canResetEmployee ? false : true,
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.UserPageResp>
        {...defaultTableConfig}
        actionRef={tableRef}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        columns={columns}
        headerTitle="员工列表"
        rowSelection={
          canSuperEditEmployee
            ? {
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
              }
            : false
        }
        toolbar={{
          actions: [
            <>
              {canSuperEditEmployee && (
                <Upload {...props} key="inducts">
                  <Button icon={<UploadOutlined />} type="primary">
                    导入文件
                  </Button>
                </Upload>
              )}
              {canAddEmployee && (
                <Button
                  key="add"
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    history.push('/human-resources/employees/add');
                  }}
                >
                  新建员工
                </Button>
              )}
            </>,
          ],
        }}
        request={async (params) => {
          const {
            employeeName,
            employeeNumber,
            activation,
            department,
            email,
            cellNumber,
            current,
            pageSize,
          } = params;
          const search = {
            employeeName,
            employeeNumber,
            email,
            cellNumber,
          };
          const filter = {
            activation,
          };
          const extra = { department };
          for (const [key, value] of Object.entries(search)) {
            if (value === undefined || value === '' || value === null) {
              Reflect.deleteProperty(search, key);
            }
          }
          return queryPagingTable<API.PageReq>(
            { current, pageSize, filter, search, extra },
            userPage,
          );
        }}
      />
      <OperateFooterToolbar
        selectedRows={selectedRows}
        actions={[{ key: 'exports', label: '批量导出' }]}
        onOperation={() => {
          handleExport(selectedRows);
        }}
      />
    </PageContainer>
  );
};

export default Employees;
