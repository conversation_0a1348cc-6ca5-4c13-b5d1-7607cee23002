import { PageContainer, ProCard, ProDescriptions } from '@ant-design/pro-components';
import React from 'react';

const BillingInformation: React.FC = () => {
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProCard>
        <ProDescriptions column={2} title="开票信息">
          <ProDescriptions.Item valueType="text" label="更新日期">
            2021-08-10
          </ProDescriptions.Item>
          <ProDescriptions.Item valueType="text" label="公司邮寄地址">
            成都市高新区科园二路10号航利中心1栋1单元10层1号
          </ProDescriptions.Item>
          <ProDescriptions.Item valueType="text" label="发票抬头">
            四川融科智联科技有限公司
          </ProDescriptions.Item>
          <ProDescriptions.Item valueType="text" label="纳税人识别号">
            91510100MA6CTDB967
          </ProDescriptions.Item>
          <ProDescriptions.Item valueType="text" label="注册地址">
            成都高新区科园二路10号1栋1单元10层1号
          </ProDescriptions.Item>
          <ProDescriptions.Item valueType="text" label="电话">
            028-67646670
          </ProDescriptions.Item>
          <ProDescriptions.Item valueType="text" label="开户名(单位名称)">
            四川融科智联科技有限公司
          </ProDescriptions.Item>
          <ProDescriptions.Item valueType="text" label="开户行">
            中国工商银行股份有限公司成都天顺路支行
          </ProDescriptions.Item>
          <ProDescriptions.Item valueType="text" label="账号">
            *******************
          </ProDescriptions.Item>
        </ProDescriptions>
      </ProCard>
    </PageContainer>
  );
};

export default BillingInformation;
