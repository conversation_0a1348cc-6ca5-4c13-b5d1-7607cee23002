import { addressBook } from '@/services/oa/auth';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef } from 'react';

const AddressBook: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();

  // 表格
  const columns: ProColumns<any>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'index',
      search: false,
    },
    {
      title: '部门',
      dataIndex: 'department',
      search: false,
    },
    {
      title: '员工号',
      dataIndex: 'employeeNumber',
      search: false,
    },
    {
      title: '姓名',
      dataIndex: 'username',
    },
    {
      title: '手机',
      dataIndex: 'phone',
      search: false,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      search: false,
    },
    {
      title: '属地',
      dataIndex: 'workPlace',
      search: false,
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable
        {...defaultTableConfig}
        rowKey="employeeNumber"
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="公司同事通讯录"
        request={async (params) => {
          const { username } = params;
          const msg = await addressBook({ searchUsername: username });
          return {
            data: msg?.data || [],
            success: true,
          };
        }}
      />
    </PageContainer>
  );
};

export default AddressBook;
