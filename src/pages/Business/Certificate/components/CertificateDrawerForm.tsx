import BaseListContext from '@/Context/BaseListContext';
import { createCer, selectCerById, updateCer } from '@/services/oa/certificate';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProForm,
  ProFormDatePicker,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useAccess, useRequest } from '@umijs/max';
import { message } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { useContext, useEffect, useRef } from 'react';

const CertificateDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  onFinish,
  initialValues,
}) => {
  const isEditPage = !!initialValues?.id;
  const { canEditCertificateManagement = false, canAddCertificateManagement = false } = useAccess();
  const canEdit =
    (canEditCertificateManagement && isEditPage) || (!isEditPage && canAddCertificateManagement);
  const formRef = useRef<ProFormInstance>();
  const { userList, userLoading, certificateTypeList, certificateTypeLoading } =
    useContext(BaseListContext);

  // 获取详情
  const { run } = useRequest(() => selectCerById({ id: initialValues?.id }), {
    manual: true,
    onSuccess: (res) => {
      setTimeout(() => formRef.current?.setFieldsValue(res), 500);
    },
  });

  useEffect(() => {
    if (isEditPage && open) {
      run();
    }
  }, [isEditPage, open]);

  return (
    <DrawerForm<API.CertificateReq>
      width={753}
      title={isEditPage ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const { id } = value;
        const formData = {
          ...value,
          id,
        };
        const msg = isEditPage ? await updateCer(formData) : await createCer(formData);
        const success = msg.code === 200;
        if (success) {
          message.success('保存成功!');
          onFinish?.(value);
        }
        return success;
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      disabled={!canEdit}
      submitter={
        canEdit
          ? {
              searchConfig: {
                submitText: '保存',
                resetText: '取消',
              },
            }
          : false
      }
    >
      <div className="rk-none">
        <ProFormText name="id" />
      </div>
      <ProForm.Group>
        <ProFormSelect
          name="userId"
          label="姓名"
          rules={[requiredRule]}
          fieldProps={{
            loading: userLoading,
            fieldNames: {
              value: 'id',
              label: 'username',
            },
            showSearch: true,
          }}
          transform={(value, namePath) => {
            return {
              [namePath]: value,
              userName: userList?.find((item) => item.id === value)?.username,
            };
          }}
          options={userList as DefaultOptionType[]}
          width="md"
        />
        <ProFormText
          name="cerNumber"
          label="证书编号"
          fieldProps={{
            autoComplete: 'none',
          }}
          width="md"
          rules={[requiredRule]}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText
          name="cerName"
          label="证书名称"
          fieldProps={{
            autoComplete: 'none',
          }}
          rules={[requiredRule]}
          width="md"
        />
        <ProFormSelect
          name="type"
          label="证书类别"
          rules={[requiredRule]}
          width="md"
          options={certificateTypeList as DefaultOptionType[]}
          fieldProps={{
            loading: certificateTypeLoading,
            fieldNames: {
              value: 'id',
              label: 'type',
            },
            showSearch: true,
          }}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText name="companyName" label="厂商名称" rules={[requiredRule]} width="md" />
        <ProFormText name="grade" label="证书等级" width="md" />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDatePicker name="receiveTime" label="取证时间" width="md" />
        <ProFormDatePicker name="valTime" label="有效期" width="md" />
      </ProForm.Group>
    </DrawerForm>
  );
};
export default CertificateDrawerForm;
