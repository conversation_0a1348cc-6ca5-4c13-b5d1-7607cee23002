import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import BaseListContext from '@/Context/BaseListContext';
import { useCertificateTypeList } from '@/hooks/useCertificateTypeList';
import { useUserList } from '@/hooks/useUserList';
import { deleteCerByIds, pageCer } from '@/services/oa/certificate';
import { queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import React, { useRef, useState } from 'react';
import CertificateDrawerForm from './components/CertificateDrawerForm';

const Certificate: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.CertificateReq[]>([]);
  const [modalVisit, setModalVisit] = useState(false);
  const id = useRef<string>();
  const {
    canAddCertificateManagement = false,
    canDeleteCertificateManagement = false,
    canEditCertificateManagement = false,
  } = useAccess();
  const { userList = [], loading: userLoading } = useUserList();
  const { certificateTypeList = [], loading: certificateTypeLoading } = useCertificateTypeList();

  const { run: deleteRecord } = useRequest((ids) => deleteCerByIds({ ids: ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: API.CertificateReq[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.cerName!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除证书“${names.join('、')}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.CertificateReq>[] = [
    {
      title: '证书编号',
      dataIndex: 'cerNumber',
      copyable: true,
      ellipsis: true,
      width: 100,
      hideInSearch: true,
      render: (dom, entity) =>
        canEditCertificateManagement ? (
          <a
            className="rk-a-span"
            onClick={() => {
              {
                setModalVisit(true);
                id.current = entity.id!;
              }
            }}
          >
            {dom}
          </a>
        ) : (
          <span>{dom}</span>
        ),
    },
    {
      title: '证书名称',
      dataIndex: 'cerName',
      width: 300,
      ellipsis: true,
    },
    {
      title: '证书类别',
      dataIndex: 'type',
      valueType: 'select',
      fieldProps: () => ({
        options: certificateTypeList,
        showSearch: true,
        loading: certificateTypeLoading,
        fieldNames: {
          value: 'id',
          label: 'type',
        },
        filterOption: true,
        optionFilterProp: 'label',
      }),
      width: 120,
      ellipsis: true,
    },
    {
      title: '证书等级',
      dataIndex: 'grade',
      hideInSearch: true,
      width: 100,
      ellipsis: true,
    },
    {
      title: '姓名',
      width: 100,
      dataIndex: 'userId',
      valueType: 'select',
      fieldProps: () => ({
        options: userList,
        showSearch: true,
        loading: userLoading,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        filterOption: true,
        optionFilterProp: 'label',
      }),
    },
    {
      title: '取证时间',
      dataIndex: 'receiveTime',
      valueType: 'date',
      hideInSearch: true,
      width: 100,
      ellipsis: true,
    },
    {
      title: '有效期',
      dataIndex: 'valTime',
      valueType: 'date',
      hideInSearch: true,
      width: 100,
      ellipsis: true,
    },
    {
      title: '厂商名称',
      dataIndex: 'companyName',
      width: 120,
      ellipsis: true,
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (text, record) => {
        return (
          <Access accessible={canDeleteCertificateManagement}>
            <Button
              type="link"
              className="inner-table-link"
              key="delete"
              onClick={() => handleDelete([record])}
            >
              删除
            </Button>
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.CertificateReq>
        {...defaultTableConfig}
        actionRef={tableRef}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        rowSelection={
          canDeleteCertificateManagement
            ? {
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
              }
            : false
        }
        columns={columns}
        headerTitle="人员证书列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddCertificateManagement}>
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  id.current = '';
                  setModalVisit(true);
                }}
              >
                新建人员证书
              </Button>
            </Access>,
          ],
        }}
        request={async (params) => {
          const { companyName, cerName, userId, type, current, pageSize } = params;
          const search = { cerName, companyName };
          const filter = {
            userId,
            type,
          };
          for (const [key, value] of Object.entries(filter)) {
            if (value === undefined || value === '' || value === null) {
              Reflect.deleteProperty(filter, key);
            }
          }
          return queryPagingTable<API.PageReq>(
            {
              current,
              pageSize,
              search,
              filter,
            },
            pageCer,
          );
        }}
      />
      <BaseListContext.Provider
        value={{
          userList,
          userLoading,
          certificateTypeList,
          certificateTypeLoading,
        }}
      >
        <CertificateDrawerForm
          open={modalVisit}
          onOpenChange={(visible) => {
            setModalVisit(visible);
          }}
          onFinish={async () => {
            tableRef.current?.reload();
          }}
          initialValues={{ id: id.current }}
        />
      </BaseListContext.Provider>

      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default Certificate;
