import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { useUserList } from '@/hooks/useUserList';
import { deleteTender, pageTender } from '@/services/oa/business';
import { queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import React, { useRef, useState } from 'react';
import BidAccountDrawerForm from './components/BidAccountDrawerForm';

const Qualification: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.TenderResp[]>([]);
  const [modalVisit, setModalVisit] = useState(false);
  const id = useRef<string>();
  const {
    canAddBiddingAccountManagement = false,
    canDeleteBiddingAccountManagement = false,
    canEditBiddingAccountManagement = false,
  } = useAccess();

  const { userList = [], loading: userLoading } = useUserList();

  const { run: deleteRecord } = useRequest((ids) => deleteTender({ ids: ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: API.TenderResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(String(item.id)!);
      names.push(item.account!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.TenderResp>[] = [
    {
      title: '商务编号',
      dataIndex: 'tenderNumber',
      copyable: true,
      ellipsis: true,
      width: 180,
      hideInSearch: true,
      render: (dom, entity) =>
        canEditBiddingAccountManagement ? (
          <a
            className="rk-a-span"
            onClick={() => {
              {
                setModalVisit(true);
                id.current = entity.id!;
              }
            }}
          >
            {dom}
          </a>
        ) : (
          <span>{dom}</span>
        ),
    },
    {
      title: '平台名称',
      dataIndex: 'platformName',
    },
    {
      title: '公司名称',
      dataIndex: 'companyName',
    },
    {
      title: '网站',
      dataIndex: 'websiteName',
      hideInSearch: true,
    },
    {
      title: '账号',
      dataIndex: 'account',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      hideInSearch: true,
    },
    {
      title: '负责人',
      width: 150,
      dataIndex: 'liabilityPerson',
      valueType: 'select',
      fieldProps: () => ({
        options: userList,
        showSearch: true,
        loading: userLoading,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        filterOption: true,
        optionFilterProp: 'label',
      }),
    },
    {
      title: '联系人',
      dataIndex: 'contactPerson',
    },
    {
      title: '联系电话',
      dataIndex: 'contactNumber',
      hideInSearch: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      hideInSearch: true,
    },
    {
      title: '操作',
      width: 140,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (text, record) => {
        return (
          <Access accessible={canDeleteBiddingAccountManagement}>
            <Button
              type="link"
              className="inner-table-link"
              key="delete"
              onClick={() => handleDelete([record])}
            >
              删除
            </Button>
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.TenderResp>
        {...defaultTableConfig}
        actionRef={tableRef}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        rowSelection={
          canDeleteBiddingAccountManagement
            ? {
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
              }
            : false
        }
        columns={columns}
        headerTitle="投标账号列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddBiddingAccountManagement}>
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  id.current = '';
                  setModalVisit(true);
                }}
              >
                新建投标账号
              </Button>
            </Access>,
          ],
        }}
        request={async (params) => {
          const {
            platformName,
            companyName,
            account,
            liabilityPerson,
            contactPerson,
            current,
            pageSize,
          } = params;
          const search = { platformName, companyName, account, contactPerson };
          const filter = {
            liabilityPersonId: liabilityPerson || undefined,
          };
          for (const [key, value] of Object.entries(search)) {
            if (value === undefined || value === '' || value === null) {
              Reflect.deleteProperty(search, key);
            }
          }
          return queryPagingTable<API.PageReq>(
            {
              current,
              pageSize,
              search,
              filter,
            },
            pageTender,
          );
        }}
      />
      <BidAccountDrawerForm
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
        initialValues={{ id: id.current }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default Qualification;
