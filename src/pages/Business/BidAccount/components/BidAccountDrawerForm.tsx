import { useUserList } from '@/hooks/useUserList';
import { createTender, getTenderById, modifyTender } from '@/services/oa/business';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProForm,
  ProFormDateTimePicker,
  ProFormInstance,
  ProFormList,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useAccess, useRequest } from '@umijs/max';
import { message, Typography } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { useEffect, useRef } from 'react';
const { Title } = Typography;

const BidAccountDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  onFinish,
  initialValues,
}) => {
  const isEditPage = !!initialValues?.id;
  const { canEditBiddingAccountManagement = false, canAddBiddingAccountManagement = false } =
    useAccess();
  const canEdit =
    (canEditBiddingAccountManagement && isEditPage) ||
    (!isEditPage && canAddBiddingAccountManagement);
  const { userList = [], loading } = useUserList();
  const formRef = useRef<ProFormInstance>();

  // 获取详情
  const { run } = useRequest(() => getTenderById({ idReq: { id: initialValues?.id } }), {
    manual: true,
    onSuccess: (res) => {
      setTimeout(() => formRef.current?.setFieldsValue(res), 500);
    },
  });

  useEffect(() => {
    if (isEditPage && open) {
      run();
    }
  }, [isEditPage, open]);

  return (
    <DrawerForm<API.TenderReq & { id?: string }>
      width={753}
      title={isEditPage ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const { id } = value;
        const formData = {
          ...value,
          id,
        };
        const msg = isEditPage ? await modifyTender(formData) : await createTender(formData);
        const success = msg.code === 200;
        if (success) {
          message.success('保存成功!');
          onFinish?.(value);
        }
        return success;
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      disabled={!canEdit}
      submitter={
        canEdit
          ? {
              searchConfig: {
                submitText: '保存',
                resetText: '取消',
              },
            }
          : false
      }
    >
      <div className="rk-none">
        <ProFormText name="id" />
      </div>
      <ProForm.Group>
        <ProFormText
          name="platformName"
          label="平台名称"
          fieldProps={{
            autoComplete: 'none',
          }}
          rules={[requiredRule]}
          width="md"
        />
        <ProFormText
          name="companyName"
          label="公司名称"
          fieldProps={{
            autoComplete: 'none',
          }}
          rules={[requiredRule]}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText
          name="websiteName"
          label="网站"
          fieldProps={{
            autoComplete: 'none',
          }}
          width="md"
        />
        <ProFormText
          name="account"
          label="账号"
          fieldProps={{
            autoComplete: 'none',
          }}
          rules={[requiredRule]}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText.Password
          name="password"
          label="密码"
          fieldProps={{
            autoComplete: 'none',
          }}
          rules={[requiredRule]}
          width="md"
        />
        <ProFormText
          name="email"
          label="邮箱"
          fieldProps={{
            autoComplete: 'none',
          }}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText.Password
          name="caPassword"
          label="ca密码"
          fieldProps={{
            autoComplete: 'none',
          }}
          width="md"
        />
        <ProFormDateTimePicker
          name="expireTime"
          label="ca到期时间"
          fieldProps={{
            format: (value) => value.format('YYYY-MM-DD HH:mm:ss'),
            getPopupContainer: () => document.body,
          }}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDateTimePicker
          name="renewalTime"
          label="ca/平台续费时间"
          rules={[requiredRule]}
          fieldProps={{
            format: (value) => value.format('YYYY-MM-DD HH:mm:ss'),
            getPopupContainer: () => document.body,
          }}
          width="md"
        />
        <ProFormSelect
          name="liabilityPersonId"
          label="负责人"
          rules={[requiredRule]}
          fieldProps={{
            loading,
            fieldNames: {
              value: 'id',
              label: 'username',
            },
            showSearch: true,
          }}
          transform={(value, namePath) => {
            return {
              [namePath]: value,
              liabilityPerson: userList?.find((item) => item.id === value)?.username,
            };
          }}
          options={userList as DefaultOptionType[]}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText
          name="contactPerson"
          label="联系人"
          fieldProps={{
            autoComplete: 'none',
          }}
          width="md"
        />
        <ProFormText
          name="contactNumber"
          label="联系电话"
          fieldProps={{
            autoComplete: 'none',
          }}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormTextArea name="remark" label="备注" width="md" />
      </ProForm.Group>
      <Title level={5}>补充信息</Title>
      <ProFormList
        style={{ width: 710 }}
        name="supplement"
        creatorButtonProps={{
          creatorButtonText: '新建',
        }}
        creatorRecord={{
          tag: '',
          value: '',
        }}
        copyIconProps={false}
      >
        <ProForm.Group>
          <ProFormText key="tag" width="md" name="tag" label="名称" />
          <ProFormText key="value" width="md" name="value" label="内容" />
        </ProForm.Group>
      </ProFormList>
    </DrawerForm>
  );
};
export default BidAccountDrawerForm;
