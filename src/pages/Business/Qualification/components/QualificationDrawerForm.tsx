import { QUALIFICATION_TYPE } from '@/enums';
import { useUserList } from '@/hooks/useUserList';
import { createAptitude, getAptitudeById, modifyAptitude } from '@/services/oa/business';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProForm,
  ProFormDateTimePicker,
  ProFormInstance,
  ProFormList,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useAccess, useRequest } from '@umijs/max';
import { message, Typography } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { useEffect, useRef } from 'react';
const { Title } = Typography;

const QualificationDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  onFinish,
  initialValues,
}) => {
  const isEditPage = !!initialValues?.id;
  const { canEditQualificationManagement = false, canAddQualificationManagement = false } =
    useAccess();
  const canEdit =
    (canEditQualificationManagement && isEditPage) ||
    (!isEditPage && canAddQualificationManagement);
  const { userList = [], loading } = useUserList();
  const formRef = useRef<ProFormInstance>();

  // 获取详情
  const { run } = useRequest(() => getAptitudeById({ idReq: { id: initialValues?.id } }), {
    manual: true,
    onSuccess: (res) => {
      setTimeout(() => formRef.current?.setFieldsValue(res), 500);
    },
  });

  useEffect(() => {
    if (isEditPage && open) {
      run();
    }
  }, [isEditPage, open]);

  return (
    <DrawerForm<API.AptitudeReq & { id?: string }>
      width={753}
      title={isEditPage ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const { id } = value;
        const formData = {
          ...value,
          id,
        };
        const msg = isEditPage ? await modifyAptitude(formData) : await createAptitude(formData);
        const success = msg.code === 200;
        if (success) {
          message.success('保存成功!');
          onFinish?.(value);
        }
        return success;
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      disabled={!canEdit}
      submitter={
        canEdit
          ? {
              searchConfig: {
                submitText: '保存',
                resetText: '取消',
              },
            }
          : false
      }
    >
      <div className="rk-none">
        <ProFormText name="id" />
      </div>
      <ProForm.Group>
        <ProFormSelect
          width="md"
          name="aptitudeType"
          label="资质类型"
          rules={[requiredRule]}
          options={QUALIFICATION_TYPE}
        />
        <ProFormText
          name="aptitudeCategory"
          label="资质类别"
          fieldProps={{
            autoComplete: 'none',
          }}
          rules={[requiredRule]}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText
          name="aptitudeName"
          label="资质名称"
          fieldProps={{
            autoComplete: 'none',
          }}
          rules={[requiredRule]}
          width="md"
        />
        <ProFormText
          name="certificateNumber"
          label="资质编号"
          fieldProps={{
            autoComplete: 'none',
          }}
          rules={[requiredRule]}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDateTimePicker
          name="authTime"
          label="认证时间"
          rules={[requiredRule]}
          fieldProps={{
            format: (value) => value.format('YYYY-MM-DD HH:mm:ss'),
            getPopupContainer: () => document.body,
          }}
          width="md"
        />
        <ProFormDateTimePicker
          name="expireTime"
          label="到期时间"
          rules={[requiredRule]}
          fieldProps={{
            format: (value) => value.format('YYYY-MM-DD HH:mm:ss'),
            getPopupContainer: () => document.body,
          }}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDateTimePicker
          name="renewalTime"
          label="续费时间"
          rules={[requiredRule]}
          fieldProps={{
            format: (value) => value.format('YYYY-MM-DD HH:mm:ss'),
            getPopupContainer: () => document.body,
          }}
          width="md"
        />
        <ProFormMoney
          name="renewalAmount"
          label="续费金额"
          locale="zh_CN"
          rules={[requiredRule]}
          min={0}
          fieldProps={{ precision: 2, step: 0.1 }}
          width="md"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormSelect
          name="liabilityPersonId"
          label="负责人"
          rules={[requiredRule]}
          fieldProps={{
            loading,
            fieldNames: {
              value: 'id',
              label: 'username',
            },
            showSearch: true,
          }}
          transform={(value, namePath) => {
            return {
              [namePath]: value,
              liabilityPerson: userList?.find((item) => item.id === value)?.username,
            };
          }}
          options={userList as DefaultOptionType[]}
          width="md"
        />
      </ProForm.Group>
      <Title level={5}>补充信息</Title>
      <ProFormList
        style={{ width: 710 }}
        name="supplement"
        creatorButtonProps={{
          creatorButtonText: '新建',
        }}
        creatorRecord={{
          tag: '',
          value: '',
        }}
        copyIconProps={false}
      >
        <ProForm.Group>
          <ProFormText key="tag" width="md" name="tag" label="名称" />
          <ProFormText key="value" width="md" name="value" label="内容" />
        </ProForm.Group>
      </ProFormList>
    </DrawerForm>
  );
};
export default QualificationDrawerForm;
