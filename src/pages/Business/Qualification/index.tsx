import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { QUALIFICATION_TYPE } from '@/enums';
import { useUserList } from '@/hooks/useUserList';
import { deleteAptitude, pageAptitude } from '@/services/oa/business';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import React, { useRef, useState } from 'react';
import QualificationDrawerForm from './components/QualificationDrawerForm';

const Qualification: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.AptitudeResp[]>([]);
  const [modalVisit, setModalVisit] = useState(false);
  const id = useRef<string>();
  const {
    canAddQualificationManagement = false,
    canDeleteQualificationManagement = false,
    canEditQualificationManagement = false,
  } = useAccess();

  const { userList = [], loading: userLoading } = useUserList();

  const { run: deleteRecord } = useRequest((ids) => deleteAptitude({ ids: ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: API.AptitudeResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(String(item.id)!);
      names.push(item.aptitudeName!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.AptitudeResp>[] = [
    {
      title: '商务编号',
      dataIndex: 'aptitudeNumber',
      copyable: true,
      ellipsis: true,
      width: 180,
      hideInSearch: true,
      render: (dom, entity) =>
        canEditQualificationManagement ? (
          <a
            className="rk-a-span"
            onClick={() => {
              {
                setModalVisit(true);
                id.current = entity.id!;
              }
            }}
          >
            {dom}
          </a>
        ) : (
          <span>{dom}</span>
        ),
    },
    {
      title: '资质名称',
      dataIndex: 'aptitudeName',
    },
    {
      title: '资质编号',
      dataIndex: 'certificateNumber',
    },
    {
      title: '资质类型',
      dataIndex: 'aptitudeType',
      valueEnum: option2enum(QUALIFICATION_TYPE),
    },
    {
      title: '资质类别',
      dataIndex: 'aptitudeCategory',
    },
    {
      title: '认证时间',
      dataIndex: 'authTime',
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '到期时间',
      dataIndex: 'expireTime',
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '续费时间',
      dataIndex: 'renewalTime',
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '续费金额',
      dataIndex: 'renewalAmount',
      valueType: 'money',
    },
    {
      title: '负责人',
      width: 150,
      dataIndex: 'liabilityPerson',
      valueType: 'select',
      fieldProps: () => ({
        options: userList,
        showSearch: true,
        loading: userLoading,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        optionFilterProps: 'label',
        filterOption: true,
      }),
    },
    {
      title: '操作',
      width: 140,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (text, record) => {
        return (
          <Access accessible={canDeleteQualificationManagement}>
            <Button
              type="link"
              className="inner-table-link"
              key="delete"
              onClick={() => handleDelete([record])}
            >
              删除
            </Button>
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.AptitudeResp>
        {...defaultTableConfig}
        actionRef={tableRef}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        rowSelection={
          canDeleteQualificationManagement
            ? {
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
              }
            : false
        }
        columns={columns}
        headerTitle="资质列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddQualificationManagement}>
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  id.current = '';
                  setModalVisit(true);
                }}
              >
                新建资质
              </Button>
            </Access>,
          ],
        }}
        request={async (params) => {
          const {
            certificateNumber,
            aptitudeType,
            aptitudeCategory,
            aptitudeName,
            renewalAmount,
            liabilityPerson,
            current,
            pageSize,
          } = params;
          const search = { aptitudeName, certificateNumber, aptitudeCategory, renewalAmount };
          const filter = {
            aptitudeType,
            liabilityPersonId: liabilityPerson,
          };
          for (const [key, value] of Object.entries(filter)) {
            if (value === undefined || value === '' || value === null) {
              Reflect.deleteProperty(filter, key);
            }
          }
          return queryPagingTable<API.PageReq>(
            {
              current,
              pageSize,
              search,
              filter,
            },
            pageAptitude,
          );
        }}
      />
      <QualificationDrawerForm
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
        initialValues={{ id: id.current }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default Qualification;
