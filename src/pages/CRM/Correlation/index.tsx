import { usePartnerList } from '@/hooks/usePartnerList';
import { useUserList } from '@/hooks/useUserList';
import { insDel, insEditSalePersson, insInsert, insPage } from '@/services/oa/institutionCustomer';
import { getRandomId, queryPagingTable } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import {
  ActionType,
  ModalForm,
  PageContainer,
  ProColumns,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { Access, useAccess, useRequest } from '@umijs/max';
import { Button, message } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { useRef, useState } from 'react';

const Correlation = () => {
  const tableRef = useRef<ActionType | undefined>();
  // 关联客户弹窗
  const [modalVisit, setModalVisit] = useState(false);
  // 关联销售弹窗
  const [modalSalesVisit, setSalesModalVisit] = useState(false);
  const [initialValues, setInitialValues] = useState<
    API.InstitutionListResp & { institutionId?: string; clientMangers?: API.PartnerInfoResp[] }
  >({});
  const [initialSalesValues, setInitialSalesValues] = useState<{
    salePerson?: string[];
    parentId?: string;
  }>({});
  const {
    canAddCorrelation = false,
    canDeleteCorrelation = false,
    canEditCorrelationSales = false,
  } = useAccess();
  const { partnerList, loading } = usePartnerList();
  const { userList, loading: userLoading } = useUserList();
  //客户
  const customers = partnerList?.filter((i) => i?.partnerType === 'CLIENT');

  const handleRelated = (record: API.InstitutionListResp) => {
    setModalVisit(true);
    setInitialValues({ ...record, institutionId: record?.id });
  };

  const handleSales = (record: API.InstitutionCustomerListResp) => {
    setSalesModalVisit(true);
    setInitialSalesValues({
      salePerson: record?.salePerson?.split(',') || [],
      parentId: record?.parentId,
    });
  };

  const { run: deleteRecord } = useRequest((value) => insDel(value), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('移除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = (record: API.InstitutionListResp & { institutionId?: string }) => {
    const { id, institutionId } = record;
    deleteRecord({ customerId: id, institutionId });
  };

  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '单位名称(账户名)',
      dataIndex: 'institution',
      fixed: 'left',
      copyable: true,
      width: 200,
      ellipsis: true,
      renderText: (text, record) => {
        return !!record.institution && `${record?.institution}`;
      },
      hideInSearch: true,
    },
    {
      title: '单位名称',
      dataIndex: 'institution',
      hideInTable: true,
    },
    {
      title: '客户',
      dataIndex: 'customerName',
      copyable: true,
      search: false,
      renderText: (text, record) => {
        return (
          !record.customerList && (record?.customerName ? `${record?.customerName}` : undefined)
        );
      },
      width: 200,
      ellipsis: true,
    },
    {
      title: '销售',
      dataIndex: 'salePerson',
      search: false,
      width: 150,
      ellipsis: true,
      renderText: (text, record) => {
        return !record.customerList && `${record.salePerson}`;
      },
    },
    {
      title: '操作',
      fixed: 'right',
      width: 150,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <>
            {record.customerList && (
              <Access key="add" accessible={canAddCorrelation}>
                <Button
                  type="link"
                  className="inner-table-link"
                  key="related"
                  onClick={() => handleRelated(record)}
                >
                  添加关联
                </Button>
              </Access>
            )}
            {!record.customerList && record.id && (
              <Access key="delete" accessible={canDeleteCorrelation}>
                <Button
                  type="link"
                  className="inner-table-link"
                  key="delete"
                  onClick={() => handleDelete(record)}
                >
                  移除
                </Button>
              </Access>
            )}
            {!record.customerList && !record.id && (
              <Access key="sales" accessible={canEditCorrelationSales}>
                <Button
                  type="link"
                  className="inner-table-link"
                  key="sales"
                  onClick={() => handleSales(record)}
                >
                  关联销售
                </Button>
              </Access>
            )}
          </>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        rowKey="tableId"
        search={{}}
        actionRef={tableRef}
        columns={columns}
        expandable={{
          columnTitle: 'customerName',
          childrenColumnName: 'customerList',
        }}
        headerTitle="单位名称与客户关联表"
        request={async (params) => {
          const msg = await queryPagingTable({ ...params }, insPage);
          const { total, records, success } = msg;
          const dataSource = records?.map((item: API.InstitutionListResp) => ({
            ...item,
            tableId: getRandomId(),
            customerList: item?.customerList?.map((i) => ({
              ...i,
              tableId: getRandomId(),
              institutionId: item.id,
            })),
          }));
          return {
            ...msg,
            data: dataSource || [],
            success: success,
            total: total,
          };
        }}
      />
      <ModalForm
        title="请选择要关联的客户"
        width={500}
        open={modalVisit}
        modalProps={{
          destroyOnClose: true,
        }}
        onOpenChange={(visible) => setModalVisit(visible)}
        onFinish={async (value) => {
          const { clientMangers } = value;
          const customerId = clientMangers?.map((i: API.PartnerInfoResp) => i.id);
          const formData = { institutionId: initialValues.id, customerId };
          const res = await insInsert(formData);
          const { code, message: msg } = res || {};
          if (code !== 200) return;
          message.success(msg);
          tableRef.current?.reloadAndRest?.();
          return code === 200;
        }}
        initialValues={initialValues}
      >
        <div style={{ display: 'none' }}>
          <ProFormText width="md" name="institutionId" />
        </div>
        <ProFormSelect
          name="clientMangers"
          label="客户"
          rules={[requiredRule]}
          fieldProps={{
            loading,
            maxTagCount: 3,
            maxTagTextLength: 6,
            mode: 'multiple',
            showSearch: true,
            fieldNames: {
              label: 'clientName',
              value: 'id',
            },
          }}
          options={
            customers.map((i: API.PartnerInfoResp) => ({
              ...i,
              disabled: initialValues?.customerList
                ?.map((it: API.InstitutionCustomerListResp) => it.id)
                ?.includes(i.id),
            })) as DefaultOptionType[]
          }
          transform={(value, namePath) => {
            return { [namePath]: customers?.filter((i) => value?.includes(i.id)) };
          }}
        />
      </ModalForm>
      <ModalForm
        title="请选择要关联的销售"
        width={500}
        open={modalSalesVisit}
        modalProps={{
          destroyOnClose: true,
        }}
        onOpenChange={(visible) => setSalesModalVisit(visible)}
        onFinish={async (value) => {
          const formData = {
            parentId: value.parentId,
            salePerson: value.salePerson?.join(','),
          };

          const res = await insEditSalePersson(formData);
          const { code, message: msg } = res || {};
          if (code !== 200) return;
          message.success(msg);
          tableRef.current?.reloadAndRest?.();
          return code === 200;
        }}
        initialValues={initialSalesValues}
      >
        <div style={{ display: 'none' }}>
          <ProFormText width="md" name="parentId" />
        </div>
        <ProFormSelect
          name="salePerson"
          label="销售"
          rules={[requiredRule]}
          fieldProps={{
            loading: userLoading,
            maxTagCount: 3,
            maxTagTextLength: 6,
            mode: 'multiple',
            showSearch: true,
            fieldNames: {
              label: 'username',
              value: 'username',
            },
          }}
          options={userList as DefaultOptionType[]}
        />
      </ModalForm>
    </PageContainer>
  );
};

export default Correlation;
