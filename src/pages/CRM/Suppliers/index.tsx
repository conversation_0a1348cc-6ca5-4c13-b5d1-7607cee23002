import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { APPROVAL_STATUS, INDUSTRY } from '@/enums';
import { deleteParById, pagePartner } from '@/services/oa/partner';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';

const Institution: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.PartnerInfoResp[]>([]);
  const { canAddSupplier = false, canDeleteSupplier = false } = useAccess();
  // 删除
  const { run: deleteRecord } = useRequest((ids) => deleteParById({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const handleDelete = async (rows: API.PartnerInfoResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.clientName!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除供应商“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.PartnerInfoResp>[] = [
    {
      title: '供应商编号',
      dataIndex: 'clientNumber',
      width: 180,
      copyable: true,
      render(dom, entity) {
        return (
          <a className="rk-a-span" onClick={() => history.push(`/crm/suppliers/edit/${entity.id}`)}>
            {dom}
          </a>
        );
      },
    },
    {
      title: '供应商全称',
      dataIndex: 'clientName',
      width: 200,
      ellipsis: true,
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
    },
    {
      title: '客户经理',
      dataIndex: 'clientMangers',
      renderText: (dom, entity) => {
        return entity.clientMangers?.map((item) => item.clientMangerName)?.join(',');
      },
    },
    {
      title: '所在城市',
      dataIndex: 'city',
      hideInSearch: true,
    },
    {
      title: '行业',
      dataIndex: 'industry',
      hideInSearch: true,
      valueEnum: option2enum(INDUSTRY),
      ellipsis: true,
    },
    {
      title: '操作',
      fixed: 'right',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !canDeleteSupplier,
      render: (text, record) => {
        const { activiStatus } = record;
        return (
          <Access accessible={canDeleteSupplier}>
            {activiStatus === '0' && (
              <Button
                type="link"
                className="inner-table-link"
                key="delete"
                onClick={() => handleDelete([record])}
              >
                删除
              </Button>
            )}
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.PartnerInfoResp>
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        actionRef={tableRef}
        rowSelection={
          !canDeleteSupplier
            ? false
            : {
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
                getCheckboxProps: (record) => ({
                  disabled: record?.activiStatus !== '0',
                }),
              }
        }
        columns={columns}
        headerTitle="供应商列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddSupplier}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  history.push('/crm/suppliers/add');
                }}
              >
                新建供应商
              </Button>
            </Access>,
          ],
        }}
        polling={5000}
        request={async ({ clientMangers, ...params }) =>
          queryPagingTable(
            {
              ...params,
              filter: { partnerType: 'VENDOR' },
              extra: {
                clientMangers,
              },
            },
            pagePartner,
          )
        }
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default Institution;
