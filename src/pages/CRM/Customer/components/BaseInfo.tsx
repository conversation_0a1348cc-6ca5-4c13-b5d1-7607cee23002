import RKCol from '@/components/RKCol';
import BaseContext from '@/Context/BaseContext';
import UserContext from '@/Context/UserContext';
import {
  BIDDING_TYPE,
  COOPERATION_WAYS,
  CUSTOMER_LEVEL,
  INDUSTRY,
  PROVINCES,
  REGION,
} from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { requiredRule } from '@/utils/setting';
import {
  ProFormDatePicker,
  ProFormDependency,
  ProFormMoney,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@ant-design/pro-components';
import { Card, Row } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import { memo, useContext } from 'react';
import { usePageInfo } from '../../hooks/usePageInfo';

type PageType = 'CLIENT' | 'INSTITUTION' | 'VENDOR';
const typeMap = {
  CLIENT: '客户',
  INSTITUTION: '机构',
  VENDOR: '供应商',
};
const BaseInfo: React.FC<WithRouteEditingProps> = ({ isEditPage }) => {
  const { pageType } = usePageInfo(); //CLIENT-客户 INSTITUTION-机构 VENDOR-供应商

  const { userList, loading } = useContext(UserContext);
  const { disabledBase } = useContext(BaseContext);
  return (
    <>
      <div className="rk-none">
        <ProFormText name={['partnerInfo', 'id']} />
        <ProFormText name={['partnerInfo', 'partnerType']} />
      </div>
      <Row gutter={24}>
        {isEditPage && (
          <RKCol>
            <ProFormText name={['partnerInfo', 'clientNumber']} label="客户编号" disabled />
          </RKCol>
        )}
        <RKCol>
          <ProFormText
            name={['partnerInfo', 'clientName']}
            label={`${typeMap?.[pageType as PageType]}全称`}
            rules={[requiredRule]}
            disabled={disabledBase}
          />
        </RKCol>

        {(pageType === 'CLIENT' || pageType === 'VENDOR') && (
          <RKCol>
            <ProFormText
              name={['partnerInfo', 'clientAbbreviation']}
              label={pageType === 'CLIENT' ? '客户简称' : '供应商简称'}
              rules={[requiredRule]}
              disabled={disabledBase}
            />
          </RKCol>
        )}

        {pageType === 'CLIENT' && (
          <>
            <RKCol>
              <ProFormSelect
                disabled={disabledBase}
                name={['partnerInfo', 'grade']}
                label="客户级别"
                rules={[requiredRule]}
                options={CUSTOMER_LEVEL}
              />
            </RKCol>
            <RKCol>
              <ProFormSelect
                disabled={disabledBase}
                name={['partnerInfo', 'clientMangerId']}
                label="客户经理"
                rules={[requiredRule]}
                fieldProps={{
                  loading,
                  fieldNames: {
                    value: 'id',
                    label: 'employeeName',
                  },
                  showSearch: true,
                }}
                options={userList as DefaultOptionType[]}
              />
            </RKCol>
          </>
        )}

        {pageType !== 'CLIENT' && (
          <RKCol>
            <ProFormSelect
              disabled={disabledBase}
              name={['partnerInfo', 'clientMangers']}
              label="客户经理"
              rules={[requiredRule]}
              fieldProps={{
                loading,
                fieldNames: {
                  value: 'id',
                  label: 'employeeName',
                },
                mode: 'multiple',
                showSearch: true,
              }}
              options={userList as DefaultOptionType[]}
              convertValue={(value = []) =>
                value?.map((item: API.ClientMangers) => item?.clientMangerId || item)
              }
            />
          </RKCol>
        )}
        <RKCol>
          <ProFormSelect
            disabled={disabledBase}
            name={['partnerInfo', 'province']}
            label="所在省份"
            rules={[requiredRule]}
            options={PROVINCES}
            fieldProps={{
              showSearch: true,
            }}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect
            disabled={disabledBase}
            name={['partnerInfo', 'area']}
            label="所在区域"
            rules={[requiredRule]}
            options={REGION}
          />
        </RKCol>
        <RKCol>
          <ProFormText
            disabled={disabledBase}
            name={['partnerInfo', 'city']}
            label="所在城市"
            rules={[requiredRule]}
          />
        </RKCol>

        <RKCol>
          <ProFormSelect
            disabled={disabledBase}
            name={['partnerInfo', 'industry']}
            label="行业"
            rules={[requiredRule]}
            options={INDUSTRY}
          />
        </RKCol>
        {pageType === 'CLIENT' && (
          <RKCol>
            <ProFormSelect
              name={['partnerInfo', 'cooperateWay']}
              label="客户类型"
              rules={[requiredRule]}
              options={COOPERATION_WAYS}
            />
          </RKCol>
        )}

        <ProFormDependency name={[['partnerInfo', 'cooperateWay']]}>
          {({ partnerInfo }) => {
            const cooperateWay = partnerInfo?.cooperateWay;
            return (
              <>
                <RKCol>
                  <ProFormText
                    disabled={disabledBase}
                    name={['partnerInfo', 'firstContact']}
                    label={`${typeMap?.[pageType as PageType]}联系人`}
                    rules={cooperateWay !== 'COOP' ? [requiredRule] : []}
                  />
                </RKCol>
                <RKCol>
                  <ProFormText
                    disabled={disabledBase}
                    name={['partnerInfo', 'firstContactWay']}
                    label={`${typeMap?.[pageType as PageType]}联系方式`}
                    rules={cooperateWay !== 'COOP' ? [requiredRule] : []}
                  />
                </RKCol>
                <RKCol>
                  <ProFormText
                    disabled={disabledBase}
                    name={['partnerInfo', 'firstPosition']}
                    label={`${typeMap?.[pageType as PageType]}联系人职务`}
                    rules={cooperateWay !== 'COOP' ? [requiredRule] : []}
                  />
                </RKCol>
                <RKCol>
                  <ProFormText
                    disabled={disabledBase}
                    name={['partnerInfo', 'firstAddress']}
                    label={`${typeMap?.[pageType as PageType]}地址`}
                    rules={[requiredRule]}
                  />
                </RKCol>
                {/* 合作方 */}
                <RKCol>
                  <ProFormText
                    disabled={disabledBase}
                    name={['partnerInfo', 'agreement']}
                    label="合作方"
                    rules={cooperateWay === 'COOP' ? [requiredRule] : []}
                  />
                </RKCol>
                <RKCol>
                  <ProFormText
                    disabled={disabledBase}
                    name={['partnerInfo', 'secondContact']}
                    label="合作方联系人"
                    rules={cooperateWay === 'COOP' ? [requiredRule] : []}
                  />
                </RKCol>
                <RKCol>
                  <ProFormText
                    disabled={disabledBase}
                    name={['partnerInfo', 'secondContactWay']}
                    label="合作方联系方式"
                    rules={cooperateWay === 'COOP' ? [requiredRule] : []}
                  />
                </RKCol>
                <RKCol>
                  <ProFormText
                    disabled={disabledBase}
                    name={['partnerInfo', 'secondPosition']}
                    label="合作方联系人职务"
                    rules={cooperateWay === 'COOP' ? [requiredRule] : []}
                  />
                </RKCol>
                <RKCol>
                  <ProFormText
                    disabled={disabledBase}
                    name={['partnerInfo', 'secondAddress']}
                    label="合作方联系人地址"
                    rules={cooperateWay === 'COOP' ? [requiredRule] : []}
                  />
                </RKCol>
              </>
            );
          }}
        </ProFormDependency>

        <RKCol>
          <ProFormText name={['partnerInfo', 'clientCreateTime']} label="创建日期" disabled />
        </RKCol>
        <ProFormDependency name={[['partnerInfo', 'activiStatus']]}>
          {({ partnerInfo }) => {
            const canEdit = partnerInfo?.activiStatus === '0' || !partnerInfo?.activiStatus;
            if (partnerInfo?.activiStatus !== '2') {
              return (
                <RKCol>
                  <ProFormSwitch
                    initialValue={'1'}
                    label="创建售前项目"
                    name={['partnerInfo', 'hasCrProject']}
                    getValueFromEvent={(val) => (val ? '1' : '0')}
                    getValueProps={(value) => ({ checked: value === '1' })}
                    disabled={!canEdit}
                  />
                </RKCol>
              );
            }
            return null;
          }}
        </ProFormDependency>
        <ProFormDependency
          name={[
            ['partnerInfo', 'hasCrProject'],
            ['partnerInfo', 'activiStatus'],
          ]}
        >
          {({ partnerInfo }) => {
            const canEdit = partnerInfo?.activiStatus === '0' || !partnerInfo?.activiStatus;

            if (partnerInfo?.hasCrProject === '1' && partnerInfo?.activiStatus !== '2') {
              return (
                <RKCol>
                  <ProFormText
                    name={['partnerInfo', 'projectName']}
                    label="售前项目名称"
                    rules={[requiredRule]}
                    disabled={!canEdit}
                  />
                </RKCol>
              );
            }
            return null;
          }}
        </ProFormDependency>
        <ProFormDependency name={[['partnerInfo', 'activiStatus']]}>
          {({ partnerInfo }) => {
            const canEdit = partnerInfo?.activiStatus === '0' || !partnerInfo?.activiStatus;

            if (partnerInfo?.activiStatus !== '2') {
              return (
                <RKCol>
                  <ProFormSwitch
                    initialValue={'0'}
                    label="投标项目报备"
                    name={['partnerInfo', 'hasCrBid']}
                    getValueFromEvent={(val) => (val ? '1' : '0')}
                    getValueProps={(value) => ({ checked: value === '1' })}
                    disabled={!canEdit}
                  />
                </RKCol>
              );
            }
            return null;
          }}
        </ProFormDependency>
      </Row>
      <ProFormDependency
        name={[
          ['partnerInfo', 'hasCrBid'],
          ['partnerInfo', 'activiStatus'],
        ]}
      >
        {({ partnerInfo }) => {
          const canEdit = partnerInfo?.activiStatus === '0' || !partnerInfo?.activiStatus;

          if (partnerInfo?.hasCrBid === '1' && partnerInfo?.activiStatus !== '2') {
            return (
              <Card title="投标项目报备基础信息" type="inner" size="small">
                <Row gutter={24}>
                  <RKCol>
                    <ProFormDatePicker
                      name={['partnerInfo', 'reportDate']}
                      label="报备日期"
                      rules={[requiredRule]}
                      disabled={!canEdit}
                    />
                  </RKCol>
                  <RKCol>
                    <ProFormText
                      name={['partnerInfo', 'bidProjectName']}
                      label="项目名称"
                      rules={[requiredRule]}
                      disabled={!canEdit}
                    />
                  </RKCol>
                  <RKCol>
                    <ProFormMoney
                      name={['partnerInfo', 'amount']}
                      label="金额"
                      rules={[requiredRule]}
                      disabled={!canEdit}
                    />
                  </RKCol>
                  <RKCol>
                    <ProFormText
                      name={['partnerInfo', 'linkRequired']}
                      label="链接"
                      disabled={!canEdit}
                    />
                  </RKCol>
                  <RKCol>
                    <ProFormSelect
                      name={['partnerInfo', 'bidType']}
                      label="类型"
                      options={COOPERATION_WAYS}
                      rules={[requiredRule]}
                      disabled={!canEdit}
                    />
                  </RKCol>
                  <RKCol>
                    <ProFormMoney
                      name={['partnerInfo', 'deposit']}
                      label="保证金"
                      rules={[requiredRule]}
                      disabled={!canEdit}
                    />
                  </RKCol>
                  <RKCol>
                    <ProFormDatePicker
                      name={['partnerInfo', 'signupDeadline']}
                      label="报名截止日期"
                      rules={[requiredRule]}
                      disabled={!canEdit}
                    />
                  </RKCol>
                  <RKCol>
                    <ProFormDatePicker
                      name={['partnerInfo', 'bidDate']}
                      label="投标日期"
                      rules={[requiredRule]}
                      disabled={!canEdit}
                    />
                  </RKCol>
                  <RKCol>
                    <ProFormSelect
                      name={['partnerInfo', 'bidMethod']}
                      label="投标方式"
                      options={BIDDING_TYPE}
                      rules={[requiredRule]}
                      disabled={!canEdit}
                    />
                  </RKCol>
                </Row>
              </Card>
            );
          }
          return null;
        }}
      </ProFormDependency>
    </>
  );
};
export default memo(withRouteEditing(BaseInfo));
