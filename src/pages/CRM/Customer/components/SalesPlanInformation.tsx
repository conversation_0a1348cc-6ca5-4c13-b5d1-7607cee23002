import UserContext from '@/Context/UserContext';
import { PROJECT_BRAND, PROJECT_PROGRESS } from '@/enums';
import { getRandomId, option2enum } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import {
  DrawerForm,
  ProColumns,
  ProFormDatePicker,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Form, Space } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { produce } from 'immer';
import { useContext, useState } from 'react';

type ValueProps = {
  key_?: string;
} & API.SalePlanResp;

const SalesPlanInformation: React.FC<{
  value?: ValueProps[];
  onChange?: (val: ValueProps[]) => void;
}> = ({ value = [], onChange }) => {
  const [form] = Form.useForm<ValueProps>();
  const [drawerVisit, setDrawerVisit] = useState(false);
  const { userList, loading } = useContext(UserContext);

  const columns: ProColumns<ValueProps>[] = [
    {
      title: '项目编号',
      dataIndex: 'projectNumber',
      width: 100,
    },
    {
      title: '销售成员',
      dataIndex: 'salePerson',
      width: 100,
      ellipsis: true,
    },
    {
      title: '项目类型',
      dataIndex: 'projectClassify',
      valueEnum: option2enum(PROJECT_BRAND),
      ellipsis: true,
      width: 120,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 120,
      ellipsis: true,
    },
    {
      title: '项目描述',
      dataIndex: 'projectDescription',
      width: 120,
      ellipsis: true,
    },
    {
      title: '销售金额',
      dataIndex: 'saleAmount',
      valueType: 'money',
      width: 120,
    },
    {
      title: '采购成本',
      dataIndex: 'purchaseCost',
      width: 120,
      valueType: 'money',
    },
    {
      title: '招标计划',
      dataIndex: 'remark',
      width: 100,
    },
    {
      title: '项目概率',
      dataIndex: 'proProgress',
      valueEnum: option2enum(PROJECT_PROGRESS),
      width: 100,
    },

    {
      title: '操作',
      fixed: 'right',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <Button
              className="inner-table-link"
              type="link"
              onClick={() => {
                setDrawerVisit(true);
                form.setFieldsValue(record);
              }}
            >
              修改
            </Button>
            <Button
              className="inner-table-link"
              type="link"
              onClick={() => {
                const updatedValue = produce(value || [], (draft) => {
                  return draft.filter((item) => item?.key_ !== record?.key_);
                });
                onChange?.(updatedValue);
              }}
            >
              删除
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <ProTable<any>
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        options={false}
        rowKey="key_"
        dataSource={value}
        className="inner-table"
        columns={columns}
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setDrawerVisit(true);
              }}
            >
              添加
            </Button>,
          ],
        }}
      />

      <DrawerForm<ValueProps>
        width={460}
        open={drawerVisit}
        onOpenChange={setDrawerVisit}
        title="添加销售计划信息"
        form={form}
        autoFocusFirstInput
        drawerProps={{
          destroyOnClose: true,
        }}
        onFinish={async (values) => {
          const updatedValue = produce(value || [], (draft) => {
            const index = draft.findIndex((item) => item.key_ === values.key_);
            if (index === -1) {
              draft.push(values);
            } else {
              draft[index] = values;
            }
          });
          onChange?.(updatedValue);
          return true;
        }}
      >
        <div className="rk-none">
          <ProFormText name="key_" initialValue={getRandomId()} />
        </div>
        <ProFormText name="projectNumber" label="项目编号" />
        <ProFormSelect
          name="salePersonId"
          label="销售成员"
          fieldProps={{
            fieldNames: {
              value: 'id',
              label: 'username',
            },
            showSearch: true,
            loading,
            options: userList as DefaultOptionType[],
          }}
          transform={(value, namePath) => {
            const user = userList?.find((item) => item.id === value);
            return {
              [namePath]: value,
              salePerson: user?.username,
            };
          }}
        />
        <ProFormSelect
          name="projectClassify"
          label="项目类型"
          rules={[requiredRule]}
          options={PROJECT_BRAND}
        />
        <ProFormText name="projectName" label="项目名称" />
        <ProFormTextArea
          name="projectDescription"
          label="项目描述"
          fieldProps={{
            autoSize: {
              minRows: 1,
              maxRows: 3,
            },
          }}
        />

        <ProFormMoney
          name="saleAmount"
          label="销售金额"
          locale="zh-CN"
          fieldProps={{
            min: 0,
          }}
        />
        <ProFormMoney
          name="purchaseCost"
          label="采购成本"
          locale="zh-CN"
          fieldProps={{
            min: 0,
          }}
        />

        <ProFormDatePicker
          name="remark"
          label="招标计划"
          fieldProps={{
            picker: 'month',
            format: 'YYYY-MM',
          }}
        />
        <ProFormSelect name="proProgress" label="项目概率" options={PROJECT_PROGRESS} />
      </DrawerForm>
    </>
  );
};
export default SalesPlanInformation;
