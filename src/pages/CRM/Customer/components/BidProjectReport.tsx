import {
  APPROVAL_STATUS,
  BIDDING_ALLOCATION_STATUS,
  BIDDING_STATUS,
  BIDDING_TYPE,
  COOPERATION_WAYS,
} from '@/enums';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Tag } from 'antd';

const columns: ProColumns<API.BidProjectReportResp>[] = [
  {
    title: '投标报备号',
    dataIndex: 'documentNumber',
    copyable: true,
    ellipsis: true,
    width: 180,
    render(dom, entity) {
      return (
        <a
          className="rk-a-span"
          onClick={() => history.push(`/sale/bid-project-report/edit/${entity.id}`)}
        >
          {dom}
        </a>
      );
    },
  },
  {
    title: '分配状态',
    dataIndex: 'tagStatus',
    width: 110,
    render(_, entity) {
      const tagStatus = BIDDING_ALLOCATION_STATUS?.find((item) => item.value === entity.tagStatus);
      return <Tag color={tagStatus?.color}>{tagStatus?.label}</Tag>;
    },
  },
  {
    title: '报备日期',
    dataIndex: 'reportDate',
    valueType: 'date',
    width: 120,
    hideInSearch: true,
  },

  {
    title: '商务人员',
    dataIndex: 'businessName',
    width: 120,
  },

  {
    title: '项目名称',
    dataIndex: 'bidProjectName',
    width: 180,
    ellipsis: true,
  },
  {
    title: '金额',
    dataIndex: 'amount',
    valueType: 'money',
    width: 120,
    hideInSearch: true,
  },
  {
    title: '链接',
    dataIndex: 'linkRequired',
    width: 150,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: '类型',
    dataIndex: 'bidType',
    width: 100,
    valueEnum: option2enum(COOPERATION_WAYS),
  },
  {
    title: '保证金',
    dataIndex: 'deposit',
    valueType: 'money',
    width: 120,
    hideInSearch: true,
  },
  {
    title: '报名截止日期',
    dataIndex: 'signupDeadline',
    valueType: 'date',
    width: 120,
    hideInSearch: true,
  },
  {
    title: '投标日期',
    dataIndex: 'bidDate',
    valueType: 'date',
    width: 120,
    hideInSearch: true,
  },
  {
    title: '投标方式',
    dataIndex: 'bidMethod',
    valueEnum: option2enum(BIDDING_TYPE),
    width: 100,
  },
  {
    title: '审批状态',
    dataIndex: 'activiStatus',
    width: 120,
    hideInSearch: true,
    valueEnum: option2enum(APPROVAL_STATUS),
  },
  {
    title: '投标状态',
    dataIndex: 'status',
    width: 120,
    valueEnum: option2enum(BIDDING_STATUS),
  },
];

const BidProjectReport: React.FC<{
  value?: API.BidProjectReportResp[];
  onChange?: (value: API.BidProjectReportResp[]) => void;
}> = ({ value }) => {
  return (
    <ProTable<API.BidProjectReportResp>
      {...defaultTableConfig}
      scroll={{ x: '100%' }}
      rowKey="id"
      dataSource={value}
      className="inner-table"
      options={false}
      columns={columns}
    />
  );
};
export default BidProjectReport;
