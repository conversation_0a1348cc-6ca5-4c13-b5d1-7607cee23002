import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import UserContext from '@/Context/UserContext';
import { getRandomId } from '@/utils';
import { requiredRule } from '@/utils/setting';
import { ProColumns, ProForm } from '@ant-design/pro-components';
import { memo, useContext, useRef } from 'react';

const Sales: React.FC = () => {
  const { userList, loading } = useContext(UserContext);
  const existIds = useRef<string[]>([]);
  const columns: ProColumns<API.SalePersonResp>[] = [
    {
      title: '员工',
      width: 150,
      dataIndex: 'employeeId',
      valueType: 'select',
      formItemProps: {
        rules: [requiredRule],
      },
      fieldProps: (form, { rowKey }) => ({
        options: userList?.map((item) => ({
          ...item,
          disabled: existIds.current.includes(item.id!),
        })),
        showSearch: true,
        loading,
        onChange: (val: string, option: any) => {
          const formArr: API.SalePersonResp[] = Object.values(form.getFieldsValue());
          existIds.current = formArr.map((item) => item.employeeId!);
          form.setFieldValue([rowKey || ''], { employeeId: val, ...option });
        },
      }),
    },
    {
      title: '工号',
      dataIndex: 'employeeNumber',
      width: 150,
      fieldProps: {
        placeholder: '',
        disabled: true,
      },
    },
    {
      title: '部门',
      dataIndex: 'department',
      width: 150,
      fieldProps: {
        placeholder: '',
        disabled: true,
      },
    },
    {
      title: '邮箱',
      width: 150,
      dataIndex: 'email',
      fieldProps: {
        placeholder: '',
        disabled: true,
      },
    },
  ];

  return (
    <ProForm.Item
      name="salePersonList"
      getValueProps={(val) => ({
        value: val?.map((item: Record<string, any>) => {
          const { employeeId } = item;
          const rowData = userList?.find((item) => item.id === employeeId);
          return {
            ...item,
            key_: item.key_ || getRandomId(),
            employeeNumber: rowData?.employeeNumber,
            department: rowData?.department,
            email: rowData?.email,
          };
        }),
      })}
      rules={[
        () => ({
          validator(_, value = []) {
            const resolve = value.every((item: API.SalePersonResp) => item.employeeId);
            if (resolve) {
              return Promise.resolve();
            }
            return Promise.reject('请选择员工!');
          },
        }),
      ]}
    >
      <RKFormEditableProTable
        columns={columns}
        copy={false}
        pagination={{
          pageSize: 10,
          disabled: false,
        }}
      />
    </ProForm.Item>
  );
};

export default memo(Sales);
