import { CONTRACT_STATUS } from '@/enums';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';
const columns: ProColumns<Record<string, any>>[] = [
  {
    title: '合同编号',
    dataIndex: 'contractNumber',
    render: (dom, entity) => {
      return <a onClick={() => history.push(`/contract/main/edit/${entity.contractId}`)}>{dom}</a>;
    },
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
  },
  {
    title: '合同类别',
    dataIndex: 'type',
    renderText: () => '主合同',
  },
  {
    title: '合同金额',
    dataIndex: 'contractAmount',
    valueType: 'money',
  },
  {
    title: '合同状态',
    dataIndex: 'contractStatus',
    valueEnum: option2enum(CONTRACT_STATUS),
  },
  {
    title: '已开票',
    dataIndex: 'billedAmount',
    valueType: 'money',
  },
  {
    title: '待开票',
    dataIndex: 'awaitAmount',
    valueType: 'money',
  },
  {
    title: '已回款',
    dataIndex: 'receivedAmount',
    valueType: 'money',
  },
  {
    title: '待回款',
    dataIndex: 'awaitReAmount',
    valueType: 'money',
  },
  {
    title: '销售',
    dataIndex: 'salePerson',
  },

  {
    title: '备注',
    dataIndex: 'remark',
  },
];

const ContractInfo: React.FC<{
  value?: API.ExecuteContractResp[];
  onChange?: (value: API.ExecuteContractResp[]) => void;
}> = ({ value }) => {
  return (
    <ProTable<API.ExecuteContractResp>
      {...defaultTableConfig}
      rowKey="contractNumber"
      dataSource={value}
      className="inner-table"
      options={false}
      columns={columns}
    />
  );
};
export default ContractInfo;
