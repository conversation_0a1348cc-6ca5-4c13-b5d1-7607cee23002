import { APPROVAL_STATUS, PROJECT_STATUS, PROJECT_TYPE } from '@/enums';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';
// import dayjs from 'dayjs';

// 定义路径与项目的映射关系
export const PROJECT_MAP = {
  NB: 'internal',
  XS: 'sales',
  SQ: 'pre-sales',
  SH: 'after-sales',
  KF: 'develop',
};

const columns: ProColumns<Record<string, any>>[] = [
  {
    title: '项目编号',
    width: 150,
    dataIndex: 'projectNumber',
    fixed: 'left',
    render: (dom, entity) => {
      const { projectClassify, projectId } = entity;
      return (
        <a
          onClick={() =>
            history.push(
              `/project/${
                PROJECT_MAP[projectClassify as keyof typeof PROJECT_MAP]
              }/edit/${projectId}`,
            )
          }
        >
          {dom}
        </a>
      );
    },
  },
  {
    title: '项目名称',
    width: 200,
    dataIndex: 'projectName',
    ellipsis: true,
  },
  {
    title: '项目类型',
    width: 100,
    dataIndex: 'projectClassify',
    valueEnum: option2enum(PROJECT_TYPE),
  },
  {
    title: '审批状态',
    dataIndex: 'activiStatus',
    width: 100,
    hideInSearch: true,
    valueEnum: option2enum(APPROVAL_STATUS),
  },
  {
    dataIndex: 'projectManger',
    title: '项目经理',
    width: 100,
  },
  {
    dataIndex: 'proCreateTime',
    title: '创建时间',
    width: 110,
    valueType: 'date',
  },
  {
    dataIndex: 'status',
    title: '项目状态',
    width: 100,
    valueEnum: option2enum(PROJECT_STATUS),
  },
  {
    dataIndex: 'spend',
    title: '目前开销',
    valueType: 'money',
    width: 110,
  },

  {
    dataIndex: 'executedWorkload',
    title: '已执行工作量',
    width: 110,
    renderText(text) {
      return text ? `${text} 人天` : '-';
    },
  },
];

const Project: React.FC<{
  value?: API.BrieflyProInfo[];
  onChange?: (value: API.BrieflyProInfo[]) => void;
}> = ({ value }) => {
  return (
    <ProTable<API.BrieflyProInfo>
      {...defaultTableConfig}
      scroll={{ x: '100%' }}
      rowKey="projectId"
      dataSource={value}
      className="inner-table"
      options={false}
      columns={columns}
    />
  );
};
export default Project;
