import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { APPROVAL_STATUS, COOPERATION_WAYS, CUSTOMER_LEVEL, INDUSTRY } from '@/enums';
import { deleteParById, pagePartner } from '@/services/oa/partner';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';

const Customer: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.PartnerInfoResp[]>([]);
  const { canAddCustomer = false, canDeleteCustomer = false } = useAccess();

  // 删除
  const { run: deleteRecord } = useRequest((ids) => deleteParById({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const handleDelete = async (rows: API.PartnerInfoResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.clientName!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除客户“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.PartnerInfoResp>[] = [
    {
      title: '客户编号',
      dataIndex: 'clientNumber',
      width: 180,
      copyable: true,
      render(dom, entity) {
        return (
          <a className="rk-a-span" onClick={() => history.push(`/crm/customer/edit/${entity.id}`)}>
            {dom}
          </a>
        );
      },
    },
    {
      title: '客户全称',
      dataIndex: 'clientName',
      width: 300,
      ellipsis: false,
    },
    {
      title: '客户简称',
      dataIndex: 'clientAbbreviation',
      width: 220,
      ellipsis: false,
    },
    {
      title: '客户等级',
      dataIndex: 'grade',
      width: 100,
      ellipsis: true,
      valueEnum: option2enum(CUSTOMER_LEVEL),
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
      ellipsis: true,
      width: 100,
    },
    {
      title: '客户经理',
      dataIndex: 'clientManger',
      ellipsis: true,
      width: 100,
    },
    {
      title: '所在城市',
      dataIndex: 'city',
      hideInSearch: true,
      ellipsis: true,
      width: 100,
    },
    {
      title: '行业',
      dataIndex: 'industry',
      valueEnum: option2enum(INDUSTRY),
      ellipsis: true,
      width: 100,
    },
    {
      title: '合作方式',
      dataIndex: 'cooperateWay',
      valueEnum: option2enum(COOPERATION_WAYS),
      ellipsis: true,
    },

    {
      title: '操作',
      fixed: 'right',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !canDeleteCustomer,
      render: (text, record) => {
        const { activiStatus } = record;
        return (
          <Access accessible={canDeleteCustomer}>
            {activiStatus === '0' && (
              <Button
                type="link"
                className="inner-table-link"
                key="delete"
                onClick={() => handleDelete([record])}
              >
                删除
              </Button>
            )}
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.PartnerInfoResp>
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        actionRef={tableRef}
        rowSelection={
          !canDeleteCustomer
            ? false
            : {
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
                getCheckboxProps: (record) => ({
                  disabled: record?.activiStatus !== '0',
                }),
              }
        }
        columns={columns}
        headerTitle="客户列表"
        toolbar={{
          actions: [
            <Access accessible={canAddCustomer} key="add">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  history.push('/crm/customer/add');
                }}
              >
                新建客户
              </Button>
            </Access>,
          ],
        }}
        polling={5000}
        request={async (params) => {
          const { industry, ...rest } = params;
          return queryPagingTable(
            { ...rest, filter: { partnerType: 'CLIENT', industry } },
            pagePartner,
          );
        }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default Customer;
