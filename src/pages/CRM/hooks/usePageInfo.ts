import { useLocation } from '@umijs/max';
/**
 *
 * @param pathname 页面 path
 * @returns 页面类型 CLIENT-客户 INSTITUTION-招标机构 VENDOR-供应商
 */

const getType = (pathname: string): 'CLIENT' | 'INSTITUTION' | 'VENDOR' => {
  //
  if (pathname.includes('customer')) {
    return 'CLIENT'; //客户
  }
  if (pathname.includes('institution')) {
    return 'INSTITUTION'; // 招标机构
  }
  if (pathname.includes('suppliers')) {
    return 'VENDOR'; // 供应商
  }
  return 'CLIENT';
};

/**
 *
 * @returns  页面类型 CLIENT-客户 INSTITUTION-招标机构 VENDOR-供应商
 */

export function usePageInfo() {
  const { pathname } = useLocation();

  return {
    pageInfo: {},
    pageType: getType(pathname),
  };
}
