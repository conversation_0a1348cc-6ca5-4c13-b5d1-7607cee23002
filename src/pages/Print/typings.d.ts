export type PrintProps = {
  title?: string;
  subTitle?: string;
  items?: PrintItem[];
  dataSource: DataProps;
};

export type PrintItem = {
  itemType: string;
  columns: Column[];
  dataIndex?: string;
};

export type ListItem = {
  title: string;
  content: string;
  dataIndex?: string;
};

export type DataProps = Record<string, any>;

export type Column = {
  title: string;
  width?: number | string;
  dataIndex: string;
  valueType?: string;
  valueEnum?: Record<string, Record<string, any>>;
  prefix?: string;
  suffix?: string;
};
