import {
  CONTRACT_CATEGORY,
  CONTRACT_CHARACTER,
  CONTRACT_SERVICES_CATEGORY,
  CONTRACT_STATUS,
  CUSTOMER_TYPE,
  INDUSTRY,
  SIGN_STATUS,
} from '@/enums';
import { option2enum } from '@/utils';
import { PrintProps } from './typings';

export const mainContractItems: PrintProps['items'] = [
  {
    itemType: 'row',
    columns: [
      {
        title: '合同类别:',
        dataIndex: 'contractCategory',
        valueEnum: option2enum(CONTRACT_CATEGORY),
      },
      {
        title: '甲方类型:',
        dataIndex: 'firstParty',
        valueEnum: option2enum(CUSTOMER_TYPE),
      },
      {
        title: '服务类别:',
        dataIndex: 'serveCategory',
        valueEnum: option2enum(CONTRACT_SERVICES_CATEGORY),
      },
      {
        title: '甲方:',
        dataIndex: 'fpName',
      },
      {
        title: '合同编号:',
        dataIndex: '合同编号',
      },
      {
        title: '乙方类型:',
        dataIndex: 'secondParty',
        valueEnum: option2enum(CUSTOMER_TYPE),
      },
      {
        title: '合同名称:',
        dataIndex: 'contractName',
      },
      {
        title: '乙方:',
        dataIndex: 'spName',
      },
      {
        title: '性质:',
        dataIndex: 'contractQuality',
        valueEnum: option2enum(CONTRACT_CHARACTER),
      },
      {
        title: '销售:',
        dataIndex: 'salePerson',
      },
      {
        title: '合同状态:',
        dataIndex: 'contractStatus',
        valueEnum: option2enum(CONTRACT_STATUS),
      },
      {
        title: '最终用户:',
        dataIndex: 'endUser',
      },
      {
        title: '合同所在地:',
        dataIndex: 'contractAddress',
      },
      {
        title: '合同金额:',
        dataIndex: 'contractAmount',
        valueType: 'money',
      },
      {
        title: '行业:',
        dataIndex: 'industry',
        valueEnum: option2enum(INDUSTRY),
      },
      {
        title: '开始日期:',
        dataIndex: 'startTime',
      },
      {
        title: '签订日期:',
        dataIndex: 'signDate',
      },
      {
        title: '结束日期:',
        dataIndex: 'endTime',
      },
      {
        title: '签订状态:',
        dataIndex: 'signStatus',
        valueEnum: option2enum(SIGN_STATUS),
      },
      {
        title: '内容概述:',
        dataIndex: 'overview',
      },
      {
        title: '单位名称:',
        dataIndex: 'institutionName',
      },
      {
        title: '纳税人识别号:',
        dataIndex: 'taxpayerNum',
      },
      {
        title: '开户银行:',
        dataIndex: 'bank',
      },
      {
        title: '银行账号:',
        dataIndex: 'account',
      },
      {
        title: '联系电话:',
        dataIndex: 'contactNumber',
      },
      {
        title: '注册地址:',
        dataIndex: 'registeredAddress',
      },
      {
        title: '合同总金额:',
        dataIndex: 'contractAmount',
        valueType: 'money',
      },
      {
        title: '开票总金额:',
        dataIndex: 'billTotalAmount',
        valueType: 'money',
      },
      {
        title: '已开票金额:',
        dataIndex: 'billedAmount',
        valueType: 'money',
      },
      {
        title: '待开票金额',
        dataIndex: 'awaitAmount',
        valueType: 'money',
      },
      {
        title: '已收款金额:',
        dataIndex: 'receivedAmount',
        valueType: 'money',
      },
      {
        title: '待收款金额:',
        dataIndex: 'awaitReAmount',
        valueType: 'money',
      },
    ],
  },
  {
    itemType: 'table',
    columns: [
      {
        title: '序号',
        dataIndex: 'estimateReTime',
        valueType: 'indexBorder',
        width: '8%',
      },
      {
        title: '预计收款时间',
        dataIndex: 'estimateReTime',
      },
      {
        title: '预计收款金额',
        dataIndex: 'estimateReAmount',
        valueType: 'money',
      },
      {
        title: '支付条件',
        dataIndex: 'payCondition',
      },
      {
        title: '税率',
        dataIndex: 'rate',
        suffix: '%',
      },
      {
        title: '税额',
        dataIndex: 'rateAmount',
        valueType: 'money',
      },
      {
        title: '不含税金额',
        dataIndex: 'excludeRaAmount',
        valueType: 'money',
      },
    ],
    dataIndex: 'collectionPlanList',
  },
];
