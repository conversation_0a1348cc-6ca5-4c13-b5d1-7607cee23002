:root {
  --text-color: #1f272e;
  --border-color: #505a62;
  --border-color1: #333c44;
}
.container {
  padding: 2px;
  .header {
    height: 36px;
    margin-bottom: 16px;
    color: var(--text-color);
    font-weight: 600;
    font-size: 24px;
    line-height: 36px;
    text-align: center;
  }
  .sub-header {
    color: var(--text-color);
    font-weight: 600;
    font-size: 20px;
    text-align: center;
  }
  .header-line {
    margin: 16px 0;
    border-bottom: solid 2px var(--border-color);
  }
  .text-line {
    margin: 16px 0;
    border-bottom: solid 1px var(--border-color);
  }
  .table {
    width: 100%;
    margin-bottom: 10px;
    border-collapse: collapse;
    :global {
      tr {
        font-size: 14px;
        page-break-inside: avoid;
      }
      td,
      th {
        text-align: left;
      }
      td {
        padding: 12px 0;
        text-align: left;
        &:nth-child(1) {
          width: 16%;
          font-weight: 500;
        }
        &:nth-child(2) {
          width: 34%;
        }
        &:nth-child(3) {
          width: 16%;
          font-weight: 600;
        }
        &:nth-child(4) {
          width: 34%;
        }
      }
      th {
        padding: 12px 0;
        font-size: 16px;
      }
      .sign-tr {
        :global {
          td {
            position: relative;
            &:nth-child(odd) {
              width: 100px;
            }
            &:nth-child(even) {
              width: auto;
            }
          }
        }
      }

      .line1 {
        position: absolute;
        bottom: 10px;
        left: 0;
        width: 200px;
        border-bottom: solid 1px var(--text-color);
      }
      .time-content {
        position: absolute;
        bottom: 10px;
        left: 0;
        .line-text {
          margin-right: 4px;

          &:before {
            display: inline-block;
            width: 50px;
            margin-right: 4px;
            color: transparent;
            border-bottom: solid 1px var(--text-color);
            content: '';
          }
        }
      }
    }
  }
  .table-2 {
    :global {
      td {
        &:nth-child(odd) {
          width: 16%;
        }
        &:nth-child(even) {
          width: auto;
        }
      }
      label {
        margin-right: 20px;
      }
      input {
        margin-right: 6px;
      }
    }
  }
}
