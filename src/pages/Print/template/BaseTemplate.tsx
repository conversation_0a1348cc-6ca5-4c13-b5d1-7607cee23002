import { isArray } from 'lodash';
import React from 'react';
import styles from '../index.less';
import { Column, DataProps, PrintItem, PrintProps } from '../typings';
import { groupIntoPairs } from '../utils';

const renderTitle = (title: string, subTitle: string) => {
  return (
    <div className={styles['print-header']}>
      <h2 className={styles['print-header-title']}>{title}</h2>
      <p className={styles['print-header-subtitle']}>{subTitle}</p>
    </div>
  );
};
const renderColumn = (column: Column, val: string | number, index?: number) => {
  if (!column) return '';
  const { valueEnum, valueType, prefix = '', suffix = '' } = column;
  if (valueType === 'indexBorder') {
    return (index || 0) + 1;
  }
  if (valueEnum) {
    // @ts-ignore
    return valueEnum?.[val]?.label;
  }
  if (valueType === 'money') {
    return `￥${val || 0}`;
  }
  return `${prefix}${val || ''}${suffix}`;
};

const renderRow = (columns: PrintItem['columns'], data: DataProps, notPairs?: boolean) => {
  const pairsArr = notPairs ? columns : groupIntoPairs(columns);
  return pairsArr.map((row, rowIndex) => {
    if (isArray(row)) {
      return row.map((col, colIndex) => (
        <div className={styles['row']} key={colIndex}>
          <div className={styles['col-title']}>{col?.title}</div>
          <div className={styles['col-content']}>{renderColumn(col, data[col?.dataIndex])}</div>
        </div>
      ));
    }
    return (
      <div className={styles['row']} key={rowIndex}>
        <div className={styles['col-title']}>{row?.title}</div>
        <div className={styles['col-content']}>{renderColumn(row, data[row?.dataIndex])}</div>
      </div>
    );
  });
};

const renderContent = (items: PrintItem[], dataSource: PrintProps['dataSource']) => {
  if (!dataSource) return;
  return items.map((item, index) => {
    const { itemType, columns = [], dataIndex } = item;
    const data = dataIndex ? dataSource[dataIndex] : dataSource;
    if (itemType === 'row') {
      return (
        <div className={styles['print-grid']} key={index}>
          {renderRow(columns, data)}
        </div>
      );
    }
    if (itemType === 'table') {
      if (!isArray(data) || !data.length) return null;
      return (
        <div key={index} className={styles['print-table']}>
          <div className={styles['row-header']}>
            {columns.map((head, headIndex) => (
              <div key={headIndex} className={styles['col-title']}>
                {head.title}
              </div>
            ))}
          </div>
          {(data as DataProps[]).map((row, rowIndex) => (
            <div key={rowIndex} className={styles['row']}>
              {columns.map((col, colIndex) => (
                <div key={colIndex} className={styles['col-title']}>
                  {renderColumn(col, row[col.dataIndex], colIndex)}
                </div>
              ))}
            </div>
          ))}
        </div>
      );
    }

    return <div key={index}></div>;
  });
};

const BaseTemplate = React.forwardRef<HTMLDivElement, PrintProps>(
  ({ items = [], title = '', subTitle = '', dataSource }, ref) => {
    return (
      <div className={styles['print-container']} ref={ref}>
        {renderTitle(title, subTitle)}
        <table className={styles['print-table-container']}>
          <thead className={styles['thead']}>
            <tr>
              <td></td>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>{renderContent(items, dataSource)}</td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  },
);

export default BaseTemplate;
