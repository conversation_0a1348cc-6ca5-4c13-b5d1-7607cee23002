import classNames from 'classnames/bind';
import React from 'react';
import styles from './index.less';
const cx = classNames.bind(styles);
type CustomerServiceReportProps = {
  data: API.WorkOrderInsertReq & API.WorkOrderPageResp;
};
const CustomerServiceReport1 = React.forwardRef<HTMLDivElement, CustomerServiceReportProps>(
  ({ data = {} }, ref) => {
    return (
      <div className={cx('container')} ref={ref}>
        <div className={cx('header')}>客户现场服务报告单</div>
        <table className={cx('table')}>
          <tbody>
            <tr>
              <th colSpan={6}>【基础信息】</th>
            </tr>
            <tr>
              <td>[工单编号]</td>
              <td>{data?.documentNumber}</td>
              <td>[报告单类型]</td>
              <td>客户现场服务</td>
            </tr>
            <tr>
              <td>[项目编号]</td>
              <td>{data?.projectNumber}</td>
              <td>[工程师工号]</td>
              <td>{data?.employeeNumber}</td>
            </tr>
            <tr>
              <td>[项目名称]</td>
              <td>{data?.projectName}</td>
              <td>[工程师姓名]</td>
              <td>{data?.employeeName}</td>
            </tr>
            <tr>
              <td>[开始时间]</td>
              <td>{data?.startTime}</td>
              <td>[联系人姓名]</td>
              <td>{data?.contactName}</td>
            </tr>
            <tr>
              <td>[结束时间]</td>
              <td>{data?.endTime}</td>
              <td>[联系人电话]</td>
              <td>{data?.contactPhone}</td>
            </tr>
          </tbody>
        </table>
        <table className={cx('table')}>
          <tbody>
            <tr>
              <th>【事件描述】</th>
            </tr>
            <tr>
              <td height={110} className={cx('text-break')}>
                {data?.describe}
              </td>
            </tr>
          </tbody>
        </table>
        <table className={cx('table')}>
          <tbody>
            <tr>
              <th>【工作内容】</th>
            </tr>
            <tr>
              <td height={110} className={cx('text-break')}>
                {data?.content}
              </td>
            </tr>
          </tbody>
        </table>
        <table className={cx('table')}>
          <tbody>
            <tr>
              <th>【建议及下一步工作计划】</th>
            </tr>
            <tr style={{ pageBreakInside: 'avoid' }}>
              <td height={110} className={cx('text-break')}>
                {data?.nextPlan}
              </td>
            </tr>
          </tbody>
        </table>
        <table className={cx('table')}>
          <tbody>
            <tr style={{ pageBreakInside: 'avoid' }}>
              <th colSpan={6}>【签名】</th>
            </tr>
            <tr className={cx('sign-tr')}>
              <td>[客户签字]</td>

              <td>
                <div className={cx('line1')} />
              </td>
              <td>[工程师签字]</td>
              <td>
                <div className={cx('line1')} />
              </td>
            </tr>
            <tr className={cx('sign-tr')}>
              <td>[签字日期]</td>
              <td>
                <div className={cx('time-content')}>
                  <span className={cx('line-text')}>年</span>
                  <span className={cx('line-text')}>月</span>
                  <span className={cx('line-text')}>日</span>
                </div>
              </td>
              <td>[签字日期]</td>
              <td>
                <div className={cx('time-content')}>
                  <span className={cx('line-text')}>年</span>
                  <span className={cx('line-text')}>月</span>
                  <span className={cx('line-text')}>日</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  },
);

export default CustomerServiceReport1;
