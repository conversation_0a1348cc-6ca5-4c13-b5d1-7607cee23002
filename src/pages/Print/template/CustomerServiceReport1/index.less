:root {
  --text-color: #1f272e;
  --border-color: #505a62;
  --border-color1: #333c44;
}
.container {
  padding: 2px;
  .header {
    height: 36px;
    margin-bottom: 16px;
    color: var(--text-color);
    font-weight: 600;
    font-size: 24px;
    line-height: 36px;
    text-align: center;
  }
  .table {
    width: 100%;
    margin-bottom: 10px;
    border: solid 1px var(--border-color);
    border-collapse: collapse;
    :global {
      tr {
        font-size: 14px;
        page-break-inside: avoid;
      }
      td,
      th {
        text-align: left;
        border-bottom: solid 1px var(--border-color);
      }
      td {
        padding: 12px;
        text-align: left;
        page-break-inside: avoid;
        &:nth-child(1) {
          width: 15%;
        }
        &:nth-child(2) {
          width: 35%;
        }
        &:nth-child(3) {
          width: 20%;
        }
        &:nth-child(4) {
          width: 30%;
        }
      }
      th {
        padding: 12px 0;
        font-size: 16px;
      }
      .sign-tr {
        :global {
          tr {
            page-break-inside: avoid;
          }
          td {
            position: relative;
            height: 60px;
            &:nth-child(odd) {
              width: 120px;
            }
            &:nth-child(even) {
              width: auto;
            }
          }
        }
      }

      .line1 {
        position: absolute;
        bottom: 10px;
        left: 0;
        width: 200px;
        border-bottom: solid 1px var(--text-color);
      }
      .time-content {
        position: absolute;
        bottom: 10px;
        left: 0;
        .line-text {
          margin-right: 4px;

          &:before {
            display: inline-block;
            width: 50px;
            margin-right: 4px;
            color: transparent;
            border-bottom: solid 1px var(--text-color);
            content: '';
          }
        }
      }
    }
  }
  .text-break {
    white-space: break-spaces;
    word-wrap: break-word;
  }
}
