import BaseListContext from '@/Context/BaseListContext';
import React, { useContext } from 'react';
import './index.less';

interface ExpenseAccountTemplateProps {
  data: API.FinaInfoResp;
}

const renderMoney = (value?: string | number) => {
  if (!value) return '';
  return `￥${parseFloat(value as string).toFixed(2)}`;
};

const typeMap = {
  RC: '日常报销单',
  CL: '差旅报销单',
};

const renderSubTitle = (str?: string) => {
  if (!str) return '';
  return typeMap[str as 'RC' | 'CL'];
};

function numberToChineseCurrency(amount?: string) {
  if (!amount) return '零元整';
  const chineseNumbers = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
  const chineseUnits = ['', '拾', '佰', '仟', '萬', '亿'];
  const chineseCurrencyUnits = ['元', '角', '分', '厘', '毫'];

  const [integerPart, decimalPart] = parseFloat(amount as string)
    .toFixed(2)
    ?.split('.');

  const integerChinese = integerPart
    .split('')
    .reverse()
    .map((digit, index, array) => {
      const unitIndex = index % 4;
      const unit = chineseUnits[unitIndex];
      const isLastZero = digit === '0';
      const isCurrentZero = array[index] === '0';
      const shouldSkipZero = isLastZero || (unitIndex === 0 && isCurrentZero);
      // @ts-ignore
      return shouldSkipZero ? '' : chineseNumbers[digit] + unit;
    })
    .reverse()
    .join('');

  const decimalChinese =
    decimalPart
      ?.split('')
      // @ts-ignore
      .map((digit, index) => chineseNumbers[digit] + chineseCurrencyUnits[index + 1])
      .join('') || '';

  return integerChinese + '元' + decimalChinese + '整';
}

const ExpenseAccountTemplate = React.forwardRef<HTMLDivElement, ExpenseAccountTemplateProps>(
  ({ data }, ref) => {
    const { dayInfoList = [], busiProjList = [], busiInfoList = [] } = data || {};
    const list: Record<string, any>[] = [
      ...dayInfoList,
      ...busiInfoList,
      ...busiProjList.map((item) => ({ ...item, actualType: '差旅补贴' })),
    ];
    const { departmentList = [] } = useContext(BaseListContext);

    return (
      <div className="expense-account-container" ref={ref}>
        <div className="ea-header">
          <p className="ea-title">四川融科智联科技有限公司</p>
          <p className="ea-sub-title">{renderSubTitle(data?.type)}</p>
        </div>
        <div className="ea-list">
          <div className="ea-row">
            <div className="ea-col-title">员工姓名</div>
            <div className="ea-col-content">{data?.employeeName}</div>
            <div className="ea-col-title">报销日期</div>
            <div className="ea-col-content">{data?.finaDate}</div>
          </div>
          <div className="ea-row no-print">
            <div className="ea-col-title">隶属部门</div>
            <div className="ea-col-content">
              {departmentList?.find((item) => item.id === data?.department)?.departmentName}
            </div>
            <div className="ea-col-title">员工编号</div>
            <div className="ea-col-content">{data?.employee}</div>
          </div>
          <div className="ea-row no-print">
            <div className="ea-col-title">报销单号</div>
            <div className="ea-col-content">{data?.documentNumber}</div>
            <div className="ea-col-title">电子邮件</div>
            <div className="ea-col-content">{data?.email}</div>
          </div>
          <div className="ea-row no-print">
            <div className="ea-col-title">报销金额</div>
            <div className="ea-col-content ea-strong">{renderMoney(data?.total)}</div>
            <div className="ea-col-title">联系电话</div>
            <div className="ea-col-content">{data?.cellNumber}</div>
          </div>
        </div>
        <div className="ea-table">
          <table>
            <tbody>
              <tr>
                <th>序号</th>
                <th>发生日期</th>
                <th>发生金额</th>
                <th>费用类型</th>
                <th>项目名称</th>
                <th className="no-print">正当理由</th>
                <th className="no-print">实际类型</th>
                <th className="no-print">实际金额</th>
                <th className="no-print">备注</th>
              </tr>
              {/* A4分辨率 794px */}
              {list?.map((item, index) => (
                <tr key={index} className={item.type === '差旅补贴' ? 'no-print' : ''}>
                  <td width={40}>{index + 1}</td>
                  <td width={80}>{item?.time}</td>
                  <td width={60}> {renderMoney(item?.money)}</td>
                  <td width={60}>{item?.type}</td>
                  <td width={120}>{item?.projectName}</td>
                  <td width={60} className="no-print">
                    {item?.reason}
                  </td>
                  <td width={60} className="no-print">
                    {item?.actualType}
                  </td>
                  <td width={60} className="no-print">
                    {renderMoney(item?.actualMoney || item.allowance)}
                  </td>
                  <td width={80} className="no-print">
                    {item?.remark}
                  </td>
                </tr>
              ))}
              <tr>
                <th colSpan={2}>合计</th>
                <th colSpan={3}>{renderMoney(data?.total)}</th>
                <th colSpan={2} className="no-print">
                  实际合计
                </th>
                <th colSpan={2} className="no-print">
                  {renderMoney(data?.actualTotal)}
                </th>
              </tr>
              <tr>
                <th colSpan={2}>合计(大写)</th>
                <th colSpan={3}>{numberToChineseCurrency(data?.total)}</th>
                <th colSpan={2} className="no-print">
                  实际合计(大写)
                </th>
                <th colSpan={3} className="no-print">
                  {numberToChineseCurrency(data?.actualTotal)}
                </th>
              </tr>
              <tr>
                <th colSpan={2}>报销人</th>
                <th colSpan={1}>{data?.employeeName}</th>
                <th colSpan={1}>报销日期</th>
                <th colSpan={1}>{data?.finaDate}</th>
                <th colSpan={4} className="no-print">
                  {data?.finaDate}
                </th>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    );
  },
);

export default ExpenseAccountTemplate;
