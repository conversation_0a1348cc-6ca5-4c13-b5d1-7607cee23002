.border {
  border: 1px solid @colorBorder;
}

.border-bottom {
  border-bottom: 1px solid @colorBorder;
}

.border-right {
  border-right: 1px solid @colorBorder;
}

.ea-border-top-none {
  border-top: none !important;
}

.ea-border-right-none {
  border-right: none !important;
}

.ea-strong {
  font-weight: 600;
}

// 报销单
.expense-account-container {
  color: @colorText;
  font-weight: 400;
  font-size: 14px;
  line-height: 16px;
  text-align: center;
  background: #fff;

  p {
    margin: 0;
    padding: 0;
  }

  .ea-header {
    .border();

    .ea-title {
      padding: 20px 6px;
      font-size: 28px;
      line-height: 28px;
      .border-bottom();
    }

    .ea-sub-title {
      padding: 16px 6px;
      font-size: 22px;
      line-height: 22px;
    }
  }

  .ea-list {
    .ea-row {
      display: flex;
      word-break: break-all;
      .border();
      .ea-border-right-none();
      .ea-border-top-none();

      .ea-col-title {
        width: 40%;
        padding: 6px;
        .border-right();
      }

      .ea-col-content {
        width: 60%;
        padding: 8px;
        .border-right();
      }
    }
  }

  .ea-table {
    width: 100%;
    margin-top: 16px;

    table {
      width: 100%;

      tr {
        th,
        td {
          padding: 8px;
          white-space: pre-wrap;
          .border();
        }
      }
    }
  }
}

@media print {
  .expense-account-container {
    color: #000;

    .ea-header {
      border-color: #000;

      .ea-title {
        border-color: #000;
      }
    }

    .ea-list {
      .ea-row {
        border-color: #000;

        .ea-col-title,
        .ea-col-content {
          border-color: #000;
        }
      }
    }

    .ea-table {
      table {
        tr {
          th,
          td {
            border-color: #000;
          }
        }
      }
    }
  }

  .no-print {
    display: none;
  }

  .print {
    display: block !important;
  }
}
