import classNames from 'classnames/bind';
import React from 'react';
import styles from './index.less';
const cx = classNames.bind(styles);
type CustomerServiceReportProps = {
  data: API.WorkOrderInsertReq & API.WorkOrderPageResp;
};
const CustomerServiceReport3 = React.forwardRef<HTMLDivElement, CustomerServiceReportProps>(
  ({ data = {} }, ref) => {
    return (
      <div className={cx('container')} ref={ref}>
        <div className={cx('header-content')}>
          <div className={cx('header')}>现场支持服务报告单</div>
          <div className={cx('sub-header')}>technical support service report</div>
        </div>
        <div className={cx('header-line')} />
        <table className={cx('table')}>
          <tbody>
            <tr>
              <th colSpan={6}>客户方信息</th>
            </tr>
            <tr>
              <td>工单编号：</td>
              <td>{data?.documentNumber}</td>
              <td>工单类型：</td>
              <td>客户咨询服务</td>
            </tr>
            <tr>
              <td>项目编号：</td>
              <td>{data?.projectNumber}</td>
              <td>工程师工号：</td>
              <td>{data?.employeeNumber}</td>
            </tr>
            <tr>
              <td>项目名称：</td>
              <td>{data?.projectName}</td>
              <td>工程师姓名：</td>
              <td>{data?.employeeName}</td>
            </tr>
            <tr>
              <td>开始时间：</td>
              <td>{data?.startTime}</td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td>结束时间：</td>
              <td>{data?.endTime}</td>
              <td></td>
              <td></td>
            </tr>
          </tbody>
        </table>
        <div className={cx('text-line')} />
        <table className={cx('table', 'table-2')}>
          <tbody>
            <tr>
              <th colSpan={2}>工时和事件</th>
            </tr>
            <tr>
              <tr>
                <td>工时（时）：</td>
                <td>{data?.workHours}</td>
              </tr>
              <tr>
                <td>事件描述：</td>
                <td>{data?.describe}</td>
              </tr>
              <tr>
                <td>工作内容：</td>
                <td>{data?.content}</td>
              </tr>
              <tr>
                <td>下一步计划：</td>
                <td>{data?.nextPlan}</td>
              </tr>
            </tr>
          </tbody>
        </table>
        <div className={cx('text-line')} />
        <table className={cx('table', 'table-2')}>
          <tbody>
            <tr>
              <th colSpan={2}>客户</th>
            </tr>
            <tr>
              <td>客户评价：</td>
              <td>
                <label>
                  <input name="excellent" type="radio" value="" />
                  优秀
                </label>
                <label>
                  <input name="good" type="radio" value="" />
                  良好
                </label>
                <label>
                  <input name="ordinary" type="radio" value="" />
                  一般
                </label>
                <label>
                  <input name="bad" type="radio" value="" />差
                </label>
              </td>
            </tr>
            <tr>
              <td>客户意见：</td>
              <td></td>
            </tr>
          </tbody>
        </table>
        <div className={cx('text-line')} />
        <table className={cx('table')}>
          <tbody>
            <tr>
              <th colSpan={6}>签名</th>
            </tr>
            <tr className={cx('sign-tr')}>
              <td>客户确认签名：</td>

              <td>
                <div className={cx('line1')} />
              </td>
              <td>工程师签名：</td>
              <td>
                <div className={cx('line1')} />
              </td>
            </tr>
            <tr className={cx('sign-tr')}>
              <td>签 名 日 期：</td>
              <td>
                <div className={cx('time-content')}>
                  <span className={cx('line-text')}>年</span>
                  <span className={cx('line-text')}>月</span>
                  <span className={cx('line-text')}>日</span>
                </div>
              </td>
              <td>签 名 日 期：</td>
              <td>
                <div className={cx('time-content')}>
                  <span className={cx('line-text')}>年</span>
                  <span className={cx('line-text')}>月</span>
                  <span className={cx('line-text')}>日</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  },
);

export default CustomerServiceReport3;
