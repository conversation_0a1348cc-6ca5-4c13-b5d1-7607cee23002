// pt = px * 3/4

.text {
  color: #000;
  text-align: left;
  vertical-align: top;
}
.border-right {
  border-right: 1px #000 solid;
}
.border-top {
  border-top: 1px #000 solid;
}
/* 模板内部style */
.print-container {
  width: 100%;
  color: #000;
  font-size: 9pt;
  font-family: inherit;
  word-break: break-all;
  .print-table-container {
    display: table;
    width: 100%;
    height: 100%;
    page-break-inside: avoid;
  }

  .print-grid {
    display: grid;
    grid-gap: 10px;
    grid-template-columns: repeat(2, 1fr);
    width: 100%;
    font-size: 12px;
    .row {
      display: flex;
      margin-bottom: 5px;
      .col {
        &-title {
          width: 40%;
          font-weight: 700;
        }
        &-content {
          flex: 1;
        }
      }
    }
  }
  // 页眉页脚占位
  .thead,
  .tfoot {
    display: table-header-group;
    height: 70px;
  }
  // 头部
  .print-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    margin-bottom: 16px;
    page-break-after: always;
    &-title {
      margin: 0;
      padding: 0;
      font-weight: 700;
      font-size: 15pt;
      line-height: 30px;
    }
    &-subtitle {
      margin: 0;
      font-weight: 600;
      font-size: 10pt;
      line-height: 24px;
      border-bottom: 1px solid #000;
    }
  }

  .print-table {
    width: 100%;
    margin: 24px 0;
    text-align: left;
    .row,
    .row-header {
      display: flex;
      box-sizing: border-box;
      width: 100%;
      line-height: 20px;
      border-left: solid 1px #000;
      .col-title,
      .col-content {
        flex: 1;
      }
      .col-title {
        padding: 8px 6px;
        border-right: solid 1px #000;
        border-bottom: solid 1px #000;
      }
      .col-content {
        padding: 4px 6px;
      }
    }
    .row-header {
      font-weight: 700;
      border-top: solid 1px #000;
    }
  }
}
