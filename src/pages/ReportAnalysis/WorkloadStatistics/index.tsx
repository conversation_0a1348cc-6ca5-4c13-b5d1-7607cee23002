import { defaultTableConfig } from '@/utils/setting';
import {
  PageContainer,
  ProCard,
  ProColumns,
  ProFormInstance,
  ProTable,
  StatisticCard,
} from '@ant-design/pro-components';
import { Link, useRequest } from '@umijs/max';
import dayjs from 'dayjs';
import { useEffect, useMemo, useRef, useState } from 'react';

import IconFont from '@/components/Icon';
import { useUserList } from '@/hooks/useUserList';
import { salesWeekAnalysisList } from '@/services/oa/analysis';
import { getDepartmentWeeklyTree } from '@/services/oa/department';
import { FallOutlined, RiseOutlined } from '@ant-design/icons';
import { Space, Typography } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import isoWeek from 'dayjs/plugin/isoWeek';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import ChartCards, { ChartCardProps } from '../components/ChartCards';
import { arrHasOwnProperty, arrSort, COLUMN_CONFIG, getTransformedData } from '../utils';
import styles from './index.less';

dayjs.extend(isoWeek);
dayjs.extend(weekOfYear);
export const METRICS = [
  {
    label: '总工时',
    value: 'total',
  },
  {
    label: '平均工时',
    value: 'average',
  },
];
export const CHART_TYPES = METRICS.map((item) => item.value);

export const METRIC_NAME = (metric: string) => METRICS.find((item) => item.value === metric)?.label;

export const sortMap = {
  ascend: 'ASC',
  descend: 'DESC',
};
const { Divider } = StatisticCard;
const { Text } = Typography;

const formatStr = 'YYYY-MM-DD HH:mm:ss';

const TYPE_MAP = {
  week: '周',
  month: '月',
  quarter: '季度',
  year: '年',
};

// 周时间处理
function getWeekRangeFromWeekNumber(input: string) {
  if (dayjs(input)?.isValid()) {
    return {
      startTime: dayjs(input).isoWeekday(1).startOf('day').format(formatStr),
      endTime: dayjs(input).isoWeekday(7).endOf('day').format(formatStr),
    };
  }

  const [yearStr, weekNumber] = input.split('-');
  const year = parseInt(yearStr, 10);
  const parsedWeekNumber = parseInt(weekNumber.replace(/th/g, ''), 10);

  const startOfWeek = dayjs().year(year).isoWeek(parsedWeekNumber).isoWeekday(1).startOf('day');
  const endOfWeek = dayjs().year(year).isoWeek(parsedWeekNumber).isoWeekday(7).endOf('day');

  return {
    startTime: startOfWeek.format(formatStr),
    endTime: endOfWeek.format(formatStr),
  };
}

// 季度时间处理
function getQuarterRange(quarterStr: string) {
  if (dayjs(quarterStr)?.isValid()) {
    return {
      startTime: dayjs(quarterStr).startOf('month').format(formatStr),
      endTime: dayjs(quarterStr).startOf('month').add(3, 'month').format(formatStr),
    };
  }

  const [year, quarter] = quarterStr.split('-');
  const quarterNumber = parseInt(quarter.replace(/Q/g, ''), 10);
  const endOfMonth = quarterNumber * 3 - 1;
  const startOfMonth = endOfMonth - 2;

  const yearObj = dayjs().year(parseInt(year, 10));
  const startDate = yearObj.month(startOfMonth).startOf('month');
  const endDate = yearObj.month(endOfMonth).endOf('month');

  return {
    startTime: startDate.format(formatStr),
    endTime: endDate.format(formatStr),
  };
}

type Unit = dayjs.OpUnitType | dayjs.QUnitType;
// 获取时间段 dayjs 转开始时间，结束时间
const formatterTime = (type: Unit, date: string) => {
  let startTime, endTime;

  if (type === 'week') {
    return getWeekRangeFromWeekNumber(date);
  } else if (type === 'quarter') {
    return getQuarterRange(date);
  } else {
    startTime = dayjs(date).startOf(type).format(formatStr);
    endTime = dayjs(date).endOf(type).format(formatStr);
  }

  return { startTime, endTime };
};

// 获取百分比
// const getPercentage = (val: number, total: number) => {
//   return (val / total) * 100;
// };

const WorkloadStatistics = () => {
  const ref = useRef<ProFormInstance>();
  const [type, setType] = useState<keyof typeof TYPE_MAP>('week');
  const dateRef = useRef<string>();
  const { userList, loading: userLoading } = useUserList();

  useEffect(() => {
    const initParams = {
      type: ref.current?.getFieldValue('type') || 'week',
      time: ref.current?.getFieldValue('time') || dayjs(),
    };
    ref.current?.setFieldsValue(initParams);
    setType(ref.current?.getFieldValue('type') || 'week');
  }, []);

  const { data, loading, run } = useRequest((params) => salesWeekAnalysisList(params), {
    manual: true,
  });
  const { data: depTree = [], loading: depLoading } = useRequest(() => getDepartmentWeeklyTree(), {
    formatResult: (res) =>
      res?.data?.map((i) => ({
        children: i.child?.map((chi) => ({
          value: chi.id,
          label: chi.departmentName,
        })),
        value: i.id,
        label: i.departmentName,
      })) || [],
  });
  const columns: ProColumns<API.WeekAnalysisDetailsResp>[] = [
    {
      title: '编号',
      dataIndex: 'documentNumber',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '汇总人',
      dataIndex: 'employeeName',
      width: 120,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '汇总维度',
      dataIndex: 'type',
      valueType: 'radio',
      fieldProps: {
        buttonStyle: 'solid',
        optionType: 'button',
        onChange: (e) => {
          setType(e.target.value as keyof typeof TYPE_MAP);
        },
      },
      valueEnum: {
        week: {
          text: '按周',
        },
        month: {
          text: '按月',
        },
        quarter: {
          text: '按季度',
        },
        year: {
          text: '按年',
        },
      },
      hideInTable: true,
    },
    {
      title: '所属部门',
      dataIndex: 'departmentBranchId',
      width: 120,
      valueType: 'treeSelect',
      fieldProps: {
        options: depTree,
        loading: depLoading,
      },
      renderText: (_, record) => {
        const { departmentBranch = '', department = '' } = record || {};
        return `${department || ''}${departmentBranch || ''}`;
      },
    },
    {
      title: '汇总时间',
      dataIndex: 'time',
      valueType: 'date',
      hideInTable: true,
      hideInSearch: type !== 'week',
      fieldProps: {
        picker: 'week',
        format: (value: any) => {
          // const val = value.isValid() ? value : dayjs();
          const date = dayjs(value);
          const year = date.weekday(4).year();
          const week = date.isoWeek();
          return `${year}-${week}周`;
        },
      },
    },
    {
      title: '汇总时间',
      dataIndex: 'time',
      valueType: 'dateMonth',
      hideInTable: true,
      hideInSearch: type !== 'month',
    },
    {
      title: '汇总时间',
      dataIndex: 'time',
      valueType: 'dateQuarter',
      hideInTable: true,
      hideInSearch: type !== 'quarter',
    },
    {
      title: '汇总时间',
      dataIndex: 'time',
      valueType: 'dateYear',
      hideInTable: true,
      hideInSearch: type !== 'year',
    },
    // {
    //   title: '技术组',
    //   dataIndex: 'departmentHeadName',
    //   width: 120,
    //   hideInSearch: true,
    // onCell: (record, rowIndex) => {
    //   if (rowIndex === 0) return { rowSpan: 5 };
    //   else return { rowSpan: 1 };
    // },
    // },

    {
      title: '售后项目工时',
      width: 100,
      dataIndex: 'sh',
      hideInSearch: true,
      sorter: true,
      // render(dom, entity) {
      //   return (
      //     <div className={styles['progress-box']}>
      //       <Progress
      //         className={styles.progress}
      //         showInfo={false}
      //         percent={getPercentage(entity.sh || 0, entity.totalHours || 0)}
      //         strokeColor="#108ee9"
      //       />
      //       <div className={styles.info}>{dom}</div>
      //     </div>
      //   );
      // },
    },
    {
      title: '内部项目工时',
      width: 100,
      dataIndex: 'nb',
      hideInSearch: true,
      sorter: true,
      // render(dom, entity) {
      //   return (
      //     <div className={styles['progress-box']}>
      //       <Progress
      //         className={styles.progress}
      //         showInfo={false}
      //         percent={getPercentage(entity.nb || 0, entity.totalHours || 0)}
      //         strokeColor="#87d068"
      //       />
      //       <div className={styles.info}>{dom}</div>
      //     </div>
      //   );
      // },
    },
    {
      title: '售前项目工时',
      width: 100,
      dataIndex: 'sq',
      hideInSearch: true,
      sorter: true,
      // render(dom, entity) {
      //   return (
      //     <div className={styles['progress-box']}>
      //       <Progress
      //         className={styles.progress}
      //         showInfo={false}
      //         percent={getPercentage(entity.sq || 0, entity.totalHours || 0)}
      //         strokeColor="#FF00FF"
      //       />

      //       <div className={styles.info}>{dom}</div>
      //     </div>
      //   );
      // },
    },
    {
      title: '非项目工时',
      width: 100,
      dataIndex: 'other',
      hideInSearch: true,
      sorter: true,
      // render(dom, entity) {
      //   return (
      //     <div className={styles['progress-box']}>
      //       <Progress
      //         className={styles.progress}
      //         showInfo={false}
      //         percent={getPercentage(entity.other || 0, entity.totalHours || 0)}
      //         strokeColor="#FF9966"
      //       />

      //       <div className={styles.info}>{dom}</div>
      //     </div>
      //   );
      // },
    },
    {
      title: '总计',
      dataIndex: 'totalHours',
      width: 100,
      hideInSearch: true,
      align: 'center',
      render(_, entity) {
        const { totalHours = 0, weekday } = entity;
        return (
          <div className={styles.total}>
            {totalHours}
            {totalHours > (weekday || 0) * 8 && <RiseOutlined className={styles.icon1} />}
            {totalHours < (weekday || 0) * 8 && <FallOutlined className={styles.icon2} />}
          </div>
        );
      },
      sorter: true,
    },
    {
      title: '汇总人',
      dataIndex: 'employeeId',
      width: 120,
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        options: userList,
        loading: userLoading,
        fieldNames: {
          label: 'username',
          value: 'id',
        },
        filterOption: true,
        optionFilterProp: 'label',
      },
    },
    {
      title: '操作',
      align: 'center',
      valueType: 'option',
      width: 130,
      fixed: 'right',
      render: (_, record) => {
        return <Link to={`/weekly-report/list/${record.id}`}>明细</Link>;
      },
    },
  ];

  const chartData: ChartCardProps[] = useMemo(() => {
    const analysisData = data?.statistics?.map((i) => ({ ...i, type: i.yearWeek }));
    //  CUSTOM
    const enumMap: Record<string, DefaultOptionType[]> = {};
    const enumOptions = enumMap['yearWeek'];
    return CHART_TYPES.map((metric) => {
      const hasMetric = arrHasOwnProperty(analysisData, metric);
      const arr = arrSort(analysisData as any, metric);
      const transformedData = getTransformedData(arr, enumOptions);
      //平均值
      const averageValue = (
        transformedData.reduce(
          (acc: number, cur: Record<string, any>) => acc + Number(cur[metric]),
          0,
        ) / transformedData.length || 0
      ).toFixed(2);

      return {
        title: `${METRIC_NAME(metric)}分布图(按周统计)`,
        type: 'column',
        chartConfig: {
          ...COLUMN_CONFIG,
          xAxis: {
            label: {
              formatter: (text: string) => {
                return text;
              },
            },
          },
          yField: [metric], // 使用转换后的值,
          meta: {
            [metric]: { alias: METRICS.find((item) => item.value === metric)?.label },
          },
          slider:
            arr.length >= 15
              ? {
                  start: 0,
                  end: 15 / arr.length,
                  formatter: (text: string) => {
                    // 在这里对文本进行处理，例如截取或缩略
                    return text?.slice(0, 6);
                  },
                }
              : false,
          annotations: [
            // 平均值
            {
              type: 'line',
              start: ['min', averageValue],
              end: ['max', averageValue],
              text: {
                content: `平均线(${averageValue})`,
                position: 'right',
                offsetY: -10,
                style: {
                  textAlign: 'right',
                  fontSize: 11,
                  fill: '#13c2c2',
                },
              },
              style: {
                stroke: '#13c2c2',
                lineWidth: 1,
                lineDash: [4, 4],
              },
            },
          ],
          color: metric === 'total' ? '#5B8FF9' : '#faad14',
        },
        ready: CHART_TYPES?.includes(metric),
        data: hasMetric ? transformedData : [],
      };
    });
  }, [data]);

  const renderChartCards = () => <ChartCards chartCardItems={chartData} loading={loading} />;

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.PartnerInfoResp>
        {...defaultTableConfig}
        formRef={ref}
        scroll={{ x: '100%' }}
        form={{
          syncToUrl: true,
          syncToInitialValues: false,
          submitter: {
            onReset: () => {
              ref.current?.setFieldsValue({
                type: 'week',
                time: dayjs().format(formatStr),
              });
              ref.current?.submit();
              setType('week');
            },
          },
          extraUrlParams: {
            time: dateRef?.current,
          },
        }}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
          span: 8,
        }}
        columns={columns}
        headerTitle="工时列表"
        request={async (params, sort) => {
          const { type, time, current, pageSize, departmentBranchId, employeeId } = params;
          const { startTime, endTime } = formatterTime(type, time);
          dateRef.current = startTime;
          const sortTotalHours = sort && sort?.totalHours ? sortMap[sort.totalHours] : undefined;
          const sortEmployeeName =
            sort && sort?.employeeName ? sortMap[sort.employeeName] : undefined;
          const sortNb = sort && sort?.nb ? sortMap[sort.nb] : undefined;
          const sortSh = sort && sort?.sh ? sortMap[sort.sh] : undefined;
          const sortSq = sort && sort?.sq ? sortMap[sort.sq] : undefined;
          const sortOther = sort && sort?.other ? sortMap[sort.other] : undefined;
          const res = await run({
            pageNum: current,
            pageSize,
            startTime,
            endTime,
            employeeId,
            departmentBranchId,
            sortTotalHours,
            sortEmployeeName,
            sortNb,
            sortSh,
            sortSq,
            sortOther,
          });

          return {
            data: res?.records || [],
            success: true,
            total: res?.total || 0,
          };
        }}
        tableExtraRender={() => {
          return (
            <>
              <StatisticCard.Group
                loading={loading}
                headerBordered
                bordered={false}
                title={
                  <Space size={36}>
                    <Space>
                      <IconFont type="icon-workday" />
                      <Text type="secondary" style={{ fontSize: 15 }}>
                        本{TYPE_MAP[type]}工作日：
                      </Text>
                      <Text style={{ fontSize: 15 }}>{data?.weekday || 0} 天</Text>
                    </Space>
                    <Space>
                      <IconFont type="icon-work_hours" />
                      <Text style={{ fontSize: 15 }} type="secondary">
                        本{TYPE_MAP[type]}工作时间：
                      </Text>
                      <Text style={{ fontSize: 15 }}>{(data?.weekday || 0) * 8} 小时</Text>
                    </Space>
                  </Space>
                }
              >
                <StatisticCard
                  statistic={{
                    title: '应提交人数',
                    value: data?.shouldSubmit,
                    status: 'success',
                  }}
                />
                <StatisticCard
                  statistic={{
                    title: '实际提交人数',
                    value: data?.alreadySubmit,
                    status: 'success',
                  }}
                />
                <StatisticCard
                  statistic={{
                    title: '未提交人数',
                    value: data?.notSubmit,
                    status: 'warning',
                  }}
                />
                <StatisticCard
                  statistic={{
                    title: '加班人数',
                    value: data?.overTimeNumber,
                    status: 'error',
                  }}
                />
                <StatisticCard
                  statistic={{
                    title: '工时不足人数',
                    value: data?.lackHours,
                    status: 'error',
                  }}
                />
                <Divider />
                <StatisticCard
                  statistic={{
                    title: '最长工时',
                    value: data?.maxHours,
                    status: 'processing',
                  }}
                />
                <StatisticCard
                  statistic={{
                    title: '最少工时',
                    value: data?.minHours,
                    status: 'warning',
                  }}
                />
              </StatisticCard.Group>
              <ProCard style={{ marginTop: 20 }}>
                {/* 统计图 */}
                {renderChartCards()}
              </ProCard>
            </>
          );
        }}
      ></ProTable>
    </PageContainer>
  );
};

export default WorkloadStatistics;
