import { EXPIRED_STATE, PROJECT_STATUS } from '@/enums';
import { option2enum } from '@/utils';
import { ProColumns } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import dayjs from 'dayjs';

export const columnsConfig: ProColumns<API.ProBaseInfoResp>[] = [
  {
    title: '项目编号',
    dataIndex: 'projectNumber',
    width: 150,
    copyable: true,
    render(dom, entity) {
      return (
        <a
          className="rk-a-span"
          onClick={() => {
            history.push(`/project/pre-sales/edit/${entity.id}`);
          }}
        >
          {dom}
        </a>
      );
    },
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
    width: 150,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'expiredState',
    width: 150,
    renderText(text, record) {
      if (!record?.endTime) return '1';
      const expired = dayjs(record.endTime).endOf('day').isBefore();
      return expired ? '1' : '0';
    },
    valueEnum: option2enum(EXPIRED_STATE),
  },
  {
    title: '项目状态',
    dataIndex: 'status',
    valueEnum: option2enum(PROJECT_STATUS),
    ellipsis: true,
    width: 150,
  },
  {
    title: '项目经理',
    dataIndex: 'projectManger',
    width: 100,
  },
];
