import { ProFormDateRangePicker, ProFormSelect } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { DimensionFields } from '../components/DimensionSetting';

// 时间字段
export const DATE = 'START_TIME';
export const DIMENSION = [
  {
    label: '项目名称',
    value: 'ID',
  },
];

export const METRICS = [
  {
    label: '目前开销',
    value: 'countMoney',
  },
  {
    label: '目前已执行工作量',
    value: 'countWeek',
  },
];
export const dimensionFields: DimensionFields[] = [
  {
    name: DATE,
    label: '开始日期',
    type: 'dateRangePicker',
    component: ProFormDateRangePicker,
    required: true,
  },
  {
    name: 'dimension',
    label: '维度',
    component: ProFormSelect,
    fieldProps: {
      options: DIMENSION,
      showSearch: true,
    },
    required: true,
  },
  {
    name: 'metrics',
    label: '指标',
    component: ProFormSelect,
    fieldProps: {
      options: METRICS,
      showSearch: true,
      mode: 'multiple',
    },
    required: true,
  },
];

export const CHART_TYPES = METRICS.map((item) => item.value);

export const METRIC_NAME = (metric: string) => METRICS.find((item) => item.value === metric)?.label;

// 查询条件初始值
export const initialValues: Record<string, any> = {
  metrics: CHART_TYPES,
  dimension: DIMENSION?.[0]?.value,
  [DATE]: [dayjs().startOf('y').format('YYYY-MM-DD'), dayjs().endOf('y').format('YYYY-MM-DD')],
};
