import {
  purchasePaymentPlanAnalysisInfo,
  purchasePaymentPlanAnalysisList,
} from '@/services/oa/analysis';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProCard, ProForm, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { DefaultOptionType } from 'antd/es/select';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import ChartCards, { ChartCardProps } from '../components/ChartCards';
import { arrHasOwnProperty, COLUMN_CONFIG, getTransformedData, PIE_CONFIG } from '../utils';
import { columnsConfig } from './columnsConfig';
import StatisticsCard from './components/StatisticsCard';
import { CHART_TYPES, DIMENSION, METRICS, METRIC_NAME } from './conifg';

const ProcurementPaymentPlanTable: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [statistics, setStatistics] = useState<Record<string, any>>();
  const [queryParams, setQueryParams] = useState({});

  // 获取统计信息
  const {
    loading,
    data = [],
    run,
  } = useRequest((params) => purchasePaymentPlanAnalysisInfo(params), {
    manual: true,
  });

  useEffect(() => {
    if (Object.entries(queryParams).length !== 0) {
      run({
        ...queryParams,
      });
    }
  }, [queryParams]);

  const chartData: ChartCardProps[] = useMemo(() => {
    //  CUSTOM
    const { value } = DIMENSION[0]!;
    const enumMap: Record<string, DefaultOptionType[]> = {};
    const enumOptions = enumMap[value];

    return CHART_TYPES.map((metric) => {
      const hasMetric = arrHasOwnProperty(data?.[value], metric);
      const arr = data?.[value] || [];
      const transformedData = getTransformedData(arr, enumOptions).map((item: any) => ({
        ...item,
        type: item?.month,
      }));
      //  CUSTOM
      const showColumnChart = ['MONTH'].includes(value);
      return {
        title: `${METRIC_NAME(metric)}分布图`,
        type: showColumnChart ? 'column' : 'pie',
        chartConfig: showColumnChart
          ? {
              ...COLUMN_CONFIG,
              yField: metric, // 使用转换后的值,
              xAxis: {
                label: {
                  formatter: (text: string) => {
                    // 在这里对文本进行处理，例如截取或缩略
                    return text.slice(0, 7);
                  },
                },
              },
              meta: {
                [metric]: { alias: METRICS.find((item) => item.value === metric)?.label },
              },
              slider:
                arr.length >= 15
                  ? {
                      start: 0,
                      end: 15 / arr.length,
                      formatter: (text: string) => {
                        // 在这里对文本进行处理，例如截取或缩略
                        return text.slice(0, 7);
                      },
                    }
                  : false,
            }
          : { ...PIE_CONFIG, angleField: metric },
        ready: true,
        data: hasMetric ? transformedData : [],
      };
    });
  }, [data]);

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      {/* 统计卡片 */}
      <ProForm.Item>
        <StatisticsCard value={statistics} />
      </ProForm.Item>
      <ProTable<API.PartnerInfoResp>
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columnsConfig}
        headerTitle="采购付款计划汇总表"
        search={{ filterType: 'query', defaultCollapsed: false }}
        tableRender={(props, dom, { toolbar, table }) => {
          return [
            <ChartCards key="charts" chartCardItems={chartData} loading={loading} />,
            <ProCard key="toolbar">{toolbar}</ProCard>,
            <ProCard key="table">{table}</ProCard>,
          ];
        }}
        request={async (params) => {
          const {
            salePerson,
            clientAbbreviation,
            payStatus,
            current,
            pageSize,
            month,
            collectTickets,
          } = params;
          const filter = { payStatus };
          const search = { salePerson, clientAbbreviation };
          const scope =
            month &&
            [
              {
                key: 'ge',
                name: 'estimatePayTime',
                val: dayjs(month[0]).startOf('month').format('YYYY-MM-DD'),
              },
              {
                key: 'le',
                name: 'estimatePayTime',
                val: dayjs(month[1]).endOf('month').format('YYYY-MM-DD'),
              },
            ].filter(Boolean);
          const extra = { collectTickets };
          setQueryParams({ current, pageSize, scope, search, filter, extra });

          const msg = await purchasePaymentPlanAnalysisList({
            pageNum: current,
            pageSize,
            scope,
            search,
            filter,
            extra,
          });
          const {
            payMoney,
            notTicketMoney,
            ticketMoney,
            notPayMoney,
            payMoneyAll,
            ticketMoneyAll,
          } = msg?.data as Record<string, any>;
          setStatistics({
            payMoney,
            notTicketMoney,
            ticketMoney,
            notPayMoney,
            payMoneyAll,
            ticketMoneyAll,
          });
          return {
            ...msg?.data,
            data: msg?.data?.records || [],
            success: true,
            total: msg?.data?.total,
          };
        }}
      />
    </PageContainer>
  );
};

export default ProcurementPaymentPlanTable;
