import { PAYMENT_STATUS } from '@/enums';
import { option2enum } from '@/utils';
import { ProColumns } from '@ant-design/pro-components';
import { history } from '@umijs/max';

//收票状态
const COLLECT_TICKET_STATUS = [
  {
    label: '未收票',
    value: 'NOT_TICKET',
    status: 'default',
  },
  {
    label: '已收票',
    value: 'TICKET',
    status: 'success',
  },
];

export const columnsConfig: ProColumns<Record<string, any>>[] = [
  {
    title: '日期范围',
    dataIndex: 'month',
    hideInTable: true,
    valueType: 'dateMonthRange',
  },
  {
    title: '付款状态',
    dataIndex: 'payStatus',
    valueEnum: option2enum(PAYMENT_STATUS),
    hideInTable: true,
  },
  {
    title: '收票状态',
    dataIndex: 'collectTickets',
    valueEnum: option2enum(COLLECT_TICKET_STATUS),
    hideInTable: true,
  },
  {
    title: '销售',
    dataIndex: 'salePerson',
    hideInTable: true,
  },
  {
    title: '供应商名称',
    dataIndex: 'clientName',
    width: 150,
    ellipsis: true,
    hideInSearch: true,
    render(dom, entity) {
      return (
        <a
          className="rk-a-span"
          onClick={() => {
            history.push(`/crm/suppliers/edit/${entity.partnerId}`);
          }}
        >
          {dom}
        </a>
      );
    },
  },
  {
    title: '供应商简称',
    dataIndex: 'clientAbbreviation',
    width: 120,
    ellipsis: true,
  },
  {
    title: '销售',
    dataIndex: 'salePerson',
    width: 120,
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '合同编号',
    dataIndex: 'contractNumber',
    width: 150,
    copyable: true,
    hideInSearch: true,
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    width: 200,
    ellipsis: true,
    hideInSearch: true,
    render(dom, entity) {
      return (
        <a
          className="rk-a-span"
          onClick={() => {
            history.push(`/contract/purchase/edit/${entity.contractId}`);
          }}
        >
          {dom}
        </a>
      );
    },
  },
  {
    title: '期次',
    dataIndex: 'period',
    width: 80,
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '计划付款金额',
    valueType: 'money',
    dataIndex: 'estimatePayAmount',
    width: 130,
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '计划付款日期',
    valueType: 'date',
    dataIndex: 'estimatePayTime',
    width: 100,
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '付款状态',
    dataIndex: 'payStatus',
    valueEnum: option2enum(PAYMENT_STATUS),
    ellipsis: true,
    width: 120,
    hideInSearch: true,
  },
];
