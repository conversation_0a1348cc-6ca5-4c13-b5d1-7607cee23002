import Icon from '@/components/Icon';
import { StatisticCard } from '@ant-design/pro-components';
const { Divider } = StatisticCard;
const iconStyles = { fontSize: '28px', filter: 'hue-rotate(282deg) opacity(0.78)' };

const StatisticsCard: React.FC<{
  value?: Record<string, any>;
  onChange?: (val: Record<string, any>) => void;
}> = ({ value = {} }) => {
  const { payMoney, notTicketMoney, ticketMoney, notPayMoney, payMoneyAll, ticketMoneyAll } =
    value || {};
  return (
    <StatisticCard.Group className="rklink-statistic">
      <StatisticCard.Group>
        <StatisticCard
          statistic={{
            title: '已付款总金额',
            prefix: '¥',
            value: payMoneyAll || 0,
            icon: <Icon type="icon-money" style={iconStyles} />,
          }}
        />
        <Divider />
        <StatisticCard
          statistic={{
            title: '已付款金额',
            prefix: '¥',
            value: payMoney || 0,
            status: 'success',
          }}
        />
        <StatisticCard
          statistic={{
            title: '未付款金额',
            prefix: '¥',
            value: notPayMoney || 0,
            status: 'success',
          }}
        />
      </StatisticCard.Group>
      <StatisticCard.Group>
        <StatisticCard
          statistic={{
            title: '已收票总金额',
            prefix: '¥',
            value: ticketMoneyAll || 0,
            icon: <Icon type="icon-money" style={iconStyles} />,
          }}
        />
        <Divider />
        <StatisticCard
          statistic={{
            title: '已收票金额',
            prefix: '¥',
            value: ticketMoney || 0,
            status: 'success',
          }}
        />
        <StatisticCard
          statistic={{
            title: '未收票金额',
            prefix: '¥',
            value: notTicketMoney || 0,
            status: 'success',
          }}
        />
      </StatisticCard.Group>
    </StatisticCard.Group>
  );
};

export default StatisticsCard;
