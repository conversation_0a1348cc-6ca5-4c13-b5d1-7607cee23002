import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';

type DetailListProps<T> = {
  columns: ProColumns<T, 'text'>[];
  params?: Record<string, any>;
  api: (data: any) => Promise<Record<string, any>>;
};
const DetailList = <T extends Record<string, any>>({
  columns,
  params,
  api,
}: DetailListProps<T>) => {
  return (
    <ProTable<T>
      {...defaultTableConfig}
      search={false}
      options={{
        reload: false,
        density: false,
        setting: false,
        search: true,
      }}
      scroll={{ x: '100%' }}
      columns={columns}
      headerTitle="明细"
      params={params}
      request={async (params) => {
        const { current, pageSize, date, keyword } = params;
        const msg = await api({
          pageNum: current,
          pageSize,
          date,
          keyword,
        });
        return {
          ...msg?.data,
          data: msg?.data?.records || [],
          success: true,
          total: msg?.data?.total,
        };
      }}
      size="small"
    />
  );
};

export default DetailList;
