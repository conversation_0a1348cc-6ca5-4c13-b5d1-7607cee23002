.card {
  margin-bottom: 24px !important;
  .icon {
    filter: hue-rotate(282deg) opacity(0.78);
  }
  svg {
    font-size: 28px;
  }
  :global {
    .ant-statistic-content {
      .ant-statistic-content-prefix,
      .ant-statistic-content-value,
      .ant-statistic-content-value-int {
        font-size: 20px !important;
      }
    }
  }
  .statistic-card {
    :global {
      .ant-pro-card-body {
        padding: 0;
        .ant-pro-card-col {
          padding: 24px !important;
        }
      }
    }
  }
}
