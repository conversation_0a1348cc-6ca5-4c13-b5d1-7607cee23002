import Icon from '@/components/Icon';
import {
  ProCard,
  ProCardProps,
  StatisticCard as AntdStatisticCard,
  StatisticProps,
} from '@ant-design/pro-components';
import styles from './index.less';
type IconType = 'statistics' | 'money' | 'percentage';
const iconMap: Record<IconType, React.ReactNode> = {
  statistics: <Icon type="icon-new_approval" />,
  percentage: <Icon type="icon-statistics" className={styles.icon} />,
  money: <Icon type="icon-money" className={styles.icon} />,
};
export interface Statistics extends StatisticProps {
  type?: IconType;
}

const StatisticCard: React.FC<{
  data: Statistics[];
  colSpan?: ProCardProps['colSpan'];
  loading?: boolean;
}> = ({ data = [], loading, colSpan = { xs: 12, sm: 8, md: 8, lg: 6, xl: 6 } }) => {
  return (
    <ProCard.Group className={styles.card} wrap loading={loading}>
      {data.map((statistic, key) => {
        return (
          <ProCard
            key={key}
            colSpan={colSpan}
            gutter={[24, 24]}
            direction="row"
            className={styles['statistic-card']}
          >
            <AntdStatisticCard
              statistic={{
                icon: statistic?.type ? iconMap?.[statistic?.type] : null,
                ...statistic,
              }}
            />
          </ProCard>
        );
      })}
    </ProCard.Group>
  );
};

export default StatisticCard;
