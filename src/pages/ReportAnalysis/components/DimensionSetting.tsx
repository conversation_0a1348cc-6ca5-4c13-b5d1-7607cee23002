import RKCol from '@/components/RKCol';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  ProCard,
  ProColumns,
  ProForm,
  ProFormInstance,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Form, FormProps, Row, Space, TimeRangePickerProps } from 'antd';
import dayjs from 'dayjs';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import { Key, useEffect, useRef, useState } from 'react';
dayjs.extend(quarterOfYear);

export const rangePresets: TimeRangePickerProps['presets'] = [
  { label: '本月', value: [dayjs().startOf('M'), dayjs().endOf('M')] },
  { label: '本季度', value: [dayjs().startOf('quarter'), dayjs().endOf('quarter')] },
  { label: '本年', value: [dayjs().startOf('y'), dayjs().endOf('y')] },
  { label: '近三个月', value: [dayjs().subtract(3, 'month'), dayjs()] },
  { label: '近半年', value: [dayjs().subtract(6, 'month'), dayjs()] },
  { label: '近1年', value: [dayjs().subtract(1, 'year'), dayjs()] },
  { label: '近2年', value: [dayjs().subtract(2, 'year'), dayjs()] },
];

export type DimensionFields<T = Record<string, any>> = {
  name: string;
  label: string;
  component: React.FC<any>;
  fieldProps?: T;
  type?: string;
  required?: boolean;
};

const DimensionSetting: React.FC<{
  fields: DimensionFields[];
  excludeColumns?: ProColumns[];
  initialValues?: FormProps['initialValues'];
  onFinish?: ((formData: Record<string, any>) => Promise<boolean | void>) | undefined;
  loading?: boolean;
  api: (data: any) => Promise<Record<string, any>>;
  /**
   * 查询的时间字段
   */
  timeFields: string;
}> = ({ fields = [], onFinish, initialValues, excludeColumns, loading, timeFields, api }) => {
  const [drawerForm] = Form.useForm();
  const formRef = useRef<ProFormInstance>();

  const [selectedKeys, setSelectedKeys] = useState<Key[]>(
    formRef.current?.getFieldValue('excludeIds') || [],
  );

  useEffect(() => formRef.current?.setFieldValue('excludeIds', selectedKeys), [selectedKeys]);

  return (
    <ProCard
      title={
        <>
          分析配置
          {excludeColumns && (
            <DrawerForm
              title="排除数据"
              form={drawerForm}
              trigger={<Button type="link">排除数据设置</Button>}
              autoFocusFirstInput
              drawerProps={{
                destroyOnClose: true,
              }}
              width={660}
              submitter={false}
              onFinish={async () => {
                formRef.current?.setFieldValue('excludeIds', selectedKeys);
                // 不返回不会关闭弹框
                return true;
              }}
            >
              <ProTable
                {...defaultTableConfig}
                className="inner-table"
                search={false}
                options={{
                  reload: false,
                  density: false,
                  setting: false,
                  search: true,
                }}
                scroll={{ x: '100%' }}
                columns={excludeColumns}
                rowSelection={{
                  preserveSelectedRowKeys: true,
                  selectedRowKeys: selectedKeys,
                  onChange: (selectedRowKeys) => {
                    setSelectedKeys(selectedRowKeys);
                  },
                }}
                headerTitle="明细"
                size="small"
                tableAlertRender={({ selectedRowKeys }) => {
                  return (
                    <Space size={24}>
                      <span>已选 {selectedRowKeys.length} 项</span>
                    </Space>
                  );
                }}
                request={async (params) => {
                  const { current, pageSize, keyword } = params;

                  const msg = await api({
                    pageNum: current,
                    pageSize,
                    date: {
                      [timeFields]: formRef.current?.getFieldValue(timeFields),
                    },
                    keyword,
                  });
                  return {
                    ...msg?.data,
                    data: msg?.data?.records || [],
                    success: true,
                    total: msg?.data?.total,
                  };
                }}
              />
            </DrawerForm>
          )}
        </>
      }
      style={{ marginBlockEnd: 24 }}
    >
      <ProForm
        layout="horizontal"
        formRef={formRef}
        submitter={{
          searchConfig: {
            submitText: '分析',
          },
          submitButtonProps: {
            loading,
          },
        }}
        initialValues={initialValues}
        onFinish={onFinish}
      >
        <div className="rk-none">
          <ProFormText name="excludeIds" />
        </div>
        <Row gutter={24}>
          {fields.map((field, index) => {
            const {
              component: FieldComponent,
              fieldProps = {},
              type,
              required = false,
              ...restProps
            } = field;
            return (
              <RKCol key={index} lg={8} md={8} sm={12}>
                <FieldComponent
                  fieldProps={{
                    presets: type === 'dateRangePicker' ? rangePresets : undefined,
                    ...fieldProps,
                  }}
                  rules={required && [requiredRule]}
                  {...restProps}
                />
              </RKCol>
            );
          })}
        </Row>
      </ProForm>
    </ProCard>
  );
};

export default DimensionSetting;
