import RkBar from '@/components/Charts/Bar';
import RKColumn from '@/components/Charts/Column';
import RkLine from '@/components/Charts/Line';
import RkPie from '@/components/Charts/Pie';
import { DEFAULT_CARD_COL_SPAN } from '@/pages/ReportAnalysis/utils';
import { ProCard, ProCardProps } from '@ant-design/pro-components';
import { Empty } from 'antd';
import styles from './index.less';

type ChartType = 'pie' | 'bar' | 'column' | 'Line';
export type ChartCardProps = {
  title: string;
  type: ChartType;
  colSpan?: ProCardProps['colSpan'];
  chartConfig?: Record<string, any>;
  ready: boolean;
  data: Record<string, any>;
};

const componentMap: Record<ChartType, React.ComponentType<any>> = {
  pie: RkPie,
  bar: RkBar,
  column: RKColumn,
  Line: RkLine,
};

const ChartCards: React.FC<{
  chartCardItems: ChartCardProps[];
  loading?: boolean;
}> = ({ chartCardItems, loading }) => {
  return (
    <ProCard
      className={styles['chart-card']}
      gutter={24}
      wrap
      direction="row"
      layout="default"
      loading={loading}
    >
      {chartCardItems.map((item, index) => {
        const {
          colSpan = DEFAULT_CARD_COL_SPAN,
          title,
          chartConfig,
          type,
          ready,
          data = [],
        } = item;
        const ChartComponent = Object.entries(componentMap).find(([key]) => key === type)?.[1];
        if (!ready || !ChartComponent) return null;

        return (
          <ProCard key={index} title={title} colSpan={colSpan}>
            {data.length > 0 ? (
              <ChartComponent {...chartConfig} data={data} />
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )}
          </ProCard>
        );
      })}
    </ProCard>
  );
};

export default ChartCards;
