import { internalAnalysisInfo, internalList } from '@/services/oa/analysis';
import { PageContainer } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { DefaultOptionType } from 'antd/es/select';
import { FC, useEffect, useMemo, useState } from 'react';
import ChartCards, { ChartCardProps } from '../components/ChartCards';
import DetailList from '../components/DetailList';
import DimensionSetting from '../components/DimensionSetting';
import StatisticCard, { Statistics } from '../components/StatisticCard';
import {
  arrHasOwnProperty,
  arrSort,
  COLUMN_CONFIG,
  getTransformedData,
  PIE_CONFIG,
} from '../utils';
import { columnsConfig } from './columnsConfig';
import {
  CHART_TYPES,
  DATE,
  DIMENSION,
  dimensionFields,
  initialValues,
  METRICS,
  METRIC_NAME,
} from './conifg';

const InternalProjectAnalysis: FC = () => {
  const [queryParams, setQueryParams] = useState(initialValues);

  const dimensionObj = useMemo(
    () => DIMENSION.find((item) => item.value === queryParams.dimension),
    [queryParams],
  );

  // CUSTOM
  // 获取统计信息
  const { loading, data, run } = useRequest((params) => internalAnalysisInfo(params), {
    manual: true,
  });

  useEffect(() => {
    run({
      ...queryParams,
      date: {
        [DATE]: queryParams[DATE],
      },
    });
  }, [queryParams]);

  const chartData: ChartCardProps[] = useMemo(() => {
    const { label, value } = dimensionObj!;
    //  CUSTOM
    const enumMap: Record<string, DefaultOptionType[]> = {};
    const enumOptions = enumMap[value];
    return CHART_TYPES.map((metric) => {
      const hasMetric = arrHasOwnProperty(data?.[value], metric);
      const arr = arrSort(data?.[value], metric);
      const transformedData = getTransformedData(arr, enumOptions);

      //  CUSTOM
      const showColumnChart = ['ID'].includes(value);

      return {
        title: `${label}-项目${METRIC_NAME(metric)}分布图`,
        type: showColumnChart ? 'column' : 'pie',
        chartConfig: showColumnChart
          ? {
              ...COLUMN_CONFIG,
              yField: metric, // 使用转换后的值,
              meta: {
                [metric]: { alias: METRICS.find((item) => item.value === metric)?.label },
              },
              slider:
                arr.length >= 15
                  ? {
                      start: 0,
                      end: 15 / arr.length,
                      formatter: (text: string) => {
                        // 在这里对文本进行处理，例如截取或缩略
                        return text.slice(0, 6);
                      },
                    }
                  : false,
            }
          : { ...PIE_CONFIG, angleField: metric },
        ready: queryParams?.metrics?.includes(metric),
        data: hasMetric ? transformedData : [],
      };
    });
  }, [data]);

  const renderChartCards = () => <ChartCards chartCardItems={chartData} loading={loading} />;

  // CUSTOM
  const statisticData: Statistics[] = [
    {
      title: '目前已执行总工作量',
      value: data?.sumWeek,
      type: 'statistics',
    },
    {
      title: '目前总开销',
      value: data?.sumMoney,
      type: 'money',
      prefix: '¥',
    },
  ];

  return (
    <PageContainer header={{ title: false }}>
      {/* 维度配置 */}
      <DimensionSetting
        //  CUSTOM
        api={internalList}
        fields={dimensionFields}
        initialValues={initialValues}
        onFinish={async (values) => {
          setQueryParams(values);
        }}
        excludeColumns={columnsConfig}
        loading={loading}
        timeFields={DATE}
      />
      {/* 统计卡片 */}
      <StatisticCard
        data={statisticData}
        // CUSTOM
        colSpan={{ xs: 12, sm: 8, md: 8, lg: 8, xl: 8 }}
        loading={loading}
      />

      {/* 统计图 */}
      {renderChartCards()}
      <DetailList
        columns={columnsConfig}
        params={{
          date: {
            [DATE]: queryParams[DATE],
          },
        }}
        // CUSTOM
        api={internalList}
      />
    </PageContainer>
  );
};

export default InternalProjectAnalysis;
