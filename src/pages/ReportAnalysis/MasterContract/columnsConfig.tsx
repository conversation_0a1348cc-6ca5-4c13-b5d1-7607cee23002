import { ProColumns } from '@ant-design/pro-components';
import { history } from '@umijs/max';

export const columnsConfig: ProColumns<API.MainContractInfoResp>[] = [
  {
    title: '合同编号',
    dataIndex: 'contractNumber',
    width: 150,
    copyable: true,
    render(dom, entity) {
      return (
        <a
          className="rk-a-span"
          onClick={() => {
            history.push(`/contract/main/details/${entity.id}`);
          }}
        >
          {dom}
        </a>
      );
    },
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    width: 150,
    ellipsis: true,
  },
  {
    title: '甲方',
    dataIndex: 'fpName',
    ellipsis: true,
    width: 150,
  },
  {
    title: '销售姓名',
    dataIndex: 'salePerson',
    width: 100,
  },
];
