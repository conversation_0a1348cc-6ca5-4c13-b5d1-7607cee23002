import { CONTRACT_CATEGORY, INDUSTRY } from '@/enums';
import { mainContractAnalysisInfo, mainContractAnalysisList } from '@/services/oa/analysis';
import { calculatePercentage } from '@/utils';
import { PageContainer } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { DefaultOptionType } from 'antd/es/select';
import { FC, useEffect, useMemo, useState } from 'react';
import ChartCards, { ChartCardProps } from '../components/ChartCards';
import DetailList from '../components/DetailList';
import DimensionSetting from '../components/DimensionSetting';
import StatisticCard, { Statistics } from '../components/StatisticCard';
import {
  arrHasOwnProperty,
  arrSort,
  COLUMN_CONFIG,
  getTransformedData,
  PIE_CONFIG,
} from '../utils';
import { columnsConfig } from './columnsConfig';
import {
  CHART_TYPES,
  DATE,
  DIMENSION,
  dimensionFields,
  initialValues,
  METRICS,
  METRIC_NAME,
} from './config';

const MasterContractReportAnalysis: FC = () => {
  const [queryParams, setQueryParams] = useState(initialValues);

  const dimensionObj = useMemo(
    () => DIMENSION.find((item) => item.value === queryParams.dimension),
    [queryParams],
  );

  // CUSTOM
  // 获取统计信息
  const { loading, data, run } = useRequest((params) => mainContractAnalysisInfo(params), {
    manual: true,
  });

  useEffect(() => {
    run({
      ...queryParams,
      date: {
        [DATE]: queryParams[DATE],
      },
    });
  }, [queryParams]);

  const chartData: ChartCardProps[] = useMemo(() => {
    const { label, value } = dimensionObj!;
    //  CUSTOM
    const enumMap: Record<string, DefaultOptionType[]> = {
      INDUSTRY: INDUSTRY,
      CONTRACT_CATEGORY: CONTRACT_CATEGORY,
    };
    const enumOptions = enumMap[value];
    return CHART_TYPES.map((metric) => {
      const hasMetric = arrHasOwnProperty(data?.[value], metric);
      const arr = arrSort(data?.[value], metric);
      const transformedData = getTransformedData(arr, enumOptions);

      //  CUSTOM
      const showColumnChart = ['FP_ID', 'END_USER_ID'].includes(value);

      return {
        title: `${label}合同${METRIC_NAME(metric)}分布图`,
        type: showColumnChart ? 'column' : 'pie',
        chartConfig: showColumnChart
          ? {
              ...COLUMN_CONFIG,
              yField: metric, // 使用转换后的值,
              meta: {
                [metric]: { alias: METRICS.find((item) => item.value === metric)?.label },
              },
              slider:
                arr.length >= 15
                  ? {
                      start: 0,
                      end: 15 / arr.length,
                      formatter: (text: string) => {
                        // 在这里对文本进行处理，例如截取或缩略
                        return text.slice(0, 6);
                      },
                    }
                  : false,
            }
          : { ...PIE_CONFIG, angleField: metric },
        ready: queryParams?.metrics?.includes(metric),
        data: hasMetric ? transformedData : [],
      };
    });
  }, [data]);

  const renderChartCards = () => <ChartCards chartCardItems={chartData} loading={loading} />;

  // CUSTOM
  const statisticData: Statistics[] = [
    {
      title: '总数量',
      value: data?.count,
      type: 'statistics',
    },
    {
      title: '总金额',
      value: data?.amount,
      type: 'money',
      prefix: '¥',
    },
    {
      title: '直签数',
      value: data?.directCount,
      type: 'percentage',
      description: `占比 ${calculatePercentage(data?.directCount, data?.count)}%`,
    },
    {
      title: '直签金额',
      value: data?.directAmount,
      prefix: '¥',
      type: 'percentage',
      description: `占比 ${calculatePercentage(data?.directAmount, data?.amount)}%`,
    },
    {
      title: '分包数',
      value: data?.subcontract,
      type: 'percentage',
      description: `占比 ${calculatePercentage(data?.subcontract, data?.count)}%`,
    },
    {
      title: '分包金额',
      prefix: '¥',
      value: data?.subcontractAmount,
      type: 'percentage',
      description: `占比 ${calculatePercentage(data?.subcontractAmount, data?.amount)}%`,
    },
  ];

  return (
    <PageContainer header={{ title: false }}>
      {/* 维度配置 */}
      <DimensionSetting
        // CUSTOM
        api={mainContractAnalysisList}
        fields={dimensionFields}
        initialValues={initialValues}
        onFinish={async (values) => {
          setQueryParams(values);
        }}
        excludeColumns={columnsConfig}
        loading={loading}
        timeFields={DATE}
      />
      {/* 统计卡片 */}
      <StatisticCard
        data={statisticData}
        // CUSTOM
        colSpan={{ xs: 12, sm: 8, md: 8, lg: 8, xl: 8 }}
        loading={loading}
      />

      {/* 统计图 */}
      {renderChartCards()}
      <DetailList
        columns={columnsConfig}
        params={{
          date: {
            [DATE]: queryParams[DATE],
          },
        }}
        // CUSTOM
        api={mainContractAnalysisList}
      />
    </PageContainer>
  );
};

export default MasterContractReportAnalysis;
