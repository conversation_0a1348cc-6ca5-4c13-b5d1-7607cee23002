import { ProFormDateRangePicker, ProFormSelect } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { DimensionFields } from '../components/DimensionSetting';
// 时间字段
export const DATE = 'SIGN_DATE';

export const DIMENSION = [
  {
    label: '销售',
    value: 'SALE_PERSON_ID',
  },
  {
    label: '甲方',
    value: 'FP_ID',
  },
  {
    label: '行业',
    value: 'INDUSTRY',
  },
  {
    label: '合同所在地',
    value: 'CONTRACT_ADDRESS',
  },
  {
    label: '合同类别',
    value: 'CONTRACT_CATEGORY',
  },
  {
    label: '最终用户',
    value: 'END_USER_ID',
  },
];

export const METRICS = [
  {
    label: '总数量',
    value: 'count',
  },
  {
    label: '总金额',
    value: 'amount',
  },
  {
    label: '直签数',
    value: 'directCount',
  },
  {
    label: '直签金额',
    value: 'directAmount',
  },
  {
    label: '分包数',
    value: 'subcontract',
  },
  {
    label: '分包金额',
    value: 'subcontractAmount',
  },
];
export const dimensionFields: DimensionFields[] = [
  {
    name: DATE,
    label: '签订日期',
    type: 'dateRangePicker',
    component: ProFormDateRangePicker,
    required: true,
  },
  {
    name: 'dimension',
    label: '维度',
    component: ProFormSelect,
    fieldProps: {
      options: DIMENSION,
      showSearch: true,
    },
    required: true,
  },
  {
    name: 'metrics',
    label: '指标',
    component: ProFormSelect,
    fieldProps: {
      options: METRICS,
      showSearch: true,
      mode: 'multiple',
    },
    required: true,
  },
];

export const CHART_TYPES = METRICS.map((item) => item.value);

export const METRIC_NAME = (metric: string) => METRICS.find((item) => item.value === metric)?.label;

// 查询条件初始值
export const initialValues: Record<string, any> = {
  metrics: CHART_TYPES,
  dimension: DIMENSION?.[0]?.value,
  [DATE]: [dayjs().startOf('y').format('YYYY-MM-DD'), dayjs().endOf('y').format('YYYY-MM-DD')],
};
