import { G2 } from '@ant-design/charts';
import { DefaultOptionType } from 'antd/es/select';
const { getTheme } = G2;
export const colors10 = getTheme().colors10;

export const DEFAULT_CARD_COL_SPAN = { xs: 24, sm: 24, md: 12, lg: 12, xl: 12 };

export const getTransformedData = (
  arr: Record<string, any>[],
  options: DefaultOptionType[] = [],
) => {
  return arr.map((item: Record<string, any>) => ({
    ...item,
    type: options?.find((type) => type.value === item.type)?.label || item.type,
  }));
};
/**
 * 数组对象是否包含某个key
 * @param arr 数据源
 * @param key 某个字段
 * @returns
 */
export const arrHasOwnProperty = (arr: Record<string, any>[] = [], key = '') =>
  arr?.at(0) && Object.prototype.hasOwnProperty.call(arr?.at(0), key);
/**
 * 数组排序
 * @param arr 数据源
 * @param key 排序字段
 * @returns
 */
export const arrSort = (arr = [], key = '') =>
  arr?.slice()?.sort((a: Record<string, any>, b: Record<string, any>) => b[key] - a[key]) || [];

// 饼图配置
export const PIE_CONFIG = {
  height: 220,
  radius: 0.8,
};
// 柱状图配置
export const COLUMN_CONFIG = {
  height: 220,
  xAxis: {
    label: {
      autoHide: true,
      formatter: (text: string) => {
        // 在这里对文本进行处理，例如截取或缩略
        return text.slice(0, 6);
      },
    },
  },
};
