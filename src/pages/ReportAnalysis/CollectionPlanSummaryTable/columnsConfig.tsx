import { COLLECT_STATUS, TICKET_STATUS } from '@/enums';
import { option2enum } from '@/utils';
import { ProColumns } from '@ant-design/pro-components';
import { history } from '@umijs/max';

export const columnsConfig: ProColumns<Record<string, any>>[] = [
  {
    title: '日期范围',
    dataIndex: 'month',
    width: 150,
    hideInTable: true,
    valueType: 'dateMonthRange',
  },
  {
    title: '收款状态',
    dataIndex: 'collectionStatus',
    valueEnum: option2enum(COLLECT_STATUS),
    hideInTable: true,
  },
  {
    title: '开票状态',
    dataIndex: 'collectTickets',
    valueEnum: option2enum(TICKET_STATUS),
    hideInTable: true,
  },
  {
    title: '销售',
    dataIndex: 'salePerson',
    hideInTable: true,
  },
  {
    title: '客户名称',
    dataIndex: 'clientName',
    width: 150,
    ellipsis: true,
    render(dom, entity) {
      return (
        <a
          className="rk-a-span"
          onClick={() => {
            history.push(`/crm/customer/edit/${entity.partnerId}`);
          }}
        >
          {dom}
        </a>
      );
    },
  },
  {
    title: '客户简称',
    dataIndex: 'clientAbbreviation',
    width: 100,
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '销售',
    dataIndex: 'salePerson',
    width: 100,
    hideInSearch: true,
  },
  {
    title: '合同编号',
    dataIndex: 'contractNumber',
    width: 150,
    copyable: true,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    width: 200,
    hideInSearch: true,
    ellipsis: true,
    render(dom, entity) {
      return (
        <a
          className="rk-a-span"
          onClick={() => {
            history.push(`/contract/main/edit/${entity.contractId}`);
          }}
        >
          {dom}
        </a>
      );
    },
  },
  {
    title: '期次',
    dataIndex: 'period',
    width: 70,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: '计划收款金额',
    valueType: 'money',
    dataIndex: 'estimateReAmount',
    width: 130,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: '计划收款日期',
    valueType: 'date',
    dataIndex: 'estimateReTime',
    width: 100,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: '收款状态',
    dataIndex: 'collectionStatus',
    valueEnum: option2enum(COLLECT_STATUS),
    width: 120,
    hideInSearch: true,
  },
];
