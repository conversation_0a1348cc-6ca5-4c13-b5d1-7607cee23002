import Icon from '@/components/Icon';
import { StatisticCard } from '@ant-design/pro-components';

const { Divider } = StatisticCard;
const iconStyles = { fontSize: '28px', filter: 'hue-rotate(282deg) opacity(0.78)' };
const StatisticsCard: React.FC<{
  value?: Record<string, any>;
  onChange?: (val: Record<string, any>) => void;
}> = ({ value = {} }) => {
  const {
    unreceivedMoney,
    uninvoicedMoney,
    receivedMoney,
    invoicedMoney,
    receivedMoneyAll,
    invoicedMoneyAll,
  } = value || {};
  return (
    <>
      <StatisticCard.Group className="rklink-statistic">
        <StatisticCard.Group>
          <StatisticCard
            statistic={{
              title: '已收款总金额',
              prefix: '¥',
              value: receivedMoneyAll || 0,
              icon: <Icon type="icon-money" style={iconStyles} />,
            }}
          />
          <Divider />
          <StatisticCard
            statistic={{
              title: '已收款金额',
              prefix: '¥',
              value: receivedMoney || 0,
              status: 'success',
            }}
          />
          <StatisticCard
            statistic={{
              title: '未收款金额',
              prefix: '¥',
              value: unreceivedMoney || 0,
              status: 'success',
            }}
          />
        </StatisticCard.Group>
        <StatisticCard.Group>
          <StatisticCard
            statistic={{
              title: '已开票总金额',
              prefix: '¥',
              value: invoicedMoneyAll || 0,
              icon: <Icon type="icon-money" style={iconStyles} />,
            }}
          />
          <Divider />
          <StatisticCard
            statistic={{
              title: '已开票金额',
              prefix: '¥',
              value: invoicedMoney || 0,
              status: 'success',
            }}
          />
          <StatisticCard
            statistic={{
              title: '未开票金额',
              prefix: '¥',
              value: uninvoicedMoney || 0,
              status: 'success',
            }}
          />
        </StatisticCard.Group>
      </StatisticCard.Group>
    </>
  );
};

export default StatisticsCard;
