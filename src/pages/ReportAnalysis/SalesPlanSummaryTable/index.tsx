import { PROJECT_BRAND, PROJECT_PROGRESS } from '@/enums';
import { salesPlanAnalysisList } from '@/services/oa/analysis';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProForm,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import React, { useRef, useState } from 'react';
import StatisticsCard from './components/StatisticsCard';

const SalesPlanSummaryTable: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [statistics, setStatistics] = useState<{
    saleAmountCount: number;
    purchaseCostCount: number;
  }>();

  // 表格
  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '客户经理',
      dataIndex: 'clientManger',
      ellipsis: true,
      width: 120,
    },
    {
      title: '客户简称',
      dataIndex: 'clientAbbreviation',
      ellipsis: true,
      width: 120,
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => {
              history.push(`/crm/customer/edit/${entity.partnerId}`);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '项目描述',
      dataIndex: 'projectDescription',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '项目类型',
      dataIndex: 'projectClassify',
      valueType: 'select',
      valueEnum: option2enum(PROJECT_BRAND),
      ellipsis: true,
      width: 120,
    },
    {
      title: '销售金额',
      dataIndex: 'saleAmount',
      valueType: 'money',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '采购成本',
      dataIndex: 'purchaseCost',
      width: 120,
      hideInSearch: true,
      valueType: 'money',
      ellipsis: true,
    },
    {
      title: '计划招标时间',
      dataIndex: 'remark',
      valueType: 'date',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '项目概率',
      dataIndex: 'proProgress',
      valueType: 'select',
      valueEnum: option2enum(PROJECT_PROGRESS),
      ellipsis: true,
      width: 120,
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      {/* 统计卡片 */}
      <ProForm.Item>
        <StatisticsCard value={statistics} />
      </ProForm.Item>
      <ProTable
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        search={{ filterType: 'query', defaultCollapsed: false }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="销售计划汇总表"
        polling={5000}
        request={async (params) => {
          const {
            clientAbbreviation,
            clientManger,
            projectClassify,
            proProgress,
            current,
            pageSize,
          } = params;
          const filter = { projectClassify, proProgress };
          const search = { clientAbbreviation, clientManger };
          const res = await queryPagingTable(
            {
              current,
              pageSize,
              search,
              filter,
            },
            salesPlanAnalysisList,
          );
          setStatistics({
            saleAmountCount: res?.saleAmountCount,
            purchaseCostCount: res?.purchaseCostCount,
          });
          return res;
        }}
      />
    </PageContainer>
  );
};

export default SalesPlanSummaryTable;
