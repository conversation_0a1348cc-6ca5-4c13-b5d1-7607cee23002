import { StatisticCard } from '@ant-design/pro-components';

const StatisticsCard: React.FC<{
  value?: any;
  onChange?: (val: any) => void;
}> = ({ value = {} }) => {
  const { saleAmountCount, purchaseCostCount } = value || {};
  return (
    <StatisticCard.Group className="rklink-statistic">
      <StatisticCard
        statistic={{
          title: '销售合计金额',
          prefix: '¥',
          value: saleAmountCount || 0,
          status: 'success',
        }}
      />
      <StatisticCard
        statistic={{
          title: '采购成本合计金额',
          prefix: '¥',
          value: purchaseCostCount || 0,
          status: 'success',
        }}
      />
    </StatisticCard.Group>
  );
};

export default StatisticsCard;
