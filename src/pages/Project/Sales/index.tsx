import { PlusOutlined } from '@ant-design/icons';
import { Access, useAccess } from '@umijs/max';
import { Button } from 'antd';
import { useState } from 'react';
import BaseProjectList from '../components/BaseProjectList';
import CreateModal from '../components/CreateModal';

//销售项目
const Sales = () => {
  const { canAddSalesProject } = useAccess();
  const [visible, setVisible] = useState(false);
  return (
    <>
      <BaseProjectList
        projectClassify="XS"
        detailPath="/project/sales/edit/"
        headerTitle="销售项目列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddSalesProject || false}>
              <Button type="primary" icon={<PlusOutlined />} onClick={() => setVisible(true)}>
                新建销售项目
              </Button>
            </Access>,
          ],
        }}
      />
      <CreateModal visible={visible} setVisible={setVisible} />
    </>
  );
};

export default Sales;
