import { ProFormDependency, ProFormTextArea } from '@ant-design/pro-components';

//合同备注,内容描述等
const ProTextArea: React.FC<{
  canEditInApproval?: boolean;
  disabledForm?: boolean;
}> = ({ canEditInApproval, disabledForm }) => {
  return (
    <>
      <ProFormDependency name={['projectClassify']}>
        {({ projectClassify }) => {
          if (projectClassify === 'SH') {
            return (
              <ProFormTextArea
                name="proContentDes"
                disabled={disabledForm && !canEditInApproval}
                label="项目内容描述"
                fieldProps={{
                  autoSize: {
                    minRows: 3,
                    maxRows: 9,
                  },
                }}
              />
            );
          }
          // Removed contractRemark field for pre-sales projects
        }}
      </ProFormDependency>
      <ProFormTextArea
        name="proRemark"
        label="项目备注"
        disabled={disabledForm && !canEditInApproval}
        fieldProps={{
          autoSize: {
            minRows: 3,
            maxRows: 6,
          },
        }}
      />
    </>
  );
};

export default ProTextArea;
