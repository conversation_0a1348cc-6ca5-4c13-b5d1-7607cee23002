import { PAYMENT, PAYMENT_TYPE, WRITE_OFF_STATUS } from '@/enums';
import { option2enum } from '@/utils';
import { ProColumns } from '@ant-design/pro-components';

//项目交付明细
export const getProDeliveryColumns = (departmentList: API.DepartmentListResp[]) =>
  [
    {
      title: '执行人',
      dataIndex: 'employeeName',
    },
    {
      title: '部门',
      valueType: 'select',
      dataIndex: 'employeeDepartment',
      fieldProps: {
        options: departmentList.map((item) => ({ value: item.id, label: item.departmentName })),
      },
    },
    {
      title: '开始日期',
      dataIndex: 'startTime',
      valueType: 'date',
    },
    {
      title: '结束日期',
      dataIndex: 'endTime',
      valueType: 'date',
    },
    {
      title: '总工时',
      dataIndex: 'countTime',
    },
  ] as ProColumns<API.WeekInfoResp>[];

//项目情况描述
export const proWayDesColumns: ProColumns<API.ProInfoDesReq>[] = [
  {
    title: '日期',
    dataIndex: 'addTime',
    valueType: 'date',
    width: 200,
    fieldProps: {
      format: 'YYYY-MM-DD',
    },
  },
  {
    title: '项目情况描述',
    dataIndex: 'description',
  },
];

//项目付款明细
export const paymentColumns: ProColumns<API.Payment>[] = [
  {
    title: '付款类型',
    dataIndex: 'paymentType',
    valueType: 'select',
    valueEnum: option2enum(PAYMENT_TYPE),
  },
  {
    title: '付款方式',
    dataIndex: 'payWay',
    valueType: 'select',
    valueEnum: option2enum(PAYMENT),
  },
  {
    title: '付款金额',
    dataIndex: 'payAmount',
    valueType: 'money',
  },
  {
    title: '付款日期',
    dataIndex: 'payTime',
    valueType: 'date',
  },
  {
    title: '销账标识',
    dataIndex: 'writeOffsTag',
    valueEnum: option2enum(WRITE_OFF_STATUS),
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    ellipsis: true,
    width: 150,
  },
];
