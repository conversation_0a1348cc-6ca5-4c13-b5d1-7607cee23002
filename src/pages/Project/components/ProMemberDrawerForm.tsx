import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import RKSelectLabel from '@/components/RKSelectLabel';
import BaseListContext from '@/Context/BaseListContext';
import { getRandomId } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProColumns,
  ProForm,
  ProFormDatePicker,
  ProFormInstance,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import dayjs from 'dayjs';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import React, { useContext, useRef } from 'react';

dayjs.extend(weekday);
dayjs.extend(localeData);

//计算奖金
export const renderBonuses = (
  row: { key_: string; contributionDegree: number },
  projectBonuses: number,
) => {
  const { key_, contributionDegree = 0 } = row || {};
  if (!contributionDegree) return row;
  const bonusAmount = (projectBonuses * contributionDegree) / 100;
  return {
    ...row,
    key_: key_ || getRandomId(),
    bonusAmount: bonusAmount,
    contributionDegree: contributionDegree,
  };
};

const ProMemberDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
  disabled,
}) => {
  const formRef = useRef<ProFormInstance>();
  const isEdit = initialValues?.employeeNumber;
  const {
    departmentList,
    departmentLoading,
    userList = [],
    userLoading,
  } = useContext(BaseListContext);
  const projectBonuses = initialValues?.projectBonuses;
  const projectClassify = initialValues?.projectClassify;

  //表格
  const columns: ProColumns<API.BonusListReq>[] = [
    {
      title: '贡献度',
      dataIndex: 'contributionDegree',
      valueType: 'digit',
      fieldProps: (form, { rowKey }) => ({
        precision: 2,
        addonAfter: '%',
        step: 0.1,
        min: 0,
        max: 100,
        onChange: () => {
          const rowData = form.getFieldValue([rowKey || '']);
          const updateRow = renderBonuses(rowData, projectBonuses);
          form.setFieldValue([rowKey || ''], updateRow);
        },
      }),
    },
    {
      title: '奖金金额',
      dataIndex: 'bonusAmount',
      valueType: 'money',
      fieldProps: {
        min: 0,
        disabled: true,
      },
    },
  ];

  return (
    <DrawerForm<API.ProMemberReq>
      title={isEdit ? '编辑' : '新建'}
      width="auto"
      formRef={formRef}
      open={open}
      onOpenChange={(visible) => {
        onOpenChange?.(visible);
      }}
      submitter={{
        searchConfig: {
          submitText: '确认',
        },
      }}
      disabled={disabled}
      onFinish={async (value) => {
        let totalContributionDegree = 0; //总贡献率
        const { bonusList } = value;
        const list = bonusList?.map((item) => ({
          contributionDegree: item.contributionDegree,
          bonusAmount: (projectBonuses * item.contributionDegree!) / 100,
        }));
        list?.forEach((item) => {
          const { contributionDegree } = item;
          totalContributionDegree += contributionDegree!;
        });
        const values = {
          ...value,
          bonusList: list,
          totalContributionDegree: totalContributionDegree,
          totalBonusAmount: (projectBonuses * totalContributionDegree) / 100,
        };
        // 奖金
        if (projectBonuses) {
          if (totalContributionDegree > 100) {
            message.warning('总贡献度不能大于100%');
          } else {
            onFinish?.(values);
            return true;
          }
        } else {
          onFinish?.(value);
          return true;
        }
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      request={async () => {
        const newList = initialValues?.bonusList?.map((item: any) => ({
          ...item,
          key_: getRandomId(),
        }));
        return { ...initialValues, bonusList: newList };
      }}
      initialValues={{
        projectBonuses: projectBonuses,
        joinTime: dayjs(new Date()).format('YYYY-MM-DD'),
      }}
    >
      <div className="rk-none">
        <ProFormText name="id" />
      </div>
      <ProForm.Group>
        <ProFormSelect
          disabled={isEdit}
          width="md"
          name="employeeName"
          label="员工姓名"
          placeholder="请输入"
          fieldProps={{
            loading: userLoading,
            showSearch: true,
            onChange: (value, option) => {
              const opt = option as {
                label: string;
                value: string;
                employeeNumber: string;
                department: string;
              };
              formRef.current?.setFieldValue('employeeNumber', opt.employeeNumber);
              formRef.current?.setFieldValue('employeeDepartment', opt.department);
              formRef.current?.setFieldValue('status', '1');
            },
            optionLabelProp: 'username',
          }}
          options={userList?.map((item) => ({
            value: item.username,
            label: <RKSelectLabel title={item.username} info={item.employeeNumber} />,
            employeeNumber: item.employeeNumber,
            department: item.department,
            username: item.username,
          }))}
          rules={[requiredRule]}
          transform={(value, namePath) => {
            return {
              [namePath]: value,
              employeeId: userList?.find((item) => item.employeeName === value)?.id,
            };
          }}
        />
        <ProFormText
          width="md"
          name="employeeNumber"
          label="员工编号"
          placeholder="请输入"
          fieldProps={{
            autoComplete: 'none',
          }}
          disabled
          rules={[requiredRule]}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormSelect
          width="md"
          disabled
          name="employeeDepartment"
          label="部门"
          placeholder="请输入"
          rules={[requiredRule]}
          fieldProps={{
            loading: departmentLoading,
            fieldNames: {
              value: 'id',
              label: 'departmentName',
            },
            showSearch: true,
          }}
          options={departmentList as DefaultOptionType[]}
        />
        <ProFormSelect
          disabled
          width="md"
          name="status"
          label="状态"
          placeholder="请输入"
          valueEnum={{ 0: '离开', 1: '正常' }}
          rules={[requiredRule]}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDatePicker
          width="md"
          name="joinTime"
          label="加入日期"
          placeholder="请输入"
          transform={(value, namePath) => {
            return { [namePath]: dayjs(value).format('YYYY-MM-DD') };
          }}
          fieldProps={{
            onChange: () => {
              formRef.current?.setFieldValue('status', '1');
            },
          }}
          rules={[requiredRule]}
        />
        <ProFormDatePicker
          width="md"
          name="outTime"
          label="离开日期"
          placeholder="请输入"
          transform={(name, namePath) => {
            return { [namePath]: dayjs(name).format('YYYY-MM-DD') };
          }}
          fieldProps={{
            onChange: (value) => {
              const joinTime = formRef.current?.getFieldValue('joinTime');
              if (value) {
                formRef.current?.setFieldValue('status', '0');
              }
              if (joinTime && !value) {
                formRef.current?.setFieldValue('status', '1');
              }
            },
          }}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText
          width="md"
          name="duty"
          label="项目职责"
          placeholder="请输入"
          fieldProps={{
            autoComplete: 'none',
          }}
        />
        {projectClassify === 'SH' && projectBonuses ? (
          <ProFormMoney
            disabled
            width="md"
            locale="zh-CN"
            name="projectBonuses"
            label="项目奖金"
            placeholder="请输入"
            fieldProps={{
              precision: 2,
              step: 0.1,
              min: 0,
            }}
          />
        ) : null}
      </ProForm.Group>
      {projectClassify === 'SH' && projectBonuses ? (
        <>
          <ProForm.Item
            name="bonusList"
            getValueProps={(val) => {
              return {
                value: val?.map((item: any) => renderBonuses(item, projectBonuses)),
              };
            }}
          >
            <RKFormEditableProTable columns={columns} headerTitle="奖金分配记录" />
          </ProForm.Item>
        </>
      ) : null}
    </DrawerForm>
  );
};

export default ProMemberDrawerForm;
