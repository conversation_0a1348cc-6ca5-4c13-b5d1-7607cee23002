import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import RKSelectLabel from '@/components/RKSelectLabel';
import { ACCOUNTING_MARK, APPROVAL_STATUS, EXPIRED_STATE, PROJECT_STATUS } from '@/enums';
import { useAvailableMainContractList } from '@/hooks/useAvailableMainContractList';
import { useUserList } from '@/hooks/useUserList';
import { getDepartmentTree } from '@/services/oa/department';
import { deleteProById, openAndClose, pageSearchProject } from '@/services/oa/project';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  ListToolBarProps,
  PageContainer,
  ProColumns,
  ProDescriptions,
  ProTable,
} from '@ant-design/pro-components';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { Badge, Button, Card, message, Modal, Space, Tag } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';

type BaseProjectProps = {
  projectClassify: string;
  detailPath: string;
  headerTitle: string;
  toolbar: ListToolBarProps;
};

//表格公用组件入口
const BaseProject: React.FC<BaseProjectProps> = ({
  projectClassify,
  detailPath,
  headerTitle,
  toolbar,
}) => {
  const tableRef = useRef<ActionType | undefined>();
  const { userList, loading } = useUserList();
  const { contractList, loading: contractLoading } = useAvailableMainContractList();
  const {
    canDeleteInternalProject,
    canDeletePreSalesProject,
    canDeleteAfterSalesProject,
    canDeleteSalesProject,
    canDeleteDevelopProject,
    canReopenInternalProject,
    canReopenPreSalesProject,
    canReopenAfterSalesProject,
    canReopenSalesProject,
    canReopenDevelopProject,
  } = useAccess();
  const deleteMap: Record<string, any> = {
    NB: canDeleteInternalProject,
    SQ: canDeletePreSalesProject,
    SH: canDeleteAfterSalesProject,
    XS: canDeleteSalesProject,
    KF: canDeleteDevelopProject,
  };
  const reopenMap: Record<string, any> = {
    NB: canReopenInternalProject,
    SQ: canReopenPreSalesProject,
    SH: canReopenAfterSalesProject,
    XS: canReopenSalesProject,
    KF: canReopenDevelopProject,
  };
  const [selectedRows, setSelectedRows] = useState<API.ProBaseInfoResp[]>([]);
  const { data: treeData = [] } = useRequest(() => getDepartmentTree());
  const [costEstimate, setCostEstimate] = useState<number>(0);
  const [spend, setSpend] = useState<number>(0);

  const { run: deleteRecord } = useRequest((ids) => deleteProById({ ids: ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: API.ProBaseInfoResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(String(item.id)!);
      names.push(item.projectName!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 重启项目
  const { run: reopen, fetches } = useRequest(
    (id) =>
      openAndClose({
        id,
        status: 'NORMAL',
      }),
    {
      manual: true,
      fetchKey: (res) => {
        return res;
      },
      onSuccess: () => {
        message.success('操作成功！');
        tableRef.current?.reloadAndRest?.();
      },
    },
  );

  // 表格
  const columns: ProColumns<API.ProBaseInfoResp>[] = [
    {
      title: '项目编号',
      dataIndex: 'projectNumber',
      copyable: true,
      ellipsis: true,
      fixed: 'left',
      width: 160,
      render: (dom, entity) => (
        <a className="rk-a-span" onClick={() => history.push(`${detailPath}${entity?.id}`)}>
          {dom}
        </a>
      ),
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
      width: 100,
      render: (dom, entity) => {
        if (entity.activiStatus === '2' && entity.hasAutoActivi === '1')
          return <Badge color="#87d068" text="自动通过" />;
        return dom;
      },
    },
    {
      title: '状态',
      dataIndex: 'expiredState',
      width: 80,
      renderText(text, record) {
        if (!record?.endTime) return '1';
        const expired = dayjs(record.endTime).endOf('day').isBefore();
        return expired ? '1' : '0';
      },
      valueEnum: option2enum(EXPIRED_STATE),
    },

    {
      title: '项目状态',
      dataIndex: 'status',
      valueEnum: option2enum(PROJECT_STATUS),
      width: 100,
    },
    {
      title: '核算标识',
      dataIndex: 'proPerformanceStatus',
      width: 100,
      hideInTable: projectClassify !== 'SQ',
      hideInSearch: projectClassify !== 'SQ',
      valueEnum: option2enum(ACCOUNTING_MARK),
      render: (_, entity) => {
        const { proPerformanceStatus } = entity;
        if (!proPerformanceStatus) return '-';
        const label = ACCOUNTING_MARK.find((item) => item.value === proPerformanceStatus)?.label;
        return proPerformanceStatus === '1' ? (
          <Tag color="orange">{label}</Tag>
        ) : (
          <Tag color="lime">{label}</Tag>
        );
      },
    },
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        loading: contractLoading,
        options: contractList?.map((item: Record<string, any>) => ({
          value: item.contractNumber,
          label: <RKSelectLabel title={item.contractNumber} info={item.contractName} />,
          disabled: item.disabled,
        })),
        optionFilterProp: 'value',
        filterOption: true,
      },
      hideInTable: true,
      hideInSearch: !['XS', 'SH', 'KF'].includes(projectClassify),
    },
    {
      title: '客户名称',
      dataIndex: 'clientName',
      search: false,
      ellipsis: true,
      width: 150,
    },
    {
      title: '销售人员',
      dataIndex: 'salePersonId',
      valueType: 'select',
      fieldProps: {
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        showSearch: true,
        options: userList as DefaultOptionType[],
        filterOption: true,
        optionFilterProp: 'label',
      },
      width: 100,
    },
    {
      title: '项目经理',
      dataIndex: 'projectMangerId',
      valueType: 'select',
      fieldProps: {
        loading,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        showSearch: true,
        options: userList as DefaultOptionType[],
        filterOption: true,
        optionFilterProp: 'label',
      },
      width: 100,
    },
    {
      title: '项目所在地',
      dataIndex: 'projectAddress',
      search: false,
      width: 100,
      ellipsis: true,
    },
    {
      title: '开始日期',
      dataIndex: 'startTime',
      width: 100,
      hideInSearch: projectClassify !== 'NB', // 仅内部项目显示搜索条件
      render: (text) => {
        return dayjs(text as string).format('YYYY-MM-DD');
      },
    },
    {
      title: '执行部门',
      dataIndex: 'executeDepartment',
      valueType: 'select',
      width: 100,
      hideInSearch: projectClassify !== 'NB', // 仅内部项目显示搜索条件
      fieldProps: () => ({
        options: treeData
          ?.filter((item: API.DepartmentTreeResp) => item.status === '1')
          ?.map((item: API.DepartmentTreeResp) => ({
            label: item.departmentName,
            value: item.id,
          })),
        showSearch: true,
        optionFilterProp: 'label',
        filterOption: true,
      }),
    },
    {
      title: '结束日期',
      dataIndex: 'endTime',
      valueType: 'date',
      hideInSearch: true,
      width: 100,
    },

    {
      title: '结束日期',
      dataIndex: 'endTime',
      valueType: 'dateMonth',
      hideInTable: true,
    },

    {
      title: '操作',
      width: 150,
      fixed: 'right',
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        const { activiStatus, projectClassify = '', status } = record;
        return (
          <Space>
            <Access accessible={deleteMap?.[projectClassify] || false}>
              {activiStatus === '0' && (
                <Button
                  type="link"
                  className="inner-table-link"
                  key="delete"
                  onClick={() => handleDelete([record])}
                >
                  删除
                </Button>
              )}
            </Access>
            <Access
              accessible={
                reopenMap?.[projectClassify] && status === 'CLOSED' && activiStatus === '2'
              }
            >
              <Button
                type="link"
                className="inner-table-link"
                key="reopen"
                loading={fetches[record.id!]?.loading}
                onClick={() => reopen(record.id!)}
              >
                重启项目
              </Button>
            </Access>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.ProBaseInfoResp>
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        rowKey="id"
        actionRef={tableRef}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          getCheckboxProps: (record) => ({
            disabled: record?.activiStatus !== '0',
          }),
        }}
        tableExtraRender={
          projectClassify === 'NB'
            ? () => (
                <Card>
                  <ProDescriptions size="small" column={3}>
                    <ProDescriptions.Item valueType="money" label="预估费用汇总">
                      {costEstimate}
                    </ProDescriptions.Item>
                    <ProDescriptions.Item valueType="money" label="当前费用汇总">
                      {spend}
                    </ProDescriptions.Item>
                  </ProDescriptions>
                </Card>
              )
            : undefined
        }
        columns={columns}
        headerTitle={headerTitle}
        toolbar={toolbar}
        polling={5000}
        request={async (params) => {
          const {
            projectNumber,
            contractNumber,
            projectName,
            projectMangerId,
            status,
            activiStatus,
            endTime,
            expiredState,
            salePersonId,
            proPerformanceStatus,
            startTime,
            executeDepartment,
            ...rest
          } = params;
          const search = { projectNumber, projectName, endTime, proPerformanceStatus, startTime };
          const filter = {
            contractNumber,
            projectClassify: projectClassify,
            status,
            activiStatus,
            projectMangerId,
            salePersonId,
            proPerformanceStatus,
            executeDepartment,
          };

          const resp: API.ProjectPageProBaseInfoResp = await queryPagingTable<API.PageReq>(
            {
              filter,
              search,
              scope: expiredState && [
                {
                  name: `TO_DATE(END_TIME, 'YYYY-MM-DD HH24:MI:SS')`,
                  key: expiredState === '0' ? 'ge' : 'lt',
                  val: dayjs().format('YYYY-MM-DD'),
                },
              ],
              ...rest,
            },
            pageSearchProject,
          );
          setCostEstimate(resp?.costEstimate || 0);
          setSpend(resp?.spend || 0);
          return resp;
        }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default BaseProject;
