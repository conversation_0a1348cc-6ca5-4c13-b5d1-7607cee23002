import ChangeLog from '@/components/ChangeLog';
import DocumentUploadModule from '@/components/DocumentUploadModule';
import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import RKPageHeader from '@/components/RKPageHeader';
import { ArrowIcon } from '@/components/TitleLink';
import BaseListContext from '@/Context/BaseListContext';
import { PAYMENT, PAYMENT_TYPE, WRITE_OFF_STATUS } from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useAvailableMainContractList } from '@/hooks/useAvailableMainContractList';
import { useDepartment } from '@/hooks/useDepartment';
import { useUserList } from '@/hooks/useUserList';
import { getDepartmentTree } from '@/services/oa/department';
import { editActivityAfterSalePro } from '@/services/oa/flow';
import {
  closeProject,
  createAfterSalePro,
  createBeforeSalePro,
  createDevelopPro,
  createInnerPro,
  createSalePro,
  editAfterSalePro,
  editBeforeSalePro,
  editDevelopPro,
  editInnerPro,
  editSalePro,
  handedProject,
  selectProjectById,
} from '@/services/oa/project';
import { getRandomId, onSuccessAndGoBack, onSuccessAndRefresh, option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  FooterToolbar,
  ModalForm,
  PageContainer,
  PageHeader,
  ProForm,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { Link, useAccess, useLocation, useModel, useRequest } from '@umijs/max';
import { useCounter } from 'ahooks';
import { Button, message, Select, Space } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import dayjs from 'dayjs';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import ProBasicInfo from './ProBasicInfo';
import ProExpense from './ProExpense';
import ProExpenseTrain from './ProExpenseTrain';
import ProMember from './ProMember';
import { getProDeliveryColumns, proWayDesColumns } from './ProTableColumn';
import ProTextArea from './ProTextArea';

type tableRow = API.ProMemberReq & {
  totalContributionDegree?: number;
  totalBonusAmount?: number;
  isBonuses?: boolean;
  projectBonuses?: number;
  bonusList?: API.BonusListReq[];
};

// 定义路径与项目的映射关系
export const PATH_PROJECT_MAP = {
  '/internal': 'NB',
  '/sales': 'XS',
  '/pre-sales': 'SQ',
  '/after-sales': 'SH',
  '/develop': 'KF',
};

const ProjectForm: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  const [handOverModalVisible, setHandOverModalVisible] = useState(false);
  const [handOverInitialValues, setHandOverInitialValues] = useState<{
    currentManager?: string;
    currentTechnical?: string;
    currentDepartment?: string;
  }>({});
  const [projectData, setProjectData] = useState<API.ProjectResp>();

  //路由跳转携带参数
  const { pathname } = useLocation();

  // 获取项目类型
  const projectClassify = useMemo(() => {
    const matchedPath = Object.keys(PATH_PROJECT_MAP).find((path) => pathname.includes(path));
    return matchedPath ? PATH_PROJECT_MAP[matchedPath as keyof typeof PATH_PROJECT_MAP] : 'UNKNOWN';
  }, [pathname]);

  const formRef = useRef<ProFormInstance>();

  // 根据前置动作获取项目信息
  const { projectInfo, setProjectInfo } = useModel('Project.model');

  useEffect(() => {
    let newProjectInfo: API.ProBaseInfoResp & { clientAbbreviation?: string | undefined } = {
      ...projectInfo,
    };
    if (pathname.includes('/pre-sales/add') && newProjectInfo?.clientAbbreviation) {
      newProjectInfo.projectName = `${dayjs(new Date()).format(
        'YYYY',
      )}-${newProjectInfo.clientAbbreviation!}-售前项目`;
    }

    formRef.current?.setFieldsValue?.(newProjectInfo);
    return () => {
      setProjectInfo({});
    };
  }, [projectInfo, formRef.current]);

  const [dataSource, setDataSource] = useState<API.WeekInfoResp[] | undefined>([]);
  const [filteredDataSource, setFilteredDataSource] = useState<API.WeekInfoResp[] | undefined>([]);
  const [expenseSource, setExpenseSource] = useState<API.ReimbursementResp[] | undefined>([]);
  const [expenseSourceTrain, setExpenseSourceTrain] = useState<
    API.TrainReimbursementResp[] | undefined
  >([]);
  const [paymentSource, setPaymentSource] = useState<API.Payment[] | undefined>([]); //项目付款明细包含该项目所有合同付款以及通用付款保证金等
  const [filteredPaymentSource, setFilteredPaymentSource] = useState<API.Payment[] | undefined>([]);
  const { approvalDetails } = useModel('useApprovalModel');
  const [current, { inc }] = useCounter(0);

  // 审核通过的合同列表
  const { contractList, loading: contractLoading } = useAvailableMainContractList();
  // 部门列表
  const { departmentList, loading: departmentLoading } = useDepartment();
  // 部门树数据 - 用于下拉框
  const { data: treeData = [] } = useRequest(() => getDepartmentTree());
  // 用户列表
  const { userList, loading: userLoading } = useUserList();

  const codeRef = useRef(0);
  // 获取用户基本信息
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  // 初始化项目成员列表，售后项目不添加默认成员
  const memberRef = useRef<tableRow[]>(
    ['SH', 'KF'].includes(projectClassify)
      ? []
      : [
          {
            employeeDepartment: currentUser?.department,
            employeeId: currentUser?.id,
            employeeName: currentUser?.username,
            employeeNumber: currentUser?.employeeNumber,
            joinTime: dayjs(new Date()).format('YYYY-MM-DD'),
            status: '1',
          },
        ],
  );
  // 判断是否为审批页面
  const isApprovalPage = pathname.includes('/approval/');

  const { run: addInnerInfo, loading: addInnerInfoLoading } = useRequest(
    (value) => createInnerPro(value),
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res) => onSuccessAndGoBack(res),
    },
  );
  const { run: addBeforeInfo, loading: addBeforeInfoLoading } = useRequest(
    (value) => createBeforeSalePro(value),
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res) => onSuccessAndGoBack(res),
    },
  );
  const { run: addAfterInfo, loading: addAfterInfoLoading } = useRequest(
    (value) => createAfterSalePro(value),
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res) => onSuccessAndGoBack(res),
    },
  );
  const { run: addSaleInfo, loading: addSaleInfoLoading } = useRequest(
    (value) => createSalePro(value),
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res) => onSuccessAndGoBack(res),
    },
  );
  const { run: addDevelopInfo, loading: addDevelopInfoLoading } = useRequest(
    (value) => createDevelopPro(value),
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res) => onSuccessAndGoBack(res),
    },
  );

  const { run: updateInnerInfo, loading: updateInnerInfoLoading } = useRequest(
    (value) => editInnerPro(value),
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res) => {
        codeRef.current = res.code;
        onSuccessAndRefresh(res, inc);
      },
    },
  );
  const { run: updateBeforeInfo, loading: updateBeforeInfoLoading } = useRequest(
    (value) => editBeforeSalePro(value),
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res) => {
        codeRef.current = res.code;
        onSuccessAndRefresh(res, inc);
      },
    },
  );
  const { run: updateAfterInfo, loading: updateAfterInfoLoading } = useRequest(
    (value) => editAfterSalePro(value),
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res) => {
        codeRef.current = res.code;
        onSuccessAndRefresh(res, inc);
      },
    },
  );
  const { run: updateAfterInfoInApproval, loading: updateAfterInfoInApprovalLoading } = useRequest(
    (value) => editActivityAfterSalePro(value),
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res) => {
        codeRef.current = res.code;
        onSuccessAndRefresh(res, inc);
      },
    },
  );
  const { run: updateSaleInfo, loading: updateSaleInfoLoading } = useRequest(
    (value) => editSalePro(value),
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res) => {
        codeRef.current = res.code;
        onSuccessAndRefresh(res, inc);
      },
    },
  );
  const { run: updateDevelopInfo, loading: updateDevelopInfoLoading } = useRequest(
    (value) => editDevelopPro(value),
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res) => {
        codeRef.current = res.code;
        onSuccessAndRefresh(res, inc);
      },
    },
  );
  // 关闭项目
  const { run: closeProjectRunner, loading: closeProjectLoading } = useRequest(
    () => closeProject({ id }),
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res) => {
        if (res.code === 200) {
          message.success('关闭成功！');
          inc();
        }
      },
    },
  );

  const {
    canEditInternalProject,
    canEditPreSalesProject,
    canEditAfterSalesProject,
    canEditSalesProject,
    canEditDevelopProject,
    canSuperEditInternalProject,
    canSuperEditPreSalesProject,
    canSuperEditAfterSalesProject,
    canSuperEditSalesProject,
    canSuperEditDevelopProject,
    canCloseInternalProject,
    canHandOverInternalProject,
    canHandOverPreSalesProject,
    canHandOverAfterSalesProject,
    canHandOverSalesProject,
    canHandOverDevelopProject,
    canClosePreSalesProject,
    canCloseAfterSalesProject,
    canCloseSalesProject,
    canCloseDevelopProject,
  } = useAccess();

  const editMap: Record<string, any> = {
    NB: canEditInternalProject,
    SQ: canEditPreSalesProject,
    SH: canEditAfterSalesProject,
    XS: canEditSalesProject,
    KF: canEditDevelopProject,
  };
  const superEditMap: Record<string, any> = {
    NB: canSuperEditInternalProject,
    SQ: canSuperEditPreSalesProject,
    SH: canSuperEditAfterSalesProject,
    XS: canSuperEditSalesProject,
    KF: canSuperEditDevelopProject,
  };
  const closeMap: Record<string, any> = {
    NB: canCloseInternalProject,
    SQ: canClosePreSalesProject,
    SH: canCloseAfterSalesProject,
    XS: canCloseSalesProject,
    KF: canCloseDevelopProject,
  };
  const handOverMap: Record<string, any> = {
    NB: canHandOverInternalProject,
    SQ: canHandOverPreSalesProject,
    SH: canHandOverAfterSalesProject,
    XS: canHandOverSalesProject,
    KF: canHandOverDevelopProject,
  };
  /**
   * 是否可编辑
   * 未提交状态，只有编辑权限可编辑
   * 新建状态，只有新建权限可编辑
   * 超管权限或者项目经理可编辑
   */

  const activiStatus = formRef.current?.getFieldValue?.('activiStatus');
  // 项目状态
  const status = formRef.current?.getFieldValue?.('status');

  // 销售经理和项目经理可编辑
  const isProjectManger =
    formRef.current?.getFieldValue?.('projectMangerId') === currentUser?.id ||
    formRef.current?.getFieldValue?.('salePersonId') === currentUser?.id;

  const canEdit =
    (activiStatus !== '1' &&
      editMap[projectClassify] &&
      isEditPage &&
      (isProjectManger || superEditMap[projectClassify])) ||
    !isEditPage;
  const disabledForm = status === 'CLOSED' || !canEdit || isApprovalPage;
  const disabledCloseAccount = !canEdit || isApprovalPage;
  // 审批通过就不允许编辑基础信息
  const isApproved = activiStatus === '2';
  // 在审批页面，技术助理可以编辑一些字段
  const canEditInApproval = isApprovalPage && approvalDetails?.hasUpdate;

  const formatData = useCallback((res: API.ProjectResp) => {
    const { proInfoDesList, proMemberList, proRemark, proBaseInfo } = res || {};
    if (proMemberList) {
      memberRef.current = proMemberList.map((item) => {
        let totalContributionDegree = 0;
        const { bonusList } = item;
        bonusList?.forEach((item1) => {
          const { contributionDegree } = item1;
          totalContributionDegree += contributionDegree!;
        });
        return {
          ...item,
          projectBonuses: proBaseInfo?.projectBonuses,
          projectId: String(item.projectId),
          totalContributionDegree: totalContributionDegree,
          totalBonusAmount: (Number(proBaseInfo?.projectBonuses) * totalContributionDegree) / 100,
        };
      });
    }
    const weekInfoData = res?.weekInfoList?.map((item) => ({ ...item, key_: getRandomId() })) || [];
    const reimbursementData =
      res?.reimbursementList?.map((item) => ({ ...item, key_: getRandomId() })) || [];
    const trainReimburseData =
      res?.trainReimburseList?.map((item) => ({ ...item, key_: getRandomId() })) || [];
    const paymentData = res?.paymentList?.map((item) => ({ ...item, key_: getRandomId() })) || [];

    setDataSource(weekInfoData);
    setFilteredDataSource(weekInfoData);

    setExpenseSource(reimbursementData);

    setExpenseSourceTrain(trainReimburseData);

    setPaymentSource(paymentData);
    setFilteredPaymentSource(paymentData);

    const initValues = {
      ...proBaseInfo,
      autoClose: `${proBaseInfo?.autoClose}`,
      proInfoDesList: proInfoDesList,
      proMemberList: memberRef.current,
      proContentDes: proRemark?.proContentDes,
      contractRemark: proRemark?.contractRemark,
      proRemark: proRemark?.proRemark,
    };
    return initValues;
  }, []);

  useEffect(() => {
    if (isApprovalPage)
      formRef.current?.setFieldsValue?.(formatData(approvalDetails?.fromData as API.ProjectResp));
  }, [isApprovalPage, approvalDetails]);

  const onSave = useCallback(() => {
    return new Promise((resolve) => {
      codeRef.current = 0;
      formRef.current?.submit();
      const checkFormInterval = setInterval(() => {
        formRef.current
          ?.validateFields()
          .then(() => {
            const isFormSubmitted = codeRef.current === 200;
            if (isFormSubmitted) {
              clearInterval(checkFormInterval); // 清除轮询
              resolve(200); // 表单提交成功，resolve Promise
            }
          })
          .catch(() => {
            resolve(500);
            clearInterval(checkFormInterval); // 清除轮询
          });
      }, 1000); // 每秒检查一次表单是否提交成功
    });
  }, []);

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <BaseListContext.Provider
        value={{
          contractList,
          contractLoading,
          departmentList,
          departmentLoading,
          userList,
          userLoading,
        }}
      >
        <ProForm
          formRef={formRef}
          disabled={disabledForm || isApproved}
          submitter={
            !disabledCloseAccount || canEditInApproval
              ? {
                  searchConfig: {
                    resetText: '取消',
                    submitText: '保存',
                  },

                  onReset: () => {
                    history.go(-1);
                  },
                  submitButtonProps: {
                    disabled: activiStatus === '1' && !canEditInApproval,
                    loading:
                      addInnerInfoLoading ||
                      addBeforeInfoLoading ||
                      addAfterInfoLoading ||
                      addSaleInfoLoading ||
                      addDevelopInfoLoading ||
                      updateInnerInfoLoading ||
                      updateBeforeInfoLoading ||
                      updateAfterInfoLoading ||
                      updateAfterInfoInApprovalLoading ||
                      updateSaleInfoLoading ||
                      updateDevelopInfoLoading,
                  },
                  resetButtonProps: {
                    disabled: activiStatus === '1' && !canEditInApproval,
                  },
                  render: (_, doms) => {
                    return <FooterToolbar>{doms}</FooterToolbar>;
                  },
                }
              : false
          }
          onFinish={async (value) => {
            let code;
            const { proInfoDesList, proMemberList, proContentDes, contractRemark, proRemark } =
              value;
            //文本域中的信息
            const remarkInfo = { proContentDes, contractRemark, proRemark };
            //基础信息
            const baseInfo = Object.fromEntries(
              Object.entries(value).filter((item) => !Array.isArray(item[1])),
            );
            const Info = {
              proBaseInfo: baseInfo,
              proInfoDesList,
              proMemberList,
              proRemark: remarkInfo,
            };
            //判断项目类型
            const { projectClassify } = value;
            if (projectClassify === 'NB') {
              const res = isEditPage ? await updateInnerInfo(Info) : await addInnerInfo(Info);
              code = res.code;
            } else if (projectClassify === 'SQ') {
              // 售前修改数据没变
              const res = isEditPage ? await updateBeforeInfo(Info) : await addBeforeInfo(Info);
              code = res.code;
            } else if (projectClassify === 'SH') {
              const res = canEditInApproval
                ? await updateAfterInfoInApproval(Info)
                : isEditPage
                ? await updateAfterInfo(Info)
                : await addAfterInfo(Info);
              code = res.code;
            } else if (projectClassify === 'XS') {
              const res = isEditPage ? await updateSaleInfo(Info) : await addSaleInfo(Info);
              code = res.code;
            } else if (projectClassify === 'KF') {
              // 开发项目使用开发项目的API
              const res = canEditInApproval
                ? await updateAfterInfoInApproval(Info)
                : isEditPage
                ? await updateDevelopInfo(Info)
                : await addDevelopInfo(Info);
              code = res.code;
            }
            const success = code === 200;

            return success;
          }}
          params={{ refresh: current }}
          request={async () => {
            if (!isEditPage || isApprovalPage) return {};
            const res = await selectProjectById({ idReq: { id: id! } });
            const data = res.data || {};
            if (res.code === 200) {
              setProjectData(data);
              return formatData(data);
            }
            return {};
          }}
          onValuesChange={(val: API.ProBaseInfoResp) => {
            //根据合同id回显合同名称=项目名称,销售经理，客户名称
            if (val.contractId) {
              const contract = contractList.find((item) => item.id === val.contractId);
              formRef.current?.setFieldsValue({
                projectName: contract?.contractName,
                salePersonId: contract?.salePersonId,
                clientId: contract?.endUserId,
                clientName: contract?.endUser,
                clientContact: contract?.fpContact,
                clientContactWay: contract?.fpContactWay,
                startTime: contract?.startTime,
                endTime: contract?.endTime,
                projectAddress: contract?.contractAddress,
              });
            }
          }}
        >
          {isEditPage && (
            <>
              {!isApprovalPage && activiStatus === '2' && status !== 'CLOSED' && (
                <PageHeader
                  extra={
                    <Space>
                      {closeMap[projectClassify] && (
                        <Button
                          type="primary"
                          disabled={false}
                          loading={closeProjectLoading}
                          onClick={() => {
                            closeProjectRunner();
                          }}
                        >
                          关闭项目
                        </Button>
                      )}
                      {handOverMap[projectClassify] && (
                        <Button
                          type="primary"
                          disabled={false}
                          onClick={() => {
                            setHandOverInitialValues({
                              currentManager: formRef.current?.getFieldValue('projectMangerId'),
                              currentTechnical:
                                formRef.current?.getFieldValue('projectTechnicalId'),
                              currentDepartment:
                                formRef.current?.getFieldValue('executeDepartment'),
                            });
                            setHandOverModalVisible(true);
                          }}
                        >
                          移交项目
                        </Button>
                      )}
                    </Space>
                  }
                />
              )}
              <ProFormDependency
                name={['activiStatus', 'projectNumber', 'projectClassify', 'hasAutoActivi']}
              >
                {({ activiStatus, projectNumber, projectClassify, hasAutoActivi }) => (
                  <RKPageHeader
                    id={id}
                    status={
                      isApprovalPage
                        ? (approvalDetails?.activiStatus as unknown as string) || '9'
                        : activiStatus
                    }
                    title={projectNumber}
                    approveType={`PROJECT_APPROVAL_${projectClassify}`}
                    onOperationCallback={() => {
                      // 如果提交审核成功，手动改状态，不调用刷新是为了防止页面闪动。
                      if (!isApprovalPage) {
                        formRef.current?.setFieldValue('activiStatus', '1');
                      }
                    }}
                    onSave={onSave}
                    saveDisabled={disabledForm}
                    saveLoading={
                      updateAfterInfoLoading ||
                      updateAfterInfoInApprovalLoading ||
                      updateBeforeInfoLoading ||
                      updateInnerInfoLoading ||
                      updateSaleInfoLoading
                    }
                    hasAutoActivi={hasAutoActivi}
                  />
                )}
              </ProFormDependency>
            </>
          )}
          {/* 不需要展示，只是为了form传值 */}
          <div style={{ display: 'none' }}>
            <ProFormText name="id" label="id" />
            <ProFormText name="projectClassify" label="项目分类" />
            <ProFormText name="clientName" />
            <ProFormText name="clientId" />
          </div>
          <>
            {/* 基础信息 */}

            <ProBasicInfo
              disabledForm={disabledForm || isApproved}
              canEditInApproval={canEditInApproval}
              disabled={disabledCloseAccount}
            />
            {/* 合同备注,项目备注,项目内容描述 */}
            <ProTextArea
              disabledForm={disabledForm || isApproved}
              canEditInApproval={canEditInApproval}
            />
          </>
          {/* 项目情况描述 */}
          <ProForm.Item
            name="proInfoDesList"
            getValueProps={(val) => ({
              value: val?.map((item: Record<string, any>) => ({
                ...item,
                key_: item.key_ || getRandomId(),
              })),
            })}
          >
            <RKFormEditableProTable
              columns={proWayDesColumns}
              headerTitle="项目情况描述"
              copy={false}
              disabled={disabledForm}
              createBtnDisabled={disabledForm}
            />
          </ProForm.Item>
          {/* 项目成员 */}
          <ProFormDependency name={['projectBonuses', 'projectClassify']}>
            {({ projectBonuses, projectClassify }) => {
              const initValue = {
                projectBonuses: projectBonuses,
                projectClassify: projectClassify,
              };
              return (
                <ProMember
                  initValue={initValue}
                  memberList={memberRef.current}
                  onDataSourceChange={(dataSource: tableRow[]) => {
                    formRef.current?.setFieldValue('proMemberList', dataSource);
                  }}
                  disabled={disabledForm}
                />
              );
            }}
          </ProFormDependency>
          {isEditPage && (
            <>
              <div>
                <ProTable<API.WeekInfoResp>
                  className="inner-table"
                  {...defaultTableConfig}
                  rowKey="key_"
                  options={false}
                  columns={getProDeliveryColumns(departmentList)}
                  headerTitle={
                    <div>
                      <div style={{ marginRight: 30 }}>项目交付明细</div>
                      <Space size={50} style={{ fontSize: 14, marginTop: 20 }}>
                        <div>
                          <span style={{ marginRight: '8px' }}>执行部门:</span>
                          <Select
                            disabled={false}
                            style={{ width: 200 }}
                            placeholder="请选择部门"
                            allowClear
                            showSearch
                            optionFilterProp="label"
                            filterOption={true}
                            options={treeData
                              ?.filter((item: API.DepartmentTreeResp) => item.status === '1')
                              ?.map((item: API.DepartmentTreeResp) => ({
                                label: item.departmentName,
                                value: item.id,
                              }))}
                            onChange={(departmentId) => {
                              if (departmentId) {
                                setFilteredDataSource(
                                  dataSource?.filter(
                                    (item) => item.employeeDepartment === departmentId,
                                  ),
                                );
                              } else {
                                setFilteredDataSource(dataSource);
                              }
                            }}
                          />
                        </div>
                        <div>
                          总工作量:
                          {filteredDataSource?.reduce(
                            (sum, item) => sum + (Number(item.countTime) || 0),
                            0,
                          )}
                        </div>
                      </Space>
                    </div>
                  }
                  dataSource={filteredDataSource}
                />
              </div>
              {/* 项目报销明细 */}
              <ProExpense dataSource={expenseSource} />
              {/* 培训报销明细 */}
              {projectClassify === 'NB' && <ProExpenseTrain dataSource={expenseSourceTrain} />}
              <div>
                <ProTable<API.Payment>
                  className="inner-table"
                  {...defaultTableConfig}
                  rowKey="key_"
                  options={false}
                  columns={[
                    {
                      title: '付款编号',
                      dataIndex: 'payNumber',
                      width: 180,
                      render: (_, entity) => {
                        return (
                          <Link to={`/finance/JR/payment-list/${entity.id}`}>
                            {entity?.payNumber}
                          </Link>
                        );
                      },
                    },
                    {
                      title: '付款人',
                      dataIndex: 'applicationUser',
                    },
                    {
                      title: '部门',
                      valueType: 'select',
                      dataIndex: 'department',
                      fieldProps: {
                        options: departmentList.map((item: API.DepartmentListResp) => ({
                          value: item.id,
                          label: item.departmentName,
                        })),
                      },
                    },
                    {
                      title: '付款类型',
                      dataIndex: 'paymentType',
                      valueType: 'select',
                      valueEnum: option2enum(PAYMENT_TYPE),
                    },
                    {
                      title: '付款方式',
                      dataIndex: 'payWay',
                      valueType: 'select',
                      valueEnum: option2enum(PAYMENT),
                    },
                    {
                      title: '付款金额',
                      dataIndex: 'payAmount',
                      valueType: 'money',
                    },
                    {
                      title: '付款日期',
                      dataIndex: 'payTime',
                      valueType: 'date',
                    },
                    {
                      title: '销账标识',
                      dataIndex: 'writeOffsTag',
                      valueEnum: option2enum(WRITE_OFF_STATUS),
                    },
                    {
                      title: '备注',
                      dataIndex: 'remark',
                      ellipsis: true,
                      width: 150,
                    },
                  ]}
                  headerTitle={
                    <div>
                      <div style={{ marginRight: 30 }}>项目付款明细</div>
                      <Space size={50} style={{ fontSize: 14, marginTop: 20 }}>
                        <div>
                          <span style={{ marginRight: '8px' }}>执行部门:</span>
                          <Select
                            disabled={false}
                            style={{ width: 200 }}
                            placeholder="请选择部门"
                            allowClear
                            showSearch
                            optionFilterProp="label"
                            filterOption={true}
                            options={treeData
                              ?.filter((item: API.DepartmentTreeResp) => item.status === '1')
                              ?.map((item: API.DepartmentTreeResp) => ({
                                label: item.departmentName,
                                value: item.id,
                              }))}
                            onChange={(departmentId) => {
                              if (departmentId) {
                                setFilteredPaymentSource(
                                  paymentSource?.filter((item) => item.department === departmentId),
                                );
                              } else {
                                setFilteredPaymentSource(paymentSource);
                              }
                            }}
                          />
                        </div>
                        <div>
                          总付款金额: ¥
                          {filteredPaymentSource
                            ?.filter((item) => item.writeOffsTag !== 'CONVERT') //不能计算销账标识为已转换的项目付款
                            ?.reduce((sum, item) => sum + (Number(item.payAmount) || 0), 0)
                            .toFixed(2)}
                        </div>
                      </Space>
                    </div>
                  }
                  dataSource={filteredPaymentSource}
                />
              </div>
            </>
          )}
          {isEditPage && (
            <ProFormDependency name={['activiStatus', 'projectMangerId']}>
              {({ activiStatus, projectMangerId }) => {
                // 判断当前用户是否为项目经理
                const isProjectManager = projectMangerId === currentUser?.id;

                return (
                  <DocumentUploadModule
                    documentFiles={projectData?.documentFiles}
                    fileType="PROJECT"
                    id={id}
                    showUploadButton={activiStatus === '2'}
                    canUpload={isProjectManager}
                    title="文档上传"
                    onUploadSuccess={() => {
                      // 刷新项目数据
                      selectProjectById({ idReq: { id: id! } }).then((res) => {
                        const data = res.data || {};
                        if (res.code === 200) {
                          setProjectData(data);
                        }
                      });
                    }}
                  />
                );
              }}
            </ProFormDependency>
          )}
          <ChangeLog />
        </ProForm>
        <ModalForm
          width="735px"
          title="移交项目"
          open={handOverModalVisible}
          initialValues={handOverInitialValues}
          onFinish={async (values) => {
            const handOverParams: API.ProHandedReq = {};
            if (values.userId) {
              const username = userList.find((item) => item.id === values.userId)?.username;
              handOverParams.projectMangerId = values.userId;
              handOverParams.projectManger = username;
            }
            if (values.executeDepartment) {
              handOverParams.executeDepartment = values.executeDepartment;
            }
            if (values.projectTechnicalId) {
              handOverParams.projectTechnicalId = values.projectTechnicalId;
            }
            handOverParams.projectId = id;

            const msg = await handedProject(handOverParams);
            const success = msg.code === 200;
            if (success) {
              message.success('操作成功!');
              inc();
            }
            return success;
          }}
          onOpenChange={(visible) => {
            if (!visible) {
              setHandOverInitialValues({}); // 关闭时清空初始值
            }
            setHandOverModalVisible(visible);
          }}
          modalProps={{
            destroyOnClose: true,
          }}
        >
          <div style={{ marginBottom: 24 }}>
            <ProForm.Item label="执行部门" style={{ marginBottom: 0 }}>
              <Space
                align="baseline"
                split={<ArrowIcon style={{ color: 'var(--primary-hover-color)' }} />}
              >
                <ProFormSelect
                  name="currentDepartment"
                  disabled
                  width="md"
                  options={treeData
                    ?.filter((item: API.DepartmentTreeResp) => item.status === '1')
                    ?.map((item: API.DepartmentTreeResp) => ({
                      label: item.departmentName,
                      value: item.id,
                    }))}
                />
                <ProFormSelect
                  name="executeDepartment"
                  width="md"
                  options={treeData
                    ?.filter((item: API.DepartmentTreeResp) => item.status === '1')
                    ?.map((item: API.DepartmentTreeResp) => ({
                      label: item.departmentName,
                      value: item.id,
                    }))}
                  placeholder="请选择新执行部门"
                />
              </Space>
            </ProForm.Item>
          </div>
          <div style={{ marginBottom: 24 }}>
            <ProForm.Item label="项目经理" style={{ marginBottom: 0 }}>
              <Space
                align="baseline"
                split={<ArrowIcon style={{ color: 'var(--primary-hover-color)' }} />}
              >
                <ProFormSelect
                  width="md"
                  name="currentManager"
                  disabled
                  options={userList as DefaultOptionType[]}
                />
                <ProFormSelect
                  name="userId"
                  width="md"
                  fieldProps={{
                    showSearch: true,
                  }}
                  options={userList as DefaultOptionType[]}
                  placeholder="请选择新项目经理"
                />
              </Space>
            </ProForm.Item>
          </div>
          {projectClassify === 'SH' && (
            <div>
              <ProForm.Item label="交付经理" style={{ marginBottom: 0 }}>
                <Space
                  align="baseline"
                  split={<ArrowIcon style={{ color: 'var(--primary-hover-color)' }} />}
                >
                  <ProFormSelect
                    width="md"
                    name="currentTechnical"
                    disabled
                    options={userList as DefaultOptionType[]}
                  />
                  <ProFormSelect
                    name="projectTechnicalId"
                    width="md"
                    fieldProps={{
                      showSearch: true,
                    }}
                    options={userList as DefaultOptionType[]}
                    placeholder="请选择新交付经理"
                  />
                </Space>
              </ProForm.Item>
            </div>
          )}
        </ModalForm>
      </BaseListContext.Provider>
    </PageContainer>
  );
};

export default withRouteEditing(ProjectForm);
