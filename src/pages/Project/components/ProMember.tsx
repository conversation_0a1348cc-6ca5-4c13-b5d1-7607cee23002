import BaseListContext from '@/Context/BaseListContext';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProForm, ProTable } from '@ant-design/pro-components';
import { Button, message, Space } from 'antd';
import { useContext, useEffect, useRef, useState } from 'react';
import ProMemberDrawerForm from './ProMemberDrawerForm';

type tableRow = API.ProMemberReq & {
  totalContributionDegree?: number;
  totalBonusAmount?: number;
  projectClassify?: string;
  projectBonuses?: number;
  bonusList?: API.BonusListReq[];
};
type tableProps = {
  disabled?: boolean;
  initValue: { projectClassify: string; projectBonuses: any };
  memberList: tableRow[];
  onDataSourceChange?: (dataSource: tableRow[]) => void;
};

const ProMember: React.FC<tableProps> = ({
  initValue,
  onDataSourceChange,
  memberList,
  disabled,
}) => {
  const tableRef = useRef<ActionType | undefined>();
  const [initialValues, setInitialValues] = useState<tableRow | undefined>();
  const [drawerVisit, setDrawerVisit] = useState(false);
  const [dataSource, setDataSource] = useState<tableRow[]>([]);
  useEffect(() => {
    setDataSource(memberList);
  }, [memberList]);
  useEffect(() => {
    onDataSourceChange?.(dataSource);
  }, [dataSource]);
  const { departmentList = [] } = useContext(BaseListContext);
  const projectClassify = initValue?.projectClassify;
  const projectBonuses = initValue?.projectBonuses;

  //编辑
  const onEdit = (record: tableRow) => {
    setDrawerVisit(true);
    setInitialValues({
      ...record,
      projectBonuses: projectBonuses,
      projectClassify: projectClassify,
    });
  };

  //项目成员
  const columns: ProColumns<tableRow>[] = [
    {
      title: 'id',
      dataIndex: 'id',
      hideInTable: true,
    },
    {
      title: '编号',
      dataIndex: 'employeeNumber',
    },
    {
      title: '姓名',
      dataIndex: 'employeeName',
    },
    {
      title: '部门',
      valueType: 'select',
      dataIndex: 'employeeDepartment',
      fieldProps: {
        options: departmentList.map((item) => ({ value: item.id, label: item.departmentName })),
      },
    },
    {
      title: '加入日期',
      dataIndex: 'joinTime',
      valueType: 'date',
    },
    {
      title: '离开日期',
      dataIndex: 'outTime',
      valueType: 'date',
    },
    {
      title: '项目职责',
      dataIndex: 'duty',
    },
    {
      title: '总贡献度',
      dataIndex: 'totalContributionDegree',
      valueType: 'percent',
      hideInTable: projectClassify === 'SH' ? false : true,
      fieldProps: {
        precision: 2,
        addonAfter: '%',
        step: 0.1,
        min: 0,
      },
    },
    {
      title: '奖金总金额',
      valueType: 'money',
      dataIndex: 'totalBonusAmount',
      hideInTable: projectClassify === 'SH' ? false : true,
      fieldProps: {
        min: 0,
      },
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      valueType: 'option',
      align: 'center',

      render: (text, record) => {
        return (
          <Space>
            <Button
              type="link"
              className="inner-table-link"
              key="edit"
              onClick={() => onEdit(record)}
              disabled={disabled}
            >
              编辑
            </Button>
            <Button
              type="link"
              key="delete"
              className="inner-table-link"
              disabled={disabled}
              onClick={() => {
                const newDataSource = dataSource?.filter(
                  (item) => item.employeeNumber !== record.employeeNumber,
                );
                setDataSource(newDataSource);
                tableRef.current?.reload();
              }}
            >
              删除
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <ProForm.Item
        name="proMemberList"
        getValueProps={(val) => {
          return {
            val: val?.map((item: tableRow) => ({
              ...item,
              projectBonuses: projectBonuses,
              totalBonusAmount:
                item.totalContributionDegree &&
                (item.totalContributionDegree * Number(projectBonuses)) / 100,
              bonusList: item.bonusList?.map((item) => ({
                ...item,
                bonusAmount:
                  item.contributionDegree &&
                  (item.contributionDegree * Number(projectBonuses)) / 100,
              })),
            })),
          };
        }}
      >
        <ProTable<tableRow>
          {...defaultTableConfig}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            disabled: false,
          }}
          rowKey="employeeId"
          className="inner-table"
          options={false}
          actionRef={tableRef}
          columns={columns}
          headerTitle="项目成员"
          toolbar={{
            actions: [
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                disabled={disabled}
                onClick={() => {
                  setDrawerVisit(true);
                  setInitialValues({
                    projectClassify: projectClassify,
                    projectBonuses: projectBonuses,
                  });
                }}
              >
                添加成员
              </Button>,
            ],
          }}
          dataSource={dataSource}
        />
        <ProMemberDrawerForm
          initialValues={initialValues}
          open={drawerVisit}
          onOpenChange={(visible) => {
            setDrawerVisit(visible);
          }}
          disabled={disabled}
          onFinish={async (values) => {
            const { employeeId } = values;
            const item1 = dataSource.filter((item) => item.employeeId !== employeeId);
            //新建时不能重复
            if (!initialValues?.employeeNumber) {
              const existIds = dataSource.map((item) => item.employeeId);
              if (existIds.includes(employeeId)) {
                message.error('项目已包含该成员，不允许重复添加！');
              } else {
                const data = [...item1, values];
                setDataSource(data);
              }
            } else {
              const data = [...item1, values];
              setDataSource(data);
            }

            tableRef.current?.reload();
          }}
        />
      </ProForm.Item>
    </>
  );
};
export default ProMember;
