import BaseListContext from '@/Context/BaseListContext';
import { getDepartmentTree } from '@/services/oa/department';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Select, Space } from 'antd';
import { useContext, useEffect, useState } from 'react';

const ProExpense: React.FC<{ dataSource?: API.ReimbursementResp[] }> = ({ dataSource }) => {
  const { departmentList = [] } = useContext(BaseListContext);
  const [filteredDataSource, setFilteredDataSource] = useState<API.ReimbursementResp[] | undefined>(
    [],
  );
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');

  // 获取部门树数据
  const { data: treeData = [] } = useRequest(() => getDepartmentTree());

  // 初始化和更新过滤后的数据源
  useEffect(() => {
    setFilteredDataSource(dataSource);
  }, [dataSource]);

  // 计算总报销金额
  const totalReimbursementAmount = filteredDataSource?.reduce(
    (sum, item) => sum + (Number(item.actualMoney) || 0),
    0,
  );

  const columns: ProColumns<API.ReimbursementResp>[] = [
    {
      title: '编号',
      dataIndex: 'finaNumber',
      render: (dom, entity) => (
        <a onClick={() => history.push(`/finance/reimbursement/list/edit/${entity.finaId}`)}>
          {dom}
        </a>
      ),
      width: 150,
    },
    {
      title: '员工姓名',
      dataIndex: 'employeeName',
      width: 120,
    },
    {
      title: '部门',
      valueType: 'select',
      dataIndex: 'employeeDepartment',
      fieldProps: {
        options: departmentList.map((item) => ({ value: item.id, label: item.departmentName })),
      },
      width: 120,
    },
    {
      title: '报销类型',
      dataIndex: 'actualType',
      valueType: 'select',
      valueEnum: { RC: '日常报销', CL: '差旅报销' },
      width: 120,
    },
    {
      title: '报销日期',
      dataIndex: 'time',
      valueType: 'date',
      width: 180,
    },
    {
      title: '报销金额',
      dataIndex: 'actualMoney',
      valueType: 'money',
      width: 180,
    },
    {
      title: '正当理由',
      dataIndex: 'reason',
      width: 250,
    },
  ];

  // 处理部门筛选
  const handleDepartmentChange = (departmentId: string | undefined) => {
    setSelectedDepartment(departmentId || '');
    if (departmentId) {
      setFilteredDataSource(dataSource?.filter((item) => item.employeeDepartment === departmentId));
    } else {
      setFilteredDataSource(dataSource);
    }
  };

  return (
    <>
      <ProTable
        className="inner-table"
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        rowKey="key_"
        options={false}
        columns={columns}
        headerTitle={
          <div>
            <div style={{ marginRight: 30 }}>项目报销明细</div>
            <Space size={50} style={{ fontSize: 14, marginTop: 20 }}>
              <div>
                <span style={{ marginRight: '8px' }}>执行部门:</span>
                <Select
                  disabled={false}
                  style={{ width: 200 }}
                  placeholder="请选择部门"
                  allowClear
                  showSearch
                  optionFilterProp="label"
                  filterOption={true}
                  value={selectedDepartment || undefined}
                  options={treeData
                    ?.filter((item: API.DepartmentTreeResp) => item.status === '1')
                    ?.map((item: API.DepartmentTreeResp) => ({
                      label: item.departmentName,
                      value: item.id,
                    }))}
                  onChange={(departmentId) => handleDepartmentChange(departmentId)}
                />
              </div>
              <div>总报销金额: ¥{totalReimbursementAmount?.toFixed(2)}</div>
            </Space>
          </div>
        }
        dataSource={filteredDataSource}
      />
    </>
  );
};

export default ProExpense;
