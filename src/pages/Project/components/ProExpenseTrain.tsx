import BaseListContext from '@/Context/BaseListContext';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { useContext } from 'react';

const ProExpense: React.FC<{ dataSource?: API.TrainReimbursementResp[] }> = ({ dataSource }) => {
  const { departmentList = [] } = useContext(BaseListContext);

  //项目报销
  const columns: ProColumns<API.TrainReimbursementResp>[] = [
    {
      title: '编号',
      dataIndex: 'trainNumber',
      render: (dom, entity) => (
        <a onClick={() => history.push(`/finance/reimbursement/training?id=${entity.id}`)}>{dom}</a>
      ),
      width: 150,
    },
    {
      title: '员工姓名',
      dataIndex: 'employeeName',
      width: 120,
    },
    {
      title: '部门',
      valueType: 'select',
      dataIndex: 'employeeDepartment',
      fieldProps: {
        options: departmentList.map((item) => ({ value: item.id, label: item.departmentName })),
      },
      width: 120,
    },
    {
      title: '员工编号',
      dataIndex: 'employeeNumber',
      width: 120,
    },
    {
      title: '培训内容',
      dataIndex: 'trainContent',
      width: 120,
    },
    {
      title: '培训费用',
      dataIndex: 'trainSpend',
      valueType: 'money',
      width: 250,
    },
  ];
  return (
    <>
      <ProTable
        className="inner-table"
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        rowKey="key_"
        options={false}
        columns={columns}
        headerTitle="培训报销明细"
        dataSource={dataSource}
      />
    </>
  );
};

export default ProExpense;
