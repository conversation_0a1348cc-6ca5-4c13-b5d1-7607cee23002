import RKCol from '@/components/RKCol';
import RKSelectLabel from '@/components/RKSelectLabel';
import TitleLink from '@/components/TitleLink';
import BaseListContext from '@/Context/BaseListContext';
import { COOPERATION_WAYS, PROJECT_STATUS } from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';

import { usePartnerList } from '@/hooks/usePartnerList';
import { option2enum } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormMoney,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useAccess } from '@umijs/max';
import { Row } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import dayjs from 'dayjs';
import { useContext, useMemo } from 'react';

const ProBasicInfo: React.FC<
  WithRouteEditingProps & {
    disabled?: boolean;
    canEditInApproval?: boolean;
    disabledForm?: boolean;
  }
> = ({ isEditPage, disabled, canEditInApproval, disabledForm }) => {
  const { canClosingAccountingPreSalesProject } = useAccess();
  const {
    contractList = [],
    contractLoading,
    departmentList = [],
    userList = [],
    userLoading,
  } = useContext(BaseListContext);
  const { partnerList, loading: partnerLoading } = usePartnerList();

  const customList = useMemo(
    () => partnerList.filter((item) => item?.partnerType === 'CLIENT'),
    [partnerList],
  );

  return (
    <>
      <Row gutter={24}>
        <ProFormDependency name={['projectClassify', 'contractId']}>
          {({ projectClassify, contractId }) => {
            if (projectClassify === 'SH' || projectClassify === 'XS' || projectClassify === 'KF') {
              return (
                <>
                  <RKCol>
                    <ProFormSelect
                      name="contractId"
                      label={
                        <TitleLink path={contractId && `/contract/main/edit/${contractId}`}>
                          合同编号
                        </TitleLink>
                      }
                      disabled={
                        projectClassify === 'SH' || projectClassify === 'KF' || disabledForm
                      }
                      rules={[requiredRule]}
                      fieldProps={{
                        loading: contractLoading,
                        showSearch: true,
                        optionLabelProp: 'contractNumber',
                        filterOption: (inputValue, option) => {
                          return option?.keywords.indexOf(inputValue) >= 0;
                        },
                      }}
                      options={contractList.map((item: Record<string, any>) => {
                        return {
                          value: item.id,
                          label: (
                            <RKSelectLabel
                              title={item.contractNumber}
                              info={item.contractName}
                              disabled={item.disabled}
                            />
                          ),
                          contractNumber: item.contractNumber,
                          keywords: `${item.contractNumber}${item.contractName}`,
                          disabled: item.disabled,
                        };
                      })}
                      transform={(value, namePath) => {
                        return {
                          [namePath]: value,
                          contractNumber: contractList.find((item) => item.id === value)
                            ?.contractNumber,
                          contractName: contractList.find((item) => item.id === value)
                            ?.contractName,
                        };
                      }}
                    />
                  </RKCol>
                </>
              );
            }
          }}
        </ProFormDependency>
        <RKCol>
          <ProFormTextArea
            name="projectName"
            label="项目名称"
            disabled={disabledForm && !canEditInApproval}
            rules={[requiredRule]}
            fieldProps={{
              autoSize: {
                minRows: 1,
                maxRows: 3,
              },
            }}
          />
        </RKCol>
        <ProFormDependency name={['projectClassify']}>
          {({ projectClassify }) => {
            if (projectClassify !== 'NB') {
              return (
                <RKCol>
                  <ProFormSelect
                    name="projectType"
                    label="项目类型"
                    options={COOPERATION_WAYS}
                    rules={[requiredRule]}
                  />
                </RKCol>
              );
            }
          }}
        </ProFormDependency>
        <ProFormDependency name={['projectClassify', 'contractId', 'clientId', 'clientName']}>
          {({ projectClassify, clientId }) => {
            if (projectClassify === 'SH' || projectClassify === 'XS' || isEditPage) {
              return (
                <RKCol>
                  <ProFormText
                    disabled
                    name="clientName"
                    label={
                      <TitleLink path={clientId && `/crm/customer/edit/${clientId}`}>
                        客户名称
                      </TitleLink>
                    }
                  />
                </RKCol>
              );
            }
            return (
              <RKCol>
                <ProFormSelect
                  name="clientId"
                  disabled
                  label={
                    <TitleLink path={clientId && `/crm/customer/edit/${clientId}`}>
                      客户名称
                    </TitleLink>
                  }
                  rules={[requiredRule]}
                  fieldProps={{
                    loading: partnerLoading,
                    fieldNames: {
                      value: 'id',
                      label: 'clientName',
                    },
                    showSearch: true,
                  }}
                  options={customList as DefaultOptionType[]}
                  transform={(value, namePath) => ({
                    [namePath]: value,
                    clientName: customList?.find((item) => item.id === value)?.clientName ?? value,
                  })}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>
        <ProFormDependency name={['projectClassify']}>
          {({ projectClassify }) => {
            if (projectClassify === 'SH') {
              return (
                <RKCol>
                  <ProFormText disabled name="customerAbbreviation" label="客户简称" />
                </RKCol>
              );
            }
          }}
        </ProFormDependency>
        <ProFormDependency name={['projectClassify']}>
          {({ projectClassify }) => {
            if (projectClassify !== 'NB' && projectClassify !== 'SQ') {
              return (
                <>
                  <RKCol>
                    <ProFormText name="clientContact" label="客户联系人" />
                  </RKCol>
                  <RKCol>
                    <ProFormText name="clientContactWay" label="客户联系方式" />
                  </RKCol>
                </>
              );
            }
          }}
        </ProFormDependency>
        <ProFormDependency name={['projectClassify']}>
          {({ projectClassify }) => {
            return (
              <>
                {projectClassify !== 'SQ' && (
                  <RKCol>
                    <ProFormSelect
                      name="salePersonId"
                      label="销售经理"
                      disabled={projectClassify === 'SH' || disabledForm}
                      fieldProps={{
                        loading: userLoading,
                        fieldNames: {
                          value: 'id',
                          label: 'username',
                        },
                        showSearch: true,
                      }}
                      options={userList as DefaultOptionType[]}
                      rules={[
                        {
                          required: ['XS'].includes(projectClassify),
                          message: '此项为必填项',
                        },
                      ]}
                    />
                  </RKCol>
                )}
                <RKCol>
                  <ProFormSelect
                    name="executeDepartment"
                    label="执行部门"
                    options={departmentList
                      ?.filter((item) => item.parentId === '0')
                      ?.map((item) => ({
                        label: item.departmentName,
                        value: item.id,
                      }))}
                    rules={[requiredRule]}
                  />
                </RKCol>
                <RKCol>
                  <ProFormSelect
                    name="projectMangerId"
                    label="项目经理"
                    fieldProps={{
                      loading: userLoading,
                      fieldNames: {
                        value: 'id',
                        label: 'username',
                      },
                      showSearch: true,
                    }}
                    disabled={
                      (disabledForm && !canEditInApproval) ||
                      (!isEditPage && projectClassify === 'SQ')
                    }
                    allowClear={false}
                    rules={[requiredRule]}
                    options={userList as DefaultOptionType[]}
                  />
                </RKCol>
                {projectClassify === 'SH' && (
                  <RKCol>
                    <ProFormSelect
                      name="projectTechnicalId"
                      label="交付经理"
                      fieldProps={{
                        loading: userLoading,
                        fieldNames: {
                          value: 'id',
                          label: 'username',
                        },
                        showSearch: true,
                      }}
                      disabled={
                        (disabledForm && !canEditInApproval) ||
                        (!isEditPage && projectClassify === 'SQ')
                      }
                      allowClear={false}
                      rules={[requiredRule]}
                      options={userList as DefaultOptionType[]}
                    />
                  </RKCol>
                )}
              </>
            );
          }}
        </ProFormDependency>
        <ProFormDependency name={['projectClassify']}>
          {({ projectClassify }) => {
            if (projectClassify === 'NB') {
              return (
                <RKCol>
                  <ProFormSelect
                    name="autoClose"
                    label="是否自动关闭"
                    valueEnum={{ true: '是', false: '否' }}
                    rules={[requiredRule]}
                  />
                </RKCol>
              );
            }
          }}
        </ProFormDependency>
        <RKCol>
          <ProFormDatePicker
            className="ant-picker"
            name="proCreateTime"
            label="创建日期"
            disabled
          />
        </RKCol>
        <RKCol>
          <ProFormDatePicker
            className="ant-picker"
            name="startTime"
            label="开始日期"
            rules={[requiredRule]}
            transform={(value, namePath) => {
              return {
                [namePath]: dayjs(value).format('YYYY-MM-DD'),
              };
            }}
          />
        </RKCol>
        <RKCol>
          <ProFormDatePicker
            className="ant-picker"
            name="endTime"
            label="结束日期"
            rules={[requiredRule]}
            transform={(value, namePath) => {
              return {
                [namePath]: dayjs(value).format('YYYY-MM-DD'),
              };
            }}
          />
        </RKCol>
        <ProFormDependency name={['projectClassify']}>
          {({ projectClassify }) => {
            if (projectClassify === 'SH') {
              return (
                <RKCol>
                  <ProFormDigit
                    name="projectCycle"
                    label="项目周期"
                    disabled={disabledForm && !canEditInApproval}
                    min={0}
                    fieldProps={{
                      autoComplete: 'none',
                      addonAfter: '天',
                    }}
                  />
                </RKCol>
              );
            }
          }}
        </ProFormDependency>
        <ProFormDependency name={['projectClassify']}>
          {({ projectClassify }) => {
            return (
              <>
                {projectClassify !== 'SQ' && (
                  <RKCol>
                    <ProFormDigit
                      name="estimatedWorkload"
                      label="预计工作量"
                      disabled={disabledForm && !canEditInApproval}
                      min={0}
                      fieldProps={{
                        autoComplete: 'none',
                        addonAfter: projectClassify === 'KF' ? '人月' : '人天',
                      }}
                    />
                  </RKCol>
                )}
                <RKCol>
                  <ProFormDigit
                    disabled
                    name="executedWorkload"
                    label="目前已执行工作量"
                    min={0}
                    fieldProps={{
                      autoComplete: 'none',
                      addonAfter: projectClassify === 'KF' ? '人月' : '人天',
                    }}
                  />
                </RKCol>
              </>
            );
          }}
        </ProFormDependency>
        <RKCol>
          <ProFormText name="projectAddress" label="项目所在地" rules={[requiredRule]} />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name="status"
            label="项目状态"
            valueEnum={option2enum(PROJECT_STATUS)}
            rules={[requiredRule]}
          />
        </RKCol>
        <ProFormDependency name={['projectClassify']}>
          {({ projectClassify }) => {
            if (['SQ', 'SH', 'KF'].includes(projectClassify)) {
              return (
                <>
                  <RKCol>
                    <ProFormMoney
                      name="projectBudget"
                      label="项目预算"
                      min={0}
                      locale="zh-CN"
                      rules={[requiredRule]}
                      fieldProps={{ precision: 2, step: 0.1 }}
                    />
                  </RKCol>
                </>
              );
            }
          }}
        </ProFormDependency>
        <ProFormDependency name={['projectClassify']}>
          {({ projectClassify }) => {
            if (['SH', 'KF'].includes(projectClassify)) {
              return (
                <>
                  <RKCol>
                    <ProFormMoney
                      name="preCost"
                      label="售前成本"
                      min={0}
                      locale="zh-CN"
                      rules={[requiredRule]}
                      fieldProps={{ precision: 2, step: 0.1 }}
                    />
                  </RKCol>
                </>
              );
            }
          }}
        </ProFormDependency>
        <ProFormDependency name={['projectClassify']}>
          {({ projectClassify }) => {
            if (projectClassify !== 'SQ') {
              return (
                <RKCol>
                  <ProFormMoney
                    name="costEstimate"
                    label="成本估算"
                    rules={projectClassify !== 'SH' ? [requiredRule] : []}
                    disabled={(projectClassify === 'SH' || disabledForm) && !canEditInApproval}
                    min={1}
                    locale="zh-CN"
                    fieldProps={{ precision: 2, step: 0.1 }}
                  />
                </RKCol>
              );
            }
          }}
        </ProFormDependency>
        <RKCol>
          <ProFormMoney
            disabled
            name="spend"
            label="目前开销"
            min={0}
            locale="zh-CN"
            fieldProps={{ precision: 2, step: 0.1 }}
            transform={(value, namePath) => {
              return { [namePath]: String(value) };
            }}
          />
        </RKCol>
        <ProFormDependency name={['projectClassify']}>
          {({ projectClassify }) => {
            if (projectClassify !== 'SQ') {
              return (
                <RKCol>
                  <ProFormTextArea
                    name="description"
                    label="项目概述"
                    disabled={disabledForm && !canEditInApproval}
                    rules={[requiredRule]}
                    fieldProps={{
                      autoSize: {
                        minRows: 1,
                        maxRows: 3,
                      },
                    }}
                  />
                </RKCol>
              );
            }
            return null;
          }}
        </ProFormDependency>
        {/* 仅售前项目有该字段，且受按钮权限控制 */}
        <ProFormDependency name={['projectClassify']}>
          {({ projectClassify }) => {
            if (projectClassify === 'SQ') {
              return (
                <>
                  <RKCol>
                    <ProFormMoney
                      name="laborCost"
                      label="人工成本"
                      min={0}
                      locale="zh-CN"
                      disabled={true}
                      fieldProps={{ precision: 2, step: 0.1 }}
                    />
                  </RKCol>
                  <RKCol>
                    <ProFormMoney
                      name="amountCost"
                      label="合计成本"
                      min={0}
                      locale="zh-CN"
                      disabled={true}
                      fieldProps={{ precision: 2, step: 0.1 }}
                    />
                  </RKCol>
                  <RKCol>
                    <ProFormSwitch
                      name="proPerformanceStatus"
                      disabled={disabled || !canClosingAccountingPreSalesProject}
                      label="是否结束核算"
                      getValueFromEvent={(val) => (val ? '1' : '0')}
                      getValueProps={(value) => ({ checked: value === '1' })}
                    />
                  </RKCol>
                </>
              );
            }
          }}
        </ProFormDependency>
        <ProFormDependency name={['projectClassify']}>
          {/* 售后属性 */}
          {({ projectClassify }) => {
            if (projectClassify === 'SH') {
              return (
                <>
                  <RKCol>
                    <ProFormDigit
                      name="projectProgress"
                      label="项目进度"
                      min={0}
                      fieldProps={{
                        autoComplete: 'none',
                        addonAfter: '%',
                      }}
                    />
                  </RKCol>
                </>
              );
            }
          }}
        </ProFormDependency>
        <ProFormDependency name={['projectClassify']}>
          {({ projectClassify }) => {
            if (projectClassify === 'NB') {
              return (
                <RKCol>
                  <ProFormSwitch name="allPersonAv" label="是否全员可见" />
                </RKCol>
              );
            }
          }}
        </ProFormDependency>
      </Row>
    </>
  );
};
export default withRouteEditing(ProBasicInfo);
