import RKSelectLabel from '@/components/RKSelectLabel';
import TitleLink from '@/components/TitleLink';
import { APPROVAL_STATUS, PROJECT_STATUS, PROJECT_TYPE } from '@/enums';
import { useAvailableMainContractList } from '@/hooks/useAvailableMainContractList';
import { usePartnerList } from '@/hooks/usePartnerList';
import { PROJECT_MAP } from '@/pages/CRM/Customer/components/Project';
import { findProjectByContract } from '@/services/oa/contract';
import { getBrieflyProInfo } from '@/services/oa/project';
import { requiredRule } from '@/utils/setting';
import {
  ProFormDependency,
  ProFormSelect,
  ProFormText,
  StepsForm,
} from '@ant-design/pro-components';
import { history, useLocation, useModel, useRequest } from '@umijs/max';
import { Button, message, Modal, Table } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { FC, memo, useEffect, useMemo, useState } from 'react';
import { PATH_PROJECT_MAP } from './ProjectForm';

const columns: ColumnsType<API.BrieflyProInfo> = [
  {
    dataIndex: 'projectNumber',
    title: '项目编号',
    width: 130,
  },
  {
    dataIndex: 'projectName',
    title: '项目名称',
    width: 150,
  },
  {
    dataIndex: 'projectClassify',
    title: '项目类型',
    width: 80,
    render: (value) => {
      const obj = PROJECT_TYPE.find((item) => item.value === value);
      return obj?.label;
    },
  },
  {
    dataIndex: 'projectManger',
    title: '项目经理',
    width: 80,
  },
  {
    dataIndex: 'proCreateTime',
    title: '创建时间',
    width: 100,
    render: (value) => {
      if (value) return dayjs(value).format('YYYY-MM-DD');
    },
  },
  {
    dataIndex: 'status',
    title: '项目状态',
    width: 80,
    render: (value) => {
      const obj = PROJECT_STATUS.find((item) => item.value === value);
      return obj?.label;
    },
  },
  {
    title: '审批状态',
    dataIndex: 'activiStatus',
    width: 80,
    render: (value) => {
      const obj = APPROVAL_STATUS.find((item) => item.value === value);
      return obj?.label;
    },
  },
  {
    dataIndex: 'spend',
    title: '目前开销',
    width: 100,
  },

  {
    dataIndex: 'executedWorkload',
    title: '已执行工作量',
    width: 100,
    render(text) {
      return text ? `${text} 人天` : '-';
    },
  },
];

const CreateModal: FC<{
  visible: boolean;
  setVisible: (val: boolean) => void;
  type?: string; //项目类型
  initialValues?: Record<string, any>;
}> = ({ visible, setVisible, type, initialValues }) => {
  const { pathname } = useLocation();
  const [current, setCurrent] = useState(0);
  // 获取项目类型
  const projectClassify = useMemo(() => {
    if (type) return type;
    const matchedPath = Object.keys(PATH_PROJECT_MAP).find((path) => pathname.includes(path));
    return matchedPath ? PATH_PROJECT_MAP[matchedPath as keyof typeof PATH_PROJECT_MAP] : 'UNKNOWN';
  }, [pathname]);

  // 项目客户只筛选type 为客户的
  const { partnerList, loading: partnerLoading } = usePartnerList();
  const customList = useMemo(
    () => partnerList.filter((item) => item?.partnerType === 'CLIENT'),
    [partnerList],
  );

  // 合同列表
  const { contractList, loading: contractLoading } = useAvailableMainContractList();

  // 根据客户 id 查询项目
  const {
    data: projectListByCustomer,
    loading: projectLoadingByCustomer,
    run: fetchProjectByCustomerId,
  } = useRequest(
    (ids) =>
      getBrieflyProInfo({
        ids,
      }),
    {
      manual: true,
    },
  );

  // 根据合同 id 查询项目
  const {
    data: projectListByContract,
    loading: projectLoadingByContract,
    run: fetchProjectByContractId,
  } = useRequest(
    (contractId) =>
      findProjectByContract({
        id: contractId,
      }),
    {
      manual: true,
    },
  );

  // 如果是 type 是 SQ --售前, (默认赋值，说明是从客户详情页入口创建项目
  useEffect(() => {
    if (type === 'SQ') {
      fetchProjectByCustomerId([initialValues?.id]);
    }
    if (['XS', 'SH', 'KF'].includes(type || '')) {
      fetchProjectByContractId(initialValues?.id);
    }
  }, [initialValues?.id, type]);

  // 获取用户基本信息
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  // 数据流设置项目信息
  const { setProjectInfo } = useModel('Project.model');

  return (
    <StepsForm
      current={current}
      onCurrentChange={setCurrent}
      onFinish={(values) => {
        if (
          values?.projectClassify === 'SQ' &&
          projectListByCustomer?.some(
            (item: API.BrieflyProInfo) =>
              item?.projectClassify === 'SQ' &&
              item?.status === 'NORMAL' &&
              ['1', '2'].includes(item.activiStatus!),
          )
        ) {
          message.error('一个客户仅允许创建一个售前项目！');
          return Promise.resolve(false);
        }

        if (
          values?.projectClassify === 'XS' &&
          projectListByContract?.some(
            (item: API.BrieflyProInfo) =>
              item?.projectClassify === 'XS' &&
              item?.status === 'NORMAL' &&
              ['1', '2'].includes(item.activiStatus!),
          )
        ) {
          message.error('一个主合同仅允许创建一个销售项目！');
          return Promise.resolve(false);
        }

        if (
          values?.projectClassify === 'KF' &&
          projectListByContract?.some(
            (item: API.BrieflyProInfo) =>
              item?.projectClassify === 'KF' &&
              item?.status === 'NORMAL' &&
              ['1', '2'].includes(item.activiStatus!),
          )
        ) {
          message.error('一个主合同仅允许创建一个开发项目！');
          return Promise.resolve(false);
        }
        setProjectInfo(values);

        history.push(
          `/project/${PROJECT_MAP[values?.projectClassify as keyof typeof PROJECT_MAP]}/add`,
        );

        return Promise.resolve(false);
      }}
      submitter={{
        render: (props) => {
          if (props.step === 0) {
            return (
              <Button type="primary" onClick={() => props.onSubmit?.()}>
                下一步
              </Button>
            );
          }
          return [
            <Button key="prev" onClick={() => props.onPre?.()}>
              上一步
            </Button>,
            <Button type="primary" key="submit" onClick={() => props.onSubmit?.()}>
              确认创建
            </Button>,
          ];
        },
      }}
      stepsFormRender={(dom, submitter) => {
        return (
          <Modal
            title="创建项目"
            width={1100}
            open={visible}
            onCancel={() => {
              setVisible(false);
              setCurrent(0);
            }}
            footer={submitter}
            destroyOnClose
          >
            {dom}
          </Modal>
        );
      }}
    >
      <StepsForm.StepForm
        name="step1"
        title={['SH', 'XS', 'KF'].includes(projectClassify) ? '选择合同' : '选择客户'}
        onFinish={() => {
          return Promise.resolve(true);
        }}
      >
        <div style={{ display: 'none' }}>
          <ProFormText name="id" label="id" />
          <ProFormText name="status" initialValue="NORMAL" />
          <ProFormText name="clientId" />
          <ProFormText name="clientName" />
          <ProFormText
            name="proCreateTime"
            label="创建日期"
            initialValue={dayjs(new Date()).format('YYYY-MM-DD')}
          />
          <ProFormText
            name="executeDepartment"
            label="执行部门"
            initialValue={projectClassify === 'SH' ? undefined : currentUser?.department}
          />
          <ProFormText name="projectMangerId" label="项目经理" initialValue={currentUser?.id} />
          <ProFormText label="项目分类" name="projectClassify" initialValue={projectClassify} />
        </div>

        <ProFormDependency name={['projectClassify', 'contractId']}>
          {({ projectClassify, contractId }) => {
            if (['SH', 'XS', 'KF'].includes(projectClassify)) {
              return (
                <ProFormSelect
                  name="contractId"
                  initialValue={initialValues?.id}
                  disabled={['XS', 'SH', 'KF'].includes(type || '')}
                  label={
                    <TitleLink path={contractId && `/contract/main/edit/${contractId}`}>
                      合同编号
                    </TitleLink>
                  }
                  rules={[requiredRule]}
                  fieldProps={{
                    loading: contractLoading,
                    showSearch: true,
                    optionLabelProp: 'contractNumber',
                    filterOption: (inputValue, option) => {
                      return option?.keywords.indexOf(inputValue) >= 0;
                    },
                    onChange: (value) => {
                      if (value) fetchProjectByContractId(value);
                    },
                  }}
                  options={contractList.map((item) => {
                    return {
                      value: item.id,
                      label: (
                        <RKSelectLabel
                          title={item.contractNumber}
                          info={item.contractName}
                          disabled={item.disabled}
                        />
                      ),
                      contractNumber: item.contractNumber,
                      keywords: `${item.contractNumber}${item.contractName}`,
                      disabled: item.disabled,
                    };
                  })}
                  // 根据合同id回显合同名称=项目名称,销售经理，客户名称
                  transform={(value, namePath) => {
                    const contract = contractList.find((item) => item.id === value);
                    return {
                      [namePath]: value,
                      contractNumber: contract?.contractNumber,
                      contractName: contract?.contractName,
                      projectName: contract?.contractName,
                      salePersonId: contract?.salePersonId,
                      clientId: contract?.fpId,
                      clientName: contract?.fpName,
                      clientContact: contract?.fpContact,
                      clientContactWay: contract?.fpContactWay,
                      startTime: contract?.startTime,
                      endTime: contract?.endTime,
                      projectAddress: contract?.contractAddress,
                      customerAbbreviation: contract?.customerAbbreviation,
                    };
                  }}
                />
              );
            }
          }}
        </ProFormDependency>
        <ProFormDependency name={['clientName', 'projectClassify', 'contractId']}>
          {({ clientName, projectClassify, contractId }) => {
            if (['SH', 'XS', 'KF'].includes(projectClassify) && contractId)
              return (
                <ProFormText
                  disabled
                  name="clientName"
                  label="客户名称"
                  fieldProps={{
                    value: clientName,
                  }}
                />
              );
            if (['NB', 'SQ'].includes(projectClassify)) {
              return (
                <ProFormSelect
                  name="clientId"
                  disabled={type === 'SQ'}
                  initialValue={initialValues?.id}
                  label="客户名称"
                  rules={[requiredRule]}
                  fieldProps={{
                    loading: partnerLoading,
                    fieldNames: {
                      value: 'id',
                      label: 'clientName',
                    },
                    showSearch: true,
                    onChange: (value) => {
                      if (value) fetchProjectByCustomerId([value]);
                    },
                  }}
                  options={customList as DefaultOptionType[]}
                  transform={(value, namePath) => ({
                    [namePath]: value,
                    clientName: customList?.find((item) => item.id === value)?.clientName ?? value,
                    clientAbbreviation:
                      customList?.find((item) => item.id === value)?.clientAbbreviation ?? value,
                  })}
                />
              );
            }
          }}
        </ProFormDependency>
      </StepsForm.StepForm>
      <StepsForm.StepForm
        name="step2"
        title={'查询已建项目'}
        style={{
          width: 1050,
        }}
      >
        <Table
          loading={projectLoadingByCustomer || projectLoadingByContract}
          className="inner-table"
          pagination={{
            size: 'small',
            pageSize: 5,
          }}
          size="small"
          dataSource={
            ['SH', 'XS', 'KF'].includes(projectClassify)
              ? projectListByContract
              : projectListByCustomer
          }
          columns={columns}
          rowKey="projectId"
          scroll={{
            x: '100%',
          }}
        />
      </StepsForm.StepForm>
    </StepsForm>
  );
};

export default memo(CreateModal);
