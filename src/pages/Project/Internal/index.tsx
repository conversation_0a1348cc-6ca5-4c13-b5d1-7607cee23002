import { PlusOutlined } from '@ant-design/icons';
import { Access, useAccess } from '@umijs/max';
import { Button } from 'antd';
import { useState } from 'react';
import BaseProjectList from '../components/BaseProjectList';
import CreateModal from '../components/CreateModal';

const Internal = () => {
  const { canAddInternalProject } = useAccess();
  const [visible, setVisible] = useState(false);

  return (
    <>
      <BaseProjectList
        projectClassify="NB"
        detailPath="/project/internal/edit/"
        headerTitle="内部项目列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddInternalProject || false}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setVisible(true);
                }}
              >
                新建内部项目
              </Button>
            </Access>,
          ],
        }}
      />
      <CreateModal visible={visible} setVisible={setVisible} />
    </>
  );
};

export default Internal;
