import { PlusOutlined } from '@ant-design/icons';
import { Access, useAccess } from '@umijs/max';
import { Button } from 'antd';
import { useState } from 'react';
import BaseProjectList from '../components/BaseProjectList';
import CreateModal from '../components/CreateModal';

//售前项目
const PreSales = () => {
  const { canAddPreSalesProject } = useAccess();
  const [visible, setVisible] = useState(false);

  return (
    <>
      <BaseProjectList
        projectClassify="SQ"
        detailPath="/project/pre-sales/edit/"
        headerTitle="售前项目列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddPreSalesProject || false}>
              <Button type="primary" icon={<PlusOutlined />} onClick={() => setVisible(true)}>
                新建售前项目
              </Button>
            </Access>,
          ],
        }}
      />
      <CreateModal visible={visible} setVisible={setVisible} />
    </>
  );
};

export default PreSales;
