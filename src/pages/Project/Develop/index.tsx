import { PlusOutlined } from '@ant-design/icons';
import { Access, useAccess } from '@umijs/max';
import { Button } from 'antd';
import { useState } from 'react';
import BaseProjectList from '../components/BaseProjectList';
import CreateModal from '../components/CreateModal';

// 开发项目
const Develop = () => {
  const { canAddDevelopProject } = useAccess();
  const [visible, setVisible] = useState(false);

  return (
    <>
      <BaseProjectList
        projectClassify="KF"
        detailPath="/project/develop/edit/"
        headerTitle="开发项目列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddDevelopProject || false}>
              <Button type="primary" icon={<PlusOutlined />} onClick={() => setVisible(true)}>
                新建开发项目
              </Button>
            </Access>,
          ],
        }}
      />
      <CreateModal visible={visible} setVisible={setVisible} />
    </>
  );
};

export default Develop;
