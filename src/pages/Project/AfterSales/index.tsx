import { PlusOutlined } from '@ant-design/icons';
import { Access, useAccess } from '@umijs/max';
import { Button } from 'antd';
import { useState } from 'react';
import BaseProjectList from '../components/BaseProjectList';
import CreateModal from '../components/CreateModal';

//售后项目
const Sales = () => {
  const { canAddAfterSalesProject } = useAccess();
  const [visible, setVisible] = useState(false);

  return (
    <>
      <BaseProjectList
        projectClassify="SH"
        detailPath="/project/after-sales/edit/"
        headerTitle="售后项目列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddAfterSalesProject || false}>
              <Button type="primary" icon={<PlusOutlined />} onClick={() => setVisible(true)}>
                新建售后项目
              </Button>
            </Access>,
          ],
        }}
      />
      <CreateModal visible={visible} setVisible={setVisible} />
    </>
  );
};

export default Sales;
