import { employeeTypeDel, getEmploymentTypeList } from '@/services/oa/employmentType';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import React, { useRef, useState } from 'react';
import EmployeeTypeModalForm from './components/EmployeeTypeModalForm';

const EmployeeType: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [modalVisit, setModalVisit] = useState(false);
  const [initialValues, setInitialValues] = useState<API.EmploymentTypeUpdateReq | undefined>();

  const { run: deleteRecord } = useRequest((id) => employeeTypeDel({ id }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (row: API.EmploymentTypeListResp) => {
    const { employeeTypeName, id } = row;

    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${employeeTypeName}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(id);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.EmploymentTypeListResp>[] = [
    {
      title: '员工类型',
      dataIndex: 'employeeTypeName',
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <a
              key="edit"
              onClick={() => {
                setModalVisit(true);
                setInitialValues(record);
              }}
            >
              修改
            </a>
            <a key="delete" onClick={() => handleDelete(record)}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.EmploymentTypeListResp>
        {...defaultTableConfig}
        actionRef={tableRef}
        columns={columns}
        headerTitle="类型列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建类型
            </Button>,
          ],
        }}
        request={async () => {
          const msg = await getEmploymentTypeList();
          return {
            data: msg?.data || [],
            success: true,
          };
        }}
      />
      <EmployeeTypeModalForm
        initialValues={initialValues}
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default EmployeeType;
