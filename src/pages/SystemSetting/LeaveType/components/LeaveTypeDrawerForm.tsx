import { PAID_VACATION_VALUE } from '@/enums';
import { createLeaveType, updateLeaveType } from '@/services/oa/leaveType';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useEffect, useRef } from 'react';

const LeaveTypeDrawerForm: React.FC<DrawerFormProps> = ({
  initialValues,
  open,
  onOpenChange,
  onFinish,
}) => {
  const formRef = useRef<ProFormInstance>();
  const isEdit = initialValues?.id;

  useEffect(() => {
    if (isEdit) formRef.current?.setFieldsValue(initialValues);
  }, [isEdit]);

  return (
    <DrawerForm
      width={460}
      title={isEdit ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const msg = isEdit ? await updateLeaveType(value) : await createLeaveType(value);
        const success = msg.code === 200;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
      submitter={{
        searchConfig: {
          submitText: '保存',
        },
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      initialValues={{ ifPpl: '0' }}
      onValuesChange={(val) => {
        if (val.ifPpl) {
          if (val.ifPpl !== '1') formRef.current?.setFieldValue('wageShare', 0);
        }
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
      </div>
      <ProFormText
        name="leaveTypeName"
        label="休假类型名称"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[
          {
            required: true,
            message: '休假类型名称为必填项',
          },
        ]}
      />
      <ProFormDigit
        name="maxLeavesAllowed"
        label="允许最大分配天数"
        tooltip="休假政策年度最大配额"
        placeholder="请输入"
        min={0}
        max={365}
        fieldProps={{
          autoComplete: 'none',
          addonAfter: '天',
          precision: 0,
        }}
      />
      <ProFormDigit
        name="applicableAfter"
        label="申请休假前最少工作天数"
        placeholder="请输入"
        min={0}
        max={365}
        fieldProps={{
          autoComplete: 'none',
          addonAfter: '天',
          precision: 0,
        }}
      />
      <ProFormDigit
        name="maxContinuousDaysAllowed"
        label="单次最长连续休假天数"
        placeholder="请输入"
        min={0}
        max={365}
        fieldProps={{
          autoComplete: 'none',
          addonAfter: '天',
          precision: 0,
        }}
      />
      <ProFormRadio.Group name="ifPpl" label="是否带薪休假" options={PAID_VACATION_VALUE} />
      <ProFormDependency key="ifPpl" name={['ifPpl']}>
        {({ ifPpl }) => {
          if (ifPpl === '1') {
            return (
              <>
                <ProFormDigit
                  min={0}
                  max={1}
                  name="wageShare"
                  label="休假时工资份额"
                  tooltip="对于休假一天,如果您仍支付日工资的50%,则在该字段中输入0.50"
                  placeholder="请输入"
                  rules={[requiredRule]}
                  fieldProps={{
                    step: 0.1,
                  }}
                />
              </>
            );
          }
          return null;
        }}
      </ProFormDependency>
    </DrawerForm>
  );
};

export default LeaveTypeDrawerForm;
