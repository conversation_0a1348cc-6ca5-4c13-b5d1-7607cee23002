import { leaveTypePage } from '@/services/oa/leaveType';
import { queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Space } from 'antd';
import React, { useRef, useState } from 'react';
import LeaveTypeDrawerForm from './components/LeaveTypeDrawerForm';

const LeaveType: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [modalVisit, setModalVisit] = useState(false);
  const [initialValues, setInitialValues] = useState<API.LeaveTypePageResp | undefined>();

  const onEdit = (row: API.LeaveTypePageResp) => {
    setModalVisit(true);
    setInitialValues(row);
  };

  // 表格
  const columns: ProColumns<API.LeaveTypePageResp>[] = [
    {
      title: '休假类型名称',
      dataIndex: 'leaveTypeName',
      width: 200,
    },
    {
      title: '单次最长连续休假天数',
      dataIndex: 'maxContinuousDaysAllowed',
    },
    {
      title: '允许最大分配天数',
      dataIndex: 'maxLeavesAllowed',
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      align: 'center',
      width: 200,
      render: (text, record) => {
        return (
          <Space>
            <a key="edit" onClick={() => onEdit(record)}>
              修改
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.LeaveTypePageResp>
        {...defaultTableConfig}
        actionRef={tableRef}
        columns={columns}
        headerTitle="休假列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建休假
            </Button>,
          ],
        }}
        request={async (params) => {
          return queryPagingTable<API.PageReq>({ ...params }, leaveTypePage);
        }}
      />
      <LeaveTypeDrawerForm
        initialValues={initialValues}
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default LeaveType;
