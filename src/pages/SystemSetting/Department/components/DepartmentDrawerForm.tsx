import BaseContext from '@/Context/BaseContext';
import { createDepartment, updateDepartment } from '@/services/oa/department';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  ModalFormProps,
  ProFormDependency,
  ProFormInstance,
  ProFormItem,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@ant-design/pro-components';
import { DefaultOptionType } from 'antd/es/select';
import React, { memo, useCallback, useContext, useEffect, useRef } from 'react';
import MembersTable from './MembersTable';

const DepartmentDrawerForm: React.FC<ModalFormProps> = ({ open, onOpenChange, initialValues }) => {
  const formRef = useRef<ProFormInstance>();
  useEffect(() => {
    formRef.current?.setFieldsValue(initialValues);
  }, [initialValues]);

  const { userLoading, userList } = useContext(BaseContext);

  const convertUser = useCallback(
    (arr: string[]) => {
      return arr.map((item) => {
        if (typeof item !== 'string') return item;
        const obj = userList.find((user: API.UserPageResp) => user.id === item);
        return {
          id: obj?.id,
          username: obj?.username,
        };
      });
    },
    [userList],
  );

  return (
    <DrawerForm<
      API.DepartmentInsertReq & API.DepartmentUpdateReq & { isLevel1: boolean; status: string }
    >
      width={660}
      title={initialValues?.id ? '编辑' : '新建'}
      open={open}
      onOpenChange={onOpenChange}
      autoFocusFirstInput
      initialValues={{
        status: '1',
      }}
      formRef={formRef}
      onFinish={async ({
        departmentName,
        id,
        parentId,
        status = '0',
        weekly = '0',
        leaders = [],
        members = [],
        departmentHead = '',
      }) => {
        const params = {
          departmentName,
          id,
          parentId,
          status,
          weekly,
          leaders,
          members,
          departmentHead,
        };
        const res = initialValues?.id
          ? await updateDepartment(params)
          : await createDepartment(params);

        return res.code === 200;
      }}
      drawerProps={{
        destroyOnClose: true,
      }}
    >
      <div className="rk-none">
        <ProFormText name="id" />
      </div>

      <ProFormText name="departmentName" label="部门" rules={[requiredRule]} />
      <ProFormDependency name={['isLevel1']}>
        {({ isLevel1 }) => {
          if (isLevel1)
            return (
              <ProFormSelect
                name="parentId"
                label="上级部门"
                rules={[requiredRule]}
                options={[{ value: '0', label: '四川融科智联科技有限公司' }]}
              />
            );
          return (
            <ProFormSelect
              name="parentId"
              label="上级部门"
              fieldProps={{
                fieldNames: {
                  label: 'label',
                  value: 'key',
                },
              }}
              rules={[requiredRule]}
              options={initialValues?.level1Department || []}
            />
          );
        }}
      </ProFormDependency>

      {initialValues?.parentId === '0' && (
        <ProFormSelect
          name="leaders"
          label="部门经理"
          rules={[requiredRule]}
          fieldProps={{
            loading: userLoading,
            fieldNames: {
              value: 'id',
              label: 'employeeName',
            },
            showSearch: true,
            mode: 'multiple',
          }}
          options={userList as DefaultOptionType[]}
          convertValue={(value = []) => {
            return value?.map((item: any) => item?.id || item);
          }}
          transform={(value, namePath) => ({
            [namePath]: convertUser(value),
          })}
        />
      )}

      {initialValues?.parentId !== '0' && (
        <>
          <ProFormSelect
            name="departmentHead"
            label="部门主管"
            rules={[requiredRule]}
            fieldProps={{
              loading: userLoading,
              fieldNames: {
                value: 'id',
                label: 'employeeName',
              },
              showSearch: true,
            }}
            options={userList as DefaultOptionType[]}
          />
          <ProFormSwitch
            label="设为一级部门"
            name="isLevel1"
            fieldProps={{
              onChange: (val) => {
                if (val) {
                  formRef.current?.setFieldValue('parentId', '0');
                } else {
                  formRef.current?.setFieldValue('parentId', undefined);
                }
              },
            }}
          />
        </>
      )}

      <ProFormSwitch
        label="启用"
        name="status"
        getValueFromEvent={(val) => (val ? '1' : '0')}
        getValueProps={(value) => ({ checked: value === '1' })}
      />
      <ProFormSwitch
        label="是否生成周报"
        name="weekly"
        getValueFromEvent={(val) => (val ? '1' : '0')}
        getValueProps={(value) => ({ checked: value === '1' })}
      />
      <ProFormDependency name={['id']}>
        {({ id }) => {
          if (!id) return null;
          return (
            <ProFormItem name="members">
              <MembersTable />
            </ProFormItem>
          );
        }}
      </ProFormDependency>
    </DrawerForm>
  );
};

export default memo(DepartmentDrawerForm);
