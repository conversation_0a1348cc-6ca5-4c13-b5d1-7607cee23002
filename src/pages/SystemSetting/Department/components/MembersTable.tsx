import BaseContext from '@/Context/BaseContext';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Switch } from 'antd';
import { Key, useContext, useEffect, useState } from 'react';

const MembersTable: React.FC<{
  onChange?: (value: API.Member[]) => void;
  value?: API.Member[];
}> = ({ value, onChange }) => {
  const { userList, userLoading } = useContext(BaseContext);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [syncArr, setSyncArr] = useState<string[]>([]);
  useEffect(() => {
    const keys: string[] = [];
    const syncKeys: string[] = [];
    value?.forEach((item) => {
      keys.push(item.id!);
      if (item.isSync) {
        syncKeys.push(item.id!);
      }
    });
    setSelectedRowKeys(keys);
    setSyncArr(syncKeys);
  }, []);

  useEffect(() => {
    const updateValue = selectedRowKeys.map((item) => ({
      id: item as string,
      isSync: syncArr.some((key) => key === item),
    }));
    onChange?.(updateValue);
  }, [syncArr, selectedRowKeys]);

  // 需要同步的数组
  const columns: ProColumns[] = [
    {
      title: '编号',
      dataIndex: 'employeeNumber',
    },
    {
      title: '姓名',
      dataIndex: 'username',
    },
    {
      title: '设为主部门',
      dataIndex: 'username',
      render: (_, entity) => {
        return (
          <Switch
            defaultChecked={syncArr.some((item) => item === entity.id)}
            onChange={(val) => {
              if (val) {
                setSyncArr((state) => {
                  return [...state, entity.id];
                });
              } else {
                setSyncArr((state) => {
                  return state.filter((item) => item !== entity.id);
                });
              }
            }}
          />
        );
      },
    },
  ];

  return (
    <ProTable
      {...defaultTableConfig}
      className="inner-table"
      scroll={{
        x: '100%',
      }}
      options={{
        reload: false,
        density: false,
        setting: false,
        search: true,
      }}
      loading={userLoading}
      request={async ({ keyword = '' }) => {
        return {
          success: true,
          data: userList?.filter((item: API.UserPageResp) => item.username!.indexOf(keyword) >= 0),
        };
      }}
      columns={columns}
      headerTitle="成员"
      rowSelection={{
        preserveSelectedRowKeys: true,
        selectedRowKeys,
        onChange: (keys) => {
          setSelectedRowKeys(keys);
        },
      }}
    />
  );
};

export default MembersTable;
