import BaseContext from '@/Context/BaseContext';
import { useUserList } from '@/hooks/useUserList';
import { departmentDel, getDepartmentTree } from '@/services/oa/department';
import { defaultTableConfig } from '@/utils/setting';
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { PageContainer, ProColumns, ProDescriptions, ProTable } from '@ant-design/pro-components';
import { Link, useRequest } from '@umijs/max';
import { Button, Divider, Menu, MenuProps, message, Modal, Space } from 'antd';
import { ItemType } from 'rc-menu/lib/interface';
import React, { useMemo, useRef, useState } from 'react';
import DepartmentDrawerForm from './components/DepartmentDrawerForm';
import styles from './index.less';

const statusValueEnum = {
  '0': { text: '禁用', status: 'Error' },
  '1': {
    text: '启用',
    status: 'Success',
  },
};

const Department: React.FC = () => {
  const departmentInfo = useRef<API.DepartmentTreeResp & { level1Department?: ItemType[] }>();
  const [drawerVisit, setDrawerVisit] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  // 部门树
  const { data, refresh } = useRequest(getDepartmentTree);
  const { loading: userLoading, userList } = useUserList(true);

  // 一级部门
  const level1Department = useMemo(() => {
    const level1 =
      (data as API.DepartmentTreeResp[])
        ?.filter((item) => item.parentId === '0')
        .map((item) => ({
          key: item.id!,
          label: item.departmentName!,
        })) || [];

    const key = level1.at(0)?.key;
    if (!selectedKeys.length && key) {
      setSelectedKeys([`${key}`]);
    }
    return level1;
  }, [data]);

  // 菜单选中事件
  const menuSelect: MenuProps['onSelect'] = (e) => {
    setSelectedKeys(e.keyPath);
  };
  // 二级部门
  const level2Department = useMemo(
    () => data?.find((item) => selectedKeys.includes(`${item?.id}`))?.child || [],
    [data, selectedKeys],
  );

  // 一级部门详情
  const level1Info = useMemo(
    () => data?.find((item) => selectedKeys.includes(`${item?.id}`)),
    [data, selectedKeys],
  );

  // 编辑
  const onEdit = async (record: API.DepartmentTreeResp) => {
    departmentInfo.current = {
      ...record,
      level1Department,
    };
    setDrawerVisit(true);
  };

  const { run: deleteRecord } = useRequest((id) => departmentDel({ id }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      refresh();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (row: API.DepartmentTreeResp) => {
    const { departmentName, id, parentId } = row;

    Modal.confirm({
      title: '确认删除',
      content:
        parentId === '0'
          ? `删除一级部门，二级部门也会被删除，您确定要删除“${departmentName}”吗?`
          : `您确定要删除“${departmentName}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(id);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.DepartmentTreeResp>[] = [
    {
      title: '部门',
      dataIndex: 'departmentName',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: statusValueEnum,
      width: 100,
    },
    {
      title: '部门主管',
      dataIndex: 'departmentHead',
      valueType: 'select',
      fieldProps: {
        options: userList,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
      },
      width: 100,
    },
    {
      title: '成员',
      width: 300,
      dataIndex: 'id',
      render(_, entity) {
        return entity?.members?.map((item, index) => (
          <Link key={item.id} to={`/human-resources/employees/edit/${item.id}`}>
            {index === 0 ? '' : '、'}
            {userList?.find((user) => user.id === item.id)?.username}
            {item?.isSync && '(主)'}
          </Link>
        ));
      },
    },
    {
      title: '操作',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <a key="edit" onClick={() => onEdit(record)}>
              修改
            </a>
            <a key="delete" onClick={() => handleDelete(record)}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <BaseContext.Provider value={{ userList, userLoading }}>
        <div className={styles.container}>
          <div className={styles.left}>
            <Menu
              className={styles.menu}
              mode="inline"
              items={level1Department}
              selectedKeys={selectedKeys}
              onSelect={menuSelect}
            />
          </div>
          <Divider type="vertical" className={styles.divider} />
          <div className={styles.right}>
            {level1Info?.id === 'other' ? (
              <ProTable<API.DepartmentTreeResp>
                {...defaultTableConfig}
                options={false}
                dataSource={level1Info.members}
                headerTitle="成员列表"
                columns={[
                  {
                    title: '姓名',
                    dataIndex: 'id',
                    valueType: 'select',
                    fieldProps: {
                      options: userList,
                      fieldNames: {
                        value: 'id',
                        label: 'username',
                      },
                    },
                    render(dom, entity) {
                      return <Link to={`/human-resources/employees/edit/${entity.id}`}>{dom}</Link>;
                    },
                  },
                ]}
              />
            ) : (
              <>
                <div className={styles.header}>
                  <ProDescriptions
                    title={
                      <div>
                        部门信息
                        <Button
                          type="link"
                          icon={<EditOutlined />}
                          onClick={() => {
                            departmentInfo.current = {
                              ...level1Info,
                              level1Department: [{ key: '0', label: '四川融科智联科技有限公司' }],
                            };
                            setDrawerVisit(true);
                          }}
                        ></Button>
                        <Button
                          type="link"
                          icon={<DeleteOutlined />}
                          onClick={() => handleDelete(level1Info as API.DepartmentTreeResp)}
                        ></Button>
                      </div>
                    }
                    column={2}
                  >
                    <ProDescriptions.Item label="名称" dataIndex="departmentName">
                      {level1Info?.departmentName}
                    </ProDescriptions.Item>
                    <ProDescriptions.Item label="上级部门">
                      四川融科智联科技有限公司
                    </ProDescriptions.Item>
                    <ProDescriptions.Item
                      label="状态"
                      dataIndex="status"
                      valueEnum={statusValueEnum}
                    >
                      {level1Info?.status}
                    </ProDescriptions.Item>
                    <ProDescriptions.Item label="部门经理" dataIndex="leaders">
                      {level1Info?.leaders?.map((item) => item?.username).join(',')}
                    </ProDescriptions.Item>
                    <ProDescriptions.Item
                      label="成员"
                      dataIndex="leaders"
                      span={2}
                      className={styles['spe-descriptions']}
                    >
                      {level1Info?.members?.map((item, index) => (
                        <Link key={index} to={`/human-resources/employees/edit/${item.id}`}>
                          {index === 0 ? '' : '、'}
                          {userList?.find((user) => user.id === item.id)?.username}
                        </Link>
                      ))}
                      &ensp;
                      {level1Info?.members?.length ? `等${level1Info?.members?.length}个` : ''}
                    </ProDescriptions.Item>
                  </ProDescriptions>
                </div>
                <ProTable<API.DepartmentTreeResp>
                  {...defaultTableConfig}
                  scroll={{
                    x: '100%',
                  }}
                  options={false}
                  dataSource={level2Department}
                  columns={columns}
                  headerTitle="子部门"
                  toolbar={{
                    actions: [
                      <Button
                        key="add"
                        type="primary"
                        icon={<PlusOutlined />}
                        // @ts-ignore
                        disabled={!level1Info?.status}
                        onClick={() => {
                          departmentInfo.current = {
                            level1Department,
                            parentId: level1Info?.id,
                          };

                          setDrawerVisit(true);
                        }}
                      >
                        新建部门
                      </Button>,
                    ],
                  }}
                />
              </>
            )}
          </div>
        </div>
        {/*  新建、编辑部门 */}
        <DepartmentDrawerForm
          open={drawerVisit}
          onOpenChange={(visible) => {
            setDrawerVisit(visible);
            if (!visible) {
              refresh();
            }
          }}
          initialValues={departmentInfo.current}
        />
      </BaseContext.Provider>
    </PageContainer>
  );
};

export default Department;
