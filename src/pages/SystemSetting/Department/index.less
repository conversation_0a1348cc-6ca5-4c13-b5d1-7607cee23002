.container {
  display: flex;
  height: calc(100vh - 194px);
  min-height: 500px;

  background-color: #fff;
  .left,
  .right,
  .divider {
    height: 100%;
    overflow: auto;
  }

  .left {
    width: 200px;
    overflow: auto;
    .menu {
      border-inline-end: none !important;
    }
  }
  .right {
    flex: 1;
    .header {
      padding: 16px 24px;
      .spe-descriptions {
        :global {
          .ant-descriptions-item-content {
            display: block;
          }
        }
      }
    }
  }
}
