import BaseContext from '@/Context/BaseContext';
import { useDepartment } from '@/hooks/useDepartment';
import { positionDel, positionList } from '@/services/oa/position';
import { groupedData } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import React, { useRef, useState } from 'react';
import PositionDrawerForm from './components/PositionDrawerForm';

const Position: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [modalVisit, setModalVisit] = useState(false);
  const [initialValues, setInitialValues] = useState<API.PositionListResp | undefined>();

  const groupDataRef = useRef<Record<string, API.PositionListResp[]>>();

  const { departmentList, loading: departmentLoading } = useDepartment();

  const onEdit = (record: API.PositionListResp) => {
    setModalVisit(true);
    setInitialValues(record);
  };

  const { run: deleteRecord } = useRequest((id) => positionDel({ id }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (row: API.PositionListResp) => {
    const { positionName, id } = row;

    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${positionName}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(id);
      },
    });
  };

  const expandedRowRender = (record: API.PositionListResp) => {
    const data = groupDataRef.current?.[record.departmentId!];
    return (
      <ProTable
        size="small"
        scroll={{ x: '100%' }}
        columns={[
          {
            title: '职位名称',
            dataIndex: 'positionName',
          },
          {
            title: '操作',
            key: 'option',
            valueType: 'option',
            align: 'center',
            width: 200,
            render: (text, record) => {
              return (
                <Space>
                  <a key="edit" onClick={() => onEdit(record)}>
                    修改
                  </a>
                  <a key="delete" onClick={() => handleDelete(record)}>
                    删除
                  </a>
                </Space>
              );
            },
          },
        ]}
        headerTitle={false}
        search={false}
        options={false}
        dataSource={data}
        pagination={false}
        rowKey="id"
      />
    );
  };

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.PositionListResp>
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={[
          {
            title: '所属一级部门',
            dataIndex: 'departmentId',
            width: '99%',
            ellipsis: true,
            valueType: 'select',
            fieldProps: {
              options: departmentList?.filter((item) => item.parentId === '0') || [],
              loading: departmentLoading,
              fieldNames: {
                label: 'departmentName',
                value: 'id',
              },
            },
          },
        ]}
        headerTitle="职位列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建职位
            </Button>,
          ],
        }}
        expandable={{ expandedRowRender }}
        request={async () => {
          const msg = await positionList();
          const groupData = groupedData(msg?.data || [], 'departmentId');
          groupDataRef.current = groupData;

          return {
            data: Object.keys(groupData).map((item) => ({
              id: item,
              departmentId: item === 'null' ? '未知' : item,
            })),
            success: true,
          };
        }}
      />
      <BaseContext.Provider
        value={{
          departmentList,
          departmentLoading,
        }}
      >
        <PositionDrawerForm
          initialValues={initialValues}
          open={modalVisit}
          onOpenChange={(visible) => {
            setModalVisit(visible);
          }}
          onFinish={async () => {
            tableRef.current?.reload();
          }}
        />
      </BaseContext.Provider>
    </PageContainer>
  );
};

export default Position;
