import BaseContext from '@/Context/BaseContext';
import { createPosition, updatePosition } from '@/services/oa/position';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import React, { useContext, useRef } from 'react';

const PositionDrawerForm: React.FC<DrawerFormProps> = ({
  initialValues,
  open,
  onOpenChange,
  onFinish,
}) => {
  const formRef = useRef<ProFormInstance>();
  const isEdit = initialValues?.id;
  const { departmentList, departmentLoading } = useContext(BaseContext);

  return (
    <DrawerForm
      width={460}
      title={isEdit ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const msg = isEdit ? await updatePosition(value) : await createPosition(value);
        const success = msg.code === 200;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
      submitter={{
        searchConfig: {
          submitText: '保存',
        },
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      initialValues={initialValues}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
      </div>
      <ProFormText
        name="positionName"
        label="职位名称"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[
          {
            required: true,
            message: '职位名称为必填项',
          },
        ]}
      />
      <ProFormSelect
        name="departmentId"
        label="所属一级部门"
        rules={[requiredRule]}
        fieldProps={{
          allowClear: false,
          loading: departmentLoading,
          fieldNames: {
            value: 'id',
            label: 'departmentName',
          },
          showSearch: true,
        }}
        options={
          departmentList?.filter(
            (item: API.DepartmentListResp) => item.parentId === '0',
          ) as DefaultOptionType[]
        }
      />
    </DrawerForm>
  );
};

export default PositionDrawerForm;
