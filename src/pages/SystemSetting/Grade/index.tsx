import { gradeDel, gradeList } from '@/services/oa/grade';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import GradeDrawerForm from './components/GradeDrawerForm';

const Grade: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [Id, setId] = useState<string>();
  const [modalVisit, setModalVisit] = useState(false);
  const [subsidyTypes, setSubsidyTypes] = useState<string[]>([]);

  const [gradeData, setGradeData] = useState<API.EmployeeGradeListResp[]>([]);

  // 获取福利等级数据
  const fetchGradeData = async () => {
    const msg = await gradeList();
    if (msg?.data) {
      // 处理数据，为每个等级创建补贴类型映射
      const processedData = msg.data.map((grade: API.EmployeeGradeListResp) => {
        const subsidyMap: Record<string, string> = {};
        grade.subsidyForms?.forEach((subsidy) => {
          subsidyMap[subsidy.subsidyType!] = subsidy.subsidyMoney!;
        });
        return {
          ...grade,
          subsidyMap,
        };
      });

      setGradeData(processedData);
      // 提取所有补贴类型
      const types = new Set<string>();
      msg.data.forEach((grade: API.EmployeeGradeListResp) => {
        grade.subsidyForms?.forEach((subsidy) => {
          if (subsidy.subsidyType) {
            types.add(subsidy.subsidyType);
          }
        });
      });
      setSubsidyTypes(Array.from(types));
    }
    return {
      data: msg?.data || [],
      success: true,
    };
  };

  useEffect(() => {
    fetchGradeData();
  }, []);

  const onEdit = async (record: API.EmployeeGradeListResp) => {
    const editId = String(record.id);
    setId(editId);
    setModalVisit(true);
  };

  useEffect(() => {
    if (!modalVisit) {
      fetchGradeData();
    }
  }, [modalVisit]);

  const { run: deleteRecord } = useRequest((id) => gradeDel({ id }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      fetchGradeData();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (row: API.EmployeeGradeListResp) => {
    const { grade, id } = row;

    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${grade}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(id);
      },
    });
  };

  // 动态生成列配置
  const generateColumns = (): ProColumns<API.EmployeeGradeListResp>[] => {
    const baseColumns: ProColumns<API.EmployeeGradeListResp>[] = [
      {
        title: '福利等级',
        dataIndex: 'grade',
        fixed: 'left',
        width: 100,
      },
      {
        title: '补贴总金额',
        dataIndex: 'count',
        width: 100,
        valueType: 'money',
        fieldProps: {
          precision: 0,
        },
      },
    ];

    // 动态添加补贴类型列
    const dynamicColumns = subsidyTypes.map((type) => ({
      title: type,
      dataIndex: ['subsidyMap', type],
      width: 100,
      valueType: 'money' as const,
      fieldProps: {
        precision: 0,
      },
    }));

    const operationColumn: ProColumns<API.EmployeeGradeListResp> = {
      title: '操作',
      width: 120,
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <a key="edit" onClick={() => onEdit(record)}>
              修改
            </a>
            <a key="delete" onClick={() => handleDelete(record)}>
              删除
            </a>
          </Space>
        );
      },
    };

    return [...baseColumns, ...dynamicColumns, operationColumn];
  };

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.EmployeeGradeListResp>
        {...defaultTableConfig}
        options={false}
        actionRef={tableRef}
        columns={generateColumns()}
        headerTitle="福利列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setId(undefined);
                setModalVisit(true);
              }}
            >
              新建福利
            </Button>,
          ],
        }}
        dataSource={gradeData}
      />
      <GradeDrawerForm
        id={Id}
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default Grade;
