import { createGrade, gradeInfo, updateGrade } from '@/services/oa/grade';
import { getRandomId } from '@/utils';
import {
  ActionType,
  DrawerForm,
  DrawerFormProps,
  EditableFormInstance,
  EditableProTable,
  ProColumns,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { message, Table, Typography } from 'antd';
import { cloneDeep } from 'lodash';
import React, { useRef, useState } from 'react';

const { Text } = Typography;

const GradeDrawerForm: React.FC<DrawerFormProps> = ({ open, onOpenChange, id, onFinish }) => {
  const isEdit = !!id;
  const formRef = useRef<ProFormInstance>();
  const tableRef = useRef<ActionType | undefined>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);
  const [dataSource, setDataSource] = useState<
    readonly {
      key: string;
      subsidyType?: string;
      subsidyMoney?: string;
    }[]
  >([]);
  const editableFormRef = useRef<EditableFormInstance>();

  const columns: ProColumns<API.SubsidyForm & { key: string }>[] = [
    {
      title: '补贴类型',
      dataIndex: 'subsidyType',
    },
    {
      title: '补贴金额',
      dataIndex: 'subsidyMoney',
      valueType: 'money',
      fieldProps: {
        min: 0,
        precision: 0,
        locale: 'zh-CN',
      },
    },
    {
      title: '操作',
      width: 60,
      valueType: 'option',
      align: 'center',
      render: () => {
        return null;
      },
    },
  ];

  return (
    <DrawerForm<API.EmployeeGradeResp & { id: string }>
      width={460}
      title={isEdit ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={(value) => {
        setDataSource([]);
        onOpenChange?.(value);
      }}
      onFinish={async (value) => {
        const { grade } = value;
        const subsidyForms = cloneDeep(dataSource);
        let count = 0;
        subsidyForms?.forEach(({ subsidyMoney }) => {
          count += Number(subsidyMoney);
        });
        const subsidyFormList = subsidyForms?.map((item) => ({
          subsidyType: item.subsidyType,
          subsidyMoney: item.subsidyMoney?.toString(),
        }));
        const formData = {
          subsidyForms: subsidyFormList,
          count: String(count),
          grade: grade!,
        };
        const msg = isEdit
          ? await updateGrade({ ...formData, id: id })
          : await createGrade(formData);
        const success = msg.code === 200;
        if (success) {
          message.success('操作成功!');
          onFinish?.(formData);
        }
        return success;
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      submitter={{
        searchConfig: {
          submitText: '保存',
        },
      }}
      request={async () => {
        if (!isEdit) return { id: '', grade: '', count: '', subsidyForms: [] };
        const res = await gradeInfo({ req: { id: id! } });
        const data = res.data || {};
        if (res.code === 200) {
          const { subsidyForms } = data;
          const list = subsidyForms?.map((item) => ({
            ...item,
            key: getRandomId(),
          }));
          const keys = list?.map((item) => item.key) as React.Key[];
          setEditableRowKeys(keys);
          setDataSource(list!);
          return {
            id: id,
            grade: data.grade,
            count: data.count,
            subsidyForms: list,
          };
        }
        return { id: '', grade: '', count: '', subsidyForms: [] };
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
        <ProFormText name="count" label="补贴总金额" />
      </div>
      <ProFormText
        name="grade"
        label="福利等级"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[
          {
            required: true,
            message: '福利等级为必填项',
          },
        ]}
      />

      <EditableProTable<{
        key: string;
        subsidyType?: string;
        subsidyMoney?: string;
      }>
        rowKey="key"
        editableFormRef={editableFormRef}
        actionRef={tableRef}
        size="small"
        headerTitle="补贴构成"
        columns={columns}
        value={dataSource}
        onChange={setDataSource}
        recordCreatorProps={{
          newRecordType: 'dataSource',
          record: () => ({ key: getRandomId() }),
        }}
        editable={{
          type: 'multiple',
          editableKeys,
          actionRender: (row) => {
            return [
              <a
                key="delete"
                onClick={() => {
                  setDataSource(dataSource?.filter((item) => item.key !== row.key));
                }}
              >
                删除
              </a>,
            ];
          },
          onValuesChange: (record, recordList) => {
            setDataSource(recordList);
          },
          onChange: setEditableRowKeys,
        }}
        summary={(data) => {
          const dataList = data.filter((item) => item.subsidyMoney);
          let totalMony = 0;
          dataList.forEach(({ subsidyMoney }) => {
            totalMony += Number(subsidyMoney);
          });
          return (
            <>
              <Table.Summary.Row>
                <Table.Summary.Cell index={0}>总金额</Table.Summary.Cell>
                <Table.Summary.Cell index={1} colSpan={2}>
                  <Text style={{ color: 'var(--primary-color)' }}>{totalMony}</Text>
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </>
          );
        }}
      />
    </DrawerForm>
  );
};

export default GradeDrawerForm;
