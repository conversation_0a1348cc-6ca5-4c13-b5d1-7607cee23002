import { getProTypeRespList } from '@/services/oa/project';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef } from 'react';
type proType = { type?: string; serialNumberRule?: string; abbreviation?: string };

const ProjectType: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();

  // 表格
  const columns: ProColumns<proType>[] = [
    {
      title: '项目类型',
      dataIndex: 'type',
      width: 200,
    },
    {
      title: '编号规则',
      dataIndex: 'serialNumberRule',
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<proType>
        {...defaultTableConfig}
        rowKey="abbreviation"
        actionRef={tableRef}
        columns={columns}
        headerTitle="项目类型列表"
        request={async () => {
          const res = await getProTypeRespList();
          return {
            data: res?.data || [],
            success: true,
          };
        }}
      />
    </PageContainer>
  );
};

export default ProjectType;
