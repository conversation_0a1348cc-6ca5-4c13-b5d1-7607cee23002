import { holidayInfo, updateHoliday } from '@/services/oa/holiday';
import { getRandomId } from '@/utils';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  DrawerForm,
  DrawerFormProps,
  EditableFormInstance,
  EditableProTable,
  ProColumns,
  ProFormDatePicker,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, message } from 'antd';
import dayjs from 'dayjs';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import { cloneDeep } from 'lodash';
import React, { useRef, useState } from 'react';

dayjs.extend(weekday);
dayjs.extend(localeData);

const HolidayListDrawerForm: React.FC<DrawerFormProps> = ({ open, onOpenChange, id, onFinish }) => {
  const isEdit = !!id;
  const formRef = useRef<ProFormInstance>();
  const tableRef = useRef<ActionType | undefined>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<readonly API.HolidayDetailsListResp[]>([]);
  const [pageInfo, setPageInfo] = useState<{
    current: number;
    pageSize: number;
  }>({
    pageSize: 5,
    current: 1,
  });

  const editableFormRef = useRef<EditableFormInstance>();
  //表格
  const columns: ProColumns<API.HolidayDetailsListResp>[] = [
    {
      title: '日期',
      dataIndex: 'holidays',
      width: 150,
      valueType: 'date',
      fieldProps: {
        format: 'MM/DD',
        picker: 'week',
      },
      formItemProps: {
        rules: [{ required: true, message: '日期为必填项' }],
      },
    },
    {
      title: '描述',
      dataIndex: 'describe',
      valueType: 'textarea',
      fieldProps: {
        autoSize: {
          minRows: 1,
          maxRows: 2,
        },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 60,
      render: () => {
        return null;
      },
    },
  ];

  return (
    <DrawerForm<API.HolidayInfoResp>
      title="编辑"
      width={460}
      formRef={formRef}
      open={open}
      onOpenChange={(visible) => {
        onOpenChange?.(visible);
      }}
      submitter={{
        searchConfig: {
          submitText: '保存',
        },
      }}
      onFinish={async (value) => {
        const list = cloneDeep(dataSource);

        list.forEach((item) => {
          delete item.id;
          const holiday = item.holidays;
          if (holiday === '' || holiday === null || holiday === undefined) return;
          if (holiday.length < 6) {
            item.holidays = `${dayjs().format('YYYY')}-${dayjs(holiday).format('MM-DD')}`;
          }
        });

        const formData = {
          ...value,
          holidayDetailsRespList: list,
        };
        const msg = await updateHoliday(formData as API.HolidayUpdateReq);
        const success = msg.code === 200;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      request={async () => {
        const res = await holidayInfo({ req: { id: id! } });
        const data = res.data || {};
        if (res.code === 200) {
          const { holidayDetailsRespList } = data;
          const keys = holidayDetailsRespList?.map((item) => item.id) as React.Key[];
          setEditableRowKeys(keys);
          setDataSource(holidayDetailsRespList!);
          return data;
        }
        return {};
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="id" placeholder="请输入" />
        <ProFormText name="status" placeholder="请输入" />
      </div>
      <ProFormText
        disabled={isEdit}
        name="holidayListName"
        label="假期表名称"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[
          {
            required: true,
            message: '假期表名称为必填项',
          },
        ]}
      />

      <ProFormDatePicker
        disabled={isEdit}
        name="startDate"
        label="开始时间"
        fieldProps={{
          format: 'MM/DD',
        }}
        placeholder="请输入"
        rules={[
          {
            required: true,
            message: '开始时间为必填项',
          },
        ]}
      />
      <ProFormDatePicker
        disabled={isEdit}
        name="endDate"
        label="结束时间"
        fieldProps={{
          format: 'MM/DD',
        }}
        placeholder="请输入"
        rules={[
          {
            required: true,
            message: '结束时间为必填项',
          },
        ]}
      />

      <EditableProTable<API.HolidayDetailsListResp>
        className="inner-table"
        pagination={{
          ...pageInfo,
          onChange(current, pageSize) {
            setPageInfo({ current, pageSize });
          },
          simple: true,
        }}
        rowKey="id"
        editableFormRef={editableFormRef}
        size="small"
        headerTitle="假期"
        actionRef={tableRef}
        columns={columns}
        toolBarRender={() => {
          return [
            <>
              <Button
                type="primary"
                onClick={() => {
                  const id = getRandomId();
                  tableRef.current?.addEditRecord?.(
                    { id: id, describe: '' },
                    { newRecordType: 'dataSource' },
                  );

                  setPageInfo((prevState) => {
                    return {
                      ...prevState,
                      current: Math.ceil(dataSource.length + 1 / prevState.pageSize),
                    };
                  });
                }}
                icon={<PlusOutlined />}
              >
                新建一行
              </Button>
            </>,
          ];
        }}
        value={dataSource}
        onChange={setDataSource}
        recordCreatorProps={false}
        editable={{
          type: 'multiple',
          editableKeys,
          actionRender: (row) => {
            return [
              <a
                key="delete"
                onClick={() => {
                  setDataSource(dataSource?.filter((item) => item.id !== row.id));
                }}
              >
                删除
              </a>,
            ];
          },
          onValuesChange: (record, recordList) => {
            setDataSource(recordList);
          },
          onChange: setEditableRowKeys,
        }}
      />
    </DrawerForm>
  );
};

export default HolidayListDrawerForm;
