import { holidayPage } from '@/services/oa/holiday';
import { queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Space } from 'antd';
import React, { useRef, useState } from 'react';
import HolidayListDrawerForm from './components/HolidayListDrawerForm';

const HolidayList: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [id, setId] = useState<string | undefined>();
  const [modalVisit, setModalVisit] = useState(false);

  const onEdit = async (record: API.HolidayPageResp) => {
    const id_ = `${record.id}`;
    setId(id_);
    setModalVisit(true);
  };

  // 表格
  const columns: ProColumns<API.HolidayPageResp>[] = [
    {
      title: '假期表名称',
      dataIndex: 'holidayListName',
      width: 200,
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
    },
    {
      title: '结束时间',
      dataIndex: 'endDate',
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        0: '禁用',
        1: '启用',
      },
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <>
              <a key="edit" onClick={() => onEdit(record)}>
                修改
              </a>
            </>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.HolidayPageResp>
        {...defaultTableConfig}
        actionRef={tableRef}
        columns={columns}
        headerTitle="假期列表"
        request={async (params) => {
          return queryPagingTable<API.PageReq>({ ...params }, holidayPage);
        }}
      />
      <HolidayListDrawerForm
        id={id}
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default HolidayList;
