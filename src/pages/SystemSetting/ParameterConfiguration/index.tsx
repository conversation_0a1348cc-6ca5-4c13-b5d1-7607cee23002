import RKPageLoading from '@/components/RKPageLoading';
import { sysData, sysDataEdit } from '@/services/oa/dictionary';
import { requiredRule } from '@/utils/setting';
import {
  <PERSON>erToolbar,
  PageContainer,
  PageHeader,
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormMoney,
} from '@ant-design/pro-components';
import { useAccess, useRequest } from '@umijs/max';
import { Col, message, Row } from 'antd';
import React, { useRef } from 'react';

const ParameterConfiguration: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const { canEditParameterConfiguration = false } = useAccess();

  // 获取详情
  const { loading, refresh } = useRequest(() => sysData(), {
    onSuccess: (res) => {
      if (res?.code === 200 && res.data) {
        setTimeout(() => {
          formRef.current?.setFieldsValue(res.data);
        }, 500);
      }
    },
    formatResult: (res) => res,
  });

  // 修改
  const { run: edit, loading: editLoading } = useRequest((value) => sysDataEdit(value), {
    manual: true,
    formatResult: (res) => res,
    onSuccess: (res) => {
      if (res.code === 200) {
        message.success('保存成功！');
        refresh();
      }
    },
  });

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <RKPageLoading loading={loading} />
      <PageHeader className="rk-page-header" />
      <ProForm
        formRef={formRef}
        submitter={
          canEditParameterConfiguration && {
            resetButtonProps: false,
            searchConfig: {
              submitText: '保存',
            },
            submitButtonProps: {
              loading: editLoading,
            },
            render: (_, doms) => {
              return <FooterToolbar>{doms}</FooterToolbar>;
            },
          }
        }
        onFinish={async (values) => {
          await edit(values);
          return true;
        }}
      >
        <Row justify="center">
          <Col>
            <ProFormDigit
              width="lg"
              name="monthlyWorkdays"
              label="月工作日"
              placeholder="请输入月工作日"
              fieldProps={{
                min: 0.01,
                precision: 2,
              }}
              rules={[requiredRule]}
            />
            <ProFormDigit
              width="lg"
              name="salaryCoefficient"
              label="工资系数"
              placeholder="请输入工资系数"
              fieldProps={{
                min: 0.01,
                precision: 2,
              }}
              rules={[requiredRule]}
            />
            <ProFormMoney
              width="lg"
              name="preDaySalary"
              label="售前项目人力工资基数(人天)"
              rules={[requiredRule]}
              fieldProps={{
                min: 0,
                precision: 2,
              }}
            />
          </Col>
        </Row>
      </ProForm>
    </PageContainer>
  );
};

export default ParameterConfiguration;
