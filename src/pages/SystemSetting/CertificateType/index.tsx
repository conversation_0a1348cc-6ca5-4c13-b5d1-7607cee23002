import { delCertificate, listCertificate } from '@/services/oa/sysCertificate';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import React, { useRef, useState } from 'react';
import CertificateTypeModalForm from './components/CertificateTypeModalForm';

const CertificateType: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [modalVisit, setModalVisit] = useState(false);
  const [initialValues, setInitialValues] = useState<API.SysCertificateListResp | undefined>();

  const { run: deleteRecord } = useRequest((id) => delCertificate({ id }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (row: API.SysCertificateListResp) => {
    const { type, id } = row;

    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${type}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(id);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.SysCertificateListResp>[] = [
    {
      title: '证书类别',
      dataIndex: 'type',
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <a
              key="edit"
              onClick={() => {
                setModalVisit(true);
                setInitialValues(record);
              }}
            >
              修改
            </a>
            <a key="delete" onClick={() => handleDelete(record)}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.SysCertificateListResp>
        {...defaultTableConfig}
        actionRef={tableRef}
        columns={columns}
        headerTitle="证书类别列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建证书类别
            </Button>,
          ],
        }}
        request={async () => {
          const msg = await listCertificate();
          return {
            data: msg?.data || [],
            success: true,
          };
        }}
      />
      <CertificateTypeModalForm
        initialValues={initialValues}
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default CertificateType;
