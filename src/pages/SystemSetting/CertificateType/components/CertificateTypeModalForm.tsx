import { createCertificate, updateCertificate } from '@/services/oa/sysCertificate';
import {
  ModalForm,
  ModalFormProps,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useRef } from 'react';

const CertificateTypeModalForm: React.FC<ModalFormProps> = ({
  initialValues,
  open,
  onOpenChange,
  onFinish,
}) => {
  const formRef = useRef<ProFormInstance>();
  const isEdit = initialValues?.id;

  return (
    <ModalForm
      width="auto"
      title={isEdit ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const msg = isEdit ? await updateCertificate(value) : await createCertificate(value);
        const success = msg.code === 200;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        centered: true,
      }}
      initialValues={initialValues}
      submitter={{
        searchConfig: {
          submitText: '保存',
        },
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
      </div>
      <ProFormText
        width="lg"
        name="type"
        label="证书类别"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[
          {
            required: true,
            message: '此项为必填项',
          },
        ]}
      />
    </ModalForm>
  );
};

export default CertificateTypeModalForm;
