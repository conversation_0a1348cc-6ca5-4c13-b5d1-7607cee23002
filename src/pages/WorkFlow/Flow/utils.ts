import { LogicFlow } from '@logicflow/core';

const { v4: uuidV4 } = require('uuid');
export const generateId = () => uuidV4().replace(/-/g, '');

interface NodeTemplate {
  id: string;
  type: string;
  properties: {
    formSchema: Record<string, any>[];
  };
}

interface ValidationError {
  nodeId: string;
  field: string;
  label: string;
  message: string;
}

export function validateFlowData(
  flowData: LogicFlow.GraphData,
  nodeTemplates: NodeTemplate[],
): ValidationError[] {
  const errors: ValidationError[] = [];

  flowData.nodes.forEach((node) => {
    const template = nodeTemplates.find((tpl) => tpl.type === node.type);
    if (!template) return;

    const schemaList = template.properties.formSchema || [];

    schemaList.forEach((field) => {
      const isRequired = field.required ?? false;

      // 如果有依赖项，需要判断依赖字段的值是否符合
      if (field.dependency) {
        const depValue = node.properties?.[field.dependency.name];
        if (!field.dependency.values.includes(depValue)) return; // 不符合依赖，不校验
      }

      if (isRequired) {
        const value = node.properties?.[field.name];
        const isEmpty = value === undefined || value === null || value === '';

        if (isEmpty) {
          errors.push({
            nodeId: node.id,
            field: field.name,
            label: field.label,
            message: `节点「${node?.properties?.title || node.type}」的「${field.label}」未填写`,
          });
        }
      }
    });
  });

  return errors;
}

export const isGraphData = (data: unknown): data is LogicFlow.GraphData => {
  if (typeof data !== 'object' || data === null) return false;

  // 简单校验必须字段
  return Array.isArray((data as any).nodes) && Array.isArray((data as any).edges);
};

export const hasCycle = (lf: LogicFlow): boolean => {
  const visited = new Set<string>();
  const recStack = new Set<string>();

  const graph = lf.getGraphRawData();
  const edges = graph.edges || [];

  const adjList: Record<string, string[]> = {};
  edges.forEach(({ sourceNodeId, targetNodeId }) => {
    if (!adjList[sourceNodeId]) adjList[sourceNodeId] = [];
    adjList[sourceNodeId].push(targetNodeId);
  });

  const dfs = (node: string): boolean => {
    if (!visited.has(node)) {
      visited.add(node);
      recStack.add(node);

      for (const neighbor of adjList[node] || []) {
        if (!visited.has(neighbor) && dfs(neighbor)) return true;
        if (recStack.has(neighbor)) return true;
      }
    }
    recStack.delete(node);
    return false;
  };

  return Object.keys(adjList).some((node) => dfs(node));
};

export const validateStartEnd = (graph: LogicFlow): string[] => {
  const nodes = (graph?.getGraphData() as LogicFlow.GraphData).nodes;
  const startCount = nodes.filter((n) => n.type === 'apply').length;
  const ccCount = nodes.filter((n) => n.type === 'cc').length;
  const errs: string[] = [];
  if (!startCount) errs.push('请添加申请节点！');
  if (ccCount > 1) errs.push('只能有一个抄送节点');
  return errs;
};

export const allNodesReachable = (graph: LogicFlow): boolean => {
  const data = graph.getGraphData() as LogicFlow.GraphData;
  const nodes = data.nodes;
  const edges = data.edges;
  const adj: Record<string, string[]> = {};
  edges.forEach((e) => {
    adj[e.sourceNodeId] = adj[e.sourceNodeId] || [];
    adj[e.sourceNodeId].push(e.targetNodeId);
  });
  const startId = nodes.find((n) => n.type === 'apply')?.id;
  if (!startId) return false;
  const visited = new Set<string>();
  function dfs(id: string) {
    visited.add(id);
    (adj[id] || []).forEach((t) => {
      if (!visited.has(t)) dfs(t);
    });
  }
  dfs(startId);
  return nodes.every((n) => (n.type === 'apply' || n.type === 'cc' ? true : visited.has(n.id)));
};

export const validateGateways = (graph: LogicFlow): string[] => {
  const { nodes, edges } = graph.getGraphData() as LogicFlow.GraphData;
  const errs: string[] = [];
  nodes
    .filter((n) => n.type === 'judgment')
    .forEach((g) => {
      const out = edges.filter((e) => e.sourceNodeId === g.id);
      if (out.length !== 2) errs.push(`条件网关 ${g.id} 必须有且仅有两条出边`);
      out.forEach((e) => {
        if (!e.properties?.condition) errs.push(`边 ${e.sourceNodeId}->${e.targetNodeId} 缺少条件`);
      });
    });
  return errs;
};

export const validateBPMN = (lf: LogicFlow) => {
  const errors: string[] = [];
  // 1. Start/End 唯一
  errors.push(...validateStartEnd(lf));

  // 2. 必须有申请节点
  const nodes = (lf.getGraphData() as LogicFlow.GraphData).nodes;
  if (!nodes.some((n) => n.type === 'apply')) {
    errors.push('请添加申请节点');
  }

  // 3. 环路校验
  if (hasCycle(lf)) {
    errors.push('流程图存在环，请检查节点配置');
  }

  // 4. 可达性
  if (!allNodesReachable(lf)) {
    errors.push('存在孤立节点或不可达节点');
  }

  // 5. 网关对称
  errors.push(...validateGateways(lf));

  return errors;
};
