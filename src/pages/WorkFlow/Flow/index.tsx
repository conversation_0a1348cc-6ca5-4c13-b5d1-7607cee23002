import { PageContainer } from '@ant-design/pro-components';
import LogicFlow from '@logicflow/core';
import '@logicflow/core/dist/index.css';
import '@logicflow/extension/lib/style/index.css';
import { Col, message, Row } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';
import DynamicProForm from './components/DynamicProForm';
import NodePanel from './components/NodePanel';
import RegisterNode from './components/registerNode';
import { theme } from './config';
import styles from './index.less';

import { workFlowInfo } from '@/services/oa/flow';
import { SelectionSelect } from '@logicflow/extension';
import { useParams, useRequest } from '@umijs/max';
import Toolbar from './components/Toolbar';
import { FormSchemaItem, LogicFlowNodeFormSchema } from './type';

export default function ApproveExample() {
  const { id = '' } = useParams();
  const [messageApi, contextHolder] = message.useMessage();

  const refContainer = useRef(null);
  const [lf, setLf] = useState({} as LogicFlow);
  const [nodeData, setNodeData] = useState<LogicFlow.NodeData>();

  const [propertyPanelDrawerVisit, setPropertyPanelDrawerVisit] = useState(false);

  // 获取流程
  const { data } = useRequest(
    () =>
      workFlowInfo({
        id,
      }),
    {
      ready: !!id,
    },
  );

  const disabledPage = useMemo(() => data?.status !== '0' && !!id, [data]);

  const initEvent = (lf: LogicFlow) => {
    lf.on('node:dbclick', ({ data }) => {
      setNodeData(data);
      setPropertyPanelDrawerVisit(true);
    });
    lf.on('connection:not-allowed', (data) => {
      messageApi.error(data.msg);
    });
  };

  useEffect(() => {
    if (refContainer.current) {
      const lf = new LogicFlow({
        hoverOutline: false,
        nodeSelectedOutline: true,
        edgeSelectedOutline: true,
        nodeTextEdit: false,
        edgeTextEdit: false,
        textEdit: false,
        snapline: true,
        grid: {
          size: 20,
          visible: true,
          type: 'dot',
          config: {
            color: '#DCDCDC', // 设置网格的颜色
          },
        },
        background: {
          // backgroundColor: '#f5f5f5',
        },
        style: theme,
        keyboard: { enabled: true },
        container: refContainer.current as HTMLElement,
        plugins: [SelectionSelect],
        isSilentMode: disabledPage,
      });

      setLf(lf);

      RegisterNode(lf);

      lf.render({});

      if (data?.process) {
        lf.render(data.process as LogicFlow.GraphConfigData);
      }

      // lf.adapterOut = function (logicFlowData) {
      //   return {
      //     json: logicFlowData,
      //     xml: jsonToBpmnXml(logicFlowData),
      //   };
      // };

      lf.translateCenter();

      initEvent(lf);
    }
  }, [data, disabledPage]);

  // 更新属性
  const updateProperty = (id: string, data: any) => {
    const node = lf.graphModel.nodesMap[id];
    if (node) {
      const properties = node.model.properties || {};
      const formSchema = data.formSchema || properties.formSchema || [];
      const baseHeight = 100;

      // 计算可见字段数（满足 dependency 的字段）
      const visibleFields = formSchema.filter((item: FormSchemaItem) => {
        if (!item.dependency || !item.dependency.name) return true;
        const curValue = (data || properties)[item.dependency.name];
        return item.dependency.values.includes(curValue);
      });

      // 高度逻辑：如果可见字段数大于2，高度+31，否则用原高度
      const newHeight = visibleFields.length > 1 ? baseHeight + 31 : baseHeight;

      node.model.setProperties({
        ...properties,
        ...data,
        height: newHeight,
        error: false,
      });
    }
  };

  return (
    <PageContainer header={{ title: false }}>
      {contextHolder}
      <div className={styles['approve-container']}>
        <Toolbar lf={lf} disabled={disabledPage} />
        <Row>
          <Col flex="100px" style={{ height: '100%' }}>
            <NodePanel lf={lf} />
          </Col>
          <Col flex="auto">
            <div
              ref={refContainer}
              className="App"
              style={{
                width: '100%',
                height: '100%',
              }}
            />
          </Col>
        </Row>
      </div>

      {/* 属性面板  */}
      <DynamicProForm
        disabled={disabledPage}
        onOpenChange={setPropertyPanelDrawerVisit}
        open={propertyPanelDrawerVisit}
        properties={(nodeData?.properties as LogicFlowNodeFormSchema['properties']) || {}}
        onFinish={async (values) => {
          if (nodeData?.id) {
            updateProperty(nodeData.id, values);
          }
          return true;
        }}
      />
    </PageContainer>
  );
}
