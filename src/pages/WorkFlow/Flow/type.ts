import LogicFlow from '@logicflow/core';
import { iconMap } from './config';

export type HtmlNodeConfig = {
  type: string;
  label: string;
};

export type IconName = keyof typeof iconMap;

export interface FormSchemaDependency {
  name: string;
  values: any[];
}

export interface FormSchemaItem {
  label: string;
  name: string;
  valueType: string;
  required?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
  dependency?: FormSchemaDependency;
  [key: string]: any;
}
export interface FormSchemaNodeProperties {
  formSchema: FormSchemaItem[];
}

export interface LogicFlowNodeFormSchema extends LogicFlow.NodeConfig {
  properties: LogicFlow.NodeConfig['properties'] & {
    formSchema: FormSchemaItem[];
  };
}
