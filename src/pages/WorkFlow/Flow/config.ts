import { APPROVAL_TYPE, APPROVE_TYPE } from '@/enums';
import { userDropDownList } from '@/services/oa/auth';
import { getDepartmentList } from '@/services/oa/department';
import { positionList as getPositionList } from '@/services/oa/position';
import {
  CheckCircleOutlined,
  ForkOutlined,
  GatewayOutlined,
  PlayCircleOutlined,
  PoweroffOutlined,
  SendOutlined,
} from '@ant-design/icons';
import { generateId } from './utils';

export const iconMap = {
  PlayCircleOutlined: PlayCircleOutlined,
  CheckCircleOutlined: CheckCircleOutlined,
  ForkOutlined: ForkOutlined,
  PoweroffOutlined: PoweroffOutlined,
  SendOutlined: SendOutlined,
  GatewayOutlined: GatewayOutlined,
};

const { data: departmentList } = await getDepartmentList();
const { data: positionList } = await getPositionList();
const { data: userList } = await userDropDownList({
  activation: true,
});

export const approveNodes = [
  {
    type: 'apply',
    label: '发起',
    icon: 'PlayCircleOutlined',
    properties: {
      width: 200,
      height: 100,
      title: '申请',
      error: true,
      formSchema: [
        {
          label: '表单类型',
          name: 'applyType',
          valueType: 'select',
          fieldProps: {
            options: APPROVAL_TYPE,
          },
          required: true,
          allowClear: false,
        },
      ],
    },
    id: generateId(),
  },
  {
    type: 'approver',
    label: '审批',
    icon: 'CheckCircleOutlined',
    properties: {
      width: 200,
      height: 100,
      title: '审批',
      error: true,
      approveType: '',
      approveDeptId: '',
      approvePositionId: '',
      formSchema: [
        {
          label: '审批类型',
          name: 'approveType',
          valueType: 'select',
          fieldProps: {
            options: APPROVE_TYPE,
          },
          required: true,
          allowClear: false,
        },
        {
          label: '审批部门',
          name: 'approveDeptId',
          valueType: 'select',
          dependency: { name: 'approveType', values: ['3'] },
          fieldProps: {
            options:
              departmentList?.map((item) => ({
                label: item.departmentName,
                value: item.id,
              })) || [],
            showSearch: true,
          },
          required: true,
        },
        {
          label: '审批职位',
          name: 'approvePositionId',
          valueType: 'select',
          dependency: { name: 'approveType', values: ['4'] },
          fieldProps: {
            options:
              positionList?.map((item) => ({
                label: item.positionName,
                value: item.id,
              })) || [],
            showSearch: true,
          },
          required: true,
        },
        {
          label: '审批人员',
          name: 'approvePersonIds',
          valueType: 'select',
          dependency: { name: 'approveType', values: ['5'] },
          fieldProps: {
            mode: 'multiple',
            options:
              userList?.map((item) => ({
                label: item.username,
                value: item.id,
              })) || [],
            showSearch: true,
          },
          required: true,
        },
      ],
    },
    id: generateId(),
  },
  {
    type: 'cc',
    label: '抄送',
    icon: 'SendOutlined',
    properties: {
      width: 200,
      height: 100,
      title: '抄送',
      error: true,
      formSchema: [
        {
          label: '抄送人员',
          name: 'ccPersonIds',
          valueType: 'select',
          mode: 'multiple',
          fieldProps: {
            options:
              userList?.map((item) => ({
                label: item.username,
                value: item.id,
              })) || [],

            showSearch: true,
          },
          required: true,
        },
      ],
    },
    id: generateId(),
  },
  // {
  //   type: 'judgment',
  //   label: '条件',
  //   icon: 'GatewayOutlined',
  //   properties: {
  //     width: 200,
  //     height: 100,
  //     title: '条件',
  //     formSchema: [],
  //   },
  //   id: generateId(),
  // },
];

export const theme = {
  outline: {
    fill: 'transparent',
    stroke: '#13c2c2',
    strokeDasharray: '3,3',
    hover: {
      stroke: '#13c2c2',
    },
  },
  anchor: {
    stroke: '#13c2c2',
    fill: '#FFFFFF',
    r: 4,
    hover: {
      fill: '#13c2c2',
      fillOpacity: 0.5,
      stroke: '#13c2c2',
      r: 10,
    },
  },
  line: {
    stroke: '#d9d9d9',
    strokeWidth: 1,
  },
  polyline: {
    stroke: '#99add1',
    strokeWidth: 1,
  },
  anchorLine: {
    stroke: '#d9d9d9',
    strokeWidth: 2,
    strokeDasharray: '3,2',
  },
  snapline: {
    stroke: '#13c2c2',
    strokeWidth: 1,
  },
};
