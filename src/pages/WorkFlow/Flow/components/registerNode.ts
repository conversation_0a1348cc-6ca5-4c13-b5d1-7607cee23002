import LogicFlow, { Model, RectNodeModel } from '@logicflow/core';
import { register } from '@logicflow/react-node-registry';
import { message } from 'antd';
import { generateId } from '../utils';
import { approveNodes } from './../config';
import NodeCard from './NodeCard';

export const preventSelfConnectionRule: Model.ConnectRule = {
  message: '不能连接自己！',
  validate: (source, target) => !!(source && target && target.id !== source.id),
};

export const ccNoOutgoingRule: Model.ConnectRule = {
  message: '抄送节点必须是最后一个节点！',
  validate: () => {
    return false;
  },
};

export const preventDuplicateTargetRule: Model.ConnectRule = {
  message: '不能重复连接相同节点',
  validate: (source, target, _sAnchor, _tAnchor, edgeID) => {
    const edges = source?.graphModel?.edges || [];
    const exist = edges.find(
      (e) => e.sourceNodeId === source?.id && e.targetNodeId === target?.id && e.id !== edgeID,
    );
    return !exist;
  },
};
export const onlyOneOutgoingRule: Model.ConnectRule = {
  message: '该节点只能有一条输出边',
  validate: (source) => {
    const edges = source?.graphModel?.edges || [];
    const outgoing = edges.filter((e) => e.sourceNodeId === source?.id);
    return outgoing.length < 1;
  },
};

export class ApplyNodeModel extends RectNodeModel {
  getConnectedTargetRules() {
    const rules = super.getConnectedTargetRules();
    const noIncomingEdge = {
      message: '申请节点不能有入边，只能作为第一个节点',
      validate: () => false,
    };
    rules.push(noIncomingEdge);
    return rules;
  }
  getConnectedSourceRules() {
    const rules = super.getConnectedSourceRules();
    rules.push(onlyOneOutgoingRule);
    return rules;
  }
}

export class ApproverNodeModel extends RectNodeModel {
  getConnectedTargetRules() {
    const rules = super.getConnectedTargetRules();
    rules.push(preventSelfConnectionRule);
    rules.push(preventDuplicateTargetRule);
    return rules;
  }
  getConnectedSourceRules() {
    const rules = super.getConnectedSourceRules();
    rules.push(onlyOneOutgoingRule);
    return rules;
  }
}

export class CcNodeModel extends RectNodeModel {
  getConnectedTargetRules() {
    const rules = super.getConnectedTargetRules();
    rules.push(preventSelfConnectionRule);
    rules.push(preventDuplicateTargetRule);

    return rules;
  }
  getConnectedSourceRules() {
    const rules = super.getConnectedSourceRules();
    rules.push(ccNoOutgoingRule);
    return rules;
  }
}

export class ConditionGatewayNodeModel extends RectNodeModel {
  getConnectedTargetRules() {
    return [preventSelfConnectionRule, preventDuplicateTargetRule]; // 可以允许多个出口
  }
}

const nodeTypes = [
  { type: 'apply', component: NodeCard, model: ApplyNodeModel },
  { type: 'approver', component: NodeCard, model: ApproverNodeModel },
  { type: 'cc', component: NodeCard, model: CcNodeModel },
  { type: 'judgment', component: NodeCard, model: ConditionGatewayNodeModel },
];

const approveNode = approveNodes?.find((node) => node.type === 'approver');

export default function RegisterNode(lf: LogicFlow) {
  nodeTypes.forEach(({ type, model, component }) => {
    register({ type, component, model }, lf);
  });

  lf.on('node:dnd-add', ({ data }) => {
    if (data.type === 'apply') {
      setTimeout(() => {
        const graphData = lf.getGraphData();
        const nodes = (graphData as { nodes: any[] }).nodes || [];
        const applyCount = nodes.filter((n) => n.type === 'apply').length;
        if (applyCount > 1) {
          message.error('只能有一个发起节点');
          lf.deleteNode(data.id);
        }
      }, 0);
    }

    if (data.type === 'judgment') {
      const { id, x = 200, y = 100 } = data;

      setTimeout(() => {
        const yesNodeId = generateId();
        const noNodeId = generateId();

        // 1. 创建两个审批节点
        lf.addNode({
          ...approveNode,
          id: yesNodeId,
          type: 'approver',
          x: x + 300,
          y: y - 80,
        });

        lf.addNode({
          ...approveNode,
          id: noNodeId,
          type: 'approver',
          x: x + 300,
          y: y + 80,
        });

        // 2. 连接判断节点到两个审批节点
        lf.addEdge({
          type: 'polyline',
          sourceNodeId: id,
          targetNodeId: yesNodeId,
          text: '是',
          properties: { condition: '是' },
        });

        lf.addEdge({
          type: 'polyline',
          sourceNodeId: id,
          targetNodeId: noNodeId,
          text: '否',
          properties: { condition: '否' },
        });
      }, 0);
    }
  });
}
