import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProFormDependency,
  ProFormText,
} from '@ant-design/pro-components';
import { Alert } from 'antd';
import { FC, memo } from 'react';
import COMPONENT_MAP from '../componentMap';
import { LogicFlowNodeFormSchema } from '../type';

interface DynamicProFormProps {
  properties: LogicFlowNodeFormSchema['properties'];
}

const DynamicProForm: FC<DrawerFormProps & DynamicProFormProps> = ({ properties, ...props }) => {
  const { formSchema } = properties;

  return (
    <DrawerForm
      title="属性面板"
      width="auto"
      drawerProps={{
        closeIcon: false,
        destroyOnClose: true,
      }}
      submitter={{}}
      initialValues={properties}
      {...props}
    >
      <ProFormText width="md" name="title" label="节点名称" rules={[requiredRule]} />
      {formSchema?.map((item, index) => {
        const { valueType, required, dependency, fieldProps, ...componentProps } = item;
        if (!valueType) return null;
        const Comp = COMPONENT_MAP[valueType] || ProFormText;
        if (Array.isArray(Comp)) {
          return null;
        }

        const isValidComponent =
          typeof Comp === 'function' ||
          (typeof Comp === 'object' && Comp !== null && '$$typeof' in Comp);

        if (!isValidComponent) {
          return (
            <Alert key={index} message={`Error: 未知组件类型 ${valueType}`} type="error" showIcon />
          );
        }
        const finalProps = { ...componentProps };
        if (fieldProps) finalProps.fieldProps = fieldProps;
        if (required) finalProps.rules = [requiredRule];

        if (dependency && dependency.name) {
          return (
            <ProFormDependency name={[dependency.name]} key={index}>
              {(values) => {
                const curValue = values[dependency.name];
                const visible = dependency.values.includes(curValue);
                try {
                  return visible ? <Comp width="md" {...finalProps} /> : null;
                } catch (e) {
                  console.error('Dependency render error:', item, e);
                  return null;
                }
              }}
            </ProFormDependency>
          );
        }
        return <Comp width="md" key={index} {...finalProps} />;
      })}
    </DrawerForm>
  );
};

export default memo(DynamicProForm);
