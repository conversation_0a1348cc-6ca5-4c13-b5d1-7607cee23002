import { option2enum } from '@/utils';
import { WarningOutlined } from '@ant-design/icons';
import { ProDescriptions } from '@ant-design/pro-components';
import { ReactNodeProps } from '@logicflow/react-node-registry';
import { Typography } from 'antd';
import classNames from 'classnames/bind';
import React from 'react';
import { FormSchemaItem } from '../../type';

import styles from './index.less';
const cx = classNames.bind(styles);

const { Title } = Typography;

// 支持 dependency 过滤的描述列生成方法
export const toProDescriptionsColumns = (
  schema: FormSchemaItem[],
  dataSource: Record<string, any>,
) => {
  return schema
    .filter((item) => {
      // 没有依赖直接显示
      if (!item.dependency || !item.dependency.name) return true;
      // 有依赖，检查依赖值
      const curValue = dataSource?.[item.dependency.name];
      return item.dependency.values.includes(curValue);
    })
    .map((item) => {
      const col: any = {
        title: item.label,
        dataIndex: item.name,
        valueType: item.valueType,
      };
      // 处理 select 的 valueEnum
      if (item.valueType === 'select' && item.fieldProps?.options) {
        const valueEnum = option2enum(item.fieldProps.options);
        col.valueEnum = valueEnum;
      }
      return col;
    });
};

const NodeCard: React.FC<ReactNodeProps> = ({ node }) => {
  const data = node.getData();

  const { title, formSchema = [] } = data?.properties || {};
  const dataSource = data?.properties || {};
  const proDescriptionsColumns = toProDescriptionsColumns(formSchema, dataSource);

  return (
    <div className={cx('approval-node-card', data.type)}>
      {/* 左侧波浪形装饰 */}
      <div className={styles['wave-decoration']}>
        <svg
          width="16"
          height="100%"
          xmlns="http://www.w3.org/2000/svg"
          preserveAspectRatio="none"
          viewBox="0 0 16 100"
        >
          <path
            d="M 8 0
                 Q 4 5, 8 10
                 T 8 20
                 Q 4 25, 8 30
                 T 8 40
                 Q 4 45, 8 50
                 T 8 60
                 Q 4 65, 8 70
                 T 8 80
                 Q 4 85, 8 90
                 T 8 100
                 L 0 100
                 L 0 0
                 Z"
            className={styles['wave-path']}
          />
        </svg>
      </div>

      {/* 主要内容区域 */}
      <div className={cx('content-area')}>
        <Title level={5} ellipsis style={{ color: 'inherit' }}>
          {title}
        </Title>
        <ProDescriptions
          size="small"
          column={1}
          columns={proDescriptionsColumns}
          dataSource={dataSource}
        />
        <div className={cx('node-status')}>{dataSource?.error && <WarningOutlined />}</div>
      </div>
    </div>
  );
};

export default NodeCard;
