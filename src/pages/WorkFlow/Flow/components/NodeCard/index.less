@apply-color: #1783ff;
@approver-color: #7863ff;
@cc-color: #d580ff;
@judgment-color: #60c42d;

.approval-node-card {
  :global {
    .ant-descriptions-item-content {
      display: inline-block !important;
      width: 70px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      .ant-space {
        flex-shrink: 1;
        flex-wrap: nowrap !important;
        width: 70px;
        min-width: 0;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        .ant-space-item {
          flex-shrink: 0;
          max-width: 70px;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
  position: relative;
  display: flex;
  align-items: stretch;
  max-width: 384px;
  height: 100%;
  overflow: hidden;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.1);
  cursor: grab;
  user-select: none;

  .wave-decoration {
    flex-shrink: 0;
    align-self: stretch;
    width: 16px;

    svg {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  .wave-path {
    stroke-linecap: round;
    stroke-width: 2;
  }

  .content-area {
    flex: 1;
    width: 100%;
    padding: 10px;
    overflow: hidden;
  }

  .node-title {
    font-size: 18px;
  }

  // 不同状态的颜色配置
  &.apply {
    color: @apply-color;
    &:hover {
      box-shadow: 0 0 15px 0 fade(@apply-color, 15%);
    }
    .wave-path {
      fill: @apply-color;
      stroke: @apply-color;
    }
  }

  &.approver {
    color: @approver-color;
    &:hover {
      box-shadow: 0 0 15px 0 fade(@approver-color, 15%);
    }
    .wave-path {
      fill: @approver-color;
      stroke: @approver-color;
    }
  }

  &.cc {
    color: @cc-color;
    &:hover {
      box-shadow: 0 0 15px 0 fade(@cc-color, 15%);
    }
    .wave-path {
      fill: @cc-color;
      stroke: @cc-color;
    }
  }

  &.judgment {
    color: @judgment-color;
    &:hover {
      box-shadow: 0 0 15px 0 fade(@judgment-color, 15%);
    }
    .wave-path {
      fill: @judgment-color;
      stroke: @judgment-color;
    }
  }
  .node-status {
    position: absolute;
    top: 13px;
    right: 10px;
    color: #ff4d4f;
  }
}
