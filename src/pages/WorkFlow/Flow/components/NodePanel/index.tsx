import LogicFlow from '@logicflow/core';
import { Space, Typography } from 'antd';
import { FC } from 'react';
import { approveNodes, iconMap } from '../../config';
import { HtmlNodeConfig } from '../../type';
import styles from './index.less';

const NodePanel: FC<{ lf: LogicFlow }> = ({ lf }) => {
  // 拖拽创建
  const dragNode = (item: HtmlNodeConfig) => {
    lf.dnd.startDrag(item);
  };

  return (
    <div className={styles['node-panel']}>
      <div className={styles.node}>
        {approveNodes?.map((item) => {
          const IconComponent = iconMap[item.icon as keyof typeof iconMap];
          return (
            <Typography.Text
              key={item.type}
              className={styles['node-item']}
              onMouseDown={() => dragNode(item)}
            >
              <Space>
                {IconComponent && <IconComponent />}
                {item.label}
              </Space>
            </Typography.Text>
          );
        })}
      </div>
    </div>
  );
};

export default NodePanel;
