import { createRkWorkFlow, workFlowEdit } from '@/services/oa/flow';
import { onSuccessAndGoBack } from '@/utils';
import LogicFlow from '@logicflow/core';
import { useParams, useRequest } from '@umijs/max';
import { Button, message } from 'antd';
import { FC } from 'react';
import { approveNodes } from '../../config';
import { isGraphData, validateBPMN, validateFlowData } from '../../utils';
import styles from './index.less';

const Toolbar: FC<{
  lf: LogicFlow;
  disabled?: boolean;
}> = ({ lf, disabled }) => {
  const { id } = useParams();
  // 创建
  const { run: createFlow, loading: createLoading } = useRequest(
    (params) => createRkWorkFlow(params),
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: onSuccessAndGoBack,
    },
  );

  // 编辑
  const { run: updateFlow, loading: updateLoading } = useRequest((params) => workFlowEdit(params), {
    manual: true,
    formatResult: (res) => res,
    onSuccess: onSuccessAndGoBack,
  });

  return (
    <div className={styles.toolbar}>
      <Button
        type="dashed"
        onClick={() => {
          lf.clearData();
        }}
        disabled={disabled}
      >
        清空画布
      </Button>
      <Button
        type="primary"
        loading={createLoading || updateLoading}
        disabled={disabled}
        onClick={() => {
          // 一次性校验所有 BPMN 规则
          const flowErrors = validateBPMN(lf);
          if (flowErrors.length) {
            message.error(flowErrors[0]);
            return;
          }

          // 2. 获取画布数据 & 格式校验
          const flowData = lf.getGraphData();
          if (!isGraphData(flowData)) {
            message.error('流程图数据格式异常，请检查节点配置');
            return;
          }

          // 字段必填校验
          const fieldErrors = validateFlowData(flowData, approveNodes);
          fieldErrors.forEach((err) => lf.setProperties(err.nodeId, { error: true }));
          if (fieldErrors.length) {
            message.error('流程节点填写不完整，请检查节点配置。');
            return;
          }

          // 构造参数并提交
          const applyData = flowData?.nodes?.find((item) => item.type === 'apply');
          const { applyType, title } = applyData?.properties || {};

          const params = {
            type: applyType,
            remark: title,
            process: flowData as API.RkWorkFlowCreateDTO['process'],
          };
          if (!!id) {
            updateFlow({ id, ...params });
          } else {
            createFlow(params);
          }
        }}
      >
        保存
      </Button>
    </div>
  );
};

export default Toolbar;
