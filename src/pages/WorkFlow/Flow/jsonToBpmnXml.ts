export function jsonToBpmnXml(data: { nodes: any[]; edges: any[] }): string {
  // 1. 节点
  const nodeXml = data.nodes
    .map((node) => {
      const label = node.text?.value || node.label || '';
      switch (node.type) {
        case 'apply':
          return `<startEvent id="${node.id}" name="${label}" />`;
        case 'finish':
          return `<endEvent id="${node.id}" name="${label}" />`;
        case 'approver':
          return `<userTask id="${node.id}" name="${label}" />`;
        case 'judgement':
          return `<exclusiveGateway id="${node.id}" name="${label}" />`;
        default:
          return '';
      }
    })
    .join('\n    ');

  // 2. 连线
  const edgeXml = data.edges
    .map((edge) => {
      const label = edge.text?.value || edge.label || '';
      return `<sequenceFlow id="${edge.id}" sourceRef="${edge.sourceNodeId}" targetRef="${
        edge.targetNodeId
      }"${label ? ` name="${label}"` : ''} />`;
    })
    .join('\n    ');

  // 3. 拼装 BPMN XML
  return `<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             targetNamespace="http://bpmn.io/schema/bpmn">
  <process id="Process_1" isExecutable="true">
    ${nodeXml}
    ${edgeXml}
  </process>
</definitions>`;
}
