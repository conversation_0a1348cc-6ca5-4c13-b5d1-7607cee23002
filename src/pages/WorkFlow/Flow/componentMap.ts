import {
  ProFormDatePicker,
  ProFormDateRangePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { FC } from 'react';

export type ValueType = 'text' | 'number' | 'select' | 'date' | 'textarea' | 'switch' | 'dateRange';

export interface ProComponentMapType {
  [key: string]: FC<any>;
}

const COMPONENT_MAP: ProComponentMapType = {
  text: ProFormText,
  number: ProFormDigit,
  select: ProFormSelect,
  date: ProFormDatePicker,
  textarea: ProFormTextArea,
  switch: ProFormSwitch,
  dateRange: ProFormDateRangePicker,
};

export default COMPONENT_MAP;
