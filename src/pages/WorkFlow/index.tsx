import {
  ActionType,
  PageContainer,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import React, { useRef } from 'react';

import { APPROVAL_TYPE, FLOW_STATUS } from '@/enums';
import { workFlowEnable, workFlowList, workFlowRemove } from '@/services/oa/flow';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { history, Link, useRequest } from '@umijs/max';
import { Button, message, Modal, Space, Tag } from 'antd';

const WorkFlow: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();

  const formRef = useRef<ProFormInstance>();
  // 启用流程
  const { run: start, fetches: startFetches } = useRequest(
    (id) =>
      workFlowEnable({
        id,
      }),
    {
      manual: true,
      fetchKey: (id) => id,
      formatResult: (res) => res,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('流程启用成功！');
        tableRef.current?.reload?.();
      },
    },
  );

  // 删除流程
  const { run: deleteFlow } = useRequest(
    (id) =>
      workFlowRemove({
        id,
      }),
    {
      manual: true,
      fetchKey: (id) => id,
      formatResult: (res) => res,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('操作成功！');
        tableRef.current?.reload?.();
      },
    },
  );

  const handleDelete = async (record: API.RkWorkFlowPageResp) => {
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除流程“${record?.remark}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteFlow(record?.id);
      },
    });
  };

  const columns: ProColumns<API.RkWorkFlowPageResp>[] = [
    {
      title: '流程名称',
      dataIndex: 'remark',
      render: (dom, record) => <Link to={`/workflow/details/${record?.id}`}>{dom}</Link>,
      width: 200,
    },
    {
      title: '流程状态',
      dataIndex: 'status',
      valueEnum: option2enum(FLOW_STATUS),
      width: 100,
    },
    {
      title: '表单类型',
      dataIndex: 'templateName',
      valueEnum: option2enum(APPROVAL_TYPE),
      render(dom) {
        return <Tag>{dom}</Tag>;
      },
      width: 150,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 80,
      align: 'center',
      render: (text, record) => {
        const { id } = record;
        return (
          <Space>
            {record?.status !== '1' && (
              <Button
                type="link"
                className="inner-table-link"
                loading={startFetches?.[id!]?.loading}
                onClick={() => {
                  start(id);
                }}
              >
                启用
              </Button>
            )}
            {record?.status === '0' && (
              <Button
                type="link"
                onClick={() => {
                  handleDelete(record);
                }}
                className="inner-table-link"
              >
                删除
              </Button>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.RkWorkFlowPageResp>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        formRef={formRef}
        actionRef={tableRef}
        columns={columns}
        headerTitle="工作流列表"
        request={async ({ status, ...params }) =>
          queryPagingTable({ extra: { status }, ...params }, workFlowList)
        }
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={() => {
              history.push('/workflow/create');
            }}
            type="primary"
          >
            创建流程
          </Button>,
        ]}
      />
    </PageContainer>
  );
};

export default WorkFlow;
