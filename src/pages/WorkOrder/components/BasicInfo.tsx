import RKCol from '@/components/RKCol';
import RKSelectLabel from '@/components/RKSelectLabel';
import TitleLink from '@/components/TitleLink';
import BaseListContext from '@/Context/BaseListContext';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { requiredRule } from '@/utils/setting';
import {
  ProForm,
  ProFormCheckbox,
  ProFormDateTimePicker,
  ProFormDependency,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { AutoComplete, Row } from 'antd';
import dayjs from 'dayjs';
import { useContext } from 'react';

export const WORK_ORDER_TYPE = [
  { value: 'CUSTOMER_ONSITE_SERVICE', label: '客户现场服务' },
  { value: 'CUSTOMER_ONLINE_SERVICE', label: '客户联机服务' },
  { value: 'CUSTOMER_CONSULTATION_SERVICE', label: '客户咨询服务' },
  { value: 'ENGINEER_WORK_RECORD', label: '工程师工作记录' },
  { value: 'SECOND_LINE_EXPERT_SUPPORT', label: '二线专家支持' },
];

const BasicInfo: React.FC<WithRouteEditingProps> = ({}) => {
  const {
    availableProjectList = [],
    availableProjectLoading,
    historyContactList,
  } = useContext(BaseListContext);

  return (
    <>
      <div className="rk-none">
        <ProFormText name="id" />
        <ProFormText name="projectId" />
      </div>

      <Row gutter={24}>
        <RKCol>
          <ProFormSelect
            name="type"
            label="工单类型"
            placeholder="请输入"
            options={WORK_ORDER_TYPE}
            rules={[requiredRule]}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name="projectType"
            label="项目类型"
            rules={[requiredRule]}
            valueEnum={{
              SH: '售后项目',
              SQ: '售前项目',
            }}
            fieldProps={{
              allowClear: false,
            }}
          />
        </RKCol>
        <ProFormDependency name={['projectType']}>
          {({ projectType }) => {
            let list: API.ProBaseInfoResp[] = [];
            if (projectType) {
              const SQList = availableProjectList?.filter((item) => item.projectClassify === 'SQ');
              const SHList = availableProjectList?.filter((item) => item.projectClassify === 'SH');
              list = projectType === 'SQ' ? SQList : SHList;
            }
            return (
              <RKCol>
                <ProFormSelect
                  name="projectNumber"
                  label="项目编号"
                  rules={[requiredRule]}
                  fieldProps={{
                    loading: availableProjectLoading,
                    showSearch: true,
                    allowClear: false,
                    optionLabelProp: 'projectNumber',
                    filterOption: (inputValue, option) => {
                      return option?.keywords.indexOf(inputValue) >= 0;
                    },
                  }}
                  options={list.map((item) => {
                    return {
                      value: item.projectNumber,
                      label: <RKSelectLabel title={item.projectNumber} info={item.projectName} />,
                      projectNumber: item.projectNumber,
                      projectName: item.projectName,
                      keywords: `${item.projectName}${item.projectNumber}`,
                    };
                  })}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>
        <ProFormDependency name={['projectName', 'projectId', 'projectType']}>
          {({ projectId, projectType }) => {
            return (
              <RKCol>
                <ProFormTextArea
                  name="projectName"
                  label={
                    <TitleLink
                      path={
                        projectId &&
                        `/project/${projectType === 'SQ' ? 'pre' : 'after'}-sales/edit/${projectId}`
                      }
                    >
                      项目名称
                    </TitleLink>
                  }
                  disabled
                  rules={[requiredRule]}
                  fieldProps={{
                    autoSize: {
                      minRows: 1,
                      maxRows: 3,
                    },
                  }}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>
        <RKCol>
          <ProFormText name="employeeNumber" label="工程师工号" disabled rules={[requiredRule]} />
        </RKCol>
        <RKCol>
          <ProFormText name="employeeName" label="工程师姓名" disabled rules={[requiredRule]} />
        </RKCol>
        <RKCol>
          <ProFormDateTimePicker
            className="ant-picker"
            name="startTime"
            label="开始时间"
            placeholder="请输入"
            rules={[requiredRule]}
            transform={(value, namePath) => {
              return {
                [namePath]: dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
              };
            }}
            fieldProps={{
              format: 'YYYY-MM-DD HH:mm',
            }}
          />
        </RKCol>
        <RKCol>
          <ProFormDateTimePicker
            className="ant-picker"
            name="endTime"
            label="结束时间"
            placeholder="请输入"
            rules={[requiredRule]}
            transform={(value, namePath) => {
              return {
                [namePath]: dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
              };
            }}
            fieldProps={{
              format: 'YYYY-MM-DD HH:mm',
            }}
          />
        </RKCol>
        <RKCol>
          {/* <ProFormText name="contactName" label="联系人姓名" placeholder="请输入" /> */}
          <ProForm.Item name="contactName" label="联系人姓名">
            <AutoComplete
              options={historyContactList?.map((item) => ({
                ...item,
                label: item?.contactName,
                value: item?.contactName,
              }))}
              placeholder="请输入"
            />
          </ProForm.Item>
        </RKCol>
        <RKCol>
          {/* <ProForm.Item name="contactPhone" label="联系人电话">
            <AutoComplete
              options={historyContactList?.map((item) => ({
                ...item,
                label: item?.contactPhone,
                value: item?.contactPhone,
              }))}
              placeholder="请输入"
            />
          </ProForm.Item> */}
          <ProFormText name="contactPhone" label="联系人电话" placeholder="请输入" />
        </RKCol>
        <RKCol lg={24} md={24} sm={24}>
          <ProFormCheckbox.Group
            name="workOrderCategory"
            label="服务内容"
            rules={[requiredRule]}
            options={[
              '巡检',
              '安装部署',
              '技术保障',
              '故障处理',
              '升级',
              '性能优化',
              '技术交流',
              '技术咨询',
            ]}
            convertValue={(value: string | string[]) => {
              // 如果是字符串，转换为数组
              if (typeof value === 'string' && value) {
                return value.split(',');
              }
              return value || [];
            }}
            transform={(value: string[] | string, namePath) => {
              // 确保无论是否修改都能正确传入字段值
              let result = '';
              if (Array.isArray(value)) {
                result = value.join(',');
              } else if (typeof value === 'string') {
                result = value;
              }
              return {
                [namePath]: result,
              };
            }}
          />
        </RKCol>
      </Row>
    </>
  );
};

export default withRouteEditing(BasicInfo);
