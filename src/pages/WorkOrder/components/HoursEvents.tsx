import RKCol from '@/components/RKCol';
import { requiredRule } from '@/utils/setting';
import { ProFormDigit, ProFormTextArea } from '@ant-design/pro-components';
import { Row } from 'antd';

const HoursEvents = () => {
  return (
    <>
      <Row gutter={24}>
        <RKCol>
          <ProFormDigit
            name="workHours"
            label="工时"
            placeholder="请输入"
            rules={[requiredRule]}
            min={0}
            fieldProps={{
              addonAfter: '时',
            }}
            transform={(value, namePath) => ({ [namePath]: value.toString() })}
          />
        </RKCol>
      </Row>
      <ProFormTextArea
        name="describe"
        label="事件描述"
        fieldProps={{
          autoSize: {
            minRows: 3,
            maxRows: 8,
          },
        }}
        rules={[requiredRule]}
      />
      <ProFormTextArea
        name="content"
        label="工作内容"
        fieldProps={{
          autoSize: {
            minRows: 3,
            maxRows: 8,
          },
        }}
        rules={[requiredRule]}
      />
      <ProFormTextArea
        name="nextPlan"
        label="下一步计划"
        fieldProps={{
          autoSize: {
            minRows: 3,
            maxRows: 8,
          },
        }}
      />
    </>
  );
};

export default HoursEvents;
