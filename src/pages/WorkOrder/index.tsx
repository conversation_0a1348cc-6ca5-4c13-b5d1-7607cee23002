import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import RKSelectLabel from '@/components/RKSelectLabel';
import { APPROVAL_STATUS } from '@/enums';
import { useProjectList } from '@/hooks/useProjectList';
import { delWorkOrder, workOrderPage } from '@/services/oa/workOrder';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import React, { useRef, useState } from 'react';

const WorkOrder: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.WorkOrderPageResp[]>([]);
  const { projectList, loading } = useProjectList();
  const { canDeleteWorkOrder = false, canAddWorkOrder = false } = useAccess();

  // 删除
  const { run: deleteRecord } = useRequest((ids) => delWorkOrder({ ids: ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const handleDelete = async (rows: API.WorkOrderPageResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.documentNumber!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除工单“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.WorkOrderPageResp>[] = [
    {
      title: '工单编号',
      dataIndex: 'documentNumber',
      copyable: true,
      ellipsis: true,
      width: 180,
      render(dom, entity) {
        return (
          <a className="rk-a-span" onClick={() => history.push(`/work-orders/edit/${entity.id}`)}>
            {dom}
          </a>
        );
      },
    },
    {
      title: '项目编号',
      dataIndex: 'projectNumber',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        loading,
        showSearch: true,
        options: projectList?.map((item) => {
          return {
            value: item.projectNumber,
            label: <RKSelectLabel title={item.projectNumber} info={item.projectName} />,
          };
        }),
      },
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 180,
      ellipsis: true,
      copyable: true,
    },
    {
      title: '工程师姓名',
      dataIndex: 'employeeName',
      width: 100,
    },
    {
      title: '联系人姓名',
      dataIndex: 'contactName',
      search: false,
      width: 100,
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
      width: 110,
    },
    {
      title: '工时',
      dataIndex: 'workHours',
      valueType: 'digit',
      fieldProps: {
        min: 0,
        addonAfter: '时',
      },
      search: false,
      width: 80,
    },
    {
      title: '事件描述',
      dataIndex: 'describe',
      search: false,
      width: 180,
      ellipsis: true,
    },
    {
      title: '操作',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !canDeleteWorkOrder,
      fixed: 'right',
      render: (text, record) => {
        const { activiStatus } = record;
        return (
          <Access accessible={canDeleteWorkOrder}>
            {activiStatus === '0' && (
              <Button
                type="link"
                className="inner-table-link"
                key="delete"
                onClick={() => handleDelete([record])}
              >
                删除
              </Button>
            )}
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.WorkOrderPageResp>
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        rowSelection={
          canDeleteWorkOrder && {
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRows(selectedRows);
            },
            getCheckboxProps: (record) => ({
              disabled: record?.activiStatus !== '0',
            }),
          }
        }
        columns={columns}
        headerTitle="工单列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddWorkOrder}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  history.push('/work-orders/add');
                }}
              >
                新建工单
              </Button>
            </Access>,
          ],
        }}
        polling={5000}
        request={async (params) => {
          const {
            documentNumber,
            projectName,
            employeeName,
            projectNumber,
            activation,
            current,
            pageSize,
            activiStatus,
          } = params;
          const search = { documentNumber, projectName, employeeName };
          const filter = { projectNumber, activation, activiStatus };
          return queryPagingTable<API.PageReq>(
            { current, pageSize, search, filter },
            workOrderPage,
          );
        }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default WorkOrder;
