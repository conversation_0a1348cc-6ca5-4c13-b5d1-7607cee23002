import ChangeLog from '@/components/ChangeLog';
import DocumentUploadModule from '@/components/DocumentUploadModule';
import RKPageHeader from '@/components/RKPageHeader';
import RKPageLoading from '@/components/RKPageLoading';
import BaseContext from '@/Context/BaseContext';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { usePartnerList } from '@/hooks/usePartnerList';
import { useUserList } from '@/hooks/useUserList';
import Project from '@/pages/CRM/Customer/components/Project';
import CreateModal from '@/pages/Project/components/CreateModal';
import {
  createMainContract,
  getMainConById,
  mainProjectListSq,
  updateMainContract,
} from '@/services/oa/contract';
import { onSuccessAndGoBack, onSuccessAndRefresh } from '@/utils';
import { useModel } from '@@/exports';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDependency,
  ProFormInstance,
} from '@ant-design/pro-components';
import { useAccess, useLocation, useRequest } from '@umijs/max';
import { Collapse, Dropdown } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import BaseInfo from './BaseInfo';
import BillingInformation from './BillingInformation';
import CollectionPlan from './CollectionPlan';
import ContractContentTable from './ContractContentTable';
import ContractFinanceDetails from './ContractFinanceDetails';
import PMTable from './PMTable';
import PurchaseContractStatistics from './PurchaseContractStatistics';
import PurchaseContractTable from './PurchaseContractTable';
import ReceiveBank from './ReceiveBank';

// 创建一个上下文
const CustomerDetails: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  const {
    canEditMasterContract,
    canAddMasterContract,
    canSuperMasterContract,
    canAddSalesProject = false,
    canAddAfterSalesProject = false,
    canAddDevelopProject = false,
  } = useAccess();
  const canEdit =
    (canEditMasterContract && isEditPage) ||
    (!isEditPage && canAddMasterContract) ||
    canSuperMasterContract;
  const { userList, loading: userLoading } = useUserList();
  const { partnerList, loading: partnerLoading } = usePartnerList();
  const { approvalDetails } = useModel('useApprovalModel');
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const codeRef = useRef(0);
  const [visibleSHCreateProjectModal, setCreateSHProjectModalVisible] = useState(false);
  const [visibleXSCreateProjectModal, setCreateXSProjectModalVisible] = useState(false);
  const [visibleKFCreateProjectModal, setCreateKFProjectModalVisible] = useState(false);

  const { pathname } = useLocation();
  const isDetailPage = pathname.includes('/contract/main/details/');
  // 判断是否为审批页面
  const isApprovalPage = pathname.includes('/approval/');
  const isAddPage = pathname.includes('/add');
  const formRef = useRef<ProFormInstance>();

  const onSave = useCallback(() => {
    return new Promise((resolve) => {
      codeRef.current = 0;
      formRef.current?.submit();

      const checkFormInterval = setInterval(() => {
        formRef.current
          ?.validateFields()
          .then(() => {
            const isFormSubmitted = codeRef.current === 200;
            if (isFormSubmitted) {
              clearInterval(checkFormInterval); // 清除轮询
              resolve(200); // 表单提交成功，resolve Promise
            }
          })
          .catch(() => {
            resolve(500);
            clearInterval(checkFormInterval); // 清除轮询
          });
      }, 1000); // 每秒检查一次表单是否提交成功
    });
  }, []);

  // 根据最终客户id获取售前项目
  const {
    data: sqProjectList,
    loading: sqProjectListLoading,
    run: fetchProjectByEndCusId,
  } = useRequest((id) => mainProjectListSq({ id }), {
    manual: true,
  });

  const {
    data,
    refresh,
    loading: pageLoading,
  } = useRequest(
    () =>
      getMainConById({
        idReq: { id: id as string },
      }),
    {
      ready: isEditPage && !isApprovalPage,
      onSuccess: (res) => {
        fetchProjectByEndCusId(res?.mainContractInfo?.endUserId);
        setTimeout(() => formRef.current?.setFieldsValue(res), 100);
      },
    },
  );

  // 新建
  const { run: add, loading: addLoading } = useRequest((value) => createMainContract(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 修改
  const { run: update, loading: editLoading } = useRequest((value) => updateMainContract(value), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      codeRef.current = res.code;
      onSuccessAndRefresh(res, refresh);
    },
    formatResult: (res) => res,
  });

  useEffect(() => {
    if (isApprovalPage && formRef.current && formRef.current?.setFieldsValue)
      formRef.current.setFieldsValue(approvalDetails?.fromData);
  }, [isApprovalPage, approvalDetails]);
  // 是否提交审批
  const isSubmit = !['1', '3', '4'].includes(data?.mainContractInfo?.activiStatus || '');
  // 只有状态为通过的合同才能新增项目
  const canAddProject = data?.mainContractInfo?.activiStatus === '2';

  return (
    <PageContainer header={{ title: true }} className="detail-container">
      <RKPageLoading loading={pageLoading} />
      <ProForm<API.MainContractResp | undefined>
        formRef={formRef}
        initialValues={{
          mainContractInfo: {
            contractStatus: 'EXECUTING',
            firstParty: 'CLIENT',
            secondParty: 'CLIENT',
          },
        }}
        disabled={
          !isSubmit ||
          isApprovalPage ||
          !canEdit ||
          isDetailPage ||
          (isEditPage &&
            ['END', 'STOP'].includes(data?.mainContractInfo?.contractStatus || '') &&
            !canSuperMasterContract)
        }
        submitter={
          isSubmit &&
          !isApprovalPage &&
          canEdit && {
            searchConfig: {
              submitText: '保存',
              resetText: '取消',
            },
            onReset: () => {
              history.go(-1);
            },

            render: (props, doms) => {
              return <FooterToolbar>{doms}</FooterToolbar>;
            },
            submitButtonProps: {
              loading: addLoading || editLoading,
            },
          }
        }
        onFinish={async (values) => {
          if (isEditPage) {
            update(values);
          } else {
            add(values);
          }
        }}
        onValuesChange={(changedValues: API.MainContractResp, values?: API.MainContractResp) => {
          if (changedValues.mainContractInfo?.firstParty) {
            formRef.current?.setFieldValue(['mainContractInfo', 'fpId'], null);
            formRef.current?.setFieldValue(['mainContractInfo', 'endUserId'], null);
          }
          // 甲方更改带出销售 并清空项目
          if (changedValues.mainContractInfo?.fpId) {
            const current = partnerList.find(
              (item) => item.id === changedValues.mainContractInfo?.fpId,
            );

            formRef.current?.setFieldValue(
              ['mainContractInfo', 'salePersonId'],
              current?.clientMangerId,
            );
            formRef.current?.setFieldValue(
              ['mainContractInfo', 'customerAbbreviation'],
              current?.clientAbbreviation,
            );
          }

          //最终用户带出客户简称
          if (changedValues.mainContractInfo?.endUserId) {
            const customer = partnerList.find(
              (item) => item.id === changedValues.mainContractInfo?.endUserId,
            );
            formRef.current?.setFieldValue(
              ['mainContractInfo', 'customerAbbreviation'],
              customer?.clientAbbreviation,
            );

            fetchProjectByEndCusId(changedValues.mainContractInfo?.endUserId);
          }

          // 框架协议，根据计划自动计算合同金额
          if (changedValues.collectionPlanList && values?.mainContractInfo?.isFrameworkAgreement) {
            const total = changedValues.collectionPlanList.reduce(
              (total: number, item: API.CollectionPlanReq) => total + (item.estimateReAmount || 0),
              0,
            );
            formRef.current?.setFieldValue(['mainContractInfo', 'contractAmount'], total);
          }
        }}
      >
        {isEditPage && (
          <ProFormDependency name={['mainContractInfo']}>
            {({ mainContractInfo }) => {
              return (
                <RKPageHeader
                  id={id}
                  status={
                    isApprovalPage
                      ? (approvalDetails?.activiStatus as unknown as string) || '9'
                      : mainContractInfo?.activiStatus
                  }
                  title={mainContractInfo?.contractNumber}
                  approveType="MAIN_CONTRACT_APPROVAL"
                  onOperationCallback={refresh}
                  onSave={onSave}
                  saveDisabled={!canEditMasterContract}
                />
              );
            }}
          </ProFormDependency>
        )}
        <BaseContext.Provider
          value={{
            userList,
            userLoading,
            partnerList,
            partnerLoading,
            sqProjectList,
            sqProjectListLoading,
          }}
        >
          <Collapse defaultActiveKey={['1', '2', '3', '4', '5', '6', '7', '8', '9']} ghost>
            <Collapse.Panel key="1" header="主合同明细" collapsible="header">
              <BaseInfo />
            </Collapse.Panel>
            <Collapse.Panel key="9" header="主合同收支明细" collapsible="header">
              <ContractFinanceDetails
                mainContractInfo={data?.mainContractInfo}
                collectionInfo={data?.collectionInfo}
                purConInfo={{
                  paidAmount: data?.purConDetailList?.reduce(
                    (total: number, record: API.PurConDetailResp) =>
                      total + (Number(record?.payedAmount) || 0),
                    0,
                  ),
                  receivedTicketAmount: data?.purConDetailList?.reduce(
                    (total: number, record: API.PurConDetailResp) =>
                      total + (Number(record?.receivedAmount) || 0),
                    0,
                  ),
                }}
              />
            </Collapse.Panel>
            <Collapse.Panel key="2" header="开票信息" collapsible="header">
              <BillingInformation />
            </Collapse.Panel>
            <Collapse.Panel key="3" header="收款银行信息" collapsible="header">
              <ReceiveBank formRef={formRef} isAddPage={isAddPage} />
            </Collapse.Panel>
            <Collapse.Panel key="4" header="合同执行情况描述" collapsible="header">
              <ContractContentTable />
            </Collapse.Panel>
            <Collapse.Panel key="5" header="项目经理" collapsible="header">
              <PMTable name="proMangerList" />
            </Collapse.Panel>
            <Collapse.Panel key="6" header="收款计划" collapsible="header">
              <CollectionPlan
                disabled={
                  isEditPage &&
                  (['END', 'STOP'].includes(data?.mainContractInfo?.contractStatus || '') ||
                    data?.mainContractInfo?.activiStatus !== '2')
                }
              />
            </Collapse.Panel>
            {isEditPage && (
              <Collapse.Panel key="7" header="采购合同信息">
                <ProFormDependency name={['purConDetailList']}>
                  {({ purConDetailList }) => {
                    //付款总金额
                    const payTotalAmount = purConDetailList?.reduce(
                      (total: number, record: API.PurConDetailResp) =>
                        total + (Number(record?.contractAmount) || 0),
                      0,
                    );
                    //已付款金额
                    const paidAmount = purConDetailList?.reduce(
                      (total: number, record: API.PurConDetailResp) =>
                        total + (Number(record?.payedAmount) || 0),
                      0,
                    );
                    //已收票金额
                    const receivedTicketAmount = purConDetailList?.reduce(
                      (total: number, record: API.PurConDetailResp) =>
                        total + (Number(record?.receivedAmount) || 0),
                      0,
                    );
                    //未付款金额
                    const unpaidAmount = purConDetailList?.reduce(
                      (total: number, record: API.PurConDetailResp) =>
                        total + (Number(record?.awaitPayAmount) || 0),
                      0,
                    );
                    return (
                      <ProForm.Item name="purConInfo">
                        <PurchaseContractStatistics
                          value={{ payTotalAmount, paidAmount, receivedTicketAmount, unpaidAmount }}
                        />
                      </ProForm.Item>
                    );
                  }}
                </ProFormDependency>

                <ProForm.Item name="purConDetailList">
                  <PurchaseContractTable />
                </ProForm.Item>
              </Collapse.Panel>
            )}
            <Collapse.Panel
              key="8"
              header="项目列表"
              extra={
                <Dropdown.Button
                  type="primary"
                  disabled={!canAddProject}
                  menu={{
                    items: [
                      ...(canAddSalesProject ? [{ key: 'XS', label: '销售项目' }] : []),
                      ...(canAddAfterSalesProject ? [{ key: 'SH', label: '售后项目' }] : []),
                      ...(canAddDevelopProject ? [{ key: 'KF', label: '开发项目' }] : []),
                    ],
                    onClick: (e) => {
                      switch (e.key) {
                        case 'XS':
                          setCreateXSProjectModalVisible(true);
                          break;
                        case 'SH':
                          setCreateSHProjectModalVisible(true);
                          break;
                        case 'KF':
                          setCreateKFProjectModalVisible(true);
                          break;
                        default:
                          return;
                      }
                    },
                  }}
                >
                  新建
                </Dropdown.Button>
              }
              collapsible="header"
            >
              <ProForm.Item name={['mainContractInfo', 'proInfoList']}>
                <Project />
              </ProForm.Item>
            </Collapse.Panel>
          </Collapse>
        </BaseContext.Provider>
        {isEditPage && (
          <ProFormDependency name={['mainContractInfo']}>
            {({ mainContractInfo }) => (
              <DocumentUploadModule
                documentFiles={data?.documentFiles}
                fileType="MAIN_CONTRACT"
                id={id}
                showUploadButton={mainContractInfo?.activiStatus === '2'}
                canUpload={mainContractInfo?.businessAssistantId === currentUser?.id}
                title="文档上传"
                onUploadSuccess={refresh}
              />
            )}
          </ProFormDependency>
        )}
        <ChangeLog />
      </ProForm>
      {/* 创建销售项目 */}
      <CreateModal
        initialValues={data?.mainContractInfo || {}}
        type="XS"
        visible={visibleXSCreateProjectModal}
        setVisible={setCreateXSProjectModalVisible}
      />
      {/* 创建售后项目 */}
      <CreateModal
        initialValues={data?.mainContractInfo || {}}
        type="SH"
        visible={visibleSHCreateProjectModal}
        setVisible={setCreateSHProjectModalVisible}
      />
      {/* 创建开发项目 */}
      <CreateModal
        initialValues={data?.mainContractInfo || {}}
        type="KF"
        visible={visibleKFCreateProjectModal}
        setVisible={setCreateKFProjectModalVisible}
      />
    </PageContainer>
  );
};

export default withRouteEditing(CustomerDetails);
