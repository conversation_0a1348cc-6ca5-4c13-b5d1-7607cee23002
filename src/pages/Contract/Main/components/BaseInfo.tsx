import RKCol from '@/components/RKCol';
import TitleLink from '@/components/TitleLink';
import BaseContext from '@/Context/BaseContext';
import {
  CONTRACT_CATEGORY,
  CONTRACT_CHARACTER,
  CONTRACT_SERVICES_CATEGORY,
  CONTRACT_STATUS,
  INDUSTRY,
  SIGN_STATUS,
} from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { requiredRule } from '@/utils/setting';
import {
  ProFormDatePicker,
  ProFormDependency,
  ProFormMoney,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Row } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import { memo, useContext } from 'react';

const BaseInfo: React.FC<WithRouteEditingProps> = ({ isEditPage }) => {
  const { userList = [], userLoading, partnerList = [], partnerLoading } = useContext(BaseContext);

  const customerList = partnerList.filter(
    (item: API.PartnerInfoResp) => item?.partnerType === 'CLIENT',
  );

  return (
    <>
      <div className="rk-none">
        <ProFormText name={['mainContractInfo', 'id']} />
      </div>
      <Row gutter={24}>
        {isEditPage && (
          <RKCol>
            <ProFormText
              name={['mainContractInfo', 'contractNumber']}
              label="合同编号"
              disabled={isEditPage}
            />
          </RKCol>
        )}
        <RKCol>
          <ProFormTextArea
            name={['mainContractInfo', 'contractName']}
            label="合同名称"
            rules={[requiredRule]}
            fieldProps={{
              autoSize: {
                minRows: 1,
                maxRows: 3,
              },
            }}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name={['mainContractInfo', 'contractCategory']}
            label="合同类别"
            rules={[requiredRule]}
            options={CONTRACT_CATEGORY}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name={['mainContractInfo', 'serveCategory']}
            label="服务类别"
            rules={[requiredRule]}
            options={CONTRACT_SERVICES_CATEGORY}
          />
        </RKCol>

        <RKCol>
          <ProFormSelect
            name={['mainContractInfo', 'contractQuality']}
            label="合同性质"
            rules={[requiredRule]}
            options={CONTRACT_CHARACTER}
          />
        </RKCol>

        <ProFormDependency name={[['mainContractInfo', 'fpId']]}>
          {({ mainContractInfo }) => {
            return (
              <RKCol>
                <ProFormSelect
                  label={
                    <TitleLink
                      path={
                        mainContractInfo?.fpId && `/crm/customer/edit/${mainContractInfo?.fpId}`
                      }
                    >
                      甲方
                    </TitleLink>
                  }
                  name={['mainContractInfo', 'fpId']}
                  rules={[requiredRule]}
                  fieldProps={{
                    fieldNames: {
                      label: 'clientName',
                      value: 'id',
                    },
                    showSearch: true,
                    loading: partnerLoading,
                  }}
                  allowClear={false}
                  options={customerList as DefaultOptionType[]}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>

        <ProFormDependency name={[['mainContractInfo', 'spId']]}>
          {({ mainContractInfo }) => {
            return (
              <RKCol>
                <ProFormSelect
                  label={
                    <TitleLink
                      path={
                        mainContractInfo?.spId && `/crm/customer/edit/${mainContractInfo?.spId}`
                      }
                    >
                      乙方
                    </TitleLink>
                  }
                  name={['mainContractInfo', 'spId']}
                  rules={[requiredRule]}
                  allowClear={false}
                  fieldProps={{
                    fieldNames: {
                      label: 'clientName',
                      value: 'id',
                    },
                    showSearch: true,
                    loading: partnerLoading,
                  }}
                  options={customerList as DefaultOptionType[]}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>
        <ProFormDependency name={[['mainContractInfo', 'endUserId']]}>
          {({ mainContractInfo }) => {
            return (
              <RKCol>
                <ProFormSelect
                  label={
                    <TitleLink
                      path={
                        mainContractInfo?.endUserId &&
                        `/crm/customer/edit/${mainContractInfo?.endUserId}`
                      }
                    >
                      最终用户
                    </TitleLink>
                  }
                  name={['mainContractInfo', 'endUserId']}
                  rules={[requiredRule]}
                  fieldProps={{
                    fieldNames: {
                      label: 'clientName',
                      value: 'id',
                    },
                    showSearch: true,
                    loading: partnerLoading,
                  }}
                  options={customerList as DefaultOptionType[]}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>

        <RKCol>
          <ProFormSelect
            name={['mainContractInfo', 'industry']}
            label="行业"
            rules={[requiredRule]}
            options={INDUSTRY}
          />
        </RKCol>

        <RKCol>
          <ProFormSelect
            name={['mainContractInfo', 'salePersonId']}
            label="销售"
            rules={[requiredRule]}
            fieldProps={{
              loading: userLoading,
              fieldNames: {
                value: 'id',
                label: 'username',
              },
              showSearch: true,
            }}
            options={userList as DefaultOptionType[]}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name={['mainContractInfo', 'businessAssistantId']}
            label="商务助理"
            rules={[requiredRule]}
            fieldProps={{
              loading: userLoading,
              fieldNames: {
                value: 'id',
                label: 'username',
              },
              showSearch: true,
            }}
            options={userList as DefaultOptionType[]}
          />
        </RKCol>
        {/* <ProFormDependency
          name={[
            ['mainContractInfo', 'projectId'],
            ['mainContractInfo', 'salePersonId'],
          ]}
        >
          {({ mainContractInfo }) => {
            return (
              <RKCol>
                <ProFormSelect
                  name={['mainContractInfo', 'projectNumber']}
                  label={
                    <TitleLink
                      path={
                        mainContractInfo?.projectId &&
                        `/project/pre-sales/edit/${mainContractInfo?.projectId}`
                      }
                    >
                      售前项目编号
                    </TitleLink>
                  }
                  fieldProps={{
                    loading: sqProjectListLoading,
                    showSearch: true,
                    optionLabelProp: 'projectNumber',
                    filterOption: (inputValue, option) => {
                      return option?.keywords.indexOf(inputValue) >= 0;
                    },
                  }}
                  options={sqProjectList?.map((item: API.ProjectNameIdResp) => {
                    return {
                      value: item.projectNumber,
                      label: <RKSelectLabel title={item.projectNumber} info={item.projectName} />,
                      projectNumber: item?.projectNumber,
                      keywords: `${item.projectName}${item.projectNumber}`,
                    };
                  })}
                />
              </RKCol>
            );
          }}
        </ProFormDependency> */}
        <RKCol>
          <ProFormMoney
            name={['mainContractInfo', 'contractAmount']}
            label="合同金额"
            locale="zh-CN"
            rules={[requiredRule]}
            min={0}
          />
        </RKCol>
        <RKCol>
          <ProFormSwitch
            name={['mainContractInfo', 'isFrameworkAgreement']}
            label="框架协议"
            getValueFromEvent={(val) => (val ? 1 : 0)}
            getValueProps={(value) => ({ checked: value === 1 })}
          />
        </RKCol>

        <RKCol>
          <ProFormSelect
            name={['mainContractInfo', 'contractStatus']}
            label="合同状态"
            rules={[requiredRule]}
            options={CONTRACT_STATUS}
          />
        </RKCol>
        <ProFormDependency name={[['mainContractInfo', 'contractStatus']]}>
          {({ mainContractInfo }) => {
            if (mainContractInfo?.contractStatus === 'STOP')
              return (
                <RKCol>
                  <ProFormDatePicker
                    rules={[requiredRule]}
                    label="终止日期"
                    name={['mainContractInfo', 'actuallyStopTime']}
                  />
                </RKCol>
              );
            if (mainContractInfo?.contractStatus === 'END')
              return (
                <RKCol>
                  <ProFormDatePicker
                    rules={[requiredRule]}
                    label="结束日期"
                    name={['mainContractInfo', 'actuallyEndTime']}
                  />
                </RKCol>
              );
          }}
        </ProFormDependency>
        <RKCol>
          <ProFormText
            name={['mainContractInfo', 'contractAddress']}
            label="合同所在地"
            rules={[requiredRule]}
          />
        </RKCol>

        <RKCol>
          <ProFormText label="客户简称" name={['mainContractInfo', 'customerAbbreviation']} />
        </RKCol>
        <RKCol>
          <ProFormDatePicker
            label="开始日期"
            name={['mainContractInfo', 'startTime']}
            rules={[requiredRule]}
          />
        </RKCol>
        <RKCol>
          <ProFormDatePicker
            label="计划结束日期"
            name={['mainContractInfo', 'endTime']}
            rules={[requiredRule]}
          />
        </RKCol>
        <RKCol>
          <ProFormDatePicker
            label="签订日期"
            name={['mainContractInfo', 'signDate']}
            rules={[requiredRule]}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name={['mainContractInfo', 'signStatus']}
            label="签订状态"
            options={SIGN_STATUS}
          />
        </RKCol>
        <RKCol lg={24} md={24} sm={24}>
          <ProFormTextArea
            name={['mainContractInfo', 'overview']}
            label="内容概述"
            fieldProps={{
              autoSize: {
                minRows: 2,
                maxRows: 4,
              },
            }}
          />
        </RKCol>
      </Row>
    </>
  );
};
export default memo(withRouteEditing(BaseInfo));
