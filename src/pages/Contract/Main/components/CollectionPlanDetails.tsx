import { COLLECTION_STATUS } from '@/enums';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, history, useAccess, useLocation } from '@umijs/max';
import { Button, Popconfirm } from 'antd';
import React from 'react';

const columns: ProColumns<API.CollectionPlanReq>[] = [
  {
    title: '期次',
    width: 50,
    dataIndex: 'index',
    valueType: 'index',
    render: (dom, entity) => {
      if (entity.period) return entity.period;
      return dom;
    },
  },
  {
    title: '计划收款金额',
    dataIndex: 'estimateReAmount',
    valueType: 'money',
  },
  {
    title: '计划收款日期',
    dataIndex: 'estimateReTime',
  },
  {
    title: '收款状态',
    dataIndex: 'status',
    valueEnum: option2enum(COLLECTION_STATUS),
  },
  {
    title: '开票金额',
    dataIndex: 'ticketAmount',
    valueType: 'money',
  },
  {
    title: '开票日期',
    dataIndex: 'ticketTime',
  },
  {
    title: '开票记录号',
    dataIndex: 'invoicingNumber',
    render(dom, entity) {
      return (
        <a onClick={() => history.push(`/finance/invoice/invoicing-list/${entity.id}`)}>{dom}</a>
      );
    },
  },
  {
    title: '到款金额',
    dataIndex: 'receiveAmount',
    valueType: 'money',
  },
  {
    title: '到款日期',
    dataIndex: 'receiveTime',
  },
  {
    title: '认领日期',
    dataIndex: 'claimTime',
  },
  {
    title: '收款记录号',
    dataIndex: 'collectNumber',
    render(dom, entity) {
      if (!entity.receiptId) return '-';
      return (
        <a onClick={() => history.push(`/finance/JR/receipt-list/details/${entity.receiptId}`)}>
          {dom}
        </a>
      );
    },
  },
];
const CollectionPlanDetails: React.FC<{
  value?: API.CollectionPlanReq[];
  onChange?: (value: API.CollectionPlanReq[]) => void;
  disabled?: boolean;
}> = ({ value, disabled }) => {
  const { canAddInvoiceTransactionRecord = false } = useAccess();
  const { pathname } = useLocation();
  const isDetailPage = pathname.includes('/contract/main/details/');
  const operateColumn: ProColumns<API.CollectionPlanReq>[] = isDetailPage
    ? []
    : [
        {
          title: '操作',
          fixed: 'right',
          key: 'option',
          width: 100,
          valueType: 'option',
          align: 'center',
          render: (text, record) => {
            const { id, invoicingNumber, contractId } = record;
            return (
              <Access accessible={canAddInvoiceTransactionRecord}>
                <Popconfirm
                  title="请选择"
                  description="请选择单票或全票："
                  onConfirm={() => {
                    history.push(`/contract/main/apply/${id}`, { isAll: 'true' });
                  }}
                  onCancel={() => {
                    history.push(`/contract/main/apply/${id}`, { isAll: 'false' });
                  }}
                  okText="全票"
                  cancelText="单票"
                  placement="topLeft"
                  disabled={!!invoicingNumber || !contractId || disabled}
                >
                  <Button
                    className="inner-table-link"
                    type="link"
                    disabled={!!invoicingNumber || !contractId || disabled}
                  >
                    开票申请
                  </Button>
                </Popconfirm>
              </Access>
            );
          },
        },
      ];
  return (
    <ProTable<API.CollectionPlanReq>
      {...defaultTableConfig}
      dataSource={value}
      options={false}
      className="inner-table"
      columns={[...columns, ...operateColumn]}
      headerTitle="收款计划详细信息表"
    />
  );
};

export default CollectionPlanDetails;
