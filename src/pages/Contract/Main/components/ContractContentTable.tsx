import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import { getRandomId } from '@/utils';
import { ProColumns, ProForm } from '@ant-design/pro-components';
import { useLocation } from '@umijs/max';
const columns: ProColumns<Record<string, any>>[] = [
  {
    title: '日期',
    width: 180,
    dataIndex: 'addTime',
    valueType: 'date',
  },
  {
    title: '内容',
    dataIndex: 'content',
    valueType: 'textarea',
    fieldProps: {
      autoSize: {
        minRows: 1,
        maxRows: 2,
      },
    },
  },
];

export default function ContractContentTable() {
  const { pathname } = useLocation();
  const isDetailPage = pathname.includes('/contract/main/details/');

  return (
    <ProForm.Item
      name="contractContentList"
      getValueProps={(val) => ({
        value: val?.map((item: Record<string, any>) => ({
          ...item,
          key_: item.key_ || getRandomId(),
        })),
      })}
    >
      <RKFormEditableProTable columns={columns} readonly={isDetailPage} />
    </ProForm.Item>
  );
}
