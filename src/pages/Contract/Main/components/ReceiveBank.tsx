import RKCol from '@/components/RKCol';
import { getInvoicingBankOption } from '@/services/oa/dictionary';
import { requiredRule } from '@/utils/setting';
import {
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Row } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import React, { useEffect } from 'react';

const ReceiveBank: React.FC<{
  formRef: React.MutableRefObject<ProFormInstance<any> | undefined>;
  isAddPage?: boolean;
}> = ({ formRef, isAddPage = false }) => {
  //开票银行
  const { data: InvoiceBankList = [], loading: InvoiceBankLoading } = useRequest(() =>
    getInvoicingBankOption(),
  );

  // 新建主合同时自动回填 hasDefault 为 true 的银行信息
  useEffect(() => {
    if (isAddPage && InvoiceBankList.length > 0) {
      const defaultBank = InvoiceBankList.find((item: any) => item.hasDefault);
      if (defaultBank) {
        formRef.current?.setFieldsValue({
          collectionBankInfo: {
            collectionBank: defaultBank.bankAs,
            institutionName: defaultBank.institutionName || '',
            taxpayerNum: defaultBank.taxpayerNum || '',
            accountAndBank: `${defaultBank.bank || ''}${defaultBank.account || ''}`,
            contact: `${defaultBank.registeredAddress || ''}${defaultBank.contactNumber || ''}`,
          },
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAddPage, InvoiceBankList]);

  return (
    <Row gutter={24}>
      <RKCol>
        <ProFormSelect
          name={['collectionBankInfo', 'collectionBank']}
          label="收款银行别名"
          rules={[requiredRule]}
          fieldProps={{
            fieldNames: {
              value: 'bankAs',
              label: 'bankAs',
            },
            showSearch: true,
            loading: InvoiceBankLoading,
            optionLabelProp: 'bankAs',
            onChange: (val, option) => {
              const {
                institutionName = '',
                taxpayerNum = '',
                bank = '',
                account = '',
                contactNumber = '',
                registeredAddress = '',
              } = (option as API.BanksDto) || {};
              formRef.current?.setFieldsValue({
                collectionBankInfo: {
                  institutionName,
                  taxpayerNum,
                  accountAndBank: `${bank}${account}`,
                  contact: `${registeredAddress}${contactNumber}`,
                },
              });
            },
          }}
          options={InvoiceBankList as DefaultOptionType[]}
        />
      </RKCol>
      <RKCol>
        <ProFormText
          name={['collectionBankInfo', 'institutionName']}
          label="名称"
          rules={[requiredRule]}
          disabled
        />
      </RKCol>
      <RKCol>
        <ProFormText
          name={['collectionBankInfo', 'taxpayerNum']}
          label="纳税人识别号"
          rules={[requiredRule]}
          disabled
        />
      </RKCol>
      <RKCol lg={12} md={16} sm={24}>
        <ProFormTextArea
          name={['collectionBankInfo', 'contact']}
          label="地址、电话"
          rules={[requiredRule]}
          disabled
          fieldProps={{
            autoSize: {
              minRows: 1,
              maxRows: 2,
            },
          }}
        />
      </RKCol>
      <RKCol lg={12} md={16} sm={24}>
        <ProFormTextArea
          name={['collectionBankInfo', 'accountAndBank']}
          label="开户行及账号"
          rules={[requiredRule]}
          disabled
          fieldProps={{
            autoSize: {
              minRows: 1,
              maxRows: 2,
            },
          }}
        />
      </RKCol>
    </Row>
  );
};

export default ReceiveBank;
