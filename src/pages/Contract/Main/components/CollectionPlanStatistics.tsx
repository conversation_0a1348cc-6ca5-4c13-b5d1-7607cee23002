import { StatisticCard } from '@ant-design/pro-components';

const CollectionPlanStatistics: React.FC<{
  value?: API.CollectionInfoResp;
  onChange?: (val: API.CollectionInfoResp) => void;
}> = ({ value = {} }) => {
  const {
    contractAmount,
    billedAmount,
    receivedAmount,
    billTotalAmount,
    awaitAmount,
    awaitReAmount,
  } = value || {};
  return (
    <StatisticCard.Group className="rklink-statistic">
      <StatisticCard
        statistic={{
          title: '合同总金额',
          prefix: '¥',
          value: contractAmount || 0,
        }}
      />
      <StatisticCard
        statistic={{
          title: '开票总金额',
          prefix: '¥',
          value: billTotalAmount || 0,
          status: 'success',
        }}
      />
      <StatisticCard
        statistic={{
          title: '已开票金额',
          prefix: '¥',
          value: billedAmount || 0,
          status: 'success',
        }}
      />
      <StatisticCard
        statistic={{
          prefix: '¥',
          title: '已收款金额',
          value: receivedAmount || 0,
          status: 'success',
        }}
      />
      <StatisticCard
        statistic={{
          prefix: '¥',
          title: '待开票金额',
          value: awaitAmount || 0,
          status: 'processing',
        }}
      />
      <StatisticCard
        statistic={{
          prefix: '¥',
          title: '待收款金额',
          value: awaitReAmount || 0,
          status: 'processing',
        }}
      />
    </StatisticCard.Group>
  );
};

export default CollectionPlanStatistics;
