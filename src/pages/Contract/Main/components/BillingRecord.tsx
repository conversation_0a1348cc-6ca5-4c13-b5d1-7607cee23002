import { BILL_TYPE, INVOICE_TYPE, TICKET_STATUS } from '@/enums';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';

const columns: ProColumns<API.InvoicingResp>[] = [
  {
    title: '开票记录号',
    dataIndex: 'invoicingNumber',
    render(dom, entity) {
      return (
        <a onClick={() => history.push(`/finance/invoice/invoicing-list/${entity.id}`)}>{dom}</a>
      );
    },
  },
  {
    title: '申请日期',
    dataIndex: 'applicationTime',
  },
  {
    title: '开票日期',
    dataIndex: 'ticketTime',
  },
  {
    title: '发票类型',
    dataIndex: 'ticketType',
    valueEnum: option2enum(INVOICE_TYPE),
  },
  {
    title: '票据类型',
    dataIndex: 'billType',
    valueEnum: option2enum(BILL_TYPE),
  },
  {
    title: '金额',
    dataIndex: 'ticketAmount',
    valueType: 'money',
  },
  {
    title: '税额',
    dataIndex: 'rateAmount',
    valueType: 'money',
  },
  {
    title: '状态',
    dataIndex: 'status',
    valueEnum: option2enum(TICKET_STATUS),
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
];
const BillingRecord: React.FC<{
  value?: API.InvoicingResp[];
  onChange?: (value: API.InvoicingResp[]) => void;
}> = ({ value }) => {
  return (
    <ProTable<API.InvoicingResp>
      {...defaultTableConfig}
      dataSource={value}
      options={false}
      className="inner-table"
      columns={columns}
      headerTitle="开票记录表"
    />
  );
};

export default BillingRecord;
