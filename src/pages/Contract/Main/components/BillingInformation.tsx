import RKCol from '@/components/RKCol';
import { requiredRule } from '@/utils/setting';
import { ProFormText } from '@ant-design/pro-components';
import { Row } from 'antd';

export default function BillingInformation() {
  return (
    <Row gutter={24}>
      <div className="rk-none">
        <ProFormText name={['ticketInfo', 'id']} />
      </div>
      <RKCol lg={8} md={8} sm={12}>
        <ProFormText
          label="单位名称"
          rules={[requiredRule]}
          name={['ticketInfo', 'institutionName']}
        />
      </RKCol>
      <RKCol lg={8} md={8} sm={12}>
        <ProFormText
          label="纳税人识别号"
          rules={[requiredRule]}
          name={['ticketInfo', 'taxpayerNum']}
        />
      </RKCol>
      <RKCol lg={8} md={8} sm={12}>
        <ProFormText label="开户银行" rules={[requiredRule]} name={['ticketInfo', 'bank']} />
      </RKCol>
      <RKCol lg={8} md={8} sm={12}>
        <ProFormText label="银行账号" rules={[requiredRule]} name={['ticketInfo', 'account']} />
      </RKCol>
      <RKCol lg={8} md={8} sm={12}>
        <ProFormText
          label="联系电话"
          rules={[requiredRule]}
          name={['ticketInfo', 'contactNumber']}
        />
      </RKCol>
      <RKCol lg={8} md={8} sm={12}>
        <ProFormText
          label="注册地址"
          rules={[requiredRule]}
          name={['ticketInfo', 'registeredAddress']}
        />
      </RKCol>
    </Row>
  );
}
