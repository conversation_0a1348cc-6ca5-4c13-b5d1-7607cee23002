import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import { calcTaxRate, getRandomId } from '@/utils';
import { ProColumns, ProForm, ProFormDependency } from '@ant-design/pro-components';
import { useLocation } from '@umijs/max';
import dayjs from 'dayjs';
import { useRef } from 'react';
import BillingRecord from './BillingRecord';
import CollectionPlanDetails from './CollectionPlanDetails';
import CollectionPlanStatistics from './CollectionPlanStatistics';

type RowProps = API.CollectionPlanReq & { key_: string };
const dateFormat = 'YYYY-MM-DD';
export const renderTaxRate = (row: RowProps) => {
  const { key_, estimateReAmount = 0, rate = 0, estimateReTime } = row || {};
  const { taxExclusiveAmount, taxInclusiveAmount } = calcTaxRate(estimateReAmount, rate);

  return {
    ...row,
    key_: key_ || getRandomId(),
    estimateReTime: estimateReTime ? dayjs(estimateReTime).format(dateFormat) : null,
    //  税额 = 不含税金额 * 税率
    rateAmount: taxInclusiveAmount,
    // 不含税金额 = 含税金额 / (1+税率）
    excludeRaAmount: taxExclusiveAmount,
    disabledDelete: row.operateStatus === 'SUBMIT',
  };
};
// 如果已经收款了 或者有开票申请了 不允许修改
const getDisabled = (row: API.CollectionPlanResp) => {
  const { invoicingNumber, collectionStatus } = row || {};
  return collectionStatus === 'COLLECT' || invoicingNumber;
};

export default function CollectionPlan({ disabled }: { disabled?: boolean }) {
  const { pathname } = useLocation();
  const isDetailPage = pathname.includes('/contract/main/details/');
  const totalRef = useRef(0);
  const currentTotalRef = useRef(0);
  const isFrameworkAgreementRef = useRef();

  const columns: ProColumns<API.CollectionPlanResp>[] = [
    {
      title: '期次',
      width: 50,
      dataIndex: 'index',
      valueType: 'index',
      renderText: (text, entity) => {
        if (entity.period) return entity.period - 1;
        return text;
      },
    },
    {
      title: '预计收款时间',
      width: 150,
      dataIndex: 'estimateReTime',
      valueType: 'date',
      fieldProps: (form, { rowKey }) => ({
        disabled: getDisabled(form?.getFieldValue([rowKey || ''])),
      }),
    },
    {
      title: '预计收款金额',
      width: 150,
      dataIndex: 'estimateReAmount',
      valueType: 'digit',
      fieldProps: (form, { rowKey }) => ({
        prefix: '¥',
        style: { width: '100%' },
        precision: 2,
        min: 0,
        max: !isFrameworkAgreementRef.current
          ? (
              totalRef.current -
              currentTotalRef.current +
              form?.getFieldValue([rowKey || ''])?.estimateReAmount
            ).toFixed(2)
          : undefined,
        onChange: (value: number) => {
          const allData: Record<string, API.CollectionPlanResp> = form.getFieldsValue() || {};
          // 计算 estimateReAmount 的总额
          const totalEstimateReAmount = Object.values(allData).reduce((total, entry) => {
            return total + (entry?.estimateReAmount || 0);
          }, 0);

          const rowData = form.getFieldValue([rowKey || '']);
          if (totalEstimateReAmount >= totalRef.current && !isFrameworkAgreementRef.current) {
            rowData.estimateReAmount = value - (totalEstimateReAmount - totalRef.current);
          }
          const updateRow = renderTaxRate(rowData as RowProps);
          form.setFieldValue([rowKey || ''], updateRow);
        },
        disabled: getDisabled(form?.getFieldValue([rowKey || ''])),
      }),
    },
    {
      title: '支付条件',
      width: 200,
      dataIndex: 'payCondition',
      valueType: 'textarea',
      fieldProps: {
        autoSize: {
          minRows: 1,
          maxRows: 3,
        },
      },
    },
    {
      title: '税率',
      width: 150,
      dataIndex: 'rate',
      valueType: 'digit',
      fieldProps: (form, { rowKey }) => ({
        precision: 2,
        addonAfter: '%',
        step: 0.1,
        min: 0,
        onChange: () => {
          const rowData = form.getFieldValue([rowKey || '']);
          const updateRow = renderTaxRate(rowData as RowProps);
          form.setFieldValue([rowKey || ''], updateRow);
        },
      }),
    },
    {
      title: '税额',
      width: 180,
      dataIndex: 'rateAmount',
      valueType: 'money',
      fieldProps: {
        min: 0,
        disabled: true,
      },
    },
    {
      title: '不含税金额',
      width: 180,
      dataIndex: 'excludeRaAmount',
      valueType: 'money',
      fieldProps: {
        min: 0,
        disabled: true,
      },
    },
  ];
  return (
    <>
      {/* 统计卡片 */}
      <ProForm.Item name="collectionInfo">
        <CollectionPlanStatistics />
      </ProForm.Item>
      <ProFormDependency
        name={[
          'collectionPlanList',
          'collectionInfo',
          ['mainContractInfo', 'isFrameworkAgreement'],
        ]}
      >
        {({ mainContractInfo = {}, collectionPlanList = [], collectionInfo = {} }) => {
          const { isFrameworkAgreement } = mainContractInfo;
          const { billedAmount = 0, contractAmount = 0 } = collectionInfo;

          totalRef.current = contractAmount;
          isFrameworkAgreementRef.current = isFrameworkAgreement;
          const total = collectionPlanList?.reduce(
            (total: number, plan: API.CollectionPlanReq) => total + (plan?.estimateReAmount || 0),
            0,
          );
          currentTotalRef.current = total;
          // 非框架协议禁用逻辑 已开票总金额 >= 合同总金额 或者 收款计划金额 >= 合同总金额
          // 框架协议不禁用
          const disabled = isFrameworkAgreement
            ? false
            : billedAmount >= contractAmount || total >= contractAmount;

          return (
            <ProForm.Item
              name="collectionPlanList"
              getValueProps={(val) => ({
                value: val?.map((item: RowProps) => renderTaxRate(item)),
              })}
              transform={(val, namePath) => {
                return {
                  [namePath]: val?.map((item: RowProps) => renderTaxRate(item)),
                };
              }}
              rules={[
                () => ({
                  validator(_, value = []) {
                    const resolve = value.every(
                      (item: API.CollectionPlanResp) =>
                        item.estimateReTime &&
                        (item.estimateReAmount || item.estimateReAmount === 0) &&
                        (item.rate || item.rate === 0),
                    );
                    if (resolve) {
                      return Promise.resolve();
                    }
                    return Promise.reject('请填写完整!');
                  },
                }),
              ]}
            >
              <RKFormEditableProTable
                columns={columns}
                readonly={isDetailPage}
                createBtnDisabled={disabled}
              />
            </ProForm.Item>
          );
        }}
      </ProFormDependency>
      {/*  收款计划详细信息表 */}
      <ProForm.Item name="collectionPlanList">
        <CollectionPlanDetails disabled={disabled} />
      </ProForm.Item>
      {/* 开票记录 */}
      <ProForm.Item name="contractInvoicingList">
        <BillingRecord />
      </ProForm.Item>
    </>
  );
}
