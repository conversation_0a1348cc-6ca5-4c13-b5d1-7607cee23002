import { StatisticCard } from '@ant-design/pro-components';

const PurchaseContractStatistics: React.FC<{
  value?: any;
  onChange?: (val: API.PurConDetailResp) => void;
}> = ({ value = {} }) => {
  const { payTotalAmount, paidAmount, receivedTicketAmount, unpaidAmount } = value || {};
  return (
    <StatisticCard.Group className="rklink-statistic">
      <StatisticCard
        statistic={{
          title: '付款总金额',
          prefix: '¥',
          value: payTotalAmount || 0,
        }}
      />
      <StatisticCard
        statistic={{
          title: '已付款金额',
          prefix: '¥',
          value: paidAmount || 0,
          status: 'success',
        }}
      />
      <StatisticCard
        statistic={{
          title: '已收票金额',
          prefix: '¥',
          value: receivedTicketAmount || 0,
          status: 'success',
        }}
      />
      <StatisticCard
        statistic={{
          prefix: '¥',
          title: '未付款金额',
          value: unpaidAmount || 0,
          status: 'success',
        }}
      />
    </StatisticCard.Group>
  );
};

export default PurchaseContractStatistics;
