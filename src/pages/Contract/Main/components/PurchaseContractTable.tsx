import { CONTRACT_SERVICES_CATEGORY_P } from '@/enums';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';

const columns: ProColumns<API.PurConDetailResp>[] = [
  {
    title: '合同编号',
    dataIndex: 'purContractNumber',
    width: 130,
    render(dom, entity) {
      return (
        <a onClick={() => history.push(`/contract/purchase/edit/${entity.purContractId}`)}>{dom}</a>
      );
    },
  },
  {
    title: '合同名称',
    dataIndex: 'purContractName',
    width: 180,
    ellipsis: true,
  },
  {
    title: '合同类别',
    dataIndex: 'serveCategory',
    valueEnum: option2enum(CONTRACT_SERVICES_CATEGORY_P),
    width: 100,
  },
  {
    title: '合同金额',
    dataIndex: 'contractAmount',
    valueType: 'money',
    width: 130,
  },
  {
    title: '合同开始时间',
    dataIndex: 'startTime',
    width: 110,
  },
  {
    title: '合同结束时间',
    dataIndex: 'endTime',
    width: 110,
  },
  {
    title: '最终用户',
    dataIndex: 'endUser',
    ellipsis: true,
    width: 130,
  },
  {
    title: '已付款金额',
    dataIndex: 'payedAmount',
    valueType: 'money',
    width: 130,
  },
  {
    title: '已收票金额',
    dataIndex: 'receivedAmount',
    valueType: 'money',
    width: 130,
  },
  {
    title: '待付款金额',
    dataIndex: 'awaitPayAmount',
    valueType: 'money',
    width: 130,
  },
];
const PurchaseContractTable: React.FC<{
  value?: API.PurConDetailResp[];
  onChange?: (value: API.PurConDetailResp[]) => void;
}> = ({ value }) => {
  return (
    <ProTable<API.PurConDetailResp>
      {...defaultTableConfig}
      rowKey="purContractId"
      scroll={{ x: '100%' }}
      dataSource={value}
      options={false}
      className="inner-table"
      columns={columns}
    />
  );
};

export default PurchaseContractTable;
