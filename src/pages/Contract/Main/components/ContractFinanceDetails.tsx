import { ProDescriptions } from '@ant-design/pro-components';
import React from 'react';

interface ContractFinanceDetailsProps {
  mainContractInfo?: API.MainContractInfoResp;
  collectionInfo?: API.CollectionInfoResp;
  purConInfo?: {
    paidAmount?: number;
    receivedTicketAmount?: number;
  };
}

const ContractFinanceDetails: React.FC<ContractFinanceDetailsProps> = ({
  mainContractInfo,
  collectionInfo,
  purConInfo,
}) => {
  return (
    <ProDescriptions column={2}>
      <ProDescriptions.Item label="差额" valueType="money">
        {mainContractInfo?.difference ?? '-'}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="差额比">
        {mainContractInfo?.differenceRatio ? `${mainContractInfo.differenceRatio}%` : '-'}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="售前成本" valueType="money">
        {mainContractInfo?.preSalesCost ?? '-'}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="垫资金额" valueType="money">
        {mainContractInfo?.advancesAmount ?? '-'}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="已付款金额" valueType="money">
        {purConInfo?.paidAmount ?? '-'}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="已收款金额" valueType="money">
        {collectionInfo?.receivedAmount ?? '-'}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="已开票金额" valueType="money">
        {collectionInfo?.billedAmount ?? '-'}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="已收票金额" valueType="money">
        {purConInfo?.receivedTicketAmount ?? '-'}
      </ProDescriptions.Item>
    </ProDescriptions>
  );
};

export default ContractFinanceDetails;
