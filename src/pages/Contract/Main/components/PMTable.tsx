import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import BaseContext from '@/Context/BaseContext';
import { getRandomId } from '@/utils';
import { requiredRule } from '@/utils/setting';
import { ProColumns, ProForm } from '@ant-design/pro-components';
import { useLocation } from '@umijs/max';
import { memo, useContext, useRef } from 'react';

const PMTable: React.FC<{ name: string }> = ({ name }) => {
  const { pathname } = useLocation();
  const isDetailPage = pathname.includes('/contract/main/details/');
  const { userList = [], userLoading } = useContext(BaseContext);
  const existIds = useRef<string[]>([]);
  const columns: ProColumns<API.ProMangerResp>[] = [
    {
      title: '员工',
      width: 150,
      dataIndex: 'employeeId',
      valueType: 'select',
      formItemProps: {
        rules: [requiredRule],
      },
      fieldProps: (form, { rowKey }) => ({
        options: userList?.map((item: API.UserPageResp) => ({
          ...item,
          disabled: existIds.current.includes(item.id!),
        })),
        showSearch: true,
        loading: userLoading,
        onChange: (val: string, option: any) => {
          const formArr: API.ProMangerResp[] = Object.values(form.getFieldsValue());
          existIds.current = formArr.map((item) => item.employeeId!);
          form.setFieldValue([rowKey || ''], { employeeId: val, ...option });
        },
      }),
    },
    {
      title: '工号',
      dataIndex: 'employeeNumber',
      width: 150,
      fieldProps: {
        placeholder: '',
        disabled: true,
      },
    },
    {
      title: '部门',
      dataIndex: 'department',
      width: 150,
      fieldProps: {
        placeholder: '',
        disabled: true,
      },
    },
    {
      title: '邮箱',
      width: 150,
      dataIndex: 'email',
      fieldProps: {
        placeholder: '',
        disabled: true,
      },
    },
  ];

  return (
    <ProForm.Item
      name={name}
      getValueProps={(val) => ({
        value: val?.map((item: Record<string, any>) => {
          const { employeeId } = item;
          const rowData = userList?.find((item: API.UserPageResp) => item.id === employeeId);
          return {
            ...item,
            key_: item.key_ || getRandomId(),
            employeeNumber: rowData?.employeeNumber,
            department: rowData?.department,
            email: rowData?.email,
          };
        }),
      })}
      rules={[
        () => ({
          validator(_, value = []) {
            const resolve = value.every((item: API.ProMangerResp) => item.employeeId);
            if (resolve) {
              return Promise.resolve();
            }
            return Promise.reject('请选择员工!');
          },
        }),
      ]}
    >
      <RKFormEditableProTable columns={columns} copy={false} readonly={isDetailPage} />
    </ProForm.Item>
  );
};

export default memo(PMTable);
