import { COST_STATUS } from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useDepartment } from '@/hooks/useDepartment';
import { mainRelevancySqFeesDetails, mainSqFeesDetails } from '@/services/oa/contract';
import { onSuccessAndRefresh } from '@/utils';
import {
  FooterToolbar,
  PageContainer,
  PageHeader,
  ProColumns,
  ProForm,
  ProTable,
} from '@ant-design/pro-components';
import { history, Link, useRequest } from '@umijs/max';
import { Collapse, Space, Tag, Typography } from 'antd';
import { useState } from 'react';

const PreSalesCostConfig: React.FC<WithRouteEditingProps> = ({ id }) => {
  // 项目周报 selectedRows
  const [projectWeeklyReportSelectedRows, setProjectWeeklyReportSelectedRows] = useState<
    API.WeekInfoFeesResp[]
  >([]);

  // 项目报销 selectedRows
  const [projectReimbursementSelectedRows, setProjectReimbursementSelectedRows] = useState<
    API.ReimbursementFeesResp[]
  >([]);
  // 项目付款 selectedRows
  const [projectPaymentSelectedRows, setProjectPaymentSelectedRows] = useState<
    API.PaymentFeesResp[]
  >([]);

  const [projectWeeklyReportData, setProjectWeeklyReportData] = useState<API.WeekInfoFeesResp[]>(
    [],
  );
  const [projectReimbursementData, setProjectReimbursementData] = useState<
    API.ReimbursementFeesResp[]
  >([]);
  const [paymentData, setPaymentData] = useState<API.PaymentFeesResp[]>([]);

  // 获取部门列表
  const { departmentList, loading: departmentLoading } = useDepartment();

  // 获取成本明细
  const {
    data: costResp,
    loading: costLoading,
    refresh,
  } = useRequest(() => mainSqFeesDetails({ id }), {
    onSuccess: (res) => {
      setProjectWeeklyReportData(res?.weekInfoFeesRespList || []);
      setProjectWeeklyReportSelectedRows(
        res?.weekInfoFeesRespList?.filter((item) => item.costStatus === '1') || [],
      );
      setProjectReimbursementData(res?.reimbursementFeesRespList || []);
      setProjectReimbursementSelectedRows(
        res?.reimbursementFeesRespList?.filter((item) => item.costStatus === '1') || [],
      );
      setPaymentData(res?.paymentFeesRespList || []);
      setProjectPaymentSelectedRows(
        res?.paymentFeesRespList?.filter((item) => item.costStatus === '1') || [],
      );
    },
  });

  //  计算项目周报人力成本|项目报销成本\项目付款成本

  const projectWeeklyReportTotalCost = projectWeeklyReportSelectedRows?.reduce((total, item) => {
    return total + (item?.cost ?? 0);
  }, 0);

  const projectReimbursementTotalCost = projectReimbursementSelectedRows?.reduce((total, item) => {
    return total + (item?.cost ?? 0);
  }, 0);
  const projectPaymentTotalCost = projectPaymentSelectedRows?.reduce((total, item) => {
    return total + (item?.cost ?? 0);
  }, 0);
  const totalCost =
    projectWeeklyReportTotalCost + projectReimbursementTotalCost + projectPaymentTotalCost;

  // 保存成本
  const { run: handleSaveCost, loading: costSaveLoading } = useRequest(
    (params) => mainRelevancySqFeesDetails(params),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        onSuccessAndRefresh(res, refresh);
      },
      formatResult: (res) => res,
    },
  );

  const baseColumns: ProColumns<any>[] = [
    {
      title: '成本',
      dataIndex: 'cost',
      valueType: 'money',
      hideInSearch: true,
    },
    {
      title: '成本状态',
      dataIndex: 'costStatus',
      hideInSearch: true,

      render: (_, record) => {
        const obj = COST_STATUS.find((item) => item.value === record.costStatus);

        return (
          <Tag color={obj?.color} key={obj?.value}>
            {obj?.label}
          </Tag>
        );
      },
    },
  ];

  // 项目周报人力 columns

  const projectWeeklyReportColumns: ProColumns<API.WeekInfoFeesResp>[] = [
    {
      title: '项目编号',
      dataIndex: 'projectNumber',
      hideInSearch: true,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      hideInSearch: true,
    },
    {
      title: '日期',
      dataIndex: 'recordDate',
      hideInSearch: true,
    },
    {
      title: '部门名称',
      dataIndex: 'employeeDepartment',
      valueType: 'select',
      fieldProps: {
        options: departmentList.filter((item) => item.parentId === '0'),
        loading: departmentLoading,
        fieldNames: {
          value: 'id',
          label: 'departmentName',
        },
        showSearch: true,
      },
    },
    {
      title: '执行人',
      dataIndex: 'employeeName',
      hideInSearch: true,
    },
    {
      title: '工时',
      dataIndex: 'workTime',
      hideInSearch: true,
    },

    ...baseColumns,
  ];

  // 项目报销 columns
  const projectReimbursementColumns: ProColumns<API.WeekInfoFeesResp>[] = [
    {
      title: '项目编号',
      dataIndex: 'projectNumber',
      hideInSearch: true,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      hideInSearch: true,
    },
    {
      title: '部门名称',
      dataIndex: 'employeeDepartment',
      valueType: 'select',
      fieldProps: {
        options: departmentList.filter((item) => item.parentId === '0'),
        loading: departmentLoading,
        fieldNames: {
          value: 'id',
          label: 'departmentName',
        },
        showSearch: true,
      },
    },
    {
      title: '报销人',
      dataIndex: 'employeeName',
      hideInSearch: true,
    },
    {
      title: '成本',
      dataIndex: 'cost',
      valueType: 'money',
      hideInSearch: true,
    },
    {
      title: '理由',
      dataIndex: 'reason',
      hideInSearch: true,
    },
    ...baseColumns,
  ];
  // 项目付款 columns
  const projectPaymentColumns: ProColumns<API.PaymentFeesResp>[] = [
    {
      title: '付款编号',
      dataIndex: 'payNumber',
      hideInSearch: true,
    },
    {
      title: '申请人',
      dataIndex: 'username',
      hideInSearch: true,
    },
    {
      title: '部门名称',
      dataIndex: 'employeeDepartment',
      valueType: 'select',
      fieldProps: {
        options: departmentList.filter((item) => item.parentId === '0'),
        loading: departmentLoading,
        fieldNames: {
          value: 'id',
          label: 'departmentName',
        },
        showSearch: true,
      },
    },

    ...baseColumns,
  ];

  return (
    <PageContainer header={{ title: true }} className="detail-container" loading={costLoading}>
      <PageHeader
        ghost
        title={
          costResp?.contractNumber && (
            <Link to={`/contract/main/edit/${id}`}>
              <Space>
                <Typography.Title level={4}>{costResp?.contractNumber}</Typography.Title>

                <Typography.Title type="secondary" level={5}>
                  （{costResp?.contractName}）
                </Typography.Title>
              </Space>
            </Link>
          )
        }
      />
      <ProForm
        layout="horizontal"
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          onReset: () => {
            history.back();
          },

          render: (props, doms) => {
            return (
              <FooterToolbar
                extra={
                  <Typography.Title type="secondary" level={5}>
                    售前成本 ：¥{costResp?.preSalesCost || 0}
                  </Typography.Title>
                }
              >
                {doms}
              </FooterToolbar>
            );
          },
          submitButtonProps: {
            loading: costSaveLoading,
          },
        }}
        onFinish={async () => {
          handleSaveCost({
            contractId: id,
            preSalesCost: totalCost,
            feesList: [
              ...projectWeeklyReportSelectedRows.map((item) => ({
                sqFeesId: item.id,
                projectId: item.projectId,
              })),
              ...projectReimbursementSelectedRows.map((item) => ({
                sqFeesId: item.id,
                projectId: item.projectId,
              })),
              ...projectPaymentSelectedRows.map((item) => ({
                sqFeesId: item.id,
                projectId: item.projectId,
              })),
            ],
          });
        }}
      >
        <Collapse
          defaultActiveKey={['projectWeeklyReport', 'projectReimbursement', 'projectPayment']}
          ghost
        >
          <Collapse.Panel key="projectWeeklyReport" header="项目周报-人力" collapsible="header">
            <ProTable
              rowSelection={{
                selectedRowKeys: projectWeeklyReportSelectedRows?.map((item) => item.id!),
                onChange: (_, selectedRows) => {
                  setProjectWeeklyReportSelectedRows(selectedRows);
                },
                getCheckboxProps: (record) => ({
                  disabled: record?.costStatus === '2',
                }),
              }}
              tableAlertRender={() => {
                return (
                  <Space size={24}>
                    <span>已选 {projectWeeklyReportSelectedRows.length} 项</span>
                    <span>{`成本: ¥${projectWeeklyReportSelectedRows.reduce(
                      (pre, item) => pre + (item?.cost ?? 0),
                      0,
                    )} `}</span>
                  </Space>
                );
              }}
              rowKey="id"
              className="inner-table"
              size="small"
              form={{
                onValuesChange: (_, values) => {
                  const departmentId = values.employeeDepartment;
                  const list = costResp?.weekInfoFeesRespList || [];
                  if (!departmentId) {
                    setProjectWeeklyReportData(list);
                    return;
                  }
                  const filteredList = list.filter(
                    (item) => item.employeeDepartment === departmentId,
                  );
                  setProjectWeeklyReportData(filteredList);
                },
              }}
              search={{
                filterType: 'query',
                labelWidth: 'auto',
                defaultCollapsed: false,
                optionRender: false,
              }}
              columns={projectWeeklyReportColumns}
              options={false}
              dataSource={projectWeeklyReportData}
              pagination={{
                defaultPageSize: 10,
                showSizeChanger: true,
                disabled: false,
              }}
            />
          </Collapse.Panel>
          <Collapse.Panel key="projectReimbursement" header="项目报销" collapsible="header">
            <ProTable
              rowSelection={{
                selectedRowKeys: projectReimbursementSelectedRows?.map((item) => item.id!),

                onChange: (_, selectedRows) => {
                  setProjectReimbursementSelectedRows(selectedRows);
                },
                getCheckboxProps: (record) => ({
                  disabled: record?.costStatus === '2',
                }),
              }}
              tableAlertRender={() => {
                return (
                  <Space size={24}>
                    <span>已选 {projectReimbursementSelectedRows.length} 项</span>
                    <span>{`成本: ¥${projectReimbursementSelectedRows.reduce(
                      (pre, item) => pre + (item?.cost ?? 0),
                      0,
                    )} `}</span>
                  </Space>
                );
              }}
              rowKey="id"
              className="inner-table"
              size="small"
              form={{
                onValuesChange: (_, values) => {
                  const departmentId = values.employeeDepartment;
                  const list = costResp?.reimbursementFeesRespList || [];

                  if (!departmentId) {
                    setProjectReimbursementData(list);
                    return;
                  }
                  const filteredList = list.filter(
                    (item) => item.employeeDepartment === departmentId,
                  );
                  setProjectReimbursementData(filteredList);
                },
              }}
              search={{
                filterType: 'query',
                labelWidth: 'auto',
                defaultCollapsed: false,
                optionRender: false,
              }}
              columns={projectReimbursementColumns}
              options={false}
              dataSource={projectReimbursementData}
              pagination={{
                defaultPageSize: 10,
                showSizeChanger: true,
                disabled: false,
              }}
            />
          </Collapse.Panel>
          <Collapse.Panel key="projectPayment" header="项目付款" collapsible="header">
            <ProTable
              rowSelection={{
                selectedRowKeys: projectPaymentSelectedRows?.map((item) => item.id!),

                onChange: (_, selectedRows) => {
                  setProjectPaymentSelectedRows(selectedRows);
                },
                getCheckboxProps: (record) => ({
                  disabled: record?.costStatus === '2',
                }),
              }}
              tableAlertRender={() => {
                return (
                  <Space size={24}>
                    <span>已选 {projectPaymentSelectedRows.length} 项</span>
                    <span>{`成本: ¥${projectPaymentSelectedRows.reduce(
                      (pre, item) => pre + (item?.cost ?? 0),
                      0,
                    )} `}</span>
                  </Space>
                );
              }}
              rowKey="id"
              className="inner-table"
              size="small"
              form={{
                onValuesChange: (_, values) => {
                  const departmentId = values.employeeDepartment;
                  const list = costResp?.paymentFeesRespList || [];
                  if (!departmentId) {
                    setPaymentData(list);
                    return;
                  }
                  const filteredList = list.filter(
                    (item) => item.employeeDepartment === departmentId,
                  );
                  setPaymentData(filteredList);
                },
              }}
              search={{
                filterType: 'query',
                labelWidth: 'auto',
                defaultCollapsed: false,
                optionRender: false,
              }}
              columns={projectPaymentColumns}
              options={false}
              dataSource={paymentData}
              pagination={{
                defaultPageSize: 10,
                showSizeChanger: true,
                disabled: false,
              }}
            />
          </Collapse.Panel>
        </Collapse>
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(PreSalesCostConfig);
