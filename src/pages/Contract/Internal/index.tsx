import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import DownloadModalForm from '@/components/ExportExcel/DownloadModalForm';
import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { APPROVAL_STATUS, CONTRACT_STATUS, EXPIRED_STATE, SIGN_STATUS } from '@/enums';
import { useDepartment } from '@/hooks/useDepartment';
import { deleteInConById, pageInnerContract } from '@/services/oa/contract';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { DownloadOutlined, PlusOutlined } from '@ant-design/icons';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import dayjs from 'dayjs';

const ContractInternal: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.InnerConInfoResp[]>([]);
  const {
    canDeleteInternalContract = false,
    canAddInternalContract = false,
    canExportInternalContract = false,
  } = useAccess();
  const { departmentList, loading: departmentLoading } = useDepartment();
  const [modalVisit, setModalVisit] = useState(false);
  // 删除
  const { run: deleteRecord } = useRequest((ids) => deleteInConById({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const handleDelete = async (rows: API.InnerConInfoResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.contractName!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除内部合同“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.InnerConInfoResp>[] = [
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      width: 150,
      copyable: true,
      fixed: 'left',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/contract/internal/edit/${entity.id}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
      width: 100,
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '合同状态',
      dataIndex: 'contractStatus',
      width: 100,
      hideInSearch: true,
      valueEnum: option2enum(CONTRACT_STATUS),
    },
    {
      title: '状态',
      dataIndex: 'endTime',
      width: 80,
      renderText(text) {
        if (!text) return '1';
        const expired = dayjs(text).endOf('day').isBefore();
        return expired ? '1' : '0';
      },
      valueEnum: option2enum(EXPIRED_STATE),
    },
    {
      title: '合同金额',
      dataIndex: 'contractAmount',
      hideInSearch: true,
      valueType: 'money',
      width: 110,
    },
    {
      title: '乙方',
      dataIndex: 'spName',
      width: 150,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '执行部门',
      dataIndex: 'execDepartment',
      ellipsis: true,
      width: 100,
      valueType: 'select',
      fieldProps: {
        options: departmentList as DefaultOptionType[],
        loading: departmentLoading,
        fieldNames: {
          label: 'departmentName',
          value: 'id',
        },
        showSearch: true,
        filterOption: true,
        optionFilterProps: 'label',
      },
    },
    {
      title: '项目经理',
      dataIndex: 'proManger',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '签订日期',
      dataIndex: 'signDate',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '合同状态',
      dataIndex: 'contractStatus',
      width: 100,
      hideInTable: true,
      valueEnum: option2enum(CONTRACT_STATUS),
    },
    {
      title: '签订状态',
      dataIndex: 'signStatus',
      valueEnum: option2enum(SIGN_STATUS),
      width: 100,
    },

    {
      title: '操作',
      fixed: 'right',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !canDeleteInternalContract,
      render: (text, record) => {
        return (
          <Access accessible={canDeleteInternalContract}>
            <a onClick={() => handleDelete([record])}>删除</a>
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.InnerConInfoResp>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        rowSelection={
          canDeleteInternalContract
            ? {
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
                getCheckboxProps: (record) => ({
                  disabled: record?.activiStatus !== '0',
                }),
              }
            : false
        }
        columns={columns}
        headerTitle="内部下单列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddInternalContract}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  history.push('/contract/internal/add');
                }}
              >
                新建内部下单
              </Button>
            </Access>,
            <Access key="export" accessible={canExportInternalContract}>
              <Button
                key="download"
                type="primary"
                onClick={() => {
                  setModalVisit(true);
                }}
                icon={<DownloadOutlined />}
              >
                导出
              </Button>
            </Access>,
          ],
        }}
        request={async ({ endTime, execDepartment, activiStatus, ...params }) => {
          return queryPagingTable(
            {
              filter: { execDepartment, activiStatus },
              scope: endTime && [
                {
                  name: `TO_DATE(END_TIME, 'YYYY-MM-DD HH24:MI:SS')`,
                  key: endTime === '0' ? 'ge' : 'lt',
                  val: dayjs().format('YYYY-MM-DD'),
                },
              ],
              ...params,
            },
            pageInnerContract,
          );
        }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
      {/* {导出} */}
      <DownloadModalForm
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        type="contract-internal"
      />
    </PageContainer>
  );
};

export default ContractInternal;
