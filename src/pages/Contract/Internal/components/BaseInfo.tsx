import RKCol from '@/components/RKCol';
import RKSelectLabel from '@/components/RKSelectLabel';
import TitleLink from '@/components/TitleLink';
import BaseListContext from '@/Context/BaseListContext';
import { CONTRACT_SERVICES_CATEGORY_I, CONTRACT_STATUS, SIGN_STATUS } from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useDepartment } from '@/hooks/useDepartment';
import { requiredRule } from '@/utils/setting';
import {
  ProFormDatePicker,
  ProFormDependency,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Row } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import { memo, useContext } from 'react';

export const toPage = (id: string, number?: string) => {
  if (!number) return;
  if (number?.includes('NB')) {
    return `/project/internal/edit/${id}`;
  } else if (number.includes('XS')) {
    return `/project/sales/edit/${id}`;
  } else if (number.includes('SQ')) {
    return `/project/pre-sales/edit/${id}`;
  } else if (number.includes('SH')) {
    return `/project/after-sales/edit/${id}`;
  } else if (number.includes('KF')) {
    return `/project/develop/edit/${id}`;
  }
};
//note 2023/3/15 乙方来源由客户变更为供应商
const BaseInfo: React.FC<WithRouteEditingProps> = ({ isEditPage }) => {
  const {
    userList,
    userLoading,
    projectList = [],
    projectLoading,
    partnerList = [],
    partnerLoading,
  } = useContext(BaseListContext);
  const { departmentList, loading: departmentLoading } = useDepartment();

  return (
    <>
      <div className="rk-none">
        <ProFormText name={['innerConInfo', 'id']} />
        <ProFormText name={['innerConInfo', 'projectId']} />
      </div>
      <Row gutter={24}>
        {isEditPage && (
          <RKCol>
            <ProFormText
              name={['innerConInfo', 'contractNumber']}
              label="合同编号"
              disabled={isEditPage}
            />
          </RKCol>
        )}
        <RKCol>
          <ProFormTextArea
            name={['innerConInfo', 'contractName']}
            label="合同名称"
            rules={[requiredRule]}
            fieldProps={{
              autoSize: {
                minRows: 1,
                maxRows: 3,
              },
            }}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name={['innerConInfo', 'serveCategory']}
            label="服务类别"
            rules={[requiredRule]}
            options={CONTRACT_SERVICES_CATEGORY_I}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name={['innerConInfo', 'execDepartment']}
            label="执行部门"
            fieldProps={{
              options: departmentList as DefaultOptionType[],
              loading: departmentLoading,
              fieldNames: {
                label: 'departmentName',
                value: 'id',
              },
            }}
            rules={[requiredRule]}
          />
        </RKCol>
        <ProFormDependency
          name={[
            ['innerConInfo', 'projectId'],
            ['innerConInfo', 'projectNumber'],
          ]}
        >
          {({ innerConInfo }) => {
            return (
              <RKCol>
                <ProFormSelect
                  name={['innerConInfo', 'projectNumber']}
                  rules={[requiredRule]}
                  label={
                    <TitleLink
                      path={
                        innerConInfo?.projectId &&
                        toPage(innerConInfo.projectId, innerConInfo.projectNumber)
                      }
                    >
                      项目编号
                    </TitleLink>
                  }
                  fieldProps={{
                    allowClear: false,
                    loading: projectLoading,
                    showSearch: true,
                    optionLabelProp: 'projectNumber',
                    filterOption: (inputValue, option) => {
                      return option?.keywords.indexOf(inputValue) >= 0;
                    },
                  }}
                  options={projectList
                    ?.filter((item) => item.projectClassify === 'NB')
                    .map((item) => {
                      return {
                        value: item.projectNumber,
                        label: <RKSelectLabel title={item.projectNumber} info={item.projectName} />,
                        projectNumber: item.projectNumber,
                        keywords: `${item.projectName}${item.projectNumber}`,
                      };
                    })}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>
        <ProFormDependency name={[['innerConInfo', 'fpId']]}>
          {({ innerConInfo }) => {
            return (
              <RKCol>
                <ProFormSelect
                  label={
                    <TitleLink
                      path={innerConInfo?.fpId && `/crm/customer/edit/${innerConInfo?.fpId}`}
                    >
                      甲方
                    </TitleLink>
                  }
                  name={['innerConInfo', 'fpId']}
                  rules={[requiredRule]}
                  fieldProps={{
                    allowClear: false,
                    fieldNames: {
                      label: 'clientName',
                      value: 'id',
                    },
                    showSearch: true,
                    loading: partnerLoading,
                  }}
                  params={partnerList}
                  request={async () => partnerList.filter((item) => item?.partnerType === 'CLIENT')}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>
        <ProFormDependency
          name={[
            ['innerConInfo', 'spId'],
            ['innerConInfo', 'id'],
          ]}
        >
          {({ innerConInfo }) => {
            const obj = partnerList
              .filter((item) => item?.partnerType === 'VENDOR')
              .find((item) => item.id === innerConInfo?.spId);
            // 编辑页面 如果找不到供应商
            if (!obj && innerConInfo?.id) {
              return (
                <RKCol>
                  <ProFormText name={['innerConInfo', 'spName']} label="乙方" />
                </RKCol>
              );
            }
            return (
              <RKCol>
                <ProFormSelect
                  label={
                    <TitleLink
                      path={innerConInfo?.spId && `/crm/suppliers/edit/${innerConInfo?.spId}`}
                    >
                      乙方
                    </TitleLink>
                  }
                  name={['innerConInfo', 'spId']}
                  rules={[requiredRule]}
                  fieldProps={{
                    allowClear: false,
                    fieldNames: {
                      label: 'clientName',
                      value: 'id',
                    },
                    showSearch: true,
                    loading: partnerLoading,
                  }}
                  params={partnerList}
                  request={async () => partnerList.filter((item) => item?.partnerType === 'VENDOR')}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>

        <RKCol>
          <ProFormSelect
            name={['innerConInfo', 'proMangerId']}
            rules={[requiredRule]}
            label="项目经理"
            fieldProps={{
              allowClear: false,
              loading: userLoading,
              fieldNames: {
                value: 'id',
                label: 'username',
              },
              showSearch: true,
            }}
            options={userList as DefaultOptionType[]}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name={['innerConInfo', 'businessAssistantId']}
            label="商务助理"
            rules={[requiredRule]}
            fieldProps={{
              loading: userLoading,
              fieldNames: {
                value: 'id',
                label: 'username',
              },
              showSearch: true,
            }}
            options={userList as DefaultOptionType[]}
          />
        </RKCol>
        <RKCol>
          <ProFormMoney
            name={['innerConInfo', 'contractAmount']}
            label="合同金额"
            locale="zh-CN"
            min={0}
            rules={[requiredRule]}
          />
        </RKCol>
        <RKCol>
          <ProFormMoney
            name={['innerConInfo', 'contractTax']}
            label="总税额"
            locale="zh-CN"
            rules={[requiredRule]}
            min={0}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name={['innerConInfo', 'contractStatus']}
            label="合同状态"
            rules={[requiredRule]}
            options={CONTRACT_STATUS}
          />
        </RKCol>
        <ProFormDependency name={[['innerConInfo', 'contractStatus']]}>
          {({ innerConInfo }) => {
            if (innerConInfo?.contractStatus === 'STOP')
              return (
                <RKCol>
                  <ProFormDatePicker
                    rules={[requiredRule]}
                    label="终止日期"
                    name={['innerConInfo', 'actuallyStopTime']}
                  />
                </RKCol>
              );
            if (innerConInfo?.contractStatus === 'END')
              return (
                <RKCol>
                  <ProFormDatePicker
                    rules={[requiredRule]}
                    label="结束日期"
                    name={['innerConInfo', 'actuallyEndTime']}
                  />
                </RKCol>
              );
          }}
        </ProFormDependency>
        <RKCol>
          <ProFormTextArea
            name={['innerConInfo', 'contractAddress']}
            label="合同所在地"
            fieldProps={{
              autoSize: {
                minRows: 1,
                maxRows: 3,
              },
            }}
            rules={[requiredRule]}
          />
        </RKCol>
        <RKCol>
          <ProFormDatePicker
            label="开始日期"
            name={['innerConInfo', 'startTime']}
            rules={[requiredRule]}
          />
        </RKCol>
        <RKCol>
          <ProFormDatePicker
            label="计划结束日期"
            name={['innerConInfo', 'endTime']}
            rules={[requiredRule]}
          />
        </RKCol>
        <RKCol>
          <ProFormDatePicker
            label="签订日期"
            name={['innerConInfo', 'signDate']}
            rules={[requiredRule]}
          />
        </RKCol>
        <RKCol>
          <ProFormDatePicker label="计划交货日期" name={['innerConInfo', 'estimatedDeliDate']} />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name={['innerConInfo', 'signStatus']}
            label="签订状态"
            options={SIGN_STATUS}
            rules={[requiredRule]}
          />
        </RKCol>
        <RKCol lg={24} md={24} sm={24}>
          <ProFormTextArea
            name={['innerConInfo', 'overview']}
            label="内容概述"
            fieldProps={{
              autoSize: {
                minRows: 2,
                maxRows: 4,
              },
            }}
          />
        </RKCol>
      </Row>
    </>
  );
};
export default memo(withRouteEditing(BaseInfo));
