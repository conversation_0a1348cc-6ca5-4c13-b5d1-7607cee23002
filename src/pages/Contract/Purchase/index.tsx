import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import DownloadModalForm from '@/components/ExportExcel/DownloadModalForm';
import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { APPROVAL_STATUS, CONTRACT_STATUS, EXPIRED_STATE } from '@/enums';
import { usePartnerList } from '@/hooks/usePartnerList';
import { deletePurConById, pagePurContract } from '@/services/oa/contract';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { DownloadOutlined, PlusOutlined } from '@ant-design/icons';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import dayjs from 'dayjs';

const ContractPurchase: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.PurContractInfoResp[]>([]);
  const {
    canAddPurchaseContract = false,
    canDeletePurchaseContract = false,
    canExportPurchaseContract = false,
  } = useAccess();
  const [modalVisit, setModalVisit] = useState(false);
  const { partnerList, loading: partnerLoading } = usePartnerList();
  const filteredPartnerList = partnerList.filter((item) => item?.partnerType === 'VENDOR');

  // 删除
  const { run: deleteRecord } = useRequest((ids) => deletePurConById({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const handleDelete = async (rows: API.PurContractInfoResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.contractName!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除采购合同“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.PurContractInfoResp>[] = [
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      width: 150,
      copyable: true,
      fixed: 'left',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/contract/purchase/edit/${entity.id}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
      width: 100,
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '乙方',
      dataIndex: 'spId',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: filteredPartnerList,
        loading: partnerLoading,
        fieldNames: {
          label: 'clientName',
          value: 'id',
        },
        showSearch: true,
      },
    },
    {
      title: '乙方',
      dataIndex: 'spName',
      width: 150,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '合同状态',
      dataIndex: 'contractStatus',
      valueType: 'select',
      width: 80,
      valueEnum: option2enum(CONTRACT_STATUS),
    },
    {
      title: '状态',
      dataIndex: 'endTime',
      width: 80,
      renderText(text) {
        if (!text) return '1';
        const expired = dayjs(text).endOf('day').isBefore();
        return expired ? '1' : '0';
      },
      valueEnum: option2enum(EXPIRED_STATE),
    },
    {
      title: '关联主合同名称',
      dataIndex: 'mainConName',
      width: 150,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '最终用户',
      dataIndex: 'endUser',
      hideInSearch: true,
      ellipsis: true,
      width: 150,
    },
    {
      title: '销售',
      dataIndex: 'salePerson',
      width: 100,
    },
    {
      title: '合同所在地',
      dataIndex: 'contractAddress',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '操作',
      fixed: 'right',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !canDeletePurchaseContract,
      render: (text, record) => {
        return (
          <Access accessible={canDeletePurchaseContract}>
            <a onClick={() => handleDelete([record])}>删除</a>
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.PurContractInfoResp>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        rowSelection={
          canDeletePurchaseContract
            ? {
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
                getCheckboxProps: (record) => ({
                  disabled: record?.activiStatus !== '0',
                }),
              }
            : false
        }
        columns={columns}
        headerTitle="采购合同列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddPurchaseContract}>
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  history.push('/contract/purchase/add');
                }}
              >
                新建采购合同
              </Button>
            </Access>,
            <Access key="export" accessible={canExportPurchaseContract}>
              <Button
                type="primary"
                onClick={() => {
                  setModalVisit(true);
                }}
                icon={<DownloadOutlined />}
              >
                导出
              </Button>
            </Access>,
          ],
        }}
        request={async ({ endTime, activiStatus, spId, ...params }) => {
          const filter = { activiStatus, spId };
          return queryPagingTable(
            {
              scope: endTime && [
                {
                  name: `TO_DATE(END_TIME, 'YYYY-MM-DD HH24:MI:SS')`,
                  key: endTime === '0' ? 'ge' : 'lt',
                  val: dayjs().format('YYYY-MM-DD'),
                },
              ],
              ...params,
              filter,
            },
            pagePurContract,
          );
        }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
      {/* {导出} */}
      <DownloadModalForm
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        type="contract-purchase"
      />
    </PageContainer>
  );
};

export default ContractPurchase;
