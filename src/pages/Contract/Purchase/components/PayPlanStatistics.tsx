import { StatisticCard } from '@ant-design/pro-components';
import { Col, Row } from 'antd';

const PayPlanStatistics: React.FC<{
  value?: API.PaySummaryResp;
  onChange?: (val: API.PaySummaryResp) => void;
}> = ({ value = {} }) => {
  const {
    contractAmount,
    receiveTotalAmount,
    receivedAmount,
    payedAmount,
    awaitReAmount,
    awaitPayAmount,
    totalTax,
    currentTax,
  } = value || {};
  return (
    <StatisticCard.Group className="rklink-statistic">
      <Row>
        <Col span={6} style={{ left: 14 }}>
          <StatisticCard
            statistic={{
              title: '合同总金额',
              prefix: '¥',
              value: contractAmount || 0,
            }}
          />
        </Col>
        <Col span={6}>
          <StatisticCard
            statistic={{
              title: '收票总金额',
              prefix: '¥',
              value: receiveTotalAmount || 0,
              status: 'success',
            }}
          />
        </Col>
        <Col span={6}>
          <StatisticCard
            statistic={{
              title: '已收票金额',
              prefix: '¥',
              value: receivedAmount || 0,
              status: 'success',
            }}
          />
        </Col>
        <Col span={6}>
          <StatisticCard
            statistic={{
              prefix: '¥',
              title: '已付款金额',
              value: payedAmount || 0,
              status: 'success',
            }}
          />
        </Col>
        <Col span={6}>
          <StatisticCard
            statistic={{
              prefix: '¥',
              title: '待收票金额',
              value: awaitReAmount || 0,
              status: 'processing',
            }}
          />
        </Col>
        <Col span={6}>
          <StatisticCard
            statistic={{
              prefix: '¥',
              title: '待付款金额',
              value: awaitPayAmount || 0,
              status: 'processing',
            }}
          />
        </Col>
        <Col span={6} style={{ left: 13 }}>
          <StatisticCard
            statistic={{
              prefix: '¥',
              title: '总税额',
              value: totalTax || 0,
            }}
          />
        </Col>
        <Col span={6} style={{ left: 13 }}>
          <StatisticCard
            statistic={{
              prefix: '¥',
              title: '当前税额',
              value: currentTax || 0,
            }}
          />
        </Col>
      </Row>
    </StatisticCard.Group>
  );
};

export default PayPlanStatistics;
