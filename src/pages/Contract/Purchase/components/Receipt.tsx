import { BILL_TYPE, INVOICE_TYPE } from '@/enums';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';

const columns: ProColumns<Record<string, any>>[] = [
  {
    title: '收票流水号',
    width: 200,
    dataIndex: 'documentNumber',
  },
  {
    title: '认领日期',
    width: 120,
    dataIndex: 'assignDate',
    valueType: 'date',
  },
  {
    title: '开票日期',
    width: 120,
    dataIndex: 'invoiceDate',
    valueType: 'date',
  },
  {
    title: '发票类型',
    dataIndex: 'invoiceType',
    width: 100,
    valueEnum: option2enum(INVOICE_TYPE),
  },
  {
    title: '票据类型',
    dataIndex: 'billType',
    width: 100,
    valueEnum: option2enum(BILL_TYPE),
  },
  {
    dataIndex: 'amountOfMoney',
    title: '金额',
    valueType: 'money',
    width: 130,
  },
  {
    dataIndex: 'taxAmount',
    title: '税额',
    valueType: 'money',
    width: 130,
  },
  {
    title: '备注',
    width: 200,
    dataIndex: 'remarks',
  },
];

const Receipt: React.FC<{
  value?: API.CollectTicketContractResp[];
}> = ({ value }) => {
  return (
    <ProTable<API.CollectTicketContractResp>
      {...defaultTableConfig}
      scroll={{ x: '100%' }}
      dataSource={value}
      className="inner-table"
      options={false}
      columns={columns}
    />
  );
};
export default Receipt;
