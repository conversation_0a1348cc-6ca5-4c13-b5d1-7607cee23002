import { APPROVAL_STATUS, SIGN_STATUS } from '@/enums';
import { mainContractDifference } from '@/services/oa/contract';
import { option2enum } from '@/utils';
import { ProDescriptions, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import React from 'react';

interface DifferenceInfoProps {
  contractId?: string;
}
const DifferenceInfo: React.FC<DifferenceInfoProps> = ({ contractId = '' }) => {
  const { data } = useRequest<API.ResultMainContractDifferenceResp>(
    () =>
      mainContractDifference({
        id: contractId,
      }),
    {
      ready: !!contractId,
      formatResult: (res) => res?.data,
    },
  );

  const columns = [
    {
      title: '采购合同编号',
      dataIndex: 'contractNumber',
    },
    {
      title: '名称',
      dataIndex: 'contractName',
    },
    {
      title: '金额',
      dataIndex: 'contractAmount',
      valueType: 'money',
    },
    {
      title: '签订状态',
      dataIndex: 'signStatus',
      valueEnum: option2enum(SIGN_STATUS),
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
    },
  ];

  return (
    <>
      <ProDescriptions column={4}>
        <ProDescriptions.Item label="签订状态" valueEnum={option2enum(SIGN_STATUS)}>
          {data?.signStatus}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="金额" valueType="money">
          {data?.contractAmount}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="与采购合同总差额" valueType="money">
          {data?.purDifference}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="与采购合同总差额比">
          {data?.purDifferenceRatio}
        </ProDescriptions.Item>
      </ProDescriptions>
      <ProTable
        style={{ marginTop: 16 }}
        columns={columns}
        dataSource={data?.purContractList}
        rowKey="id"
        search={false}
        options={false}
        toolBarRender={false}
      />
    </>
  );
};

export default DifferenceInfo;
