import RKCol from '@/components/RKCol';
import { requiredRule } from '@/utils/setting';
import { ProFormText } from '@ant-design/pro-components';
import { Row } from 'antd';

export default function PayInfo() {
  return (
    <Row gutter={24}>
      <RKCol>
        <ProFormText
          label="单位名称"
          rules={[requiredRule]}
          disabled
          name={['payInfo', 'institutionName']}
        />
      </RKCol>
      <RKCol>
        <ProFormText label="开户银行" rules={[requiredRule]} name={['payInfo', 'bank']} />
      </RKCol>
      <RKCol>
        <ProFormText label="银行账号" rules={[requiredRule]} name={['payInfo', 'account']} />
      </RKCol>
      <RKCol>
        <ProFormText
          label="纳税人识别号"
          rules={[requiredRule]}
          name={['payInfo', 'taxpayerNumber']}
        />
      </RKCol>
      <RKCol>
        <ProFormText label="备注" name={['payInfo', 'remark']} />
      </RKCol>
    </Row>
  );
}
