import { PAYMENT, PAY_STATUS } from '@/enums';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, history, useAccess } from '@umijs/max';
import { Button } from 'antd';

const columns: ProColumns<API.PayPlanDetailInfoResp>[] = [
  {
    title: '期次',
    width: 50,
    dataIndex: 'index',
    valueType: 'index',
    renderText: (text, entity) => {
      if (entity.period) return entity.period - 1;
      return text;
    },
  },
  {
    title: '计划付款金额',
    dataIndex: 'estimatePayAmount',
    valueType: 'money',
    width: 130,
  },
  {
    title: '计划付款日期',
    dataIndex: 'estimatePayTime',
    width: 130,
  },
  {
    title: '付款方式',
    dataIndex: 'payWay',
    valueEnum: option2enum(PAYMENT),
    width: 130,
    ellipsis: true,
  },
  {
    title: '付款状态',
    dataIndex: 'payPlanStatus',
    valueEnum: option2enum(PAY_STATUS),
    width: 120,
  },
  {
    title: '收票金额',
    dataIndex: 'receiptAmount',
    width: 130,
    valueType: 'money',
  },
  {
    title: '对方开票日期',
    dataIndex: 'countInvoicedTime',
    width: 130,
  },
  {
    title: '付款记录号',
    dataIndex: 'payNumber',
    width: 180,
    render(dom, entity) {
      if (!entity.paymentId) return '-';
      return (
        <a onClick={() => history.push(`/finance/JR/payment-list/${entity.paymentId}`)}>{dom}</a>
      );
    },
  },
  {
    title: '付款申请编号',
    dataIndex: 'paymentAppNumber',
    width: 180,
    render(dom, entity) {
      if (!entity.paymentAppId) return '-';
      return (
        <a onClick={() => history.push(`/finance/payment/contract/${entity.paymentAppId}`)}>
          {dom}
        </a>
      );
    },
  },
  {
    title: '实际付款金额',
    dataIndex: 'payAmount',
    width: 150,
    valueType: 'money',
  },
  {
    title: '付款日期',
    dataIndex: 'payTime',
    width: 130,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100,
    ellipsis: true,
  },
];

type PayPlanDetail = API.PayPlanDetailInfoResp & {
  id?: string;
};
const PayPlanDetails: React.FC<{
  value?: PayPlanDetail[];
  onChange?: (value: PayPlanDetail[]) => void;
  disabled?: boolean;
}> = ({ value, disabled }) => {
  const { canAddContractPayment = false } = useAccess();
  const operateColumn: ProColumns<PayPlanDetail>[] = [
    {
      title: '操作',
      fixed: 'right',
      width: 100,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !canAddContractPayment,
      render: (text, record) => {
        const { operateStatus, id, contractId } = record;

        return (
          <Access accessible={canAddContractPayment}>
            <Button
              type="link"
              /** 禁用逻辑
               * 付款操作状态如果为提交
               * 没有合同 id
               * 合同状态为终止或者结束
               */
              disabled={operateStatus === 'SUBMIT' || !contractId || disabled}
              onClick={() => history.push(`/finance/payment/contract/${id}`)}
            >
              付款申请
            </Button>
          </Access>
        );
      },
    },
  ];
  return (
    <ProTable<PayPlanDetail>
      {...defaultTableConfig}
      scroll={{ x: '100%' }}
      dataSource={value}
      options={false}
      className="inner-table"
      columns={[...columns, ...operateColumn]}
      headerTitle="付款计划详细信息表"
    />
  );
};

export default PayPlanDetails;
