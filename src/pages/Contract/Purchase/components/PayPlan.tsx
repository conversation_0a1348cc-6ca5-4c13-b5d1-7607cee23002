import RKFormEditableProTable from '@/components/RKFormEditableProTable';
import { calcTaxRate, getRandomId } from '@/utils';
import { requiredRule } from '@/utils/setting';
import { ProColumns, ProForm, ProFormDependency } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { useRef } from 'react';
import PayPlanDetails from './PayPlanDetails';

import PayPlanStatistics from './PayPlanStatistics';

type RowProps = API.PayPlanDetailInfoResp & { key_: string };
const dateFormat = 'YYYY-MM-DD';
const renderTaxRate = (row: RowProps) => {
  const { key_, estimatePayAmount = 0, rate = 0, estimatePayTime } = row || {};
  const { taxExclusiveAmount, taxInclusiveAmount } = calcTaxRate(estimatePayAmount, rate);

  return {
    ...row,
    key_: key_ || getRandomId(),
    estimateReTime: estimatePayTime ? dayjs(estimatePayTime).format(dateFormat) : null,
    //  税额 = 不含税金额 * 税率
    rateAmount: taxInclusiveAmount,
    // 不含税金额 = 含税金额 / (1+税率）
    excludeRaAmount: taxExclusiveAmount,
    // 如果发起了开票申请 则不能再更改本行数据
    readonly: row.operateStatus === 'SUBMIT',
  };
};

export default function PayPlan({
  disabled,
  ticketDisabled,
}: {
  disabled?: boolean;
  ticketDisabled?: boolean;
}) {
  const totalRef = useRef(0);
  const currentTotalRef = useRef(0);
  const isFrameworkAgreementRef = useRef();

  const columns: ProColumns<API.PayPlanDetailInfoResp>[] = [
    {
      title: '期次',
      width: 50,
      dataIndex: 'index',
      valueType: 'index',
      renderText: (text, entity) => {
        if (entity.period) return entity.period - 1;
        return text;
      },
    },
    {
      title: '预计付款时间',
      width: 130,
      dataIndex: 'estimatePayTime',
      valueType: 'date',
      formItemProps: {
        rules: [requiredRule],
      },
    },
    {
      title: '预计付款金额',
      width: 130,
      dataIndex: 'estimatePayAmount',
      valueType: 'digit',
      fieldProps: (form, { rowKey }) => ({
        prefix: '¥',
        style: { width: '100%' },
        precision: 2,
        min: 0,
        max: !isFrameworkAgreementRef.current
          ? (
              totalRef.current -
              currentTotalRef.current +
              form?.getFieldValue([rowKey || ''])?.estimatePayAmount
            ).toFixed(2)
          : undefined,
        onChange: (value: number) => {
          const allData: Record<string, API.PayPlanDetailInfoResp> = form.getFieldsValue() || {};
          // 计算 estimatePayAmount 的总额
          const totalEstimatePayAmount = Object.values(allData).reduce((total, entry) => {
            return total + (entry?.estimatePayAmount || 0);
          }, 0);

          const rowData = form.getFieldValue([rowKey || '']);
          if (totalEstimatePayAmount >= totalRef.current && !isFrameworkAgreementRef.current) {
            rowData.estimatePayAmount = value - (totalEstimatePayAmount - totalRef.current);
          }
          const updateRow = renderTaxRate(rowData as RowProps);
          form.setFieldValue([rowKey || ''], updateRow);
        },
      }),
      formItemProps: {
        rules: [requiredRule],
      },
    },
    {
      title: '付款条件',
      width: 200,
      dataIndex: 'payCondition',
      formItemProps: {
        rules: [requiredRule],
      },
      valueType: 'textarea',
      fieldProps: {
        allowClear: false,
        autoSize: {
          minRows: 1,
          maxRows: 3,
        },
      },
    },
    {
      title: '税率',
      width: 130,
      dataIndex: 'rate',
      valueType: 'digit',
      fieldProps: (form, { rowKey }) => ({
        precision: 2,
        addonAfter: '%',
        step: 0.1,
        min: 0,
        onChange: () => {
          const rowData = form.getFieldValue([rowKey || '']);
          const updateRow = renderTaxRate(rowData as RowProps);
          form.setFieldValue([rowKey || ''], updateRow);
        },
      }),
    },
    {
      title: '税额',
      width: 130,
      dataIndex: 'rateAmount',
      valueType: 'money',
      fieldProps: {
        min: 0,
        disabled: true,
      },
    },
    {
      title: '不含税金额',
      width: 130,
      dataIndex: 'excludeRaAmount',
      valueType: 'money',
      fieldProps: {
        min: 0,
        disabled: true,
      },
    },
  ];

  return (
    <>
      {/* 统计卡片 */}
      <ProForm.Item name="paySummary">
        <PayPlanStatistics />
      </ProForm.Item>

      <ProFormDependency
        name={[
          'paySummary',
          'payPlanDetailInfoList',
          ['purContractInfo', 'contractAmount'],
          ['purContractInfo', 'isFrameworkAgreement'],
        ]}
      >
        {({ purContractInfo, payPlanDetailInfoList, paySummary }) => {
          const { isFrameworkAgreement = 0 } = purContractInfo || {};
          const { receivedAmount = 0, contractAmount = 0 } = paySummary || {};

          totalRef.current = contractAmount;
          isFrameworkAgreementRef.current = isFrameworkAgreement;
          const total = payPlanDetailInfoList?.reduce(
            (total: number, plan: API.PayPlanDetailInfoReq) =>
              total + (plan?.estimatePayAmount || 0),
            0,
          );
          currentTotalRef.current = total;
          // 非框架协议禁用逻辑 已收票总金额 >= 合同总金额 或者 付款计划金额 >= 合同总金额
          // 框架协议不禁用
          const disabledEdit = isFrameworkAgreement
            ? false
            : receivedAmount >= contractAmount || total >= contractAmount;
          return (
            <ProForm.Item
              name="payPlanDetailInfoList"
              getValueProps={(val) => ({
                value: val?.map((item: RowProps) => renderTaxRate(item)),
              })}
              rules={[
                () => ({
                  validator(_, value = []) {
                    const resolve = value.every(
                      (item: API.PayPlanDetailInfoResp) =>
                        item.estimatePayAmount && item.estimatePayTime,
                    );
                    if (resolve) {
                      return Promise.resolve();
                    }
                    return Promise.reject('请填写完整!');
                  },
                }),
              ]}
            >
              <RKFormEditableProTable
                columns={columns}
                createBtnDisabled={disabledEdit || disabled}
              />
            </ProForm.Item>
          );
        }}
      </ProFormDependency>
      {/*  付款计划详细信息表 */}
      <ProForm.Item name="payPlanDetailInfoList" label="">
        <PayPlanDetails disabled={disabled || ticketDisabled} />
      </ProForm.Item>
    </>
  );
}
