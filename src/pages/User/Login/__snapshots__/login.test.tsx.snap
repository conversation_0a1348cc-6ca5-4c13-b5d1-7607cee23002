// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Login Page should login success 1`] = `
<DocumentFragment>
  <div
    class="ant-pro"
  >
    <div
      class="ant-design-pro ant-pro-layout screen-md ant-pro-layout-fix-siderbar ant-pro-layout-mix"
    >
      <div
        class="ant-pro-layout-bg-list "
      />
      <section
        class="ant-layout ant-layout-has-sider"
        style="min-height: 100%; flex-direction: row;"
      >
        <div
          style="width: 256px; overflow: hidden; flex: 0 0 256px; max-width: 256px; min-width: 256px; transition: all 0.2s ease 0s;"
        />
        <aside
          class="ant-layout-sider ant-layout-sider-light ant-pro-sider ant-pro-sider-fixed ant-pro-sider-layout-mix ant-pro-sider-light ant-pro-sider-mix"
          style="flex: 0 0 256px; max-width: 256px; min-width: 256px; width: 256px;"
        >
          <div
            class="ant-layout-sider-children"
          >
            <div
              style="flex: 1; overflow-y: auto; overflow-x: hidden;"
            >
              <ul
                class="ant-menu ant-menu-root ant-menu-inline ant-menu-light ant-pro-sider-menu  ant-pro-base-menu"
                data-menu-list="true"
                role="menu"
                style="background-color: transparent; width: 100%;"
                tabindex="0"
              >
                <li
                  class="ant-menu-item ant-menu-item-selected ant-menu-item-only-child ant-pro-base-menu-menu-item"
                  data-menu-id="rc-menu-uuid-test-/welcome"
                  role="menuitem"
                  style="padding-left: 16px;"
                  tabindex="-1"
                  title="Welcome"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    <div
                      class="ant-pro-base-menu-item-title"
                    >
                      <span
                        class="anticon ant-pro-base-menu-item-icon "
                      >
                        <span
                          aria-label="smile"
                          class="anticon anticon-smile"
                          role="img"
                        >
                          <svg
                            aria-hidden="true"
                            data-icon="smile"
                            fill="currentColor"
                            focusable="false"
                            height="1em"
                            viewBox="64 64 896 896"
                            width="1em"
                          >
                            <path
                              d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
                            />
                          </svg>
                        </span>
                      </span>
                      <span
                        class="ant-pro-base-menu-item-text ant-pro-base-menu-item-text-has-icon"
                      >
                        Welcome
                      </span>
                    </div>
                  </span>
                </li>
                <li
                  class="ant-menu-item ant-menu-item-only-child ant-pro-base-menu-menu-item"
                  data-menu-id="rc-menu-uuid-test-/list"
                  role="menuitem"
                  style="padding-left: 16px;"
                  tabindex="-1"
                  title="Search Table"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    <a
                      href="/list"
                    >
                      <div
                        class="ant-pro-base-menu-item-title"
                      >
                        <span
                          class="anticon ant-pro-base-menu-item-icon "
                        >
                          <span
                            aria-label="table"
                            class="anticon anticon-table"
                            role="img"
                          >
                            <svg
                              aria-hidden="true"
                              data-icon="table"
                              fill="currentColor"
                              focusable="false"
                              height="1em"
                              viewBox="64 64 896 896"
                              width="1em"
                            >
                              <path
                                d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 208H676V232h212v136zm0 224H676V432h212v160zM412 432h200v160H412V432zm200-64H412V232h200v136zm-476 64h212v160H136V432zm0-200h212v136H136V232zm0 424h212v136H136V656zm276 0h200v136H412V656zm476 136H676V656h212v136z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span
                          class="ant-pro-base-menu-item-text ant-pro-base-menu-item-text-has-icon"
                        >
                          Search Table
                        </span>
                      </div>
                    </a>
                  </span>
                </li>
              </ul>
              <div
                aria-hidden="true"
                style="display: none;"
              />
            </div>
            <div
              class="ant-pro-sider-links "
            >
              <ul
                class="ant-menu ant-menu-root ant-menu-inline ant-menu-light ant-pro-sider-link-menu "
                data-menu-list="true"
                role="menu"
                tabindex="0"
              />
              <div
                aria-hidden="true"
                style="display: none;"
              />
            </div>
            <div
              class="ant-pro-sider-collapsed-button"
            >
              <svg
                aria-hidden="true"
                fill="currentColor"
                height="1em"
                viewBox="0 0 12 12"
                width="1em"
              >
                <path
                  d="M6.432 7.967a.448.448 0 01-.318.133h-.228a.46.46 0 01-.318-.133L2.488 4.85a.305.305 0 010-.43l.427-.43a.293.293 0 01.42 0L6 6.687l2.665-2.699a.299.299 0 01.426 0l.42.431a.305.305 0 010 .43L6.432 7.967z"
                />
              </svg>
            </div>
          </div>
        </aside>
        <div
          class="ant-pro-layout-container "
          style="position: relative;"
        >
          <header
            class="ant-layout-header"
            style="height: 56px; line-height: 56px; background-color: transparent; z-index: 19;"
          />
          <header
            class="ant-layout-header ant-pro-layout-header ant-pro-layout-header-fixed-header ant-pro-layout-header-mix ant-pro-layout-header-fixed-header-action ant-pro-layout-header-header"
          >
            <div
              class="ant-pro-global-header"
            >
              <div
                class="ant-pro-global-header-logo ant-pro-global-header-logo-mix"
              >
                <a>
                  <img
                    alt="logo"
                    height="22"
                    src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
                    width="auto"
                  />
                  <h1>
                    OA系统
                  </h1>
                </a>
              </div>
              <div
                style="flex: 1;"
              />
              <div
                class="ant-pro-global-header-right-content "
                style="min-width: auto; height: 100%;"
              >
                <div
                  style="height: 100%;"
                >
                  <div
                    style="display: flex; align-items: center; height: 100%; justify-content: flex-end;"
                  >
                    <div
                      class="css-1doylhz"
                    >
                      <span
                        class="css-lye32u"
                      >
                        <span
                          aria-label="question-circle"
                          class="anticon anticon-question-circle"
                          role="img"
                        >
                          <svg
                            aria-hidden="true"
                            data-icon="question-circle"
                            fill="currentColor"
                            focusable="false"
                            height="1em"
                            viewBox="64 64 896 896"
                            width="1em"
                          >
                            <path
                              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                            />
                            <path
                              d="M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z"
                            />
                          </svg>
                        </span>
                      </span>
                      <span
                        class="ant-dropdown-trigger css-19fba91"
                      >
                        <span
                          class="ant-avatar ant-avatar-sm ant-avatar-circle ant-avatar-image css-10ti6ek"
                        >
                          <img
                            alt="avatar"
                            src="https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png"
                          />
                        </span>
                        <span
                          class="css-15vbrjz anticon"
                        >
                          Serati Ma
                        </span>
                      </span>
                      <span
                        class="ant-dropdown-trigger css-lye32u"
                        style="cursor: pointer; padding: 12px; display: inline-flex; align-items: center; justify-content: center; font-size: 18px; vertical-align: middle;"
                      >
                        <i
                          class="anticon"
                        >
                          <svg
                            aria-hidden="true"
                            fill="currentColor"
                            focusable="false"
                            height="1em"
                            viewBox="0 0 24 24"
                            width="1em"
                          >
                            <path
                              d="M0 0h24v24H0z"
                              fill="none"
                            />
                            <path
                              class="css-c4d79v"
                              d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z "
                            />
                          </svg>
                        </i>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </header>
          <main
            class="ant-layout-content ant-pro-layout-content ant-pro-layout-has-header ant-pro-layout-content-has-page-container"
          >
            <div
              class="ant-pro-page-container"
            >
              <div
                class="ant-page-header ant-pro-page-container-warp-page-header  ant-page-header-ghost"
              >
                <div
                  class="ant-page-header-heading "
                >
                  <div
                    class="ant-page-header-heading-left "
                  >
                    <span
                      class="ant-page-header-heading-title "
                      title="Welcome"
                    >
                      Welcome
                    </span>
                  </div>
                </div>
              </div>
              <div
                class="ant-pro-grid-content"
              >
                <div
                  class="ant-pro-grid-content-children "
                >
                  <div
                    class="ant-pro-layout-watermark-wrapper"
                    style="position: relative;"
                  >
                    <div
                      class="ant-pro-page-container-children-content "
                    >
                      <div
                        class="ant-card ant-card-bordered"
                        style="border-radius: 8px;"
                      >
                        <div
                          class="ant-card-body"
                        >
                          <div
                            style="background-position: 100% -30%; background-repeat: no-repeat; background-size: 274px auto; background-image: url(https://gw.alipayobjects.com/mdn/rms_a9745b/afts/img/A*BuFmQqsB2iAAAAAAAAAAAAAAARQnAQ);"
                          >
                            <div
                              style="font-size: 20px; color: rgba(0, 0, 0, 0.88);"
                            >
                              欢迎使用 OA系统
                            </div>
                            <p
                              style="font-size: 14px; color: rgba(0, 0, 0, 0.65); line-height: 22px; margin-top: 16px; margin-bottom: 32px; width: 65%;"
                            >
                              OA系统 是一个整合了 umi，Ant Design 和 ProComponents 的脚手架方案。致力于在设计规范和基础组件的基础上，继续向上构建，提炼出典型模板/业务组件/配套设计资源，进一步提升企业级中后台产品设计研发过程中的『用户』和『设计者』的体验。
                            </p>
                            <div
                              style="display: flex; flex-wrap: wrap; gap: 16px;"
                            >
                              <div
                                style="background-color: rgb(255, 255, 255); box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03),
      0 1px 6px -1px rgba(0, 0, 0, 0.02),
      0 2px 4px 0 rgba(0, 0, 0, 0.02); border-radius: 8px; font-size: 14px; color: rgba(0, 0, 0, 0.65); line-height: 22px; padding: 16px 19px; min-width: 220px; flex: 1;"
                              >
                                <div
                                  style="display: flex; gap: 4px; align-items: center;"
                                >
                                  <div
                                    style="width: 48px; height: 48px; line-height: 22px; background-size: 100%; text-align: center; padding: 8px 16px 16px 12px; color: rgb(255, 255, 255); font-weight: bold; background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/daaf8d50-8e6d-4251-905d-676a24ddfa12.svg);"
                                  >
                                    1
                                  </div>
                                  <div
                                    style="font-size: 16px; color: rgba(0, 0, 0, 0.88); padding-bottom: 8px;"
                                  >
                                    了解 umi
                                  </div>
                                </div>
                                <div
                                  style="font-size: 14px; color: rgba(0, 0, 0, 0.65); text-align: justify; line-height: 22px; margin-bottom: 8px;"
                                >
                                  umi 是一个可扩展的企业级前端应用框架,umi 以路由为基础的，同时支持配置式路由和约定式路由，保证路由的功能完备，并以此进行功能扩展。
                                </div>
                                <a
                                  href="https://umijs.org/docs/introduce/introduce"
                                  rel="noreferrer"
                                  target="_blank"
                                >
                                  了解更多 &gt;
                                </a>
                              </div>
                              <div
                                style="background-color: rgb(255, 255, 255); box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03),
      0 1px 6px -1px rgba(0, 0, 0, 0.02),
      0 2px 4px 0 rgba(0, 0, 0, 0.02); border-radius: 8px; font-size: 14px; color: rgba(0, 0, 0, 0.65); line-height: 22px; padding: 16px 19px; min-width: 220px; flex: 1;"
                              >
                                <div
                                  style="display: flex; gap: 4px; align-items: center;"
                                >
                                  <div
                                    style="width: 48px; height: 48px; line-height: 22px; background-size: 100%; text-align: center; padding: 8px 16px 16px 12px; color: rgb(255, 255, 255); font-weight: bold; background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/daaf8d50-8e6d-4251-905d-676a24ddfa12.svg);"
                                  >
                                    2
                                  </div>
                                  <div
                                    style="font-size: 16px; color: rgba(0, 0, 0, 0.88); padding-bottom: 8px;"
                                  >
                                    了解 ant design
                                  </div>
                                </div>
                                <div
                                  style="font-size: 14px; color: rgba(0, 0, 0, 0.65); text-align: justify; line-height: 22px; margin-bottom: 8px;"
                                >
                                  antd 是基于 Ant Design 设计体系的 React UI 组件库，主要用于研发企业级中后台产品。
                                </div>
                                <a
                                  href="https://ant.design"
                                  rel="noreferrer"
                                  target="_blank"
                                >
                                  了解更多 &gt;
                                </a>
                              </div>
                              <div
                                style="background-color: rgb(255, 255, 255); box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03),
      0 1px 6px -1px rgba(0, 0, 0, 0.02),
      0 2px 4px 0 rgba(0, 0, 0, 0.02); border-radius: 8px; font-size: 14px; color: rgba(0, 0, 0, 0.65); line-height: 22px; padding: 16px 19px; min-width: 220px; flex: 1;"
                              >
                                <div
                                  style="display: flex; gap: 4px; align-items: center;"
                                >
                                  <div
                                    style="width: 48px; height: 48px; line-height: 22px; background-size: 100%; text-align: center; padding: 8px 16px 16px 12px; color: rgb(255, 255, 255); font-weight: bold; background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/daaf8d50-8e6d-4251-905d-676a24ddfa12.svg);"
                                  >
                                    3
                                  </div>
                                  <div
                                    style="font-size: 16px; color: rgba(0, 0, 0, 0.88); padding-bottom: 8px;"
                                  >
                                    了解 Pro Components
                                  </div>
                                </div>
                                <div
                                  style="font-size: 14px; color: rgba(0, 0, 0, 0.65); text-align: justify; line-height: 22px; margin-bottom: 8px;"
                                >
                                  ProComponents 是一个基于 Ant Design 做了更高抽象的模板组件，以 一个组件就是一个页面为开发理念，为中后台开发带来更好的体验。
                                </div>
                                <a
                                  href="https://procomponents.ant.design"
                                  rel="noreferrer"
                                  target="_blank"
                                >
                                  了解更多 &gt;
                                </a>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class="ant-pro-layout-watermark"
                      style="z-index: 9; position: absolute; left: 0px; top: 0px; width: 100%; height: 100%; background-size: 332px; pointer-events: none; background-repeat: repeat;"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div
              class="ant-pro-setting-drawer-handle "
              style="width: 48px; height: 48px;"
            >
              <span
                aria-label="setting"
                class="anticon anticon-setting"
                role="img"
                style="color: rgb(255, 255, 255); font-size: 20px;"
              >
                <svg
                  aria-hidden="true"
                  data-icon="setting"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
                  />
                </svg>
              </span>
            </div>
          </main>
          <footer
            class="ant-layout-footer"
            style="padding: 0px; background: none;"
          >
            <div
              class="ant-pro-global-footer"
            >
              <div
                class="ant-pro-global-footer-list "
              >
                <a
                  class="ant-pro-global-footer-list-link "
                  href="https://pro.ant.design"
                  rel="noreferrer"
                  target="_blank"
                  title="OA系统"
                >
                  OA系统
                </a>
                <a
                  class="ant-pro-global-footer-list-link "
                  href="https://github.com/ant-design/ant-design-pro"
                  rel="noreferrer"
                  target="_blank"
                  title="github"
                >
                  <span
                    aria-label="github"
                    class="anticon anticon-github"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="github"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M511.6 76.3C264.3 76.2 64 276.4 64 523.5 64 718.9 189.3 885 363.8 946c23.5 5.9 19.9-10.8 19.9-22.2v-77.5c-135.7 15.9-141.2-73.9-150.3-88.9C215 726 171.5 718 184.5 703c30.9-15.9 62.4 4 98.9 57.9 26.4 39.1 77.9 32.5 104 26 5.7-23.5 17.9-44.5 34.7-60.8-140.6-25.2-199.2-111-199.2-213 0-49.5 16.3-95 48.3-131.7-20.4-60.5 1.9-112.3 4.9-120 58.1-5.2 118.5 41.6 123.2 45.3 33-8.9 70.7-13.6 112.9-13.6 42.4 0 80.2 4.9 113.5 13.9 11.3-8.6 67.3-48.8 121.3-43.9 2.9 7.7 24.7 58.3 5.5 118 32.4 36.8 48.9 82.7 48.9 132.3 0 102.2-59 188.1-200 212.9a127.5 127.5 0 0138.1 91v112.5c.8 9 0 17.9 15 17.9 177.1-59.7 304.6-227 304.6-424.1 0-247.2-200.4-447.3-447.5-447.3z"
                      />
                    </svg>
                  </span>
                </a>
                <a
                  class="ant-pro-global-footer-list-link "
                  href="https://ant.design"
                  rel="noreferrer"
                  target="_blank"
                  title="Ant Design"
                >
                  Ant Design
                </a>
              </div>
              <div
                class="ant-pro-global-footer-copyright "
              >
                <span
                  aria-label="copyright"
                  class="anticon anticon-copyright"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="copyright"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm5.6-532.7c53 0 89 33.8 93 83.4.3 4.2 3.8 7.4 8 7.4h56.7c2.6 0 4.7-2.1 4.7-4.7 0-86.7-68.4-147.4-162.7-147.4C407.4 290 344 364.2 344 486.8v52.3C344 660.8 407.4 734 517.3 734c94 0 162.7-58.8 162.7-141.4 0-2.6-2.1-4.7-4.7-4.7h-56.8c-4.2 0-7.6 3.2-8 7.3-4.2 46.1-40.1 77.8-93 77.8-65.3 0-102.1-47.9-102.1-133.6v-52.6c.1-87 37-135.5 102.2-135.5z"
                    />
                  </svg>
                </span>
                 2022 Produced by Ant Financial Experience Department
              </div>
            </div>
          </footer>
        </div>
      </section>
    </div>
  </div>
</DocumentFragment>
`;

exports[`Login Page should show login form 1`] = `
<DocumentFragment>
  <div
    class="css-trkbkn"
  >
    <div
      class="css-15lsw8v"
      data-lang="true"
    >
      <span
        class="ant-dropdown-trigger"
        style="cursor: pointer; padding: 12px; display: inline-flex; align-items: center; justify-content: center; font-size: 18px; vertical-align: middle;"
      >
        <i
          class="anticon"
        >
          <svg
            aria-hidden="true"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="0 0 24 24"
            width="1em"
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
            />
            <path
              class="css-c4d79v"
              d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z "
            />
          </svg>
        </i>
      </span>
    </div>
    <div
      style="flex: 1; padding: 32px 0px;"
    >
      <div
        class="ant-pro-form-login-container "
      >
        <div
          class="ant-pro-form-login-top  "
        >
          <div
            class="ant-pro-form-login-header "
          >
            <span
              class="ant-pro-form-login-logo "
            >
              <img
                alt="logo"
                src="/images/logo.png"
              />
            </span>
            <span
              class="ant-pro-form-login-title "
            >
              Ant Design
            </span>
          </div>
          <div
            class="ant-pro-form-login-desc "
          >
            Ant Design is the most influential web design specification in Xihu district
          </div>
        </div>
        <div
          class="ant-pro-form-login-main "
          style="width: 328px; min-width: 280px; max-width: 75vw;"
        >
          <div
            class="ant-pro"
          >
            <form
              autocomplete="off"
              class="ant-form ant-form-vertical ant-pro-form"
            >
              <input
                style="display: none;"
                type="text"
              />
              <div
                class="ant-tabs ant-tabs-top ant-tabs-centered"
              >
                <div
                  class="ant-tabs-nav"
                  role="tablist"
                >
                  <div
                    class="ant-tabs-nav-wrap"
                  >
                    <div
                      class="ant-tabs-nav-list"
                      style="transform: translate(0px, 0px);"
                    >
                      <div
                        class="ant-tabs-tab ant-tabs-tab-active"
                      >
                        <div
                          aria-controls="rc-tabs-test-panel-account"
                          aria-selected="true"
                          class="ant-tabs-tab-btn"
                          id="rc-tabs-test-tab-account"
                          role="tab"
                          tabindex="0"
                        >
                          Account Login
                        </div>
                      </div>
                      <div
                        class="ant-tabs-tab"
                      >
                        <div
                          aria-controls="rc-tabs-test-panel-mobile"
                          aria-selected="false"
                          class="ant-tabs-tab-btn"
                          id="rc-tabs-test-tab-mobile"
                          role="tab"
                          tabindex="0"
                        >
                          Phone Login
                        </div>
                      </div>
                      <div
                        class="ant-tabs-ink-bar ant-tabs-ink-bar-animated"
                      />
                    </div>
                  </div>
                  <div
                    class="ant-tabs-nav-operations ant-tabs-nav-operations-hidden"
                  >
                    <button
                      aria-controls="rc-tabs-test-more-popup"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-hidden="true"
                      class="ant-tabs-nav-more"
                      id="rc-tabs-test-more"
                      style="visibility: hidden; order: 1;"
                      tabindex="-1"
                      type="button"
                    >
                      <span
                        aria-label="ellipsis"
                        class="anticon anticon-ellipsis"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="ellipsis"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
                          />
                        </svg>
                      </span>
                    </button>
                  </div>
                </div>
                <div
                  class="ant-tabs-content-holder"
                >
                  <div
                    class="ant-tabs-content ant-tabs-content-top"
                  >
                    <div
                      aria-hidden="false"
                      aria-labelledby="rc-tabs-test-tab-account"
                      class="ant-tabs-tabpane ant-tabs-tabpane-active"
                      id="rc-tabs-test-panel-account"
                      role="tabpanel"
                      tabindex="0"
                    />
                  </div>
                </div>
              </div>
              <div
                class="ant-form-item"
              >
                <div
                  class="ant-row ant-form-item-row"
                >
                  <div
                    class="ant-col ant-form-item-control"
                  >
                    <div
                      class="ant-form-item-control-input"
                    >
                      <div
                        class="ant-form-item-control-input-content"
                      >
                        <span
                          class="ant-input-affix-wrapper ant-input-affix-wrapper-lg"
                        >
                          <span
                            class="ant-input-prefix"
                          >
                            <span
                              aria-label="user"
                              class="anticon anticon-user"
                              role="img"
                            >
                              <svg
                                aria-hidden="true"
                                data-icon="user"
                                fill="currentColor"
                                focusable="false"
                                height="1em"
                                viewBox="64 64 896 896"
                                width="1em"
                              >
                                <path
                                  d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
                                />
                              </svg>
                            </span>
                          </span>
                          <input
                            class="ant-input ant-input-lg"
                            id="username"
                            placeholder="Username: admin or user"
                            type="text"
                            value=""
                          />
                          <span
                            class="ant-input-suffix"
                          >
                            <span
                              class="ant-input-clear-icon ant-input-clear-icon-hidden"
                              role="button"
                              tabindex="-1"
                            >
                              <span
                                aria-label="close-circle"
                                class="anticon anticon-close-circle"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="close-circle"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  viewBox="64 64 896 896"
                                  width="1em"
                                >
                                  <path
                                    d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm165.4 618.2l-66-.3L512 563.4l-99.3 118.4-66.1.3c-4.4 0-8-3.5-8-8 0-1.9.7-3.7 1.9-5.2l130.1-155L340.5 359a8.32 8.32 0 01-1.9-5.2c0-4.4 3.6-8 8-8l66.1.3L512 464.6l99.3-118.4 66-.3c4.4 0 8 3.5 8 8 0 1.9-.7 3.7-1.9 5.2L553.5 514l130 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="ant-form-item"
              >
                <div
                  class="ant-row ant-form-item-row"
                >
                  <div
                    class="ant-col ant-form-item-control"
                  >
                    <div
                      class="ant-form-item-control-input"
                    >
                      <div
                        class="ant-form-item-control-input-content"
                      >
                        <span
                          class="ant-input-affix-wrapper ant-input-password ant-input-password-large ant-input-affix-wrapper-lg"
                        >
                          <span
                            class="ant-input-prefix"
                          >
                            <span
                              aria-label="lock"
                              class="anticon anticon-lock"
                              role="img"
                            >
                              <svg
                                aria-hidden="true"
                                data-icon="lock"
                                fill="currentColor"
                                focusable="false"
                                height="1em"
                                viewBox="64 64 896 896"
                                width="1em"
                              >
                                <path
                                  d="M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"
                                />
                              </svg>
                            </span>
                          </span>
                          <input
                            class="ant-input ant-input-lg"
                            id="password"
                            placeholder="Password: ant.design"
                            type="password"
                            value=""
                          />
                          <span
                            class="ant-input-suffix"
                          >
                            <span
                              aria-label="eye-invisible"
                              class="anticon anticon-eye-invisible ant-input-password-icon"
                              role="img"
                              tabindex="-1"
                            >
                              <svg
                                aria-hidden="true"
                                data-icon="eye-invisible"
                                fill="currentColor"
                                focusable="false"
                                height="1em"
                                viewBox="64 64 896 896"
                                width="1em"
                              >
                                <path
                                  d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"
                                />
                                <path
                                  d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"
                                />
                              </svg>
                            </span>
                          </span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                style="margin-bottom: 24px;"
              >
                <label
                  class="ant-checkbox-wrapper ant-checkbox-wrapper-checked"
                >
                  <span
                    class="ant-checkbox ant-checkbox-checked"
                  >
                    <input
                      checked=""
                      class="ant-checkbox-input"
                      id="autoLogin"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="ant-checkbox-inner"
                    />
                  </span>
                  <span>
                    Remember me
                  </span>
                </label>
                <a
                  style="float: right;"
                >
                  Forgot Password ?
                </a>
              </div>
              <button
                class="ant-btn ant-btn-primary ant-btn-lg"
                style="width: 100%;"
                type="button"
              >
                <span>
                  Login
                </span>
              </button>
            </form>
          </div>
          <div
            class="ant-pro-form-login-main-other "
          >
            Login with :
            <span
              aria-label="alipay-circle"
              class="anticon anticon-alipay-circle css-18yqg2u"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="alipay-circle"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M308.6 545.7c-19.8 2-57.1 10.7-77.4 28.6-61 53-24.5 150 99 150 71.8 0 143.5-45.7 199.8-119-80.2-38.9-148.1-66.8-221.4-59.6zm460.5 67c100.1 33.4 154.7 43 166.7 44.8A445.9 445.9 0 00960 512c0-247.4-200.6-448-448-448S64 264.6 64 512s200.6 448 448 448c155.9 0 293.2-79.7 373.5-200.5-75.6-29.8-213.6-85-286.8-120.1-69.9 85.7-160.1 137.8-253.7 137.8-158.4 0-212.1-138.1-137.2-229 16.3-19.8 44.2-38.7 87.3-49.4 67.5-16.5 175 10.3 275.7 43.4 18.1-33.3 33.4-69.9 44.7-108.9H305.1V402h160v-56.2H271.3v-31.3h193.8v-80.1s0-13.5 13.7-13.5H557v93.6h191.7v31.3H557.1V402h156.4c-15 61.1-37.7 117.4-66.2 166.8 47.5 17.1 90.1 33.3 121.8 43.9z"
                />
              </svg>
            </span>
            <span
              aria-label="taobao-circle"
              class="anticon anticon-taobao-circle css-18yqg2u"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="taobao-circle"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zM315.7 291.5c27.3 0 49.5 22.1 49.5 49.4s-22.1 49.4-49.5 49.4a49.4 49.4 0 110-98.8zM366.9 578c-13.6 42.3-10.2 26.7-64.4 144.5l-78.5-49s87.7-79.8 105.6-116.2c19.2-38.4-21.1-58.9-21.1-58.9l-60.2-37.5 32.7-50.2c45.4 33.7 48.7 36.6 79.2 67.2 23.8 23.9 20.7 56.8 6.7 100.1zm427.2 55c-15.3 143.8-202.4 90.3-202.4 90.3l10.2-41.1 43.3 9.3c80 5 72.3-64.9 72.3-64.9V423c.6-77.3-72.6-85.4-204.2-38.3l30.6 8.3c-2.5 9-12.5 23.2-25.2 38.6h176v35.6h-99.1v44.5h98.7v35.7h-98.7V622c14.9-4.8 28.6-11.5 40.5-20.5l-8.7-32.5 46.5-14.4 38.8 94.9-57.3 23.9-10.2-37.8c-25.6 19.5-78.8 48-171.8 45.4-99.2 2.6-73.7-112-73.7-112l2.5-1.3H472c-.5 14.7-6.6 38.7 1.7 51.8 6.8 10.8 24.2 12.6 35.3 13.1 1.3.1 2.6.1 3.9.1v-85.3h-101v-35.7h101v-44.5H487c-22.7 24.1-43.5 44.1-43.5 44.1l-30.6-26.7c21.7-22.9 43.3-59.1 56.8-83.2-10.9 4.4-22 9.2-33.6 14.2-11.2 14.3-24.2 29-38.7 43.5.5.8-50-28.4-50-28.4 52.2-44.4 81.4-139.9 81.4-139.9l72.5 20.4s-5.9 14-18.4 35.6c290.3-82.3 307.4 50.5 307.4 50.5s19.1 91.8 3.8 235.7z"
                />
              </svg>
            </span>
            <span
              aria-label="weibo-circle"
              class="anticon anticon-weibo-circle css-18yqg2u"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="weibo-circle"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-44.4 672C353.1 736 236 680.4 236 588.9c0-47.8 30.2-103.1 82.3-155.3 69.5-69.6 150.6-101.4 181.1-70.8 13.5 13.5 14.8 36.8 6.1 64.6-4.5 14 13.1 6.3 13.1 6.3 56.2-23.6 105.2-25 123.1.7 9.6 13.7 8.6 32.8-.2 55.1-4.1 10.2 1.3 11.8 9 14.1 31.7 9.8 66.9 33.6 66.9 75.5.2 69.5-99.7 156.9-249.8 156.9zm207.3-290.8a34.9 34.9 0 00-7.2-34.1 34.68 34.68 0 00-33.1-10.7 18.24 18.24 0 01-7.6-35.7c24.1-5.1 50.1 2.3 67.7 21.9 17.7 19.6 22.4 46.3 14.9 69.8a18.13 18.13 0 01-22.9 11.7 18.18 18.18 0 01-11.8-22.9zm106 34.3s0 .1 0 0a21.1 21.1 0 01-26.6 13.7 21.19 21.19 0 01-13.6-26.7c11-34.2 4-73.2-21.7-101.8a104.04 104.04 0 00-98.9-32.1 21.14 21.14 0 01-25.1-16.3 21.07 21.07 0 0116.2-25.1c49.4-10.5 102.8 4.8 139.1 45.1 36.3 40.2 46.1 95.1 30.6 143.2zm-334.5 6.1c-91.4 9-160.7 65.1-154.7 125.2 5.9 60.1 84.8 101.5 176.2 92.5 91.4-9.1 160.7-65.1 154.7-125.3-5.9-60.1-84.8-101.5-176.2-92.4zm80.2 141.7c-18.7 42.3-72.3 64.8-117.8 50.1-43.9-14.2-62.5-57.7-43.3-96.8 18.9-38.4 68-60.1 111.5-48.8 45 11.7 68 54.2 49.6 95.5zm-93-32.2c-14.2-5.9-32.4.2-41.2 13.9-8.8 13.8-4.7 30.2 9.3 36.6 14.3 6.5 33.2.3 42-13.8 8.8-14.3 4.2-30.6-10.1-36.7zm34.9-14.5c-5.4-2.2-12.2.5-15.4 5.8-3.1 5.4-1.4 11.5 4.1 13.8 5.5 2.3 12.6-.3 15.8-5.8 3-5.6 1-11.8-4.5-13.8z"
                />
              </svg>
            </span>
          </div>
        </div>
      </div>
    </div>
    <footer
      class="ant-layout-footer"
      style="padding: 0px; background: none;"
    >
      <div
        class="ant-pro-global-footer"
      >
        <div
          class="ant-pro-global-footer-list "
        >
          <a
            class="ant-pro-global-footer-list-link "
            href="https://pro.ant.design"
            rel="noreferrer"
            target="_blank"
            title="OA系统"
          >
            OA系统
          </a>
          <a
            class="ant-pro-global-footer-list-link "
            href="https://github.com/ant-design/ant-design-pro"
            rel="noreferrer"
            target="_blank"
            title="github"
          >
            <span
              aria-label="github"
              class="anticon anticon-github"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="github"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M511.6 76.3C264.3 76.2 64 276.4 64 523.5 64 718.9 189.3 885 363.8 946c23.5 5.9 19.9-10.8 19.9-22.2v-77.5c-135.7 15.9-141.2-73.9-150.3-88.9C215 726 171.5 718 184.5 703c30.9-15.9 62.4 4 98.9 57.9 26.4 39.1 77.9 32.5 104 26 5.7-23.5 17.9-44.5 34.7-60.8-140.6-25.2-199.2-111-199.2-213 0-49.5 16.3-95 48.3-131.7-20.4-60.5 1.9-112.3 4.9-120 58.1-5.2 118.5 41.6 123.2 45.3 33-8.9 70.7-13.6 112.9-13.6 42.4 0 80.2 4.9 113.5 13.9 11.3-8.6 67.3-48.8 121.3-43.9 2.9 7.7 24.7 58.3 5.5 118 32.4 36.8 48.9 82.7 48.9 132.3 0 102.2-59 188.1-200 212.9a127.5 127.5 0 0138.1 91v112.5c.8 9 0 17.9 15 17.9 177.1-59.7 304.6-227 304.6-424.1 0-247.2-200.4-447.3-447.5-447.3z"
                />
              </svg>
            </span>
          </a>
          <a
            class="ant-pro-global-footer-list-link "
            href="https://ant.design"
            rel="noreferrer"
            target="_blank"
            title="Ant Design"
          >
            Ant Design
          </a>
        </div>
        <div
          class="ant-pro-global-footer-copyright "
        >
          <span
            aria-label="copyright"
            class="anticon anticon-copyright"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="copyright"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm5.6-532.7c53 0 89 33.8 93 83.4.3 4.2 3.8 7.4 8 7.4h56.7c2.6 0 4.7-2.1 4.7-4.7 0-86.7-68.4-147.4-162.7-147.4C407.4 290 344 364.2 344 486.8v52.3C344 660.8 407.4 734 517.3 734c94 0 162.7-58.8 162.7-141.4 0-2.6-2.1-4.7-4.7-4.7h-56.8c-4.2 0-7.6 3.2-8 7.3-4.2 46.1-40.1 77.8-93 77.8-65.3 0-102.1-47.9-102.1-133.6v-52.6c.1-87 37-135.5 102.2-135.5z"
              />
            </svg>
          </span>
           2022 Produced by Ant Financial Experience Department
        </div>
      </div>
    </footer>
  </div>
</DocumentFragment>
`;
