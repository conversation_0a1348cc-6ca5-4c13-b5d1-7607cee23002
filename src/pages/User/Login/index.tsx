import Footer from '@/components/Footer';
import { login } from '@/services/oa/auth';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { LoginForm, ProFormText } from '@ant-design/pro-components';
import { useEmotionCss } from '@ant-design/use-emotion-css';
import { Helmet, history } from '@umijs/max';
import { message } from 'antd';
import classNames from 'classnames/bind';
import React, { useState } from 'react';
import Settings from '../../../../config/defaultSettings';
import ForgetPasswordModal from './components/ForgetPasswordModal';
import styles from './index.less';

const cx = classNames.bind(styles);

const Login: React.FC = () => {
  const [modalVisit, setModalVisit] = useState(false);

  const containerClassName = useEmotionCss(() => {
    return {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage: "url('/images/bg2.jpeg')",
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
    };
  });

  const handleSubmit = async (values: any) => {
    try {
      // 登录
      const res = await login(values);
      const { code, data } = res || {};
      if (code === 200) {
        message.success('登录成功');
        localStorage.setItem('RKLINK_OA_TOKEN', data?.authorization || '');
        const urlParams = new URL(window.location.href).searchParams;

        setTimeout(() => {
          history.push(urlParams.get('redirect') || '/');
          window.location.reload();
        }, 500);
        return;
      }
    } catch (error) {
      console.log('🚗 🚗 🚗 ~ file: index.tsx:47 ~ handleSubmit ~ error:', error);
    }
  };

  return (
    <div className={containerClassName}>
      <Helmet>
        <title>登录 - {Settings.title}</title>
      </Helmet>
      <div className={cx('login-content')}>
        <div className={cx('login-bg')} />
        <div className={cx('login-form')}>
          <LoginForm
            contentStyle={{
              minWidth: 280,
              maxWidth: '75vw',
            }}
            logo={<img alt="logo" src="/images/logo_simple.png" />}
            title="融科智联-OA"
            onFinish={async (values) => {
              await handleSubmit(values as any);
            }}
          >
            <ProFormText
              name="account"
              fieldProps={{
                size: 'large',
                prefix: <UserOutlined />,
              }}
              placeholder="请输入企业邮箱或手机号"
              rules={[
                {
                  required: true,
                  message: '请输入企业邮箱或手机号!',
                },
              ]}
            />
            <ProFormText.Password
              name="password"
              fieldProps={{
                size: 'large',
                prefix: <LockOutlined />,
              }}
              placeholder="请输入密码"
              rules={[
                {
                  required: true,
                  message: '请输入密码！',
                },
              ]}
            />
            <div
              style={{
                marginBlockEnd: 24,
                overflow: 'hidden',
              }}
            >
              <a
                style={{
                  float: 'right',
                }}
                onClick={() => {
                  setModalVisit(true);
                }}
              >
                忘记密码
              </a>
            </div>
          </LoginForm>
          <Footer />
        </div>
      </div>
      <ForgetPasswordModal open={modalVisit} onOpenChange={setModalVisit} />
    </div>
  );
};

export default Login;
