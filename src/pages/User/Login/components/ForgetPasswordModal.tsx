import { forgetPassword } from '@/services/oa/auth';
import { requiredRule } from '@/utils/setting';
import { ModalForm, ModalFormProps, ProFormText } from '@ant-design/pro-form';
import { useRequest } from '@umijs/max';
import { Result } from 'antd';
import { FC, memo, useEffect, useState } from 'react';

const ForgetPasswordModal: FC<ModalFormProps> = (props) => {
  const [done, setDone] = useState(false);
  useEffect(() => {
    setDone(false);
  }, [props.open]);
  const { run: reset } = useRequest((email) => forgetPassword(email), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      setDone(true);
    },
    formatResult: (res) => res,
  });

  return (
    <ModalForm
      title="重置密码"
      onFinish={async (values) => {
        await reset(values);
      }}
      modalProps={{
        destroyOnClose: true,
      }}
      width={460}
      style={{ minHeight: 100 }}
      submitter={
        !done && {
          resetButtonProps: false,
        }
      }
      {...props}
    >
      {done ? (
        <Result
          status="success"
          title="密码重置成功！"
          subTitle="您的新密码已经发至您的邮箱，请登录邮箱查看！"
        />
      ) : (
        <ProFormText
          name="email"
          label="请输入您的邮箱账号，进行重置密码"
          rules={[
            requiredRule,
            {
              type: 'email',
            },
          ]}
          placeholder="请输入"
        />
      )}
    </ModalForm>
  );
};

export default memo(ForgetPasswordModal);
