import { SALARY_CHANGE_REASON } from '@/enums';
import { useGradeList } from '@/hooks/useGradeList';
import {
  EMPLOYEE_TYPE,
  GENDER,
  WORK_STATUS,
} from '@/pages/HumanResources/Employees/components/EmployeesEnums';
import { getDepartmentTree } from '@/services/oa/department';
import { summaryAddRecord, summaryEdit, summaryPage, summaryRemove } from '@/services/oa/salary';
import {
  decryptWithAES,
  decryptWithRSA,
  encryptWithAES,
  encryptWithRSA,
  generateAESKey,
  option2enum,
} from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import {
  ActionType,
  ModalForm,
  PageContainer,
  ProColumns,
  ProFormDatePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { Access, useAccess, useRequest } from '@umijs/max';
import { Form, message, Modal } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import React, { useRef, useState } from 'react';
// // 生成 256-bit (32 bytes) 的 AES 密钥
const aesKey = generateAESKey(); // 示例输出: a1b2c3d4e5f67890...

const encryptedData = encryptWithRSA(aesKey);

const Payroll: React.FC = () => {
  const tableRef = useRef<ActionType>();
  const [adjustmentForm] = Form.useForm();
  const [adjustmentModalVisible, setAdjustmentModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<API.UserSalaryResp>();
  const [modalMode, setModalMode] = useState<'adjust' | 'addRecord'>('adjust');
  // 控制展开行的状态
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const { gradeList, loading: gradeLoading } = useGradeList();
  const { data: treeData = [] } = useRequest(() => getDepartmentTree());

  const {
    canEditSalaryRecord = false,
    canAddSalaryRecord = false,
    canDeleteSalaryRecord = false,
  } = useAccess();

  // 调整工资
  const { run: handleAdjustSalary, loading: adjustLoading } = useRequest(
    (values) => {
      const preTaxSalary =
        (values.benefitMoney - 0 || 0) +
        (values.baseSalary - 0 || 0) +
        (values.otherSalary - 0 || 0);
      return summaryEdit({
        ...values,
        employeeId: currentRecord?.employeeId,
        baseSalary: encryptWithAES((values.baseSalary || 0).toString(), aesKey),
        otherSalary: encryptWithAES((values.otherSalary || 0).toString(), aesKey),
        preTaxSalary: encryptWithAES(preTaxSalary.toString(), aesKey),
        aesKey: encryptedData,
      });
    },
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res: any) => {
        if (res?.code === 200) {
          message.success('调整成功');
          setAdjustmentModalVisible(false);
          tableRef.current?.reload();
        }
      },
    },
  );

  // 新增记录
  const { run: handleAddRecord, loading: addRecordLoading } = useRequest(
    (values) => {
      const preTaxSalary =
        (values.benefitMoney - 0 || 0) +
        (values.baseSalary - 0 || 0) +
        (values.otherSalary - 0 || 0);

      return summaryAddRecord({
        ...values,
        employeeId: currentRecord?.employeeId,
        baseSalary: encryptWithAES((values.baseSalary || 0).toString(), aesKey),
        otherSalary: encryptWithAES((values.otherSalary || 0).toString(), aesKey),
        preTaxSalary: encryptWithAES(preTaxSalary.toString(), aesKey),

        aesKey: encryptedData,
      });
    },
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res: any) => {
        if (res?.code === 200) {
          message.success('新增成功');
          setAdjustmentModalVisible(false);
          tableRef.current?.reload();
        }
      },
    },
  );

  // 删除记录
  const { run: handleDeleteRecord } = useRequest((id: string) => summaryRemove({ id }), {
    manual: true,
    formatResult: (res) => res,
    onSuccess: (res: any) => {
      if (res?.code === 200) {
        message.success('删除成功');
        tableRef.current?.reload();
      }
    },
  });

  // 确认删除
  const confirmDelete = (record: API.AdjustmentRecordResp) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条调整记录吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        await handleDeleteRecord(record.id as string);
      },
    });
  };

  // 展开行 - 调整记录
  const expandedRowRender = (record: API.UserSalaryResp) => {
    const columns: ProColumns<API.AdjustmentRecordResp>[] = [
      {
        title: '调整日期',
        dataIndex: 'adjustmentDate',
        valueType: 'date',
      },
      {
        title: '变更原因',
        dataIndex: 'changeReason',
        valueType: 'select',
        valueEnum: option2enum(SALARY_CHANGE_REASON),
      },
      {
        title: '基本工资',
        dataIndex: 'baseSalary',
        valueType: 'money',
      },
      {
        title: '其他工资',
        dataIndex: 'otherSalary',
        valueType: 'money',
      },
      {
        title: '调整比例',
        dataIndex: 'adjustmentRatio',
        render: (_, record) => {
          const ratio = record.adjustmentRatio || 0;
          return `${ratio > 0 ? '+' : ''}${ratio}%`;
        },
      },
      {
        title: '福利等级',
        dataIndex: 'benefitLevel',
      },
      {
        title: '税前工资',
        dataIndex: 'preTaxSalary',
        valueType: 'money',
      },
      {
        title: '备注',
        dataIndex: 'remark',
        ellipsis: true,
      },
      {
        title: '操作',
        valueType: 'option',
        width: 80,
        render: (_, record) => {
          return [
            <Access key="delete" accessible={canDeleteSalaryRecord}>
              <a onClick={() => confirmDelete(record)}>删除</a>
            </Access>,
          ];
        },
      },
    ];

    return (
      <ProTable<API.AdjustmentRecordResp>
        columns={columns}
        headerTitle="调整记录"
        search={false}
        options={false}
        pagination={false}
        dataSource={record.adjustmentRecord || []}
        rowKey={(record) => record.id || Math.random().toString()}
      />
    );
  };

  const columns: ProColumns<API.UserSalaryResp>[] = [
    {
      title: '序号',
      dataIndex: 'sequenceNumber',
      width: 80,
      search: false,
    },
    {
      title: '姓名',
      dataIndex: 'employeeName',
      width: 100,
    },
    {
      title: '性别',
      dataIndex: 'sex',
      width: 80,
      valueEnum: option2enum(GENDER),
      search: false,
    },
    {
      title: '员工编号',
      dataIndex: 'employeeNumber',
      width: 120,
    },
    {
      title: '员工类型',
      dataIndex: 'employeeType',
      width: 100,
      hideInTable: true,
      valueEnum: option2enum(EMPLOYEE_TYPE),
    },
    {
      title: '部门',
      dataIndex: 'department',
      width: 120,
      search: false,
    },
    {
      title: '部门',
      dataIndex: 'department',
      hideInTable: true,
      valueType: 'select',
      fieldProps: () => ({
        options: treeData
          ?.filter((item: API.DepartmentTreeResp) => item.status === '1')
          ?.map((item: API.DepartmentTreeResp) => ({
            label: item.departmentName,
            value: item.id,
          })),
        showSearch: true,
        optionFilterProp: 'label',
        filterOption: true,
      }),
    },
    {
      title: '子部门',
      dataIndex: 'departmentBranch',
      width: 120,
      search: false,
    },
    {
      title: '岗位',
      dataIndex: 'designation',
      width: 120,
      search: false,
    },
    {
      title: '基本工资',
      dataIndex: 'baseSalary',
      width: 120,
      valueType: 'money',
      search: false,
    },
    {
      title: '其他工资',
      dataIndex: 'otherSalary',
      width: 120,
      valueType: 'money',
      search: false,
    },
    {
      title: '福利等级',
      dataIndex: 'benefitLevel',
      width: 100,
      search: false,
    },
    {
      title: '税前工资',
      dataIndex: 'preTaxSalary',
      width: 120,
      search: false,
      valueType: 'money',

      tooltip: '=(基本工资+福利等级)',
    },
    {
      title: '岗位状态',
      dataIndex: 'workStatus',
      width: 100,
      initialValue: '3',
      valueEnum: option2enum(WORK_STATUS?.filter((item) => ['3', '12'].includes(item.value))),
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <Access key="adjust" accessible={canEditSalaryRecord}>
          <a
            onClick={() => {
              setCurrentRecord(record);
              setModalMode('adjust');
              setAdjustmentModalVisible(true);
              adjustmentForm.resetFields();
            }}
          >
            调整
          </a>
        </Access>,
        <Access key="add" accessible={canAddSalaryRecord}>
          <a
            onClick={() => {
              setCurrentRecord(record);
              setModalMode('addRecord');
              setAdjustmentModalVisible(true);
              adjustmentForm.resetFields();
            }}
          >
            新增记录
          </a>
        </Access>,
      ],
    },
  ];

  return (
    <PageContainer header={{ title: false }}>
      <ProTable<API.UserSalaryResp>
        {...defaultTableConfig}
        rowKey="employeeId"
        columns={columns}
        actionRef={tableRef}
        scroll={{ x: '100%' }}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        expandable={{
          expandedRowRender,
          expandedRowKeys,
          onExpand: (expanded, record) => {
            if (expanded) {
              setExpandedRowKeys([record.employeeId as string]);
            } else {
              setExpandedRowKeys([]);
            }
          },
        }}
        request={async (params) => {
          const { current, pageSize, departmentId, employeeType, workStatus, ...rest } = params;
          const res = await summaryPage({
            pageNum: current,
            pageSize,
            extra: { employeeType, workStatus },
            filter: { departmentId },
            search: { ...rest },
          });

          const startIndex = (current! - 1) * pageSize!;
          const records = res.data?.records || [];
          const dataWithSequence = records.map((record: API.UserSalaryResp, index: number) => {
            const hasAESKey =
              record.aesKey && typeof record.aesKey === 'string' && record.aesKey.trim() !== '';
            const hasBaseSalary =
              record.baseSalary &&
              typeof record.baseSalary === 'string' &&
              record.baseSalary.trim() !== '';
            const hasOtherSalary =
              record.otherSalary &&
              typeof record.otherSalary === 'string' &&
              record.otherSalary.trim() !== '';
            const hasPreTaxSalary =
              record.preTaxSalary &&
              typeof record.preTaxSalary === 'string' &&
              record.preTaxSalary.trim() !== '';

            // 解密 baseSalary
            let decryptedBaseSalary = undefined;
            if (hasAESKey && hasBaseSalary) {
              try {
                decryptedBaseSalary = decryptWithAES(
                  record.baseSalary ?? '',
                  decryptWithRSA(record.aesKey ?? ''),
                ).toString();
              } catch (error) {
                console.error('baseSalary 解密失败:', error);
              }
            }

            // 解密 otherSalary
            let decryptedOtherSalary = undefined;
            if (hasAESKey && hasOtherSalary) {
              try {
                decryptedOtherSalary = decryptWithAES(
                  record.otherSalary ?? '',
                  decryptWithRSA(record.aesKey ?? ''),
                ).toString();
              } catch (error) {
                console.error('baseSalary 解密失败:', error);
              }
            }

            // 解密 preTaxSalary
            let decryptedPreTaxSalary = undefined;
            if (hasAESKey && hasPreTaxSalary) {
              try {
                decryptedPreTaxSalary = decryptWithAES(
                  record.preTaxSalary ?? '',
                  decryptWithRSA(record.aesKey ?? ''),
                ).toString();
              } catch (error) {
                console.error('preTaxSalary 解密失败:', error);
              }
            }

            // 解密 adjustmentRecord 中的字段（如果存在）
            let decryptedAdjustmentRecords = [] as API.AdjustmentRecordResp[];
            if (Array.isArray(record.adjustmentRecord)) {
              decryptedAdjustmentRecords = record.adjustmentRecord.map((adjustment) => {
                const hasEncryptedBaseSalary =
                  adjustment.baseSalary &&
                  typeof adjustment.baseSalary === 'string' &&
                  adjustment.baseSalary.trim() !== '';
                const hasEncryptedOtherSalary =
                  adjustment.otherSalary &&
                  typeof adjustment.otherSalary === 'string' &&
                  adjustment.otherSalary.trim() !== '';
                const hasPreTaxSalary =
                  adjustment.preTaxSalary &&
                  typeof adjustment.preTaxSalary === 'string' &&
                  adjustment.preTaxSalary.trim() !== '';

                let decryptedAdjustmentBaseSalary = undefined;
                let decryptedAdjustmentPreTaxSalary = undefined;
                let decryptedAdjustmentOtherSalary = undefined;

                if (hasAESKey && hasEncryptedBaseSalary) {
                  try {
                    decryptedAdjustmentBaseSalary = decryptWithAES(
                      adjustment.baseSalary ?? '',
                      decryptWithRSA(adjustment.aesKey ?? ''),
                    ).toString();
                  } catch (error) {
                    console.error('adjustment.baseSalary 解密失败:', error);
                  }
                }
                if (hasAESKey && hasEncryptedOtherSalary) {
                  try {
                    decryptedAdjustmentOtherSalary = decryptWithAES(
                      adjustment.otherSalary ?? '',
                      decryptWithRSA(adjustment.aesKey ?? ''),
                    ).toString();
                  } catch (error) {
                    console.error('adjustment.baseSalary 解密失败:', error);
                  }
                }
                if (hasAESKey && hasPreTaxSalary) {
                  try {
                    decryptedAdjustmentPreTaxSalary = decryptWithAES(
                      adjustment.preTaxSalary ?? '',
                      decryptWithRSA(adjustment.aesKey ?? ''),
                    ).toString();
                  } catch (error) {
                    console.error('adjustment.baseSalary 解密失败:', error);
                  }
                }

                return {
                  ...adjustment,
                  baseSalary: decryptedAdjustmentBaseSalary,
                  otherSalary: decryptedAdjustmentOtherSalary,
                  preTaxSalary: decryptedAdjustmentPreTaxSalary,
                };
              });
            }

            return {
              ...record,
              sequenceNumber: startIndex + index + 1,
              baseSalary: decryptedBaseSalary,
              otherSalary: decryptedOtherSalary,
              preTaxSalary: decryptedPreTaxSalary,
              adjustmentRecord: decryptedAdjustmentRecords,
            };
          });

          return {
            data: dataWithSequence,
            success: res.code === 200,
            total: res.data?.total || 0,
          };
        }}
      />

      {/* 调整表单 */}
      <ModalForm
        title={
          modalMode === 'adjust'
            ? `调整工资(${currentRecord?.employeeName}-${currentRecord?.department}-${currentRecord?.employeeNumber})`
            : `新增记录(${currentRecord?.employeeName}-${currentRecord?.department}-${currentRecord?.employeeNumber})`
        }
        form={adjustmentForm}
        open={adjustmentModalVisible}
        onOpenChange={setAdjustmentModalVisible}
        onFinish={async (values) => {
          if (modalMode === 'adjust') {
            await handleAdjustSalary(values);
          } else {
            await handleAddRecord(values);
          }
          return true;
        }}
        width="35%"
        submitter={{
          submitButtonProps: {
            loading: modalMode === 'adjust' ? adjustLoading : addRecordLoading,
          },
        }}
      >
        <div className="rk-none">
          <ProFormDigit name="benefitMoney" label="福利金额" />
        </div>
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          {modalMode === 'addRecord' && (
            <ProFormDatePicker
              width="lg"
              name="adjustmentDate"
              label="调整日期"
              rules={[requiredRule]}
              fieldProps={{
                disabledDate: (current) => {
                  // 禁用今天和今天之后的日期
                  const today = new Date();
                  today.setHours(0, 0, 0, 0);
                  return current && current.valueOf() >= today.valueOf();
                },
              }}
            />
          )}
          <ProFormSelect
            width="lg"
            name="changeReason"
            label="变更原因"
            options={SALARY_CHANGE_REASON}
            rules={[requiredRule]}
          />
          <ProFormDigit
            width="lg"
            name="baseSalary"
            label="基本工资"
            min={0}
            fieldProps={{
              precision: 2,
              prefix: '¥',
            }}
            rules={[
              requiredRule,
              {
                validator: (_, value) => {
                  if (value === undefined || value === null) {
                    return Promise.resolve();
                  }
                  // 检查小数点前的位数是否超过10位
                  const strValue = String(value);
                  const integerPart = strValue.split('.')[0];
                  if (integerPart.length > 10) {
                    return Promise.reject('基本工资小数点前最多10位数字');
                  }
                  return Promise.resolve();
                },
              },
            ]}
          />
          <ProFormDigit
            width="lg"
            name="otherSalary"
            label="其他工资"
            min={0}
            fieldProps={{
              precision: 2,
              prefix: '¥',
            }}
            rules={[
              {
                validator: (_, value) => {
                  if (value === undefined || value === null) {
                    return Promise.resolve();
                  }
                  // 检查小数点前的位数是否超过10位
                  const strValue = String(value);
                  const integerPart = strValue.split('.')[0];
                  if (integerPart.length > 10) {
                    return Promise.reject('其他工资小数点前最多10位数字');
                  }
                  return Promise.resolve();
                },
              },
            ]}
          />

          <ProFormSelect
            width="lg"
            name="benefitLevelId"
            label="福利等级"
            fieldProps={{
              loading: gradeLoading,
              fieldNames: {
                value: 'id',
                label: 'grade',
              },
              showSearch: true,
            }}
            options={gradeList as DefaultOptionType[]}
            rules={[requiredRule]}
            transform={(value, namePath) => ({
              [namePath]: value,
              // 计算福利金额
              benefitMoney: gradeList.find((item) => item.id === value)?.count || 0,
            })}
          />

          <ProFormTextArea width="lg" name="remark" label="备注" />
        </div>
      </ModalForm>
    </PageContainer>
  );
};

export default Payroll;
