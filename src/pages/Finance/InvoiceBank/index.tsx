import {
  defaultInvoicingBank,
  deleteInvoicingBank,
  getInvoicingBankOption,
} from '@/services/oa/dictionary';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message, Space, Switch } from 'antd';
import React, { useRef, useState } from 'react';
import InvoiceBankDrawerForm from './components/InvoiceBankDrawerForm';

const InvoiceBank: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [modalVisit, setModalVisit] = useState(false);

  const { run: deleteRecord } = useRequest((value) => deleteInvoicingBank(value), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const handleDelete = (record: API.BanksDto) => {
    const { id } = record;
    deleteRecord({ id });
  };

  const [defaultLoadingId, setDefaultLoadingId] = useState<string | undefined>();
  const { run: setDefaultBank } = useRequest((value) => defaultInvoicingBank(value), {
    manual: true,
    onSuccess: (res) => {
      setDefaultLoadingId(undefined);
      if (res.code !== 200) return;
      message.success('设置为默认成功');
      tableRef.current?.reload();
    },
    formatResult: (res) => res,
  });

  // 表格
  const columns: ProColumns<API.BanksDto>[] = [
    {
      title: '开票银行别名',
      dataIndex: 'bankAs',
      ellipsis: true,
      width: 120,
    },
    {
      title: '单位名称',
      dataIndex: 'institutionName',
      ellipsis: true,
      width: 120,
    },
    {
      title: '纳税人识别号',
      dataIndex: 'taxpayerNum',
      ellipsis: true,
      width: 120,
    },
    {
      title: '开户银行',
      dataIndex: 'bank',
      ellipsis: true,
      width: 120,
    },
    {
      title: '银行账号',
      dataIndex: 'account',
      ellipsis: true,
      width: 120,
    },
    {
      title: '联系电话',
      dataIndex: 'contactNumber',
      ellipsis: true,
      width: 100,
    },
    {
      title: '注册地址',
      dataIndex: 'registeredAddress',
      ellipsis: true,
      width: 120,
    },
    {
      title: '是否默认',
      dataIndex: 'hasDefault',
      width: 100,
      align: 'center',
      render: (hasDefault, record) => (
        <Switch
          checked={!!hasDefault}
          disabled={!!hasDefault || defaultLoadingId === record.id}
          loading={defaultLoadingId === record.id}
          onClick={() => {
            if (!hasDefault && record.id) {
              setDefaultLoadingId(record.id);
              setDefaultBank({ id: record.id });
            }
          }}
        />
      ),
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      align: 'center',
      width: 100,
      render: (text, record) => {
        return (
          <Space>
            <a key="delete" onClick={() => handleDelete(record)}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable
        {...defaultTableConfig}
        actionRef={tableRef}
        columns={columns}
        scroll={{ x: '100%' }}
        headerTitle="开票银行信息表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisit(true);
              }}
            >
              新建开票银行
            </Button>,
          ],
        }}
        pagination={false}
        request={async () => {
          const msg = await getInvoicingBankOption();
          return {
            data: msg?.data || [],
            success: msg.code === 200,
          };
        }}
      />
      <InvoiceBankDrawerForm
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default InvoiceBank;
