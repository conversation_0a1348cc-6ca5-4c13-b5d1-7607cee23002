import { createInvoicingBank } from '@/services/oa/dictionary';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useRef } from 'react';

const InvoiceBankDrawerForm: React.FC<DrawerFormProps> = ({ open, onOpenChange, onFinish }) => {
  const formRef = useRef<ProFormInstance>();

  return (
    <DrawerForm
      width={460}
      title={'新建'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const msg = await createInvoicingBank(value);
        const success = msg.code === 200;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
      submitter={{
        searchConfig: {
          submitText: '保存',
        },
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
    >
      <ProFormText
        name="bankAs"
        label="开票银行别名"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
      />
      <ProFormText
        name="institutionName"
        label="单位名称"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
      />
      <ProFormText
        name="taxpayerNum"
        label="纳税人识别号"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
      />
      <ProFormText
        name="bank"
        label="开户银行"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
      />
      <ProFormText
        name="account"
        label="银行账号"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
      />
      <ProFormText
        name="contactNumber"
        label="联系电话"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
      />
      <ProFormText
        name="registeredAddress"
        label="注册地址"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
      />
    </DrawerForm>
  );
};

export default InvoiceBankDrawerForm;
