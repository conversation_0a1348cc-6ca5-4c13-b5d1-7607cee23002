import { StatisticCard } from '@ant-design/pro-components';

const StatisticsCard: React.FC<{
  value?: any;
  onChange?: (val: any) => void;
}> = ({ value = {} }) => {
  const { busiCount, actualCount } = value || {};
  return (
    <StatisticCard.Group className="rklink-statistic">
      <StatisticCard
        statistic={{
          title: '累计报销金额',
          prefix: '¥',
          value: busiCount || 0,
        }}
      />
      <StatisticCard
        statistic={{
          title: '累计实际金额',
          prefix: '¥',
          value: actualCount || 0,
          status: 'success',
        }}
      />
    </StatisticCard.Group>
  );
};

export default StatisticsCard;
