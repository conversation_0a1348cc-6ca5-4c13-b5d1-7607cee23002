import RKSelectLabel from '@/components/RKSelectLabel';
import { useProjectList } from '@/hooks/useProjectList';
import { getDepartmentTree } from '@/services/oa/department';
import { finaCount } from '@/services/oa/fina';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProForm,
  ProTable,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import React, { useRef, useState } from 'react';
import StatisticsCard from './components/StatisticsCard';

const Statistical: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const { projectList, loading } = useProjectList();
  const [statistics, setStatistics] = useState<{
    busiCount?: number;
    actualCount?: number;
  }>({});
  const { data: departmentList = [], loading: departmentLoading } = useRequest(() =>
    getDepartmentTree(),
  );

  const columns: ProColumns<API.FinalDetailsResp>[] = [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        loading,
        showSearch: true,
        optionLabelProp: 'projectName',
        options: projectList?.map((item) => ({
          value: item.projectName,
          label: <RKSelectLabel title={item.projectName} info={item.projectNumber} />,
        })),
      },
    },
    {
      title: '部门',
      dataIndex: 'departmentName',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        loading: departmentLoading,
        showSearch: true,
        options: departmentList
          ?.filter((item) => item.status === '1')
          ?.map((item) => ({
            label: item.departmentName,
            value: item.departmentName,
          })),
      },
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      search: false,
      width: 200,
      ellipsis: true,
    },
    {
      title: '报销人',
      dataIndex: 'username',
      search: false,
    },
    {
      title: '部门',
      dataIndex: 'departmentName',
      hideInSearch: true,
    },
    {
      title: '报销单号',
      dataIndex: 'finaNumber',
      ellipsis: true,
      search: false,
      width: 200,
    },
    {
      title: '报销类型',
      dataIndex: 'finaType',
      search: false,
    },
    {
      title: '发生日期',
      dataIndex: 'finaDate',
      valueType: 'date',
      search: false,
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      valueType: 'date',
      hideInTable: true,
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      valueType: 'date',
      hideInTable: true,
    },
    {
      title: '费用类型',
      dataIndex: 'type',
      search: false,
    },
    {
      title: '报销金额',
      dataIndex: 'busi',
      search: false,
      valueType: 'money',
    },
    {
      title: '实际报销类型',
      dataIndex: 'actualType',
      search: false,
    },
    {
      title: '实际金额',
      dataIndex: 'actual',
      search: false,
      valueType: 'money',
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      {/* 统计卡片 */}
      <ProForm.Item>
        <StatisticsCard value={statistics} />
      </ProForm.Item>
      <ProTable<API.FinalDetailsResp>
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        rowKey="finaId"
        search={{
          collapsed: false,
          collapseRender: () => false,
        }}
        columns={columns}
        headerTitle="统计列表"
        request={async (params) => {
          const { projectName, departmentName, startTime, endTime, current, pageSize } = params;
          const FinaSearch = {
            pageNum: current!,
            pageSize: pageSize!,
            projectName: projectName,
            departName: departmentName,
            startTime,
            endTime,
          };
          const res = await finaCount(FinaSearch);

          //设置报销金额
          setStatistics({
            busiCount: res.data?.busiCount,
            actualCount: res.data?.actualCount,
          });
          return {
            data: res.data?.page?.records || [],
            success: true,
            total: res.data?.page?.total,
          };
        }}
      />
    </PageContainer>
  );
};

export default Statistical;
