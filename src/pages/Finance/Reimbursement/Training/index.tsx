import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { APPROVAL_STATUS, RESTITUTION_STATUS } from '@/enums';
import { deleteTrainIds, pageSearchTrainRe } from '@/services/oa/train';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, useAccess, useLocation, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import TrainDrawerForm from './components/TrainDrawerForm';

const Training: React.FC = () => {
  const {
    canAddTrainingExpenseReimbursement = false,
    canDeleteTrainingExpenseReimbursement = false,
  } = useAccess();
  const tableRef = useRef<ActionType | undefined>();
  const [id, setId] = useState<string | undefined>();
  const [drawerVisit, setDrawerVisit] = useState(false);
  const [selectedRows, setSelectedRows] = useState<API.TrainReimbursementReq[]>([]);

  const onEdit = (record: API.TrainReimbursementReq) => {
    const { id } = record;
    setDrawerVisit(true);
    setId(id);
  };

  // 从内部项目跳转，带有id则打开抽屉
  const location = useLocation();
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const id = queryParams.get('id');
    if (id) {
      setDrawerVisit(true);
      setId(id);
    }
  }, [location]);

  // 删除
  const { run: deleteRecord } = useRequest((ids) => deleteTrainIds({ ids: ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: API.TrainReimbursementReq[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(String(item.id)!);
      names.push(item.trainNumber!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.TrainReimbursementResp>[] = [
    {
      title: '编号',
      dataIndex: 'trainNumber',
      copyable: true,
      ellipsis: true,
      width: 160,
      fixed: 'left',
      render: (text, record) => (
        <a className="rk-a-span" onClick={() => onEdit(record)}>
          {text}
        </a>
      ),
    },
    {
      title: '员工姓名',
      dataIndex: 'employeeName',
      search: false,
      width: 100,
      ellipsis: true,
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
      width: 100,
      ellipsis: true,
    },
    {
      title: '返还状态',
      dataIndex: 'state',
      valueEnum: option2enum(RESTITUTION_STATUS),
      width: 100,
      ellipsis: true,
    },

    {
      title: '培训费用',
      dataIndex: 'trainSpend',
      valueType: 'money',
      search: false,
      width: 100,
      ellipsis: true,
    },
    {
      title: '培训内容',
      dataIndex: 'trainContent',
      width: 160,
      ellipsis: true,
      search: false,
    },
    {
      title: '发票提交状态',
      dataIndex: 'operateState',
      valueType: 'select',
      search: false,
      valueEnum: { SUBMIT: '已提交', NOT_SUBMIT: '未提交', NULL: '无' },
      width: 100,
      ellipsis: true,
    },
    {
      title: '返还期数',
      dataIndex: 'count',
      search: false,
      renderText: (text) => {
        return <>{text ? `${text}个月` : ''}</>;
      },
      width: 100,
      ellipsis: true,
    },
    {
      title: '返还开始时间',
      dataIndex: 'startTime',
      search: false,
      width: 100,
      ellipsis: true,
    },
    {
      title: '每月返还金额',
      dataIndex: 'amount',
      valueType: 'money',
      search: false,
    },
    {
      title: '操作',
      width: 100,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      hideInTable: !canDeleteTrainingExpenseReimbursement,
      render: (text, record) => {
        const { activiStatus } = record;
        return (
          <Access accessible={canDeleteTrainingExpenseReimbursement}>
            {activiStatus === '0' && (
              <Button
                type="link"
                className="inner-table-link"
                key="delete"
                onClick={() => handleDelete([record])}
              >
                删除
              </Button>
            )}
          </Access>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.TrainReimbursementResp>
        {...defaultTableConfig}
        actionRef={tableRef}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        rowSelection={
          canDeleteTrainingExpenseReimbursement && {
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRows(selectedRows);
            },
            getCheckboxProps: (record) => ({
              disabled: record?.activiStatus !== '0',
            }),
          }
        }
        columns={columns}
        headerTitle="培训列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddTrainingExpenseReimbursement}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setDrawerVisit(true);
                  setId(undefined);
                }}
              >
                新建培训报销
              </Button>
            </Access>,
          ],
        }}
        request={async (params) => {
          const { trainNumber, current, pageSize, state, activiStatus } = params;
          const search = { trainNumber };
          const filter = { state, activiStatus };
          return queryPagingTable<API.PageReq>(
            { current, pageSize, search, filter },
            pageSearchTrainRe,
          );
        }}
        polling={5000}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />

      <TrainDrawerForm
        id={id}
        open={drawerVisit}
        onOpenChange={(visible) => {
          setDrawerVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default Training;
