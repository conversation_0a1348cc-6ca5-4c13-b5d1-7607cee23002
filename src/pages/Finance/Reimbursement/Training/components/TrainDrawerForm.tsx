import RKPageHeader from '@/components/RKPageHeader';
import RKSelectLabel from '@/components/RKSelectLabel';
import TitleLink from '@/components/TitleLink';
import { RESTITUTION_STATUS } from '@/enums';
import {
  creatTrainRe,
  selectTrainReById,
  trainProjectList,
  updateTrainRe,
} from '@/services/oa/train';
import { queryFormData } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useAccess, useModel, useRequest } from '@umijs/max';
import { useCounter } from 'ahooks';
import { message } from 'antd';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useRef, useState } from 'react';

//通过培训金额计算返还期数
const calculateMonth = (money: number) => {
  let count = 0;
  if (money <= 10000) {
    count = 12;
  } else if (money > 10000 && money <= 30000) {
    count = 24;
  } else if (money > 30000 && money <= 50000) {
    count = 36;
  } else {
    count = 50;
  }
  return { count };
};

//通过返还期数、返还起始月、培训金额计算变更剩余返还月数、返还结束月、剩余返还金额、次年返还金额
const calculate = (count: number, startTime: string, trainMoney: number) => {
  //每月返还额=(培训金额/返还期数) 保留两位小数
  const amount = Number((trainMoney / count).toFixed(2));
  //当天日期
  const nowDay = dayjs().format('YYYY-MM-DD');
  //返还结束月
  const endTime = `${dayjs(startTime)
    .add(count - 1, 'month')
    .format('YYYY-MM')}-10`;
  //剩余返还月数----返还期数-已经返还的月数
  ///已返还月数,从今日到返还开始时间之间的值为正数时代表已经开始返还了，返还了几个月
  const diffValue = dayjs(nowDay).diff(startTime, 'month');
  const returnMonths = dayjs(nowDay).isAfter(startTime) ? diffValue + 1 : diffValue;
  //剩余返还的月数
  const remainderCounts = count - (returnMonths < 0 ? 0 : returnMonths);
  const remainderCount = remainderCounts > 0 ? remainderCounts : 0;

  //剩余返还金额---总额-已返还月数*每月返还额 若剩余返还月数小于或等于0则代表已经还完
  //已返还金额
  const RemainingAmount = trainMoney - amount * (returnMonths < 0 ? 0 : returnMonths);
  const remainderAmount = remainderCounts > 0 ? RemainingAmount.toFixed(2) : 0; //判断是否为负数

  //次年返还金额--判断次年是否为最后一年，若是则每月返还额*月数，否则为12*每月返还额，同时判断今年是否是最后一年，若是则为0
  const nextYearAmounts = dayjs().add(1, 'year').isSame(endTime, 'year')
    ? (dayjs(endTime).month() + 1) * amount
    : 12 * amount;
  const nextYearAmount =
    remainderCounts <= 0 || dayjs(nowDay).isSame(endTime, 'year') ? 0 : nextYearAmounts;

  return { endTime, remainderCount, remainderAmount, nextYearAmount, amount };
};

const TrainDrawerForm: React.FC<DrawerFormProps> = ({ open, onOpenChange, id, onFinish }) => {
  const formRef = useRef<ProFormInstance>();
  const isEdit = !!id;
  const {
    canEditTrainingExpenseReimbursement,
    canAddTrainingExpenseReimbursement,
    canSuperEditTrainingExpenseReimbursement,
  } = useAccess();
  const canEdit =
    (canEditTrainingExpenseReimbursement && isEdit) ||
    (!isEdit && canAddTrainingExpenseReimbursement);
  const [current, { inc }] = useCounter(0);
  const [status, setStatus] = useState<string>();
  const [trainMoney, setTrainMoney] = useState<number>(0);
  const canSuperEdit = canSuperEditTrainingExpenseReimbursement && status && status === '2';

  // 页面是否禁止编辑
  const isDisabled = (isEdit && status && Number(status) > 0 && !canSuperEdit) || !canEdit || false;

  // 获取用户基本信息
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  const { run: add } = useRequest((value) => creatTrainRe(value), {
    manual: true,
    formatResult: (res) => res,
  });

  const { run: edit, loading: editLoading } = useRequest((value) => updateTrainRe(value), {
    manual: true,
    formatResult: (res) => res,
  });

  const codeRef = useRef(0);

  const onSave = useCallback(() => {
    return new Promise((resolve) => {
      codeRef.current = 0;
      formRef.current?.submit();

      const checkFormInterval = setInterval(() => {
        formRef.current
          ?.validateFields()
          .then(() => {
            const isFormSubmitted = codeRef.current === 200;
            if (isFormSubmitted) {
              clearInterval(checkFormInterval); // 清除轮询
              resolve(200); // 表单提交成功，resolve Promise
            }
          })
          .catch(() => {
            resolve(500);
            clearInterval(checkFormInterval); // 清除轮询
          });
      }, 1000); // 每秒检查一次表单是否提交成功
    });
  }, []);

  // 获取项目
  const { data: nbProjectList, loading: nbProjectListLoading } = useRequest(() =>
    trainProjectList(),
  );

  useEffect(() => {
    if (trainMoney) {
      const { count } = calculateMonth(trainMoney);
      const startTime = formRef.current?.getFieldValue('startTime');
      const { endTime, amount, remainderCount, remainderAmount, nextYearAmount } = calculate(
        count,
        startTime,
        trainMoney,
      );
      formRef.current?.setFieldsValue({
        count,
        startTime,
        endTime,
        remainderAmount,
        nextYearAmount,
        amount,
        remainderCount,
      });
    }
  }, [trainMoney]);

  return (
    <DrawerForm<API.ProMemberReq>
      title={isEdit ? '编辑' : '新建'}
      width={'758'}
      formRef={formRef}
      open={open}
      onOpenChange={(visible) => {
        onOpenChange?.(visible);
      }}
      disabled={isDisabled}
      submitter={
        isDisabled
          ? false
          : {
              searchConfig: {
                submitText: '保存',
              },
            }
      }
      onFinish={async (value) => {
        const msg = isEdit ? await edit({ ...value, id: id }) : await add(value);
        const success = msg.code === 200;
        codeRef.current = msg.code!;
        if (success) {
          message.success('保存成功!');
          onFinish?.(value);
        }
        return isEdit ? false : success;
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      params={{ refresh: current }}
      request={async () => {
        const res = await queryFormData(
          {
            idReq: { id },
          },
          isEdit,
          selectTrainReById,
        );
        setStatus(res.activiStatus);
        return res;
      }}
      initialValues={{
        employeeId: currentUser?.id,
        employeeNumber: currentUser?.employeeNumber,
        employeeName: currentUser?.username,
        startTime: `${dayjs().add(1, 'month').format('YYYY-MM')}-10`,
      }}
      onValuesChange={(val, values) => {
        //培训金额改变直接影响返还期数、每月返还额
        if (val.trainSpend) {
          setTrainMoney(val.trainSpend);
        }
        //返还起始月改变直接影响剩余返还月、返还结束月、剩余返还金额、次年返还金额
        if (val.startTime) {
          const { count, trainSpend } = values;

          const { endTime, amount, remainderCount, remainderAmount, nextYearAmount } = calculate(
            count!,
            val.startTime,
            trainSpend!,
          );
          formRef.current?.setFieldsValue({
            amount,
            endTime,
            remainderCount,
            remainderAmount,
            nextYearAmount,
          });
        }
        if (val.count) {
          const { startTime, trainSpend } = values;

          const { endTime, amount, remainderCount, remainderAmount, nextYearAmount } = calculate(
            val.count!,
            startTime!,
            trainSpend!,
          );
          formRef.current?.setFieldsValue({
            amount,
            endTime,
            remainderCount,
            remainderAmount,
            nextYearAmount,
          });
        }
      }}
    >
      {id && (
        <ProFormDependency name={['activiStatus', 'trainNumber']}>
          {({ activiStatus, trainNumber }) => {
            return (
              <RKPageHeader
                id={id}
                status={activiStatus}
                title={trainNumber}
                approveType="TRAINING_REIMBURSEMENT_APPROVAL"
                onOperationCallback={inc}
                pageHeaderType="drawer"
                onSave={onSave}
                saveLoading={editLoading}
                saveDisabled={!canEditTrainingExpenseReimbursement}
              />
            );
          }}
        </ProFormDependency>
      )}
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
      </div>

      <ProForm.Group title="基础信息">
        <ProFormDependency name={[['employeeId']]}>
          {({ employeeId }) => {
            return (
              <ProFormText
                disabled
                width="md"
                name="employeeName"
                label={
                  <TitleLink path={employeeId && `/human-resources/employees/edit/${employeeId}`}>
                    员工姓名
                  </TitleLink>
                }
                placeholder="请输入"
                fieldProps={{
                  autoComplete: 'none',
                }}
                rules={[requiredRule]}
              />
            );
          }}
        </ProFormDependency>
        <ProFormDependency name={[['employeeId']]}>
          {({ employeeId }) => {
            return (
              <ProFormText
                disabled
                width="md"
                name="employeeNumber"
                label={
                  <TitleLink path={employeeId && `/human-resources/employees/edit/${employeeId}`}>
                    员工编号
                  </TitleLink>
                }
                placeholder="请输入"
                fieldProps={{
                  autoComplete: 'none',
                }}
                rules={[requiredRule]}
              />
            );
          }}
        </ProFormDependency>
        <ProFormMoney
          width="md"
          name="trainSpend"
          label="培训费用"
          placeholder="请输入"
          locale="zh_CN"
          min={0}
          fieldProps={{ precision: 2, step: 0.1 }}
          rules={[requiredRule]}
          disabled={!!(isDisabled || canSuperEdit)}
        />
        <ProFormSelect
          width="md"
          name="operateState"
          label="发票"
          placeholder="请输入"
          valueEnum={{ SUBMIT: '已提交', NOT_SUBMIT: '未提交', NULL: '无' }}
          rules={[requiredRule]}
          disabled={!!(isDisabled || canSuperEdit)}
        />
        <ProFormTextArea
          width="md"
          name="trainContent"
          label="培训内容"
          fieldProps={{
            autoSize: {
              minRows: 1,
              maxRows: 3,
            },
          }}
          rules={[requiredRule]}
          disabled={!!(isDisabled || canSuperEdit)}
        />
        <ProFormDependency name={['projectId']}>
          {({ projectId }) => {
            return (
              <ProFormSelect
                width="md"
                name="projectId"
                label={
                  <TitleLink path={projectId && `/project/internal/edit/${projectId}`}>
                    项目编号
                  </TitleLink>
                }
                disabled={!!(isDisabled || canSuperEdit)}
                rules={[requiredRule]}
                fieldProps={{
                  loading: nbProjectListLoading,
                  showSearch: true,
                  optionLabelProp: 'projectNumber',
                  filterOption: (inputValue, option) => {
                    return option?.keywords.indexOf(inputValue) >= 0;
                  },
                }}
                options={nbProjectList?.map((item: API.ProjectNameIdResp) => {
                  return {
                    value: item.id,
                    label: <RKSelectLabel title={item.projectNumber} info={item.projectName} />,
                    projectNumber: item?.projectNumber,
                    keywords: `${item.projectName}${item.projectNumber}`,
                  };
                })}
              />
            );
          }}
        </ProFormDependency>
      </ProForm.Group>

      <ProForm.Group title="返还信息">
        <ProFormDigit
          width="md"
          name="count"
          label="返还期数"
          placeholder="请输入"
          min={0}
          fieldProps={{ precision: 0, addonAfter: '个月' }}
        />
        <ProFormDatePicker.Month
          width="md"
          name="startTime"
          label="返还起始月"
          placeholder="请输入"
          transform={(value, namePath) => {
            return {
              [namePath]: `${dayjs(value).format('YYYY-MM')}-10`,
            };
          }}
          fieldProps={{
            format: 'YYYY-MM-DD',
          }}
        />
        <ProFormDigit
          width="md"
          name="remainderCount"
          label="剩余返还月"
          placeholder="请输入"
          min={0}
          fieldProps={{ precision: 0, addonAfter: '个月' }}
        />
        <ProFormDatePicker.Month
          width="md"
          name="endTime"
          label="返还结束月"
          placeholder="请输入"
          transform={(value, namePath) => {
            return {
              [namePath]: `${dayjs(value).format('YYYY-MM')}-10`,
            };
          }}
          fieldProps={{
            format: 'YYYY-MM-DD',
          }}
        />
        <ProFormMoney
          width="md"
          name="remainderAmount"
          label="剩余返还金额"
          placeholder="请输入"
          locale="zh_CN"
          min={0}
          fieldProps={{ precision: 2, step: 0.1 }}
        />
        <ProFormMoney
          width="md"
          name="nextYearAmount"
          label="次年返还金额"
          placeholder="请输入"
          locale="zh_CN"
          min={0}
          fieldProps={{ precision: 2, step: 0.1 }}
        />
        <ProFormMoney
          width="md"
          name="amount"
          label="每月返还额"
          placeholder="请输入"
          locale="zh_CN"
          min={0}
          fieldProps={{ precision: 2, step: 0.1 }}
        />
        <ProFormSelect
          width="md"
          name="state"
          label="返还状态"
          placeholder="请输入"
          options={RESTITUTION_STATUS}
        />
      </ProForm.Group>
    </DrawerForm>
  );
};

export default TrainDrawerForm;
