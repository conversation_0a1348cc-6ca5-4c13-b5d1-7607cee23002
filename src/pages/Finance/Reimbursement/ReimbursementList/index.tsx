import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { APPROVAL_STATUS } from '@/enums';
import { finaDel, finaPage } from '@/services/oa/fina';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  ModalForm,
  PageContainer,
  ProColumns,
  ProFormSelect,
  ProTable,
} from '@ant-design/pro-components';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { Button, Form, message, Modal } from 'antd';
import React, { useRef, useState } from 'react';

const ReimbursementList: React.FC = () => {
  const [form] = Form.useForm<{ type: string }>();
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.FinaPageResp[]>([]);
  const { canAddExpenseReimbursement = false, canDeleteExpenseReimbursement = false } = useAccess();

  const { run: deleteRecord } = useRequest((ids) => finaDel({ ids: ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: API.FinaPageResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(String(item.id)!);
      names.push(item.documentNumber!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.FinaPageResp>[] = [
    {
      title: '编号',
      dataIndex: 'documentNumber',
      copyable: true,
      ellipsis: true,
      width: 180,
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/finance/reimbursement/list/edit/${entity?.id}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
    },
    {
      title: '审核人',
      dataIndex: 'currentAudiUsers',
      width: 120,
      hideInSearch: true,
      renderText(text) {
        return text?.join(',');
      },
    },
    {
      title: '员工姓名',
      dataIndex: 'employeeName',
    },
    {
      title: '报销类型',
      dataIndex: 'type',
      valueType: 'select',
      valueEnum: { RC: '日常报销', CL: '差旅报销' },
    },
    {
      title: '报销日期',
      dataIndex: 'finaDate',
      valueType: 'date',
    },
    {
      title: '报销总金额',
      dataIndex: 'total',
      valueType: 'money',
      fieldProps: {
        locale: 'zh-CN',
        min: 0,
      },
      search: false,
    },
    {
      title: '实际总金额',
      dataIndex: 'actualTotal',
      valueType: 'money',
      fieldProps: {
        locale: 'zh-CN',
        min: 0,
      },
      search: false,
    },
    {
      title: '操作',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !canDeleteExpenseReimbursement,
      render: (text, record) => {
        const { activiStatus } = record;
        return (
          <Access accessible={canDeleteExpenseReimbursement}>
            {activiStatus === '0' && (
              <Button
                type="link"
                className="inner-table-link"
                key="delete"
                onClick={() => handleDelete([record])}
              >
                删除
              </Button>
            )}
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.FinaPageResp>
        {...defaultTableConfig}
        actionRef={tableRef}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        rowSelection={
          canDeleteExpenseReimbursement
            ? {
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
                getCheckboxProps: (record) => ({
                  disabled: record?.activiStatus !== '0',
                }),
              }
            : false
        }
        columns={columns}
        headerTitle="报销列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddExpenseReimbursement}>
              <ModalForm<{
                type: string;
              }>
                title="新建报销"
                width="40%"
                trigger={
                  <Button type="primary">
                    <PlusOutlined />
                    新建报销
                  </Button>
                }
                form={form}
                autoFocusFirstInput
                modalProps={{
                  destroyOnClose: true,
                }}
                submitter={{
                  searchConfig: {
                    submitText: '确认创建',
                  },
                }}
                submitTimeout={2000}
                onFinish={async (values) => {
                  history.push('/finance/reimbursement/list/add', {
                    type: values.type,
                  });
                  return true;
                }}
              >
                <div style={{ display: 'flex', justifyContent: 'center', padding: '20px 0' }}>
                  <ProFormSelect
                    name="type"
                    label="报销类型"
                    valueEnum={{ RC: '日常报销', CL: '差旅报销' }}
                    width="lg"
                    rules={[requiredRule]}
                  />
                </div>
              </ModalForm>
            </Access>,
          ],
        }}
        polling={5000}
        request={async (params) => {
          const { documentNumber, employeeName, finaDate, type, activiStatus, current, pageSize } =
            params;
          const search = { documentNumber, employeeName };
          const filter = { type, activiStatus };
          const extra = { finaDate };
          return queryPagingTable<API.PageReq>(
            { current, pageSize, search, filter, extra },
            finaPage,
          );
        }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default ReimbursementList;
