import RKSelectLabel from '@/components/RKSelectLabel';
import TitleLink from '@/components/TitleLink';
import BaseListContext from '@/Context/BaseListContext';
import { PROJECT_TYPE } from '@/enums';
import { getRandomId } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import {
  DrawerForm,
  ProColumns,
  ProFormDatePicker,
  ProFormInstance,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { ProFormDependency } from '@ant-design/pro-form';
import { Button, Form, Space } from 'antd';
import { produce } from 'immer';
import { useContext, useRef, useState } from 'react';
import {
  ACTUAL_Type,
  errorMessages,
  Expense_Type,
  requiredTrafficRules,
} from './DailyReimbursementDetailsInfo';

export const Travel_type = [
  { label: '飞机票', value: '飞机票' },
  { label: '火车轮船费', value: '火车轮船费' },
  { label: '长途汽车费', value: '长途汽车费' },
  { label: '公共地铁', value: '公共地铁' },
  { label: '出租车费', value: '出租车费' },
  { label: '住宿费', value: '住宿费' },
];

type ValueProps = {
  key_?: string;
} & API.BusiProjReq;

const TravelReimbursementDetailsInfo: React.FC<{
  value?: ValueProps[];
  onChange?: (val: ValueProps[]) => void;
}> = ({ value, onChange }) => {
  const [form] = Form.useForm<ValueProps>();
  const formRef = useRef<ProFormInstance>();
  const [drawerVisit, setDrawerVisit] = useState(false);
  const { data } = useContext(BaseListContext);
  const {
    availableProjectList = [],
    availableProjectLoading,
    shWorkOrderList = [],
    shWorkOrderLoading,
    sqWorkOrderLoading,
  } = useContext(BaseListContext);

  const columns: ProColumns<ValueProps>[] = [
    {
      title: '项目类型',
      dataIndex: 'projectType',
      valueType: 'select',
      fieldProps: {
        options: PROJECT_TYPE,
      },
      width: 100,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      ellipsis: true,
      width: 180,
    },
    {
      title: '日期',
      dataIndex: 'time',
      valueType: 'date',
      width: 110,
    },
    {
      title: '费用类型',
      dataIndex: 'type',
      valueType: 'select',
      width: 110,
      fieldProps: {
        showSearch: true,
        options: [...Travel_type, ...Expense_Type],
      },
    },
    {
      title: '金额',
      dataIndex: 'money',
      valueType: 'money',
      fieldProps: {
        locale: 'zh-CN',
        min: 0,
        precision: 2,
        step: 0.1,
      },
      formItemProps: {
        rules: [requiredRule],
      },
      width: 120,
    },
    {
      title: '实际报销类型',
      dataIndex: 'actualType',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        options: [...Travel_type, ...ACTUAL_Type, { label: '出差补贴', value: '出差补贴' }],
      },
      width: 120,
    },
    {
      title: '实际报销金额',
      dataIndex: 'actualMoney',
      valueType: 'money',
      fieldProps: {
        locale: 'zh-CN',
        min: 0,
        precision: 2,
        step: 0.1,
      },
      formItemProps: {
        rules: [requiredRule],
      },
      width: 120,
    },
    {
      title: '正当理由',
      dataIndex: 'reason',
      formItemProps: {
        rules: [requiredRule],
      },
      width: 180,
      ellipsis: true,
    },
    {
      title: '操作',
      fixed: 'right',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <Button
              className="inner-table-link"
              type="link"
              onClick={() => {
                setDrawerVisit(true);
                form.setFieldsValue(record);
              }}
              disabled={data?.disabled}
            >
              修改
            </Button>
            <Button
              className="inner-table-link"
              type="link"
              onClick={() => {
                const updatedValue = produce(value || [], (draft) => {
                  return draft.filter((item) => item?.key_ !== record?.key_);
                });
                onChange?.(updatedValue);
              }}
            >
              删除
            </Button>
            <Button
              className="inner-table-link"
              type="link"
              onClick={() => {
                const newRecord = { ...record, key_: getRandomId() };
                const updatedValue = produce(value || [], (draft) => {
                  draft.push(newRecord);
                });
                onChange?.(updatedValue);
              }}
            >
              复制
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <ProTable
        {...defaultTableConfig}
        options={false}
        scroll={{ x: '100%' }}
        rowKey="key_"
        headerTitle="差旅报销明细表"
        dataSource={value}
        className="inner-table"
        columns={columns}
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setDrawerVisit(true);
              }}
            >
              添加
            </Button>,
          ],
        }}
      />

      <DrawerForm<ValueProps>
        width={460}
        open={drawerVisit}
        onOpenChange={setDrawerVisit}
        title="差旅报销明细"
        form={form}
        formRef={formRef}
        autoFocusFirstInput
        drawerProps={{
          destroyOnClose: true,
        }}
        submitter={{
          submitButtonProps: {
            disabled: data?.disabled,
          },
          resetButtonProps: {
            disabled: data?.disabled,
          },
        }}
        onFinish={async (values) => {
          const updatedValue = produce(value || [], (draft) => {
            const index = draft.findIndex((item) => item.key_ === values.key_);
            if (index === -1) {
              draft.push(values);
            } else {
              draft[index] = values;
            }
          });
          onChange?.(updatedValue);
          return true;
        }}
        onValuesChange={(val) => {
          if (val.projectType) {
            formRef.current?.setFieldsValue({
              workId: undefined,
              projectId: undefined,
              projectName: undefined,
            });
          }
        }}
      >
        <div className="rk-none">
          <ProFormText name="key_" initialValue={getRandomId()} />
          <ProFormText name="finaType" initialValue={'CL'} />
          <ProFormText name="projectId" />
        </div>
        <ProFormSelect
          name="projectType"
          label="项目类型"
          rules={[requiredRule]}
          fieldProps={{
            showSearch: true,
            onChange() {
              formRef.current?.setFieldValue('workNumber', undefined);
            },
          }}
          options={PROJECT_TYPE}
        />
        <ProFormDependency name={['projectType', 'workId']}>
          {({ projectType, workId }) => {
            if (['SH'].includes(projectType)) {
              return (
                <>
                  <ProFormSelect
                    name="workId"
                    label={
                      <TitleLink path={workId && `/work-orders/edit/${workId}`}>工单编号</TitleLink>
                    }
                    rules={[requiredRule]}
                    fieldProps={{
                      showSearch: true,
                      loading: shWorkOrderLoading || sqWorkOrderLoading,
                      optionLabelProp: 'workNumber',
                      filterOption: (inputValue, option) => {
                        return option?.keywords.indexOf(inputValue) >= 0;
                      },
                      options: shWorkOrderList?.map((item) => ({
                        value: item.workId,
                        label: <RKSelectLabel title={item.workNumber} info={item.projectName} />,
                        workNumber: item.workNumber,
                        keywords: `${item.workNumber}${item.projectName}`,
                        projectName: item.projectName,
                        projectNumber: item.projectNumber,
                        projectId: item.projectId,
                      })),
                      onChange: (value, option) => {
                        const opt = option as API.CurrentUserWorkResp;
                        formRef.current?.setFieldsValue({
                          projectId: opt?.projectId,
                          projectName: opt?.projectName,
                          projectNumber: opt?.projectNumber,
                          workNumber: opt?.workNumber,
                        });
                      },
                    }}
                  />
                </>
              );
            }
          }}
        </ProFormDependency>

        <ProFormDependency name={['projectType']}>
          {({ projectType }) => {
            return (
              <>
                <ProFormSelect
                  name="projectNumber"
                  disabled={['SH'].includes(projectType)}
                  label="项目编号"
                  rules={[requiredRule]}
                  fieldProps={{
                    showSearch: true,
                    loading: availableProjectLoading,
                    optionLabelProp: 'projectNumber',
                    onChange: (value, option) => {
                      formRef.current?.setFieldValue(
                        'projectName',
                        (option as { projectName?: string })?.projectName,
                      );
                      formRef.current?.setFieldValue('projectId', (option as { id?: string })?.id);
                    },
                    filterOption: (inputValue, option) => {
                      return option?.keywords.indexOf(inputValue) >= 0;
                    },
                  }}
                  options={availableProjectList
                    ?.filter((item) => item?.projectClassify === projectType)
                    .map((item) => {
                      return {
                        value: item.projectNumber,
                        label: <RKSelectLabel title={item.projectNumber} info={item.projectName} />,
                        projectName: item.projectName,
                        projectNumber: item.projectNumber,
                        id: item.id,
                        keywords: `${item.projectName}${item.projectNumber}`,
                      };
                    })}
                />
              </>
            );
          }}
        </ProFormDependency>

        <ProFormText disabled name="projectName" label="项目名称" rules={[requiredRule]} />

        <ProFormDatePicker
          name="time"
          label="日期"
          disabled={data?.disabled}
          rules={[requiredRule]}
        />
        <ProFormSelect
          name="invoice"
          tooltip="如果发票有其它状况请在此项进行选择并备注原因!"
          label="发票"
          valueEnum={{ 多提供发票: '多提供发票', 无发票: '无发票' }}
          disabled={data?.disabled}
        />
        <ProFormSelect
          name="type"
          label="费用类型"
          rules={[requiredRule]}
          fieldProps={{
            showSearch: true,
            options: [...Travel_type, ...Expense_Type],
          }}
          disabled={data?.disabled}
        />
        <ProFormMoney
          name="money"
          label="金额"
          rules={[requiredRule]}
          locale="zh-CN"
          fieldProps={{
            min: 0,
            precision: 2,
            step: 0.1,
          }}
          disabled={data?.disabled}
        />
        <ProFormSelect
          name="actualType"
          label="实际报销类型"
          rules={[requiredRule]}
          fieldProps={{
            showSearch: true,
            options: [...Travel_type, ...ACTUAL_Type, { label: '出差补贴', value: '出差补贴' }],
          }}
          disabled={data?.disabled}
        />
        <ProFormMoney
          name="actualMoney"
          label="实际报销金额"
          rules={[requiredRule]}
          locale="zh-CN"
          fieldProps={{
            min: 0,
            precision: 2,
            step: 0.1,
          }}
          disabled={data?.disabled}
        />
        <ProFormDependency name={['actualType']}>
          {({ actualType }) => {
            const typeOfTraffic = [
              '车辆费用-油费',
              '车辆费用-其他',
              '交通费',
              '飞机票',
              '火车轮船费',
              '长途汽车费',
              '公共地铁',
              '出租车费',
            ].includes(actualType);
            return (
              <ProFormTextArea
                name="reason"
                label="正当理由"
                tooltip={typeOfTraffic && `${errorMessages} e.g: 从北京到上海`}
                fieldProps={{
                  autoSize: {
                    minRows: 1,
                    maxRows: 3,
                  },
                }}
                disabled={data?.disabled}
                rules={[typeOfTraffic ? requiredTrafficRules : requiredRule]}
              />
            );
          }}
        </ProFormDependency>
        <ProFormTextArea
          name="remark"
          label="备注"
          disabled={data?.disabled}
          fieldProps={{
            autoSize: {
              minRows: 1,
              maxRows: 3,
            },
          }}
        />
      </DrawerForm>
    </>
  );
};
export default TravelReimbursementDetailsInfo;
