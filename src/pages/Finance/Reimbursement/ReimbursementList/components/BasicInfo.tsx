import RKCol from '@/components/RKCol';
import TitleLink from '@/components/TitleLink';
import BaseListContext from '@/Context/BaseListContext';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { getRandomId } from '@/utils';
import { requiredRule } from '@/utils/setting';
import { ProForm, ProFormDependency, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { Row } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { useContext } from 'react';
import DailyReimbursementDetailsInfo from './DailyReimbursementDetailsInfo';
import ReimbursementDetailsStatistics from './ReimbursementDetailsStatistics';
import TravelDetails from './TravelDetails';
import TravelReimbursementDetailsInfo from './TravelReimbursementDetailsInfo';

const BasicInfo: React.FC<WithRouteEditingProps> = () => {
  const { departmentList = [], departmentLoading } = useContext(BaseListContext);

  return (
    <>
      <Row gutter={24}>
        <ProFormDependency name={[['employeeId']]}>
          {({ employeeId }) => {
            return (
              <RKCol>
                <ProFormText
                  label={
                    <TitleLink path={employeeId && `/human-resources/employees/edit/${employeeId}`}>
                      员工名称
                    </TitleLink>
                  }
                  disabled
                  name="employeeName"
                  placeholder="请输入"
                  rules={[requiredRule]}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>
        <ProFormDependency name={[['employeeId']]}>
          {({ employeeId }) => {
            return (
              <RKCol>
                <ProFormText
                  label={
                    <TitleLink path={employeeId && `/human-resources/employees/edit/${employeeId}`}>
                      员工编号
                    </TitleLink>
                  }
                  name="employee"
                  disabled
                  rules={[requiredRule]}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>
        <RKCol>
          <ProFormSelect
            name="department"
            label="部门"
            disabled
            options={departmentList as DefaultOptionType[]}
            rules={[requiredRule]}
            fieldProps={{
              fieldNames: {
                label: 'departmentName',
                value: 'id',
              },
              showSearch: true,
              loading: departmentLoading,
            }}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name="type"
            label="报销类型"
            valueEnum={{ RC: '日常报销', CL: '差旅报销' }}
            rules={[requiredRule]}
            disabled
          />
        </RKCol>
      </Row>
      {/* 统计卡片 */}
      <ProFormDependency name={['busiProjList', 'dayInfoList', 'busiInfoList']}>
        {({ busiProjList, dayInfoList, busiInfoList }) => {
          //实际总金额=实际金额+补贴金额
          let total = 0, //报销总金额
            allowanceTotal = 0, //补贴总金额
            actual = 0; //实际金额
          //日常报销
          dayInfoList?.forEach((item: { money?: number; actualMoney?: number }) => {
            if (item.money) total += item.money; //报销金额
            if (item.actualMoney) actual += item.actualMoney; //实际报销金额
          });
          //差旅报销
          busiInfoList?.forEach((item: { money?: number; actualMoney?: number }) => {
            if (item.money) total += item.money; //报销金额
            if (item.actualMoney) actual += item.actualMoney; //实际报销金额
          });
          busiProjList?.forEach((item: API.BusiProjReq) => {
            allowanceTotal += Number(item.allowance);
          });
          const values =
            {
              total: `${total.toFixed(2)}`,
              actualTotal: allowanceTotal
                ? ` ${(actual + allowanceTotal).toFixed(2)} `
                : actual.toFixed(2),
              allowance: `${allowanceTotal.toFixed(2)}`,
              actual: `${actual.toFixed(2)}`,
            } || {};
          return (
            <ProForm.Item name="collectionInfo">
              <ReimbursementDetailsStatistics value={values} />
            </ProForm.Item>
          );
        }}
      </ProFormDependency>
      <ProFormDependency name={['type']}>
        {({ type }) => {
          if (type === 'RC') {
            return (
              <ProForm.Item
                name="dayInfoList"
                getValueProps={(val) => ({
                  value: val?.map((item: Record<string, any>) => ({
                    ...item,
                    key_: item.key_ || getRandomId(),
                  })),
                })}
              >
                <DailyReimbursementDetailsInfo />
              </ProForm.Item>
            );
          }
          if (type === 'CL') {
            return (
              <>
                <ProForm.Item
                  name="busiInfoList"
                  getValueProps={(val) => ({
                    value: val?.map((item: Record<string, any>) => ({
                      ...item,
                      key_: item.key_ || getRandomId(),
                    })),
                  })}
                >
                  <TravelReimbursementDetailsInfo />
                </ProForm.Item>
                <ProForm.Item
                  name="busiProjList"
                  getValueProps={(val) => ({
                    value: val?.map((item: Record<string, any>) => ({
                      ...item,
                      key_: item.key_ || getRandomId(),
                    })),
                  })}
                >
                  <TravelDetails />
                </ProForm.Item>
              </>
            );
          }
        }}
      </ProFormDependency>
    </>
  );
};

export default withRouteEditing(BasicInfo);
