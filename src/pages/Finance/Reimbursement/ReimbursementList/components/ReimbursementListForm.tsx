import ChangeLog from '@/components/ChangeLog';
import RK<PERSON>ageHeader from '@/components/RKPageHeader';
import RKPageLoading from '@/components/RKPageLoading';
import BaseListContext from '@/Context/BaseListContext';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useAvailableProjectList } from '@/hooks/useAvailableProjectList';
import { useDepartment } from '@/hooks/useDepartment';
import ExpenseAccountTemplate from '@/pages/Print/template/ExpenseAccountTemplate';
import { currentUserWork, finaCreate, finaInfo, finaUpdate } from '@/services/oa/fina';
import { getRandomId, onSuccessAndGoBack, onSuccessAndRefresh } from '@/utils';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDependency,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { useAccess, useLocation, useModel, useRequest } from '@umijs/max';
import { Collapse } from 'antd';
import { useCallback, useEffect, useRef } from 'react';
import BasicInfo from './BasicInfo';

const formatData = (res?: API.FinaInfoResp) => {
  const { dayInfoList, busiProjList, busiInfoList } = res || {};
  return {
    ...res,
    dayInfoList: dayInfoList?.map((item) => ({
      ...item,
      key_: getRandomId(),
      money: Number(item.money),
      actualMoney: Number(item.actualMoney),
    })),
    busiInfoList: busiInfoList?.map((item) => ({
      ...item,
      key_: getRandomId(),
      money: Number(item.money),
      actualMoney: Number(item.actualMoney),
    })),
    busiProjList: busiProjList?.map((item) => ({ ...item, key_: getRandomId() })),
  };
};

interface LocationState {
  type?: string;
}

const ReimbursementListForm: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  const {
    canEditExpenseReimbursement,
    canAddExpenseReimbursement,
    canSuperEditExpenseReimbursement,
  } = useAccess();

  //判断是否为编辑页面,工单状态为通过时整个表单不可编辑
  const formRef = useRef<ProFormInstance>();
  const { availableProjectList, loading: availableProjectLoading } = useAvailableProjectList();

  // 获取用户基本信息
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  const { pathname, state } = useLocation();
  // 判断是否为审批页面
  const isApprovalPage = pathname.includes('/approval/');
  // 新建时从列表页获取用户选择的报销类型
  const { type = null } = (state as LocationState) || {};

  const { approvalDetails } = useModel('useApprovalModel');

  //部门列表
  const { departmentList, loading: departmentLoading } = useDepartment();

  //当前用户可选择的售后工单列表
  const { data: shWorkOrderList = [], loading: shWorkOrderLoading } = useRequest(() =>
    currentUserWork({ projectType: 'SH' }),
  );
  //当前用户可选择的售前工单列表
  const { data: sqWorkOrderList = [], loading: sqWorkOrderLoading } = useRequest(() =>
    currentUserWork({ projectType: 'SQ' }),
  );

  const {
    data,
    loading: dataLoading,
    refresh,
  } = useRequest(
    () =>
      finaInfo({
        req: {
          id,
        },
      }),
    {
      ready: isEditPage && !isApprovalPage,
    },
  );
  // 高级编辑审批通过后仍可编辑
  // 如果审批通过了都不可修改
  const canEdit =
    (canEditExpenseReimbursement && isEditPage && data?.activiStatus === '0') ||
    (!isEditPage && canAddExpenseReimbursement);
  const canSuperEdit = canSuperEditExpenseReimbursement && data?.activiStatus === '2';
  useEffect(() => {
    const formData = isApprovalPage ? formatData(approvalDetails?.fromData) : formatData(data);

    formRef.current?.setFieldsValue(formData);
  }, [data, approvalDetails]);
  const codeRef = useRef(0);

  const { run: add, loading: addLoading } = useRequest((value) => finaCreate(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  const { run: edit, loading: editLoading } = useRequest((value) => finaUpdate(value), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      codeRef.current = res.code;
      onSuccessAndRefresh(res, refresh);
    },
    formatResult: (res) => res,
  });

  const onSave = useCallback(() => {
    return new Promise((resolve) => {
      codeRef.current = 0;
      formRef.current?.submit();

      const checkFormInterval = setInterval(() => {
        formRef.current
          ?.validateFields()
          .then(() => {
            const isFormSubmitted = codeRef.current === 200;
            if (isFormSubmitted) {
              clearInterval(checkFormInterval); // 清除轮询
              resolve(200); // 表单提交成功，resolve Promise
            }
          })
          .catch(() => {
            resolve(500);
            clearInterval(checkFormInterval); // 清除轮询
          });
      }, 1000); // 每秒检查一次表单是否提交成功
    });
  }, []);

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <RKPageLoading loading={dataLoading} />
      <ProForm
        formRef={formRef}
        submitter={
          canEdit || canSuperEdit
            ? {
                searchConfig: {
                  submitText: '保存',
                  resetText: '取消',
                },
                onReset: () => {
                  history.go(-1);
                },
                submitButtonProps: {
                  loading: addLoading || editLoading,
                  disabled: !canEdit && !canSuperEdit,
                },
                resetButtonProps: {
                  disabled: !canEdit && !canSuperEdit,
                },
                render: (props, doms) => {
                  return <FooterToolbar>{doms}</FooterToolbar>;
                },
              }
            : false
        }
        disabled={!canEdit || isApprovalPage}
        onFinish={async (value) => {
          const { busiProjList, dayInfoList, busiInfoList } = value;

          //实际总金额=实际金额+补贴金额;实际金额为报销中的实际报销金额；报销总金额=报销明细中的金额
          let total = 0, //报销总金额
            allowanceTotal = 0, //补贴总金额
            actual = 0; //实际金额
          //日常报销明细
          dayInfoList?.forEach((item: { money?: number; actualMoney?: number }) => {
            if (item.money) total += item.money; //报销金额
            if (item.actualMoney) actual += item.actualMoney; //实际报销金额
          });
          //差旅报销明细
          busiInfoList?.forEach((item: { money?: number; actualMoney?: number }) => {
            if (item.money) total += item.money; //报销金额
            if (item.actualMoney) actual += item.actualMoney; //实际报销金额
          });
          //差旅信息
          busiProjList?.forEach((item: API.BusiProjReq) => {
            allowanceTotal += Number(item.allowance); //补贴
          });

          const formData = {
            ...value,
            actualTotal: `${actual + (allowanceTotal ? allowanceTotal : 0)}`,
            total: `${total.toFixed(2)}`,
            allowance: `${allowanceTotal}`,
            actual: `${actual.toFixed(2)}`,
          };
          const res = isEditPage ? await edit({ ...formData, id: id }) : await add(formData);
          const success = res.code === 200;
          return success;
        }}
        initialValues={{
          employeeId: currentUser?.id,
          employee: currentUser?.employeeNumber,
          department: currentUser?.department,
          employeeName: currentUser?.username,
          email: currentUser?.email,
          cellNumber: currentUser?.phone,
          type,
        }}
      >
        <BaseListContext.Provider
          value={{
            departmentList,
            departmentLoading,
            availableProjectList,
            availableProjectLoading,
            shWorkOrderList,
            shWorkOrderLoading,
            sqWorkOrderList,
            sqWorkOrderLoading,
            data: {
              disabled: (!canEdit && !canSuperEdit) || isApprovalPage,
            },
          }}
        >
          {isEditPage && (
            <ProFormDependency name={['documentNumber', 'activiStatus']}>
              {({ documentNumber, activiStatus }) => {
                return (
                  <RKPageHeader
                    id={id}
                    title={documentNumber}
                    status={isApprovalPage ? approvalDetails?.activiStatus || '9' : activiStatus}
                    approveType="REIMBURSEMENT_APPROVAL"
                    print={
                      isApprovalPage
                        ? undefined
                        : {
                            printData: data as Record<string, any>,
                            printType: 'reimbursement',
                          }
                    }
                    onOperationCallback={() => {
                      if (!isApprovalPage) refresh();
                    }}
                    onSave={onSave}
                    saveLoading={editLoading}
                    saveDisabled={!canEditExpenseReimbursement}
                  />
                );
              }}
            </ProFormDependency>
          )}
          {/* 不需要展示，只是为了form传值 */}
          <div style={{ display: 'none' }}>
            <ProFormText name="id" label="id" />
            <ProFormText name="total" label="报销总金额" />
            <ProFormText name="actualTotal" label="实际总金额" />
            <ProFormText name="allowance" label="补贴金额" />
            <ProFormText name="actual" label="实际金额" />
            <ProFormText name="email" label="email" />
            <ProFormText name="cellNumber" />
            <ProFormText name="employeeId" label="employeeId" />
            <ProFormText name="activiStatus" />
          </div>

          <Collapse defaultActiveKey={['1', '2']} ghost collapsible="header">
            <Collapse.Panel key="1" header="基础信息 ">
              <BasicInfo />
            </Collapse.Panel>
            <Collapse.Panel key="2" header="报销单展示 ">
              <ExpenseAccountTemplate
                data={
                  isApprovalPage
                    ? (approvalDetails?.fromData as Record<string, any>)
                    : (data as Record<string, any>)
                }
              />
            </Collapse.Panel>
          </Collapse>
        </BaseListContext.Provider>
        <ChangeLog />
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(ReimbursementListForm);
