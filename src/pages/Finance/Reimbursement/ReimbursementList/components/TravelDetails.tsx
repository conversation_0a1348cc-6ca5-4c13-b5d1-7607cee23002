import RKSelectLabel from '@/components/RKSelectLabel';
import TitleLink from '@/components/TitleLink';
import BaseListContext from '@/Context/BaseListContext';
import { PROJECT_TYPE } from '@/enums';
import { computeSubsidy } from '@/services/oa/fina';
import { getRandomId } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import {
  DrawerForm,
  ProColumns,
  ProFormDateTimePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, Form, Space } from 'antd';
import dayjs from 'dayjs';
import { produce } from 'immer';
import { useContext, useRef, useState } from 'react';

type ValueProps = {
  key_?: string;
} & API.SalePlanResp;

const TravelDetails: React.FC<{
  value?: ValueProps[];
  onChange?: (val: ValueProps[]) => void;
}> = ({ value = [], onChange }) => {
  const [form] = Form.useForm<ValueProps>();
  const [drawerVisit, setDrawerVisit] = useState(false);
  const formRef = useRef<ProFormInstance>();
  const {
    availableProjectList = [],
    availableProjectLoading,
    shWorkOrderList = [],
    shWorkOrderLoading,
    sqWorkOrderLoading,
  } = useContext(BaseListContext);

  //根据出发和返程时间计算补贴金
  const { run: FinaSubsidy } = useRequest((value) => computeSubsidy(value), {
    manual: true,
    formatResult: (res) => res,
  });

  const { data } = useContext(BaseListContext);

  const columns: ProColumns<ValueProps>[] = [
    {
      title: '项目类型',
      dataIndex: 'projectType',
      valueType: 'select',
      fieldProps: {
        options: PROJECT_TYPE,
      },
      width: 100,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      ellipsis: true,
      width: 180,
    },
    {
      title: '出差事由',
      dataIndex: 'workDesc',
      width: 180,
      ellipsis: true,
    },
    {
      title: '出发时间',
      dataIndex: 'goDate',
      valueType: 'date',
      width: 110,
    },
    {
      title: '出发地',
      dataIndex: 'leave',
      width: 80,
      ellipsis: true,
    },
    {
      title: '返程时间',
      dataIndex: 'backDate',
      valueType: 'date',
      width: 110,
    },
    {
      title: '到达地',
      dataIndex: 'arrive',
      width: 80,
      ellipsis: true,
    },
    {
      title: '停留天数',
      dataIndex: 'days',
      valueType: 'digit',
      width: 80,
    },
    {
      title: '补贴金额',
      dataIndex: 'allowance',
      valueType: 'money',
      fieldProps: {
        locale: 'zh-CN',
        min: 0,
        defaultValue: 0,
      },
      width: 120,
    },

    {
      title: '操作',
      fixed: 'right',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <Button
              className="inner-table-link"
              type="link"
              onClick={() => {
                setDrawerVisit(true);
                form.setFieldsValue(record);
              }}
              disabled={data?.disabled}
            >
              修改
            </Button>
            <Button
              className="inner-table-link"
              type="link"
              onClick={() => {
                const updatedValue = produce(value || [], (draft) => {
                  return draft.filter((item) => item?.key_ !== record?.key_);
                });
                onChange?.(updatedValue);
              }}
            >
              删除
            </Button>
            <Button
              className="inner-table-link"
              type="link"
              onClick={() => {
                const newRecord = { ...record, key_: getRandomId() };
                const updatedValue = produce(value || [], (draft) => {
                  draft.push(newRecord);
                });
                onChange?.(updatedValue);
              }}
            >
              复制
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <ProTable<any>
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        headerTitle="差旅信息明细表"
        options={false}
        rowKey="key_"
        dataSource={value}
        className="inner-table"
        columns={columns}
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setDrawerVisit(true);
              }}
            >
              添加
            </Button>,
          ],
        }}
      />

      <DrawerForm<ValueProps>
        width={460}
        open={drawerVisit}
        onOpenChange={setDrawerVisit}
        title="差旅信息明细"
        form={form}
        formRef={formRef}
        autoFocusFirstInput
        drawerProps={{
          destroyOnClose: true,
        }}
        submitter={{
          submitButtonProps: {
            disabled: data?.disabled,
          },
          resetButtonProps: {
            disabled: data?.disabled,
          },
        }}
        onFinish={async (values) => {
          const updatedValue = produce(value || [], (draft) => {
            const index = draft.findIndex((item) => item.key_ === values.key_);
            if (index === -1) {
              draft.push(values);
            } else {
              draft[index] = values;
            }
          });
          onChange?.(updatedValue);
          return true;
        }}
        onValuesChange={(val) => {
          if (val.projectType) {
            formRef.current?.setFieldsValue({
              workId: undefined,
              projectId: undefined,
              projectName: undefined,
            });
          }
        }}
      >
        <div className="rk-none">
          <ProFormText name="key_" initialValue={getRandomId()} />
          <ProFormText name="finaType" initialValue={'CL'} />
          <ProFormText name="projectId" />
        </div>
        <ProFormSelect
          name="projectType"
          label="项目类型"
          rules={[requiredRule]}
          fieldProps={{
            showSearch: true,
            onChange() {
              formRef.current?.setFieldValue('workNumber', undefined);
            },
          }}
          options={PROJECT_TYPE}
        />
        <ProFormDependency name={['projectType', 'workId']}>
          {({ projectType, workId }) => {
            if (['SH'].includes(projectType)) {
              return (
                <>
                  <ProFormSelect
                    name="workId"
                    label={
                      <TitleLink path={workId && `/work-orders/edit/${workId}`}>工单编号</TitleLink>
                    }
                    rules={[requiredRule]}
                    fieldProps={{
                      showSearch: true,
                      loading: shWorkOrderLoading || sqWorkOrderLoading,
                      optionLabelProp: 'workNumber',
                      filterOption: (inputValue, option) => {
                        return option?.keywords.indexOf(inputValue) >= 0;
                      },
                      options: shWorkOrderList?.map((item) => ({
                        value: item.workId,
                        label: <RKSelectLabel title={item.workNumber} info={item.projectName} />,
                        workNumber: item.workNumber,
                        keywords: `${item.workNumber}${item.projectName}`,
                        projectName: item.projectName,
                        projectNumber: item.projectNumber,
                        projectId: item.projectId,
                      })),
                      onChange: (value, option) => {
                        const opt = option as API.CurrentUserWorkResp;
                        formRef.current?.setFieldsValue({
                          projectId: opt?.projectId,
                          projectName: opt?.projectName,
                          projectNumber: opt?.projectNumber,
                          workNumber: opt?.workNumber,
                        });
                      },
                    }}
                  />
                </>
              );
            }
          }}
        </ProFormDependency>

        <ProFormDependency name={['projectType']}>
          {({ projectType }) => {
            return (
              <>
                <ProFormSelect
                  name="projectNumber"
                  disabled={['SH'].includes(projectType)}
                  label="项目编号"
                  rules={[requiredRule]}
                  fieldProps={{
                    showSearch: true,
                    loading: availableProjectLoading,
                    optionLabelProp: 'projectNumber',
                    onChange: (value, option) => {
                      formRef.current?.setFieldValue(
                        'projectName',
                        (option as { projectName?: string })?.projectName,
                      );
                      formRef.current?.setFieldValue('projectId', (option as { id?: string })?.id);
                    },
                    filterOption: (inputValue, option) => {
                      return option?.keywords.indexOf(inputValue) >= 0;
                    },
                  }}
                  options={availableProjectList
                    ?.filter((item) => item?.projectClassify === projectType)
                    .map((item) => {
                      return {
                        value: item.projectNumber,
                        label: <RKSelectLabel title={item.projectNumber} info={item.projectName} />,
                        projectName: item.projectName,
                        projectNumber: item.projectNumber,
                        keywords: `${item.projectName}${item.projectNumber}`,
                      };
                    })}
                />
              </>
            );
          }}
        </ProFormDependency>

        <ProFormText disabled name="projectName" label="项目名称" rules={[requiredRule]} />
        <ProFormTextArea
          name="workDesc"
          label="出差事由"
          rules={[requiredRule]}
          fieldProps={{
            autoSize: {
              minRows: 1,
              maxRows: 3,
            },
          }}
        />
        <ProFormDateTimePicker
          name="goDate"
          label="出发时间"
          rules={[requiredRule]}
          fieldProps={{
            format: 'YYYY-MM-DD HH:mm',
            onChange: async (val, valString) => {
              const backDate = formRef.current?.getFieldValue('backDate');
              if (valString && backDate) {
                const res = await FinaSubsidy({
                  goDate: dayjs(val).format('YYYY-MM-DD HH:mm:ss'),
                  backDate: dayjs(backDate).format('YYYY-MM-DD HH:mm:ss'),
                });
                formRef.current?.setFieldsValue({
                  allowance: Number(res?.data),
                });
              }
            },
          }}
          transform={(value, namePath) => {
            return {
              [namePath]: dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
            };
          }}
        />
        <ProFormText name="leave" label="出发地" rules={[requiredRule]} />
        <ProFormDateTimePicker
          name="backDate"
          label="返程时间"
          rules={[requiredRule]}
          fieldProps={{
            format: 'YYYY-MM-DD HH:mm',
            onChange: async (val, valString) => {
              const goDate = formRef.current?.getFieldValue('goDate');
              if (goDate && valString) {
                const res = await FinaSubsidy({
                  goDate: dayjs(goDate).format('YYYY-MM-DD HH:mm:00'),
                  backDate: dayjs(val).format('YYYY-MM-DD HH:mm:ss'),
                });
                formRef.current?.setFieldsValue({
                  allowance: Number(res?.data),
                });
              }
            },
          }}
          transform={(value, namePath) => {
            return {
              [namePath]: dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
            };
          }}
        />
        <ProFormText name="arrive" label="到达地" rules={[requiredRule]} />
        <ProFormDigit
          name="days"
          label="停留天数"
          rules={[requiredRule]}
          disabled={data?.disabled}
        />
        <ProFormMoney
          name="allowance"
          label="补贴金额"
          rules={[requiredRule]}
          locale="zh-CN"
          fieldProps={{
            min: 0,
            precision: 2,
            step: 0.1,
          }}
          disabled={data?.disabled}
        />
        <ProFormTextArea
          name="remark"
          label="备注"
          fieldProps={{
            autoSize: {
              minRows: 1,
              maxRows: 3,
            },
          }}
          disabled={data?.disabled}
        />
      </DrawerForm>
    </>
  );
};
export default TravelDetails;
