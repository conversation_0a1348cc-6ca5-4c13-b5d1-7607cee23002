import RKSelectLabel from '@/components/RKSelectLabel';
import BaseListContext from '@/Context/BaseListContext';
import { PROJECT_TYPE } from '@/enums';
import { getRandomId, option2enum } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import { chineseRangePattern } from '@/utils/validator';
import { PlusOutlined } from '@ant-design/icons';
import {
  DrawerForm,
  ProColumns,
  ProFormDatePicker,
  ProFormDependency,
  ProFormInstance,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Form, Space } from 'antd';
import { produce } from 'immer';
import { useContext, useRef, useState } from 'react';

//费用类型
export const Expense_Type = [
  { label: '差旅费', value: '差旅费' },
  { label: '办公费', value: '办公费' },
  { label: '外地餐费', value: '外地餐费' },
  { label: '本地餐费', value: '本地餐费' },
  { label: '购东西', value: '购东西' },
  { label: '车辆费用-油费', value: '车辆费用-油费' },
  { label: '车辆费用-其他', value: '车辆费用-其他' },
  { label: '交通费', value: '交通费' },
  { label: '培训费', value: '培训费' },
  { label: '福利费', value: '福利费' },
  { label: '项目采购成本', value: '项目采购成本' },
  { label: '其他', value: '其他' },
];

//实际报销类型
export const ACTUAL_Type = [
  { label: '补贴', value: '补贴' },
  { label: '办公费', value: '办公费' },
  { label: '招待费', value: '招待费' },
  { label: '礼品费', value: '礼品费' },
  { label: '车辆费用-油费', value: '车辆费用-油费' },
  { label: '车辆费用-其他', value: '车辆费用-其他' },
  { label: '交通费', value: '交通费' },
  { label: '培训费', value: '培训费' },
  { label: '福利费', value: '福利费' },
  { label: '差旅费', value: '差旅费' },
  { label: '项目采购成本', value: '项目采购成本' },
  { label: '其他', value: '其他' },
];

export const errorMessages = '格式必须为从【出发地】到【目的地】  e.g: 从北京到上海 ';

export const requiredTrafficRules = {
  required: true,
  pattern: chineseRangePattern,
  message: errorMessages,
};

type ValueProps = {
  key_?: string;
} & API.BusiProjReq;

const DailyReimbursementDetailsInfo: React.FC<{
  value?: ValueProps[];
  onChange?: (val: ValueProps[]) => void;
}> = ({ value, onChange }) => {
  const [form] = Form.useForm<ValueProps>();
  const formRef = useRef<ProFormInstance>();
  const [drawerVisit, setDrawerVisit] = useState(false);
  const { availableProjectList = [], availableProjectLoading } = useContext(BaseListContext);
  const { data } = useContext(BaseListContext);

  const columns: ProColumns<ValueProps>[] = [
    {
      title: '项目类型',
      dataIndex: 'projectType',
      width: 100,
      ellipsis: true,
      valueEnum: option2enum(PROJECT_TYPE),
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 180,
      ellipsis: true,
    },
    {
      title: '日期',
      dataIndex: 'time',
      valueType: 'date',
      width: 100,
    },
    {
      title: '实际报销类型',
      dataIndex: 'actualType',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        options: ACTUAL_Type,
      },
      width: 120,
    },
    {
      title: '实际报销金额',
      dataIndex: 'actualMoney',
      valueType: 'money',
      fieldProps: {
        locale: 'zh-CN',
        min: 0,
        precision: 2,
        step: 0.1,
      },
      formItemProps: {
        rules: [requiredRule],
      },
      width: 120,
    },
    {
      title: '正当理由',
      dataIndex: 'reason',
      formItemProps: {
        rules: [requiredRule],
      },
      width: 180,
      ellipsis: true,
    },

    {
      title: '操作',
      fixed: 'right',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <Button
              className="inner-table-link"
              type="link"
              onClick={() => {
                setDrawerVisit(true);
                form.setFieldsValue(record);
              }}
              disabled={data?.disabled}
            >
              修改
            </Button>
            <Button
              className="inner-table-link"
              type="link"
              onClick={() => {
                const updatedValue = produce(value || [], (draft) => {
                  return draft.filter((item) => item?.key_ !== record?.key_);
                });
                onChange?.(updatedValue);
              }}
            >
              删除
            </Button>
            <Button
              className="inner-table-link"
              type="link"
              onClick={() => {
                const newRecord = { ...record, key_: getRandomId() };
                const updatedValue = produce(value || [], (draft) => {
                  draft.push(newRecord);
                });
                onChange?.(updatedValue);
              }}
            >
              复制
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <ProTable
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        headerTitle="日常报销明细表"
        options={false}
        rowKey="key_"
        dataSource={value}
        className="inner-table"
        columns={columns}
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setDrawerVisit(true);
              }}
            >
              添加
            </Button>,
          ],
        }}
      />
      <DrawerForm<ValueProps>
        width={460}
        open={drawerVisit}
        onOpenChange={setDrawerVisit}
        title="日常报销明细"
        form={form}
        formRef={formRef}
        autoFocusFirstInput
        drawerProps={{
          destroyOnClose: true,
        }}
        submitter={{
          submitButtonProps: {
            disabled: data?.disabled,
          },
          resetButtonProps: {
            disabled: data?.disabled,
          },
        }}
        onFinish={async (values) => {
          const updatedValue = produce(value || [], (draft) => {
            const index = draft.findIndex((item) => item.key_ === values.key_);
            if (index === -1) {
              draft.push(values);
            } else {
              draft[index] = values;
            }
          });

          onChange?.(updatedValue);
          return true;
        }}
      >
        <div className="rk-none">
          <ProFormText name="key_" initialValue={getRandomId()} />
          <ProFormText name="finaType" initialValue={'RC'} />
          <ProFormText name="projectId" />
        </div>
        <ProFormSelect
          name="projectNumber"
          label="项目编号"
          rules={[requiredRule]}
          fieldProps={{
            showSearch: true,
            loading: availableProjectLoading,
            optionLabelProp: 'projectNumber',
            onChange: (value, option) => {
              formRef.current?.setFieldValue(
                'projectName',
                (option as { projectName?: string })?.projectName,
              );
              formRef.current?.setFieldValue('projectId', (option as { id?: string })?.id);
            },
            filterOption: (inputValue, option) => {
              return option?.keywords.indexOf(inputValue) >= 0;
            },
          }}
          options={availableProjectList?.map((item) => {
            return {
              value: item.projectNumber,
              label: <RKSelectLabel title={item.projectNumber} info={item.projectName} />,
              projectNumber: item.projectNumber,
              projectName: item.projectName,
              id: item.id,
              keywords: `${item.projectName}${item.projectNumber}`,
            };
          })}
        />
        <ProFormText disabled name="projectName" label="项目名称" rules={[requiredRule]} />

        <ProFormDatePicker
          name="time"
          label="日期"
          disabled={data?.disabled}
          rules={[requiredRule]}
        />
        <ProFormSelect
          name="invoice"
          label="发票"
          tooltip="如果发票有其它状况请在此项进行选择并备注原因!"
          valueEnum={{ 多提供发票: '多提供发票', 无发票: '无发票' }}
          disabled={data?.disabled}
        />
        <ProFormSelect
          rules={[requiredRule]}
          name="type"
          label="费用类型"
          fieldProps={{
            showSearch: true,
            options: Expense_Type,
          }}
          disabled={data?.disabled}
        />
        <ProFormMoney
          name="money"
          label="金额"
          rules={[requiredRule]}
          locale="zh-CN"
          fieldProps={{
            min: 0,
            precision: 2,
            step: 0.1,
          }}
          disabled={data?.disabled}
        />
        <ProFormSelect
          name="actualType"
          label="实际报销类型"
          fieldProps={{
            showSearch: true,
            options: ACTUAL_Type,
          }}
          rules={[requiredRule]}
          disabled={data?.disabled}
        />
        <ProFormMoney
          name="actualMoney"
          label="实际报销金额"
          rules={[requiredRule]}
          locale="zh-CN"
          fieldProps={{
            min: 0,
            precision: 2,
            step: 0.1,
          }}
          disabled={data?.disabled}
        />
        <ProFormDependency name={['actualType']}>
          {({ actualType }) => {
            const typeOfTraffic = ['车辆费用-油费', '车辆费用-其他', '交通费'].includes(actualType);
            return (
              <ProFormTextArea
                name="reason"
                label="正当理由"
                tooltip={typeOfTraffic && errorMessages}
                fieldProps={{
                  autoSize: {
                    minRows: 1,
                    maxRows: 3,
                  },
                }}
                disabled={data?.disabled}
                rules={[typeOfTraffic ? requiredTrafficRules : requiredRule]}
              />
            );
          }}
        </ProFormDependency>
        <ProFormTextArea
          name="remark"
          label="备注"
          fieldProps={{
            autoSize: {
              minRows: 1,
              maxRows: 3,
            },
          }}
          disabled={data?.disabled}
        />
      </DrawerForm>
    </>
  );
};
export default DailyReimbursementDetailsInfo;
