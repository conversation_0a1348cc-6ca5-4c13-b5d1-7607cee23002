import { StatisticCard } from '@ant-design/pro-components';

const ReimbursementDetailsStatistics: React.FC<{
  value?: API.FinaInfoResp;
  onChange?: (val: API.FinaInfoResp) => void;
}> = ({ value }) => {
  const { total, actualTotal, allowance, actual } = value || {};

  return (
    <StatisticCard.Group className="rklink-statistic">
      <StatisticCard
        statistic={{
          prefix: '¥',
          title: '实际金额',
          value: actual || 0,
          status: 'success',
        }}
      />
      <StatisticCard
        statistic={{
          title: '补贴金额',
          prefix: '¥',
          value: allowance || 0,
          status: 'success',
        }}
      />
      <StatisticCard
        statistic={{
          title: '实际总金额',
          prefix: '¥',
          value: actualTotal || 0,
          status: 'success',
        }}
      />
      <StatisticCard
        statistic={{
          title: '报销总金额',
          prefix: '¥',
          value: total || 0,
          status: 'success',
        }}
      />
    </StatisticCard.Group>
  );
};

export default ReimbursementDetailsStatistics;
