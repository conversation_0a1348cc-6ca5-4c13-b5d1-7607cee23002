import { COLLECTION_STATUS, PAYMENT, PAY_STATUS } from '@/enums';
import DifferenceInfo from '@/pages/Contract/Purchase/components/DifferenceInfo';
import { paymentColumns } from '@/pages/Project/components/ProTableColumn';
import { getMainContractExtraInfo } from '@/services/oa/contract';
import { groupedData, option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { history, Link, useRequest } from '@umijs/max';
import { Card, Collapse, Descriptions, Empty } from 'antd';
import { FC, memo, useMemo } from 'react';

export const planColumns: ProColumns<API.PayPlanDetailInfoResp>[] = [
  {
    title: '期次',
    width: 50,
    dataIndex: 'index',
    valueType: 'index',
    renderText: (text, entity) => {
      if (entity.period) return entity.period - 1;
      return text;
    },
  },
  {
    title: '计划付款金额',
    dataIndex: 'estimatePayAmount',
    valueType: 'money',
    width: 130,
  },
  {
    title: '计划付款日期',
    dataIndex: 'estimatePayTime',
    width: 130,
  },
  {
    title: '付款方式',
    dataIndex: 'payWay',
    valueEnum: option2enum(PAYMENT),
    width: 130,
    ellipsis: true,
  },
  {
    title: '付款状态',
    dataIndex: 'payPlanStatus',
    valueEnum: option2enum(PAY_STATUS),
    width: 120,
  },
  {
    title: '收票金额',
    dataIndex: 'receiptAmount',
    width: 130,
    valueType: 'money',
  },
  {
    title: '对方开票日期',
    dataIndex: 'countInvoicedTime',
    width: 130,
  },
  {
    title: '付款记录号',
    dataIndex: 'payNumber',
    width: 180,
    render(dom, entity) {
      if (!entity.paymentId) return '-';
      return (
        <a onClick={() => history.push(`/finance/JR/payment-list/${entity.paymentId}`)}>{dom}</a>
      );
    },
  },
  {
    title: '付款申请编号',
    dataIndex: 'paymentAppNumber',
    width: 180,
    render(dom, entity) {
      if (!entity.paymentAppId) return '-';
      return (
        <a onClick={() => history.push(`/finance/payment/contract/${entity.paymentAppId}`)}>
          {dom}
        </a>
      );
    },
  },
  {
    title: '实际付款金额',
    dataIndex: 'payAmount',
    width: 150,
    valueType: 'money',
  },
  {
    title: '付款日期',
    dataIndex: 'payTime',
    width: 130,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100,
    ellipsis: true,
  },
];

export const collectionColumns: ProColumns<API.CollectionPlanReq>[] = [
  {
    title: '期次',
    width: 50,
    dataIndex: 'index',
    valueType: 'index',
    render: (dom, entity) => {
      if (entity.period) return entity.period;
      return dom;
    },
  },
  {
    title: '计划收款金额',
    dataIndex: 'estimateReAmount',
    valueType: 'money',
    width: 130,
  },
  {
    title: '计划收款日期',
    dataIndex: 'estimateReTime',
    width: 130,
  },
  {
    title: '收款状态',
    dataIndex: 'status',
    valueEnum: option2enum(COLLECTION_STATUS),
    width: 120,
  },
  {
    title: '开票金额',
    dataIndex: 'ticketAmount',
    valueType: 'money',
    width: 130,
  },
  {
    title: '开票日期',
    dataIndex: 'ticketTime',
    width: 130,
  },
  {
    title: '开票记录号',
    dataIndex: 'invoicingNumber',
    width: 180,
    render(dom, entity) {
      return (
        <a onClick={() => history.push(`/finance/invoice/invoicing-list/${entity.id}`)}>{dom}</a>
      );
    },
  },
  {
    title: '到款金额',
    dataIndex: 'receiveAmount',
    valueType: 'money',
    width: 130,
  },
  {
    title: '到款日期',
    dataIndex: 'receiveTime',
    width: 170,
  },
  {
    title: '认领日期',
    dataIndex: 'claimTime',
    width: 170,
  },
  {
    title: '收款记录号',
    dataIndex: 'collectNumber',
    width: 180,
    render(dom, entity) {
      if (!entity.receiptId) return '-';
      return (
        <a onClick={() => history.push(`/finance/JR/receipt-list/details/${entity.receiptId}`)}>
          {dom}
        </a>
      );
    },
  },
];

const AssociatedInfo: FC<{
  contractId: string;
  contractType: 'IC' | 'PC';
}> = ({ contractId, contractType }) => {
  const { data, loading } = useRequest(
    () =>
      getMainContractExtraInfo({
        id: contractId,
        contractCategory: contractType,
      }),
    {
      ready: Boolean(contractId),
    },
  );
  // 分组关联主合同的其他采购合同的付款计划
  const groupedPayPlan = useMemo(
    () => groupedData(data?.otherPayPlanDetailInfoList || [], 'contractNumber'),
    [data?.otherPayPlanDetailInfoList],
  );

  return (
    <Collapse defaultActiveKey={['1', '2', '3', '4', '5', '6', '7']} ghost>
      {contractType === 'PC' && (
        <Collapse.Panel key="7" header="差额信息" collapsible="header">
          <DifferenceInfo contractId={contractId} />
        </Collapse.Panel>
      )}
      <Collapse.Panel key="1" header="付款计划详细信息表" collapsible="header">
        <ProTable<API.PayPlanDetailInfoResp>
          {...defaultTableConfig}
          scroll={{ x: '100%' }}
          loading={loading}
          dataSource={data?.payPlanDetailInfoList}
          options={false}
          columns={planColumns}
          tableExtraRender={(_, data) =>
            data.length > 0 && (
              <Card
                style={{
                  margin: '0 24px',
                }}
              >
                <Descriptions size="small" column={3}>
                  <Descriptions.Item label="合同名称">
                    {data?.at(0)?.contractName}
                  </Descriptions.Item>
                  <Descriptions.Item label="合同编号">
                    <Link
                      to={`/contract/${contractType === 'PC' ? 'purchase' : 'internal'}/edit/${
                        data?.at(0)?.contractId
                      }`}
                    >
                      {data?.at(0)?.contractNumber}
                    </Link>
                  </Descriptions.Item>
                  <Descriptions.Item label="合同总金额">
                    {data?.at(0)?.contractAmount}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            )
          }
        />
      </Collapse.Panel>
      {/* 以下内容只有采购合同有 */}
      {contractType === 'PC' && (
        <>
          <Collapse.Panel key="2" header="关联主合同收款计划详细信息表" collapsible="header">
            <ProTable<API.CollectionPlanResp>
              {...defaultTableConfig}
              scroll={{ x: '100%' }}
              loading={loading}
              dataSource={data?.collectionPlanList}
              options={false}
              columns={collectionColumns}
              tableExtraRender={(_, data) =>
                data.length > 0 && (
                  <Card
                    style={{
                      margin: '0 24px',
                    }}
                  >
                    <Descriptions size="small" column={3}>
                      <Descriptions.Item label="主合同名称">
                        {data?.at(0)?.contractName}
                      </Descriptions.Item>
                      <Descriptions.Item label="主合同编号">
                        <Link to={`/contract/main/edit/${data?.at(0)?.contractId}`}>
                          {data?.at(0)?.contractNumber}
                        </Link>
                      </Descriptions.Item>
                      <Descriptions.Item label="主合同总金额">
                        {data?.at(0)?.contractAmount}
                      </Descriptions.Item>
                    </Descriptions>
                  </Card>
                )
              }
            />
          </Collapse.Panel>
          <Collapse.Panel
            key="3"
            header="关联主合同的其他采购合同的付款计划详细信息表"
            collapsible="header"
          >
            {!data?.otherPayPlanDetailInfoList?.length && (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )}

            {Object.entries(groupedPayPlan).map(([key, value]) => (
              <ProTable<API.PayPlanDetailInfoResp>
                key={key}
                {...defaultTableConfig}
                scroll={{ x: '100%' }}
                loading={loading}
                dataSource={value}
                options={false}
                columns={planColumns}
                // headerTitle={
                //   // <Link to={`/contract/purchase/edit/${value?.at(0)?.contractId}`}>
                //   `${value?.at(0)?.contractName} (${key})`
                //   // </Link>
                // }
                tableExtraRender={(_, data) =>
                  data.length > 0 && (
                    <Card
                      style={{
                        margin: '0 24px',
                      }}
                    >
                      <Descriptions size="small" column={3}>
                        <Descriptions.Item label="采购合同名称">
                          {data?.at(0)?.contractName}
                        </Descriptions.Item>
                        <Descriptions.Item label="采购合同编号">
                          <Link to={`/contract/purchase/edit/${data?.at(0)?.contractId}`}>
                            {data?.at(0)?.contractNumber}
                          </Link>
                        </Descriptions.Item>
                        <Descriptions.Item label="采购总金额">
                          {data?.at(0)?.contractAmount}
                        </Descriptions.Item>
                      </Descriptions>
                    </Card>
                  )
                }
                style={{
                  marginTop: 0,
                }}
              />
            ))}
          </Collapse.Panel>
          <Collapse.Panel key="4" header="关联主合同的项目付款明细" collapsible="header">
            <ProTable<API.Payment & { projectId?: string; projectNumber?: string }>
              {...defaultTableConfig}
              scroll={{ x: '100%' }}
              loading={loading}
              dataSource={data?.paymentList}
              options={false}
              columns={[
                {
                  title: '项目编号',
                  dataIndex: 'projectNumber',
                  width: 180,
                  render: (dom, entity) => {
                    const { projectNumber, projectId } = entity;
                    const projectType = projectNumber?.split('-').at(0);
                    let url;
                    switch (projectType) {
                      case 'NB':
                        url = `/project/internal/edit/`;
                        break;
                      case 'XS':
                        url = `/project/sales/edit/`;
                        break;
                      case 'SQ':
                        url = `/project/pre-sales/edit/`;
                        break;
                      case 'SH':
                        url = `/project/after-sales/edit/`;
                        break;
                      default:
                        return;
                    }
                    return <Link to={`${url}${projectId}`}>{entity?.projectNumber}</Link>;
                  },
                },
                {
                  title: '付款编号',
                  dataIndex: 'payNumber',
                  width: 180,
                  render: (dom, entity) => {
                    return (
                      <Link to={`/finance/JR/payment-list/${entity.id}`}>{entity?.payNumber}</Link>
                    );
                  },
                },
                ...paymentColumns,
              ]}
            />
          </Collapse.Panel>
        </>
      )}
      {/* 内部合同查询项目本身的付款明细 */}
      {contractType === 'IC' && (
        <Collapse.Panel key="4" header="项目付款明细" collapsible="header">
          <ProTable<API.Payment>
            {...defaultTableConfig}
            scroll={{ x: '100%' }}
            loading={loading}
            dataSource={data?.paymentList}
            options={false}
            columns={[
              {
                title: '付款编号',
                dataIndex: 'payNumber',
                width: 180,
                render: (dom, entity) => {
                  return (
                    <Link to={`/finance/JR/payment-list/${entity.id}`}>{entity?.payNumber}</Link>
                  );
                },
              },
              ...paymentColumns,
            ]}
          />
        </Collapse.Panel>
      )}
    </Collapse>
  );
};

export default memo(AssociatedInfo);
