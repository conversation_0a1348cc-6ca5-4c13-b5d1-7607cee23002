import { CONTRACT_TYPE } from '@/enums';
import { selectContractPayList } from '@/services/oa/conInvoiced';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { DrawerForm, DrawerFormProps, ProColumns, ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Space, Tooltip } from 'antd';
import React, { memo } from 'react';

const pagePath = {
  IC: '/contract/internal/edit/',
  PC: '/contract/purchase/edit/',
};

const ContractListDrawerForm: React.FC<DrawerFormProps> = ({ open, onOpenChange }) => {
  const columns: ProColumns<API.ContractPaymentResp>[] = [
    {
      title: '合同名称',
      dataIndex: 'contractName',
      width: 180,
      ellipsis: true,
      render(dom, entity) {
        const path = pagePath[(entity.contractType as 'IC') || 'PC'];
        if (!path) return dom;
        return (
          <a className="rk-a-span" onClick={() => history.push(`${path}${entity.id}`)}>
            {dom}
          </a>
        );
      },
    },
    {
      title: '合同类型',
      dataIndex: 'contractType',
      width: 100,
      valueEnum: option2enum(CONTRACT_TYPE),
    },
    {
      title: '乙方',
      dataIndex: 'spName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '最终用户',
      width: 150,
      ellipsis: true,
      dataIndex: 'endUser',
    },
  ];

  return (
    <DrawerForm
      title={
        <Space>
          <span>合同列表</span>
          <Tooltip
            placement="bottom"
            title={
              '本人（采购合同的销售或是内部合同的项目经理）管理的存在未发起付款申请的采购或内部合同'
            }
          >
            <QuestionCircleOutlined style={{ color: 'rgba(0, 0, 0, 0.45)', cursor: 'help' }} />
          </Tooltip>
        </Space>
      }
      width={640}
      open={open}
      onOpenChange={(visible) => {
        onOpenChange?.(visible);
      }}
      submitter={false}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
    >
      <ProTable<API.ContractPaymentResp>
        {...defaultTableConfig}
        pagination={false}
        size="small"
        className="inner-table"
        scroll={{ x: '100%' }}
        options={false}
        columns={columns}
        request={async () => {
          // 查询当前用户（采购合同的销售或是内部合同的项目经理）管理的存在未发起付款申请的采购或内部合同
          const res = await selectContractPayList();
          return {
            data: res?.data || [],
            total: res?.data?.length || 0,
            success: true,
          };
        }}
      />
    </DrawerForm>
  );
};

export default memo(ContractListDrawerForm);
