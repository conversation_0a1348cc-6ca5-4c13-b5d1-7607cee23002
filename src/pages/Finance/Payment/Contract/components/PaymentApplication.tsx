import RKCol from '@/components/RKCol';
import RKPageHeader from '@/components/RKPageHeader';
import TitleLink from '@/components/TitleLink';
import { PAYMENT } from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { selectPaymentAppById } from '@/services/oa/conInvoiced';
import { applicationPay } from '@/services/oa/contract';
import { requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormInstance,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useLocation, useModel, useRequest } from '@umijs/max';
import { Row } from 'antd';
import { useCallback, useEffect, useRef } from 'react';
import AssociatedInfo from './AssociatedInfo';

const PaymentApplication: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  const formRef = useRef<ProFormInstance>();
  const { approvalDetails } = useModel('useApprovalModel');
  // 判断是否为审批页面
  const { pathname } = useLocation();
  const isApprovalPage = pathname.includes('/approval/');

  const pagePath = {
    IC: '/contract/internal/edit/',
    PC: '/contract/purchase/edit/',
  };
  const codeRef = useRef(0);
  // 获取当前用户
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  // 获取详情
  const {
    data = {},
    loading,
    refresh,
  } = useRequest(
    () =>
      selectPaymentAppById({
        idReq: { id },
      }),
    {
      ready: isEditPage && !isApprovalPage,
      onSuccess: (res) => {
        setTimeout(() => {
          formRef.current?.setFieldsValue(
            {
              ...res,
              payStatus: res?.payStatus || 'NOT_PAY',
              operateStatus: 'SUBMIT',
              payWay: res?.payWay || 'FP',
              applicationUser: res?.applicationUser || currentUser?.username,
              applicationUserId: res?.applicationUserId || currentUser?.id,
              payAmount: res?.payAmount,
            } || {},
          );
        }, 500);
      },
    },
  );

  // 申请付款
  const { run: apply, loading: applyLoading } = useRequest((value) => applicationPay(value), {
    manual: true,
    onSuccess: (res) => {
      codeRef.current = res.code;
      refresh();
    },
    formatResult: (res) => res,
  });

  useEffect(() => {
    if (isApprovalPage) formRef.current?.setFieldsValue(approvalDetails?.fromData);
  }, [isApprovalPage, approvalDetails]);
  // 审批中
  const isProcessed = (data?.activiStatus && data?.activiStatus === '1') || false;

  const handleFormSubmitSuccess = () => {
    codeRef.current = 200;
    refresh?.();
  };

  const onSave = useCallback(() => {
    return new Promise((resolve) => {
      codeRef.current = 0;
      formRef.current?.submit();

      const checkFormInterval = setInterval(() => {
        formRef.current
          ?.validateFields()
          .then(() => {
            const isFormSubmitted = codeRef.current === 200;
            if (isFormSubmitted) {
              clearInterval(checkFormInterval); // 清除轮询
              resolve(200); // 表单提交成功，resolve Promise
            }
          })
          .catch(() => {
            resolve(500);
            clearInterval(checkFormInterval); // 清除轮询
          });
      }, 1000); // 每秒检查一次表单是否提交成功
    });
  }, []);

  return (
    <PageContainer header={{ title: false }} className="detail-container" loading={loading}>
      <ProForm<API.PaymentAppReq>
        formRef={formRef}
        omitNil={false}
        disabled={isProcessed || isApprovalPage || data?.activiStatus === '2'}
        submitter={
          !isProcessed &&
          !isApprovalPage && {
            searchConfig: {
              submitText: '保存',
              resetText: '取消',
            },
            onReset: () => {
              history.go(-1);
            },

            render: (props, doms) => {
              return <FooterToolbar>{doms}</FooterToolbar>;
            },
            submitButtonProps: {
              loading: applyLoading,
              disabled: false,
            },
            resetButtonProps: {
              disabled: false,
            },
          }
        }
        onFinish={async (values) => {
          apply(values);
        }}
      >
        {isEditPage && (
          <ProFormDependency name={['applicationNumber', 'activiStatus', 'audiRecordList']}>
            {({ activiStatus, applicationNumber }) => {
              return (
                <RKPageHeader
                  id={id}
                  status={
                    isApprovalPage
                      ? (approvalDetails?.activiStatus as unknown as string) || '9'
                      : activiStatus
                  }
                  title={applicationNumber}
                  approveType="CONTRACT_PROCUREMENT_PAYMENT_APPLICATION"
                  // onOperationCallback={refresh}
                  onSave={onSave}
                  saveLoading={applyLoading}
                  onSubmit={handleFormSubmitSuccess}
                />
              );
            }}
          </ProFormDependency>
        )}
        <div className="rk-none">
          <ProFormText name="id" />
          <ProFormText name="contractId" />
          <ProFormText name="payPlanId" />
          <ProFormText name="contractType" />
          <ProFormText name="activiStatus" initialValue="0" />
          <ProFormText name="applicationUserId" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText name="contractNumber" label="合同编号" disabled />
          </RKCol>
          <ProFormDependency name={['contractId', 'contractType', 'contractName']}>
            {({ contractId, contractType }) => {
              const path = pagePath[(contractType as 'IC') || 'PC'];
              return (
                <RKCol>
                  <ProFormText
                    name="contractName"
                    label={
                      <TitleLink path={contractId && `${path}${contractId}`}>合同名称</TitleLink>
                    }
                    disabled
                  />
                </RKCol>
              );
            }}
          </ProFormDependency>

          <RKCol>
            <ProFormDatePicker name="estimatePayTime" label="计划付款日期" disabled />
          </RKCol>
          <RKCol>
            <ProFormText name="applicationUser" label="申请人" disabled />
          </RKCol>
          <RKCol>
            <ProFormText
              name="fpName"
              label={
                <TitleLink path={data?.fpId && `/crm/customer/edit/${data?.fpId}`}>甲方</TitleLink>
              }
              disabled
            />
          </RKCol>
          <RKCol>
            <ProFormText
              name="institutionName"
              label="单位名称(账户名)"
              rules={[requiredRule]}
              disabled
            />
          </RKCol>
          <RKCol>
            <ProFormText
              name="bank"
              label="开户银行"
              rules={[requiredRule]}
              disabled={isProcessed || isApprovalPage}
            />
          </RKCol>
          <RKCol>
            <ProFormText
              name="account"
              label="银行账号"
              rules={[requiredRule]}
              disabled={isProcessed || isApprovalPage}
            />
          </RKCol>
          <RKCol>
            <ProFormText
              name="taxpayerNumber"
              label="纳税人识别号"
              rules={[requiredRule]}
              disabled={isProcessed || isApprovalPage}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect name="payWay" label="付款方式" options={PAYMENT} allowClear={false} />
          </RKCol>
          <RKCol>
            <ProFormMoney name="payAmount" label="付款金额" rules={[requiredRule]} disabled />
          </RKCol>
          <RKCol>
            <ProFormMoney name="awaitReAmount" label="待收票金额" disabled />
          </RKCol>
          <RKCol>
            <ProFormText name="remark" label="备注" />
          </RKCol>
          <RKCol>
            <ProFormText name="useWay" label="用途" />
          </RKCol>
        </Row>
        <ProFormDependency name={['contractType', 'contractId']}>
          {({ contractType, contractId }) => {
            return <AssociatedInfo contractId={contractId} contractType={contractType} />;
          }}
        </ProFormDependency>
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(PaymentApplication);
