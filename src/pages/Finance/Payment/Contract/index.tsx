import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import { APPROVAL_STATUS, CONTRACT_TYPE, PAYMENT, PAYMENT_STATUS } from '@/enums';
import { cancelPayApplication, pagePaymentApp } from '@/services/oa/conInvoiced';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { Access, history, useAccess, useLocation, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import ContractListDrawerForm from './components/ContractListDrawerForm';

const ContractPayment: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const { state } = useLocation();
  const [drawerVisit, setDrawerVisit] = useState(false);
  const { canAddContractPayment = false, canRevokeContractPayment = false } = useAccess();
  // 撤销付款
  const { run: cancel } = useRequest((id) => cancelPayApplication({ idReq: { id } }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('操作成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const handleCancel = async (row: API.PaymentAppResp) => {
    Modal.confirm({
      title: '确认撤销',
      content: `您确定要撤销付款申请“${row.applicationNumber}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        cancel(row.id);
      },
    });
  };
  // 表格
  const columns: ProColumns<API.PaymentAppResp>[] = [
    {
      title: '编号',
      dataIndex: 'applicationNumber',
      width: 200,
      copyable: true,
      initialValue: state,
      fixed: 'left',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/finance/payment/contract/${entity.id}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
      width: 100,
    },
    {
      title: '合同类型',
      dataIndex: 'contractType',
      valueEnum: option2enum(CONTRACT_TYPE),
      width: 100,
    },
    {
      title: '付款方式',
      dataIndex: 'payWay',
      valueEnum: option2enum(PAYMENT),
      width: 100,
      hideInSearch: true,
    },
    {
      title: '付款金额',
      dataIndex: 'payAmount',
      width: 150,
      hideInSearch: true,
      valueType: 'money',
    },
    {
      title: '申请人',
      dataIndex: 'applicationUser',
      width: 120,
    },
    {
      title: '付款状态',
      dataIndex: 'payStatus',
      valueType: 'select',
      valueEnum: option2enum(PAYMENT_STATUS),
      width: 150,
    },
    {
      title: '付款日期',
      dataIndex: 'payTime',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '单位名称(账户名)',
      dataIndex: 'institutionName',
      width: 150,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '操作',
      fixed: 'right',
      width: 100,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        const { activiStatus } = record;
        return (
          <Access accessible={canRevokeContractPayment}>
            {activiStatus === '0' && <a onClick={() => handleCancel(record)}>撤销</a>}
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.PaymentAppResp>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="合同付款申请表"
        polling={5000}
        request={async (params) => {
          const { activiStatus, payStatus } = params;
          const filter = { activiStatus, payStatus };
          return queryPagingTable({ ...params, filter }, pagePaymentApp);
        }}
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddContractPayment}>
              <Button type="primary" icon={<PlusOutlined />} onClick={() => setDrawerVisit(true)}>
                发起付款申请
              </Button>
            </Access>,
          ],
        }}
      />
      <ContractListDrawerForm open={drawerVisit} onOpenChange={setDrawerVisit} />
    </PageContainer>
  );
};

export default ContractPayment;
