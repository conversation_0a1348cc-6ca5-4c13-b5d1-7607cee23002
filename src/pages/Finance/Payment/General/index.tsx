import {
  ActionType,
  ModalForm,
  PageContainer,
  ProColumns,
  ProFormSelect,
  ProTable,
} from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { APPROVAL_STATUS, PAYMENT, PAYMENT_STATUS, PAYMENT_TYPE } from '@/enums';
import { paymentApplicationDel, paymentApplicationPage } from '@/services/oa/paymentApplication';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { Access, history, useAccess, useLocation, useRequest } from '@umijs/max';
import { Button, Form, message, Modal } from 'antd';

const GeneralPayment: React.FC = () => {
  const [form] = Form.useForm<{ paymentType: string }>();
  const tableRef = useRef<ActionType | undefined>();
  const { state } = useLocation();
  const [selectedRows, setSelectedRows] = useState<API.PaymentApplicationPageResp[]>([]);
  const { canAddGeneral = false, canDeleteGeneral = false } = useAccess();
  // 删除付款
  const { run: deleteRecord } = useRequest((ids) => paymentApplicationDel({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('操作成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const handleDelete = async (rows: API.PaymentApplicationPageResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.documentNumber!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除付款申请“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };
  // 表格
  const columns: ProColumns<API.PaymentApplicationPageResp>[] = [
    {
      title: '编号',
      dataIndex: 'documentNumber',
      width: 180,
      copyable: true,
      initialValue: state,
      fixed: 'left',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/finance/payment/general/details/${entity.id}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
      width: 100,
    },
    {
      title: '付款类型',
      dataIndex: 'paymentType',
      valueEnum: option2enum(PAYMENT_TYPE.filter((item) => item.value !== 'GOODS')),
      width: 100,
    },
    {
      title: '付款方式',
      dataIndex: 'paymentMethod',
      valueEnum: option2enum(PAYMENT),
      width: 100,
      hideInSearch: true,
    },
    {
      title: '付款金额',
      dataIndex: 'paymentAmount',
      width: 150,
      hideInSearch: true,
      valueType: 'money',
    },
    {
      title: '申请人',
      dataIndex: 'username',
      width: 120,
    },
    {
      title: '付款状态',
      dataIndex: 'payStatus',
      valueType: 'select',
      valueEnum: option2enum(PAYMENT_STATUS),
      width: 150,
    },
    {
      title: '申请日期',
      dataIndex: 'applicationDate',
      valueType: 'date',
      hideInSearch: true,
      width: 150,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      hideInSearch: true,
      width: 200,
      ellipsis: true,
    },
    {
      title: '操作',
      fixed: 'right',
      width: 100,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !canDeleteGeneral,
      render: (text, record) => {
        const { activiStatus } = record;
        return (
          <Access accessible={canDeleteGeneral}>
            {activiStatus === '0' && <a onClick={() => handleDelete([record])}>删除</a>}
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.PaymentApplicationPageResp>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="项目付款申请表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddGeneral}>
              <ModalForm<{
                paymentType: string;
              }>
                title="新建付款申请"
                width="40%"
                trigger={
                  <Button type="primary">
                    <PlusOutlined />
                    新建付款申请
                  </Button>
                }
                form={form}
                autoFocusFirstInput
                modalProps={{
                  destroyOnClose: true,
                }}
                submitter={{
                  searchConfig: {
                    submitText: '确认创建',
                  },
                }}
                submitTimeout={2000}
                onFinish={async (values) => {
                  history.push('/finance/payment/general/add', {
                    paymentType: values.paymentType,
                  });
                  return true;
                }}
              >
                <div style={{ display: 'flex', justifyContent: 'center', padding: '20px 0' }}>
                  <ProFormSelect
                    name="paymentType"
                    label="付款类型"
                    rules={[requiredRule]}
                    options={PAYMENT_TYPE.filter((item) => item.value !== 'GOODS')}
                    width="lg"
                    fieldProps={{
                      allowClear: false,
                    }}
                  />
                </div>
              </ModalForm>
            </Access>,
          ],
        }}
        rowSelection={
          canDeleteGeneral && {
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRows(selectedRows);
            },
            getCheckboxProps: (record) => ({
              disabled: record?.activiStatus !== '0',
            }),
          }
        }
        polling={5000}
        request={async (params) => {
          const { activiStatus, payStatus } = params;
          const filter = { activiStatus, payStatus };
          return queryPagingTable({ ...params, filter }, paymentApplicationPage);
        }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default GeneralPayment;
