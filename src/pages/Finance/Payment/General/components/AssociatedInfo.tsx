import { contractReceiptsAndPayments } from '@/services/oa/contract';
import { groupedData } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProTable } from '@ant-design/pro-components';
import { Link, useRequest } from '@umijs/max';
import { Card, Collapse, Descriptions, Empty } from 'antd';
import { FC, memo, useMemo } from 'react';
import { collectionColumns, planColumns } from '../../Contract/components/AssociatedInfo';

const AssociatedInfo: FC<{
  projectId: string;
}> = ({ projectId }) => {
  const { data, loading } = useRequest(
    () =>
      contractReceiptsAndPayments({
        id: projectId,
      }),
    {
      ready: Boolean(projectId),
    },
  );

  // 分组关联主合同的其他采购合同的付款计划
  const groupedPayPlan = useMemo(
    () => groupedData(data?.payPlanDetailInfoList || [], 'contractNumber'),
    [data?.payPlanDetailInfoList],
  );

  return (
    <Collapse defaultActiveKey={['1', '2']} ghost>
      <Collapse.Panel key="1" header="关联主合同收款计划详细信息表" collapsible="header">
        <ProTable<API.PayPlanDetailInfoResp>
          {...defaultTableConfig}
          loading={loading}
          dataSource={data?.collectionPlanList}
          options={false}
          columns={collectionColumns}
          tableExtraRender={(_, data) =>
            data.length > 0 && (
              <Card
                style={{
                  margin: '0 24px',
                }}
              >
                <Descriptions size="small" column={3}>
                  <Descriptions.Item label="主合同名称">
                    {data?.at(0)?.contractName}
                  </Descriptions.Item>
                  <Descriptions.Item label="主合同编号">
                    <Link to={`/contract/main/edit/${data?.at(0)?.contractId}`}>
                      {data?.at(0)?.contractNumber}
                    </Link>
                  </Descriptions.Item>
                  <Descriptions.Item label="主合同总金额">
                    {data?.at(0)?.contractAmount}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            )
          }
        />
      </Collapse.Panel>
      <Collapse.Panel
        key="2"
        header="关联主合同的采购合同的付款计划详细信息表"
        collapsible="header"
      >
        {!data?.payPlanDetailInfoList?.length && <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
        {Object.entries(groupedPayPlan).map(([key, value]) => (
          <ProTable<API.PayPlanDetailInfoResp>
            {...defaultTableConfig}
            key={key}
            loading={loading}
            scroll={{ x: '100%' }}
            dataSource={value}
            options={false}
            columns={planColumns}
            // headerTitle={
            //   // <Link to={`/contract/purchase/edit/${value?.at(0)?.contractId}`}>
            //   `${value?.at(0)?.contractName} (${key})`
            //   // </Link>
            // }
            tableExtraRender={(_, data) =>
              data.length > 0 && (
                <Card
                  style={{
                    margin: '0 24px',
                  }}
                >
                  <Descriptions size="small" column={3}>
                    <Descriptions.Item label="采购合同名称">
                      {data?.at(0)?.contractName}
                    </Descriptions.Item>
                    <Descriptions.Item label="采购合同编号">
                      <Link to={`/contract/purchase/edit/${data?.at(0)?.contractId}`}>
                        {data?.at(0)?.contractNumber}
                      </Link>
                    </Descriptions.Item>
                    <Descriptions.Item label="采购总金额">
                      {data?.at(0)?.contractAmount}
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              )
            }
            style={{
              marginTop: 0,
            }}
          />
        ))}
      </Collapse.Panel>
    </Collapse>
  );
};

export default memo(AssociatedInfo);
