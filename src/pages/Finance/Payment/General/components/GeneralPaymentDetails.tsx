import RKCol from '@/components/RKCol';
import RK<PERSON><PERSON>Header from '@/components/RKPageHeader';
import RKPageLoading from '@/components/RKPageLoading';
import RKSelectLabel from '@/components/RKSelectLabel';
import TitleLink from '@/components/TitleLink';
import { PAYMENT, PAYMENT_TYPE } from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useAvailableProjectList } from '@/hooks/useAvailableProjectList';
import { useDepartment } from '@/hooks/useDepartment';
import { usePartnerList } from '@/hooks/usePartnerList';
import { toPage } from '@/pages/Contract/Internal/components/BaseInfo';
import {
  paymentApplicationCreate,
  paymentApplicationInfo,
  paymentApplicationUpdate,
} from '@/services/oa/paymentApplication';
import { onSuccessAndGoBack } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormInstance,
  ProFormMoney,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useAccess, useLocation, useModel, useRequest } from '@umijs/max';
import { message, Row, Typography } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import dayjs from 'dayjs';
import { useCallback, useEffect, useRef } from 'react';
import AssociatedInfo from './AssociatedInfo';

interface LocationState {
  paymentType?: string;
}

const GeneralPaymentDetails: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  const formRef = useRef<ProFormInstance>();
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const { departmentList, loading: departmentLoading } = useDepartment();
  const { partnerList, loading: partnerLoading } = usePartnerList();
  const { availableProjectList, loading: availableProjectLoading } = useAvailableProjectList();
  const { canAddGeneral, canEditGeneral } = useAccess();
  const { approvalDetails } = useModel('useApprovalModel');
  const codeRef = useRef(0);

  const { pathname = '', state } = useLocation();
  // 判断是否为审批页面
  const isApprovalPage = pathname.includes('/approval/');
  // 新建时从列表页获取用户选择的付款类型
  const { paymentType: selectedPaymentType = null } = (state as LocationState) || {};

  // 新建
  const { run: add, loading: addLoading } = useRequest((value) => paymentApplicationCreate(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 修改
  const { run: update, loading: editLoading } = useRequest(
    (value) => paymentApplicationUpdate(value),
    {
      manual: true,
      formatResult: (res) => res,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        codeRef.current = res.code;
        message.success('保存成功！');
      },
    },
  );

  const onSave = useCallback(() => {
    return new Promise((resolve) => {
      codeRef.current = 0;
      formRef.current?.submit();

      const checkFormInterval = setInterval(() => {
        formRef.current
          ?.validateFields()
          .then(() => {
            const isFormSubmitted = codeRef.current === 200;
            if (isFormSubmitted) {
              clearInterval(checkFormInterval); // 清除轮询
              resolve(200); // 表单提交成功，resolve Promise
            }
          })
          .catch(() => {
            resolve(500);
            clearInterval(checkFormInterval); // 清除轮询
          });
      }, 1000); // 每秒检查一次表单是否提交成功
    });
  }, []);

  // 获取详情
  const {
    data = {},
    loading,
    refresh,
  } = useRequest(
    () =>
      paymentApplicationInfo({
        req: { id },
      }),
    {
      ready: isEditPage && !isApprovalPage,
      onSuccess: (res) => {
        setTimeout(() => {
          formRef.current?.setFieldsValue(res);
        }, 500);
      },
    },
  );

  useEffect(() => {
    if (isApprovalPage) formRef.current?.setFieldsValue(approvalDetails?.fromData);
  }, [isApprovalPage, approvalDetails]);
  // 是否提交审批
  const isSubmit = (data?.activiStatus && data?.activiStatus !== '0') || false;

  const canEdit = (canEditGeneral && isEditPage) || (!isEditPage && canAddGeneral);

  const clientList = partnerList.filter((item) => item.partnerType === 'CLIENT'); //客户

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <RKPageLoading loading={loading} />
      <ProForm<API.PaymentApplicationInsertReq>
        formRef={formRef}
        initialValues={{
          paymentType: selectedPaymentType,
          username: currentUser?.username,
          userId: currentUser?.id,
          departmentName: currentUser?.department,
          applicationDate: dayjs().format('YYYY-MM-DD'),
        }}
        disabled={isSubmit || isApprovalPage || !canEdit}
        submitter={
          !isSubmit &&
          !isApprovalPage &&
          canEdit && {
            searchConfig: {
              submitText: '保存',
              resetText: '取消',
            },
            onReset: () => {
              history.go(-1);
            },

            render: (props, doms) => {
              return <FooterToolbar>{doms}</FooterToolbar>;
            },
            submitButtonProps: {
              loading: addLoading || editLoading,
            },
            resetButtonProps: {
              disabled: false,
            },
          }
        }
        onFinish={async (values) => {
          if (isEditPage) {
            update(values);
          } else {
            add(values);
          }
        }}
      >
        {isEditPage && (
          <ProFormDependency name={['documentNumber', 'activiStatus']}>
            {({ activiStatus, documentNumber }) => {
              return (
                <RKPageHeader
                  id={id}
                  status={
                    isApprovalPage
                      ? (approvalDetails?.activiStatus as unknown as string) || '9'
                      : activiStatus
                  }
                  title={documentNumber}
                  approveType="UNIVERSAL_PAYMENT"
                  onOperationCallback={refresh}
                  onSave={onSave}
                  saveDisabled={!canEditGeneral}
                />
              );
            }}
          </ProFormDependency>
        )}
        <div className="rk-none">
          <ProFormText name="id" />
          <ProFormText name="userId" />
          <ProFormText name="documentNumber" />
          <ProFormText name="projectId" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText disabled name="username" label="申请人" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormSelect
              disabled
              name="departmentName"
              label="所属部门"
              rules={[requiredRule]}
              fieldProps={{
                loading: departmentLoading,
                fieldNames: {
                  value: 'id',
                  label: 'departmentName',
                },
                showSearch: true,
              }}
              options={departmentList as DefaultOptionType[]}
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker
              name="applicationDate"
              label="申请日期"
              rules={[requiredRule]}
              disabled
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="paymentType"
              label="付款类型"
              rules={[requiredRule]}
              options={PAYMENT_TYPE.filter((item) => item.value !== 'GOODS')}
              fieldProps={{
                allowClear: false,
              }}
              disabled
            />
          </RKCol>

          <RKCol>
            <ProFormSelect
              name="paymentMethod"
              label="付款方式"
              fieldProps={{
                showSearch: true,
                allowClear: false,
              }}
              options={PAYMENT}
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker
              name="expectedPaymentDate"
              label="预计付款日期"
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker name="paymentDeadline" label="付款截止日期" rules={[requiredRule]} />
          </RKCol>
          <ProFormDependency name={['paymentType']}>
            {({ paymentType }) => {
              if (paymentType && paymentType !== 'GENERAL') {
                return (
                  <RKCol>
                    <ProFormDatePicker
                      name="expectedReturnTime"
                      label="预计退回时间"
                      rules={[requiredRule]}
                    />
                  </RKCol>
                );
              }
            }}
          </ProFormDependency>
          <ProFormDependency name={['projectId', 'projectNumber']}>
            {({ projectId, projectNumber }) => {
              return (
                <RKCol>
                  <ProFormSelect
                    name="projectNumber"
                    label={
                      <TitleLink path={projectId && toPage(projectId, projectNumber)}>
                        项目编号
                      </TitleLink>
                    }
                    rules={[requiredRule]}
                    fieldProps={{
                      showSearch: true,
                      loading: availableProjectLoading,
                      optionLabelProp: 'projectNumber',
                      onChange: (value, option) => {
                        formRef.current?.setFieldsValue({
                          projectName: (option as { projectName?: string }).projectName,
                          projectId: (option as { id?: string }).id,
                          customerId: (option as { clientId?: string }).clientId,
                          customerName: (option as { clientName?: string }).clientName,
                        });
                      },
                      filterOption: (inputValue, option) => {
                        return option?.keywords.indexOf(inputValue) >= 0;
                      },
                    }}
                    options={availableProjectList?.map((item) => {
                      return {
                        value: item.projectNumber,
                        label: <RKSelectLabel title={item.projectNumber} info={item.projectName} />,
                        projectName: item.projectName,
                        projectNumber: item.projectNumber,
                        id: item.id,
                        keywords: `${item.projectName}${item.projectNumber}`,
                        clientId: item.clientId,
                        clientName: item.clientName,
                      };
                    })}
                  />
                </RKCol>
              );
            }}
          </ProFormDependency>
          <RKCol>
            <ProFormText disabled name="projectName" label="项目名称" rules={[requiredRule]} />
          </RKCol>
          <ProFormDependency name={['customerId', 'customerName']}>
            {({ customerId }) => {
              return (
                <RKCol>
                  <ProFormSelect
                    name="customerId"
                    label={
                      <TitleLink path={customerId && `/crm/customer/edit/${customerId}`}>
                        客户名称
                      </TitleLink>
                    }
                    rules={[requiredRule]}
                    fieldProps={{
                      loading: partnerLoading,
                      fieldNames: {
                        value: 'id',
                        label: 'clientName',
                      },
                      showSearch: true,
                    }}
                    options={clientList as DefaultOptionType[]}
                    transform={(value, namePath) => ({
                      [namePath]: value,
                      customerName: partnerList.find((item) => item.id === value)?.clientName,
                    })}
                  />
                </RKCol>
              );
            }}
          </ProFormDependency>
          <RKCol>
            <ProFormTextArea
              name="remarks"
              label="说明"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 3,
                },
              }}
            />
          </RKCol>
          <ProFormDependency name={['paymentType']}>
            {({ paymentType }) => {
              if (paymentType && paymentType === 'GENERAL') {
                return (
                  <RKCol>
                    <ProFormRadio.Group
                      initialValue="1"
                      name="hasInvoice"
                      label="是否有发票"
                      options={[
                        { label: '有票', value: '1' },
                        { label: '无票', value: '0' },
                      ]}
                      rules={[requiredRule]}
                    />
                  </RKCol>
                );
              }
            }}
          </ProFormDependency>
        </Row>
        <Typography.Title level={5} style={{ marginBlock: 16 }}>
          收款账户信息
        </Typography.Title>
        <Row gutter={24}>
          <RKCol>
            <ProFormText name="accountName" label="单位名称(账户名)" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormText name="account" label="账号" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormText name="openingBank" label="开户行" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormMoney
              name="paymentAmount"
              label="付款金额"
              locale="zh_CN"
              min={0}
              fieldProps={{ precision: 2, step: 1 }}
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="directions"
              label="备注"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 3,
                },
              }}
            />
          </RKCol>
        </Row>
        <ProFormDependency name={['projectId']}>
          {({ projectId }) => {
            return <AssociatedInfo projectId={projectId} />;
          }}
        </ProFormDependency>
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(GeneralPaymentDetails);
