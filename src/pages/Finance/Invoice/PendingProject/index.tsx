import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef } from 'react';

import { PAYMENT_STATUS } from '@/enums';
import { toPage } from '@/pages/Contract/Internal/components/BaseInfo';
import { missingTrackProject } from '@/services/oa/collectTicket';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { history } from '@umijs/max';

const PendingProject: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();

  // 表格列配置
  const columns: ProColumns<API.PendingReceiptProjectResp>[] = [
    {
      title: '项目付款申请编号',
      dataIndex: 'documentNumber',
      width: 200,
      ellipsis: true,
      copyable: true,
      fixed: 'left',
      render(dom, entity) {
        const { id } = entity;
        return (
          <a
            className="rk-a-span"
            onClick={() => {
              history.push(`/finance/payment/general/details/${id}`);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '项目编号',
      dataIndex: 'projectNumber',
      width: 200,
      copyable: true,
      ellipsis: true,
      render(dom, entity) {
        const { projectId = '', projectNumber } = entity;
        return (
          <a
            className="rk-a-span"
            onClick={() => {
              history.push(toPage(projectId, projectNumber) || '');
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '付款状态',
      dataIndex: 'payStatus',
      width: 120,
      ellipsis: true,
      valueEnum: option2enum(PAYMENT_STATUS),
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 200,
      ellipsis: true,
    },
    {
      title: '申请人姓名',
      dataIndex: 'username',
      search: false,
      width: 120,
    },
    {
      title: '申请日期',
      dataIndex: 'applicationDate',
      search: false,
      width: 120,
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      search: false,
      width: 200,
      ellipsis: true,
    },
    {
      title: '付款金额',
      dataIndex: 'paymentAmount',
      search: false,
      width: 150,
      valueType: 'money',
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.PendingReceiptProjectResp>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
          labelWidth: 120,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="项目待收票记录表"
        request={async (params) => {
          const { projectNumber, documentNumber, projectName, payStatus, current, pageSize } =
            params;
          const search = {
            documentNumber,
            projectNumber,
            projectName,
          };
          const filter = {
            payStatus,
          };
          const res = await missingTrackProject({
            pageNum: current,
            pageSize,
            search,
            filter,
          });
          return {
            data: res.data?.records || [],
            total: res.data?.total || 0,
            success: res.code === 200,
          };
        }}
      />
    </PageContainer>
  );
};

export default PendingProject;
