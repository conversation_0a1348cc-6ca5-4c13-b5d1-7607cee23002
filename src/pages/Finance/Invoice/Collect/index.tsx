import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef } from 'react';

import { CLAIM_STATUS } from '@/enums';
import { useUserList } from '@/hooks/useUserList';
import { rangePresets } from '@/pages/ReportAnalysis/components/DimensionSetting';
import { collectTicketDelete, collectTicketPage } from '@/services/oa/collectTicket';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import dayjs from 'dayjs';

const Invoicing: React.FC = () => {
  const { canDeleteInvoiceCollectRecord = false, canAddInvoiceCollectRecord = false } = useAccess();
  const { userList, loading: userLoading } = useUserList();
  const tableRef = useRef<ActionType | undefined>();
  const { run: handleDelete } = useRequest((ids) => collectTicketDelete({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('操作成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const confirmDelete = async (row: API.CollectTicketPageResp) => {
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除收票记录“${row.documentNumber}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        handleDelete([row.id]);
      },
    });
  };
  // 表格
  const columns: ProColumns<API.CollectTicketPageResp>[] = [
    {
      title: '收票流水号',
      dataIndex: 'documentNumber',
      width: 200,
      copyable: true,
      fixed: 'left',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/finance/invoice/collect-list/edit/${entity.id}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '认领状态',
      dataIndex: 'claimStatus',
      valueEnum: option2enum(CLAIM_STATUS),
      width: 100,
    },
    {
      title: '认领人',
      dataIndex: 'assignPersonName',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '认领人',
      dataIndex: 'assignPersonId',
      width: 120,
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: userList,
        loading: userLoading,
        showSearch: true,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        optionFilterProp: 'label',
        filterOption: true,
      },
    },
    {
      title: '开票日期',
      dataIndex: 'invoiceDate',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '领票日期',
      dataIndex: 'assignDate',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '合同号(项目号)',
      dataIndex: 'contractNumber',
      width: 120,
    },
    {
      title: '日期范围',
      valueType: 'dateRange',
      dataIndex: 'days',
      fieldProps: {
        presets: rangePresets,
      },
      // initialValue: [dayjs().startOf('y'), dayjs().endOf('y')],
      hideInTable: true,
    },
    {
      title: '发票金额',
      dataIndex: 'totalAmount',
      width: 120,
      valueType: 'money',
      fieldProps: {
        min: 0,
      },
    },
    {
      title: '单位名称',
      dataIndex: 'institutionName',
      width: 120,
    },
    {
      title: '发票号',
      dataIndex: 'invoiceNo',
      width: 120,
      hideInTable: true,
    },
    {
      title: '操作',
      fixed: 'right',
      width: 100,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !canDeleteInvoiceCollectRecord,
      render: (_, record) => {
        const { claimStatus } = record;
        return (
          <Access accessible={canDeleteInvoiceCollectRecord}>
            {claimStatus === 'NOT_CLAIM' && <a onClick={() => confirmDelete(record)}>删除</a>}
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.CollectTicketPageResp>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="收票记录表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddInvoiceCollectRecord}>
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  history.push('/finance/invoice/collect-list/add');
                }}
              >
                新建收票记录
              </Button>
            </Access>,
          ],
        }}
        request={async (params) => {
          const {
            documentNumber,
            totalAmount,
            institutionName,
            assignPersonId,
            days,
            contractNumber,
            current,
            pageSize,
            invoiceNo,
            claimStatus,
          } = params;
          const search = {
            documentNumber,
            totalAmount,
            institutionName,
            contractNumber,
          };
          const filter = { claimStatus };
          const extra = { assignPersonId, invoiceNo };
          const scope =
            days &&
            [
              {
                key: 'ge',
                name: 'invoiceDate',
                val: dayjs(days[0]).format('YYYY-MM-DD'),
              },
              {
                key: 'le',
                name: 'invoiceDate',
                val: dayjs(days[1]).format('YYYY-MM-DD'),
              },
            ].filter(Boolean);
          const res = await collectTicketPage({
            pageNum: current,
            pageSize,
            search,
            extra,
            scope,
            filter,
          });
          return {
            data: res.data?.records || [],
            total: res.data?.total || 0,
            success: res.code === 200,
          };
        }}
      />
    </PageContainer>
  );
};

export default Invoicing;
