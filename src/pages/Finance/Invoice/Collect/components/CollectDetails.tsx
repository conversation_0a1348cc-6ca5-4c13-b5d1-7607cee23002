import RKCol from '@/components/RKCol';
import RKSelectLabel from '@/components/RKSelectLabel';
import TitleLink from '@/components/TitleLink';
import { BILL_TYPE, INVOICE_TYPE } from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { toPage } from '@/pages/Contract/Internal/components/BaseInfo';
import {
  collectTicketCancel,
  collectTicketClaim,
  collectTicketInfo,
  collectTicketInsert,
  collectTicketUpdate,
  contractList,
} from '@/services/oa/collectTicket';
import { onSuccessAndGoBack, onSuccessAndRefresh } from '@/utils';
import { requiredRule } from '@/utils/setting';
import { useLocation } from '@@/exports';
import {
  DrawerForm,
  FooterToolbar,
  FormListActionType,
  PageContainer,
  PageHeader,
  ProCard,
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormGroup,
  ProFormInstance,
  ProFormList,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Access, useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal, Row, Space, Typography } from 'antd';
import Decimal from 'decimal.js';
import { useRef, useState } from 'react';

const CollectDetails: React.FC<WithRouteEditingProps> = ({ id, isEditPage }) => {
  const formRef = useRef<ProFormInstance>();
  const invoiceListAction = useRef<FormListActionType<{ invoiceNo: string; list: [] }>>();
  const { pathname } = useLocation();
  const [drawerVisit, setDrawerVisit] = useState(false);
  const {
    canClaimInvoiceCollectRecord = false,
    canRevokeInvoiceCollectRecord = false,
    canEditInvoiceCollectRecord = false,
    canAddInvoiceCollectRecord = false,
  } = useAccess();
  const isAddPage = pathname.includes('/collect-list/add');

  // 获取详情
  const {
    data = {},
    refresh,
    loading,
  } = useRequest(() => collectTicketInfo({ id }), {
    ready: !isAddPage,
    onSuccess: (res) => {
      setTimeout(() => {
        formRef.current?.setFieldsValue(
          {
            ...res,
            invoiceNumberList: res?.invoiceNumberList?.length
              ? res?.invoiceNumberList
              : [
                  {
                    invoiceNo: '',
                    list: [
                      {
                        content: '',
                        model: '',
                        unit: '',
                        amount: 0,
                        money: 0,
                        taxRate: 0,
                        tax: 0,
                      },
                    ],
                  },
                ],
          } || {},
        );
      }, 500);
    },
  });

  // 新建收票
  const { run: add, loading: addLoading } = useRequest((value) => collectTicketInsert(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 编辑收票
  const { run: edit, loading: editLoading } = useRequest((value) => collectTicketUpdate(value), {
    manual: true,
    onSuccess: (res) => onSuccessAndRefresh(res, refresh),
    formatResult: (res) => res,
  });
  //获取合同
  const {
    run: getContract,
    data: contractLists = [],
    loading: contractLoading,
  } = useRequest(
    () => contractList({ institutionName: formRef.current?.getFieldValue('institutionName') }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.length) {
          setDrawerVisit(true);
        } else {
          message.warning('暂无合同信息');
        }
      },
      formatResult: (res) => res?.data,
    },
  );
  // 领票
  const { run: claim, loading: claimLoading } = useRequest((value) => collectTicketClaim(value), {
    manual: true,
    onSuccess: (res) => {
      setDrawerVisit(false);
      onSuccessAndRefresh(res, refresh);
    },
    formatResult: (res) => res,
  });
  // 撤销
  const { run: revoke, loading: revokeLoading } = useRequest(() => collectTicketCancel({ id }), {
    manual: true,
    onSuccess: (res) => {
      setDrawerVisit(false);
      onSuccessAndRefresh(res, refresh);
    },
    formatResult: (res) => res,
  });

  // 添加计算总额的函数
  const calculateTotal = () => {
    const { invoiceNumberList } = formRef?.current?.getFieldsValue();
    //计算invoiceNumberList内的金额的总和
    const total = invoiceNumberList?.reduce((acc: Decimal, cur: any) => {
      const listTotal = cur?.list?.reduce((a: Decimal, c: any) => {
        return a.plus(new Decimal(c?.money || 0));
      }, new Decimal(0));
      return acc.plus(listTotal || 0);
    }, new Decimal(0));
    //计算invoiceNumberList内的税额的总和
    const totalTax = invoiceNumberList?.reduce((acc: Decimal, cur: any) => {
      const listTaxTotal = cur?.list?.reduce((a: Decimal, c: any) => {
        return a.plus(new Decimal(c?.tax || 0));
      }, new Decimal(0));
      return acc.plus(listTaxTotal || 0);
    }, new Decimal(0));

    formRef?.current?.setFieldsValue({
      amountOfMoney: total.toNumber(),
      taxAmount: totalTax.toNumber(),
      totalAmount: total.plus(totalTax).toNumber(),
    });
  };

  const canEdit =
    (canEditInvoiceCollectRecord && data?.claimStatus === 'NOT_CLAIM') ||
    (canAddInvoiceCollectRecord && isAddPage);

  return (
    <PageContainer header={{ title: false }} className="detail-container" loading={loading}>
      <PageHeader
        className="rk-page-header"
        extra={
          isEditPage ? (
            <Space>
              {data?.claimStatus === 'NOT_CLAIM' && (
                <Access accessible={canClaimInvoiceCollectRecord}>
                  <Button
                    loading={claimLoading || contractLoading}
                    type="primary"
                    onClick={() => {
                      getContract();
                    }}
                  >
                    领票
                  </Button>
                </Access>
              )}
              {data?.claimStatus === 'CLAIM' && (
                <Access accessible={canRevokeInvoiceCollectRecord}>
                  <Button
                    loading={revokeLoading}
                    type="primary"
                    onClick={() => {
                      Modal.confirm({
                        title: '确认撤销',
                        content: `您确定要撤销领票吗？`,
                        okText: '确认',
                        cancelText: '取消',
                        onOk: async () => {
                          revoke();
                        },
                      });
                    }}
                  >
                    撤销
                  </Button>
                </Access>
              )}
            </Space>
          ) : null
        }
      />
      <ProForm<API.InvoicingReq>
        formRef={formRef}
        disabled={!canEdit}
        initialValues={{
          invoiceNumberList: [
            {
              invoiceNo: '',
              list: [
                {
                  content: '',
                  model: '',
                  unit: '',
                  amount: 0,
                  money: 0,
                  taxRate: 0,
                  tax: 0,
                },
              ],
            },
          ],
        }}
        onFieldsChange={(changedFields) => {
          // 如果变化的字段包含在 list 中，则重新计算总额
          if (
            changedFields.some(
              (field: Record<string, any>) => field.name?.[0] === 'invoiceNumberList',
            )
          ) {
            calculateTotal();
          }
        }}
        submitter={
          canEdit && {
            searchConfig: {
              submitText: '保存',
              resetText: '取消',
            },
            onReset: () => {
              history.go(-1);
            },
            render: (props, doms) => {
              return <FooterToolbar>{doms}</FooterToolbar>;
            },
            submitButtonProps: {
              loading: addLoading || editLoading,
              disabled: false,
            },
            resetButtonProps: {
              disabled: false,
            },
          }
        }
        onFinish={async (values) => {
          if (isAddPage) {
            add(values);
          } else {
            edit(values);
          }
        }}
      >
        <div className="rk-none">
          <ProFormText name="id" />
          <ProFormText name="claimStatus" />
          <ProFormText name="contractType" />
        </div>
        <Row gutter={24}>
          <ProFormDependency name={['documentNumber']}>
            {({ documentNumber }) => {
              if (!documentNumber) return null;
              return (
                <RKCol>
                  <ProFormText name="documentNumber" label="收票流水号" disabled />
                </RKCol>
              );
            }}
          </ProFormDependency>
          <ProFormDependency name={['contractId', 'contractType', 'contractNumber']}>
            {({ contractId, contractType, contractNumber }) => {
              if (!contractId) return null;
              return (
                <RKCol>
                  <ProFormText
                    name="contractNumber"
                    label={
                      <TitleLink
                        path={
                          contractType === 'PC'
                            ? `/contract/purchase/edit/${contractId}`
                            : contractType === 'IC'
                            ? `/contract/internal/edit/${contractId}`
                            : toPage(contractId, contractNumber)
                        }
                      >
                        合同号(项目号)
                      </TitleLink>
                    }
                    disabled
                  />
                </RKCol>
              );
            }}
          </ProFormDependency>
          <RKCol>
            <ProFormText name="institutionName" label="单位名称" rules={[requiredRule]} />
          </RKCol>
          <ProFormDependency name={['contractName']}>
            {({ contractName }) => {
              if (!contractName) return null;
              return (
                <RKCol>
                  <ProFormText name="contractName" label="合同名称" rules={[requiredRule]} />
                </RKCol>
              );
            }}
          </ProFormDependency>
          <RKCol>
            <ProFormText name="taxpayerNumber" label="纳税人识别号" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="invoiceType"
              label="发票类型"
              options={INVOICE_TYPE}
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormMoney
              name="amountOfMoney"
              label="合计金额"
              rules={[requiredRule]}
              fieldProps={{
                min: 0,
              }}
              disabled
            />
          </RKCol>
          <RKCol>
            <ProFormDigit
              name="taxAmount"
              label="合计税额"
              rules={[requiredRule]}
              disabled
              fieldProps={{
                precision: 2,
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormMoney
              name="totalAmount"
              label="价税合计"
              rules={[requiredRule]}
              fieldProps={{
                min: 0,
              }}
              disabled
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="billType"
              label="票据类型"
              options={BILL_TYPE}
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker name="invoiceDate" label="开票日期" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="remarks"
              label="备注"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 3,
                },
              }}
            />
          </RKCol>
          <ProFormDependency name={['assignDate', 'assignPersonName']}>
            {({ assignDate, assignPersonName }) => {
              return (
                <>
                  {assignDate ? (
                    <RKCol>
                      <ProFormDatePicker
                        name="assignDate"
                        label="领票日期"
                        rules={[requiredRule]}
                      />
                    </RKCol>
                  ) : null}
                  {assignPersonName ? (
                    <RKCol>
                      <ProFormText name="assignPersonName" label="领票人" disabled />
                    </RKCol>
                  ) : null}
                </>
              );
            }}
          </ProFormDependency>
        </Row>
        <Typography.Title level={5} style={{ marginBlock: 16 }}>
          <Space>
            <div>发票信息</div>
            <Button
              type="primary"
              size="small"
              onClick={() => {
                invoiceListAction?.current?.add();
              }}
            >
              添加发票
            </Button>
          </Space>
        </Typography.Title>
        <ProFormList
          name="invoiceNumberList"
          label=""
          required
          min={1}
          actionRef={invoiceListAction}
          creatorButtonProps={false}
          copyIconProps={
            !canEdit
              ? false
              : {
                  tooltipText: '复制此项到末尾',
                }
          }
          deleteIconProps={
            !canEdit
              ? false
              : {
                  tooltipText: '不需要这行了',
                }
          }
          itemRender={({ listDom, action }, { record }) => {
            return (
              <ProCard
                bordered
                extra={action}
                title={record?.name}
                style={{
                  marginBlockEnd: 8,
                }}
              >
                {listDom}
              </ProCard>
            );
          }}
        >
          <ProFormGroup>
            <ProFormText name="invoiceNo" label="发票号" rules={[requiredRule]} />
          </ProFormGroup>
          <ProFormList
            name="list"
            label=""
            min={1}
            creatorButtonProps={{
              creatorButtonText: '添加明细',
            }}
            copyIconProps={
              !canEdit
                ? false
                : {
                    tooltipText: '复制此项到末尾',
                  }
            }
            deleteIconProps={
              !canEdit
                ? false
                : {
                    tooltipText: '不需要这行了',
                  }
            }
          >
            {(f, index, action) => {
              return (
                <ProFormGroup key="group">
                  <Row gutter={24}>
                    <RKCol lg={5} md={5} sm={5}>
                      <ProFormTextArea
                        name="content"
                        label="开票内容"
                        fieldProps={{
                          autoSize: {
                            minRows: 1,
                            maxRows: 3,
                          },
                        }}
                        rules={[requiredRule]}
                      />
                    </RKCol>
                    <RKCol lg={3} md={3} sm={3}>
                      <ProFormText name="model" label="规格型号" rules={[requiredRule]} />
                    </RKCol>
                    <RKCol lg={3} md={3} sm={3}>
                      <ProFormText name="unit" label="单位" rules={[requiredRule]} />
                    </RKCol>
                    <RKCol lg={3} md={3} sm={3}>
                      <ProFormDigit name="amount" label="数量" rules={[requiredRule]} />
                    </RKCol>
                    <RKCol lg={3} md={3} sm={3}>
                      <ProFormMoney
                        name="money"
                        label="金额"
                        rules={[requiredRule]}
                        initialValue={0}
                        fieldProps={{
                          min: 0,
                          onChange: (e) => {
                            if (e || e === 0) {
                              const { taxRate } = action.getCurrentRowData();
                              const money = new Decimal(e);
                              const rate = new Decimal(taxRate).dividedBy(100);
                              const taxAmount = money.times(rate).toDecimalPlaces(2);

                              action.setCurrentRowData({
                                tax: taxAmount.toNumber(),
                              });

                              const { invoiceNumberList } = formRef?.current?.getFieldsValue();
                              //计算invoiceNumberList内的金额的总和
                              const total = invoiceNumberList?.reduce((acc: Decimal, cur: any) => {
                                const listTotal = cur?.list?.reduce((a: Decimal, c: any) => {
                                  return a.plus(new Decimal(c?.money || 0));
                                }, new Decimal(0));
                                return acc.plus(listTotal || 0);
                              }, new Decimal(0));

                              //计算invoiceNumberList内的税额的总和
                              const totalTax = invoiceNumberList?.reduce(
                                (acc: Decimal, cur: any) => {
                                  const listTaxTotal = cur?.list?.reduce((a: Decimal, c: any) => {
                                    return a.plus(new Decimal(c?.tax || 0));
                                  }, new Decimal(0));
                                  return acc.plus(listTaxTotal || 0);
                                },
                                new Decimal(0),
                              );

                              formRef?.current?.setFieldsValue({
                                amountOfMoney: total.toNumber(),
                                taxAmount: totalTax.toNumber(),
                                totalAmount: total.plus(totalTax).toNumber(),
                              });
                            }
                          },
                        }}
                      />
                    </RKCol>
                    <RKCol lg={3} md={3} sm={3}>
                      <ProFormDigit
                        name="taxRate"
                        label="税率"
                        rules={[requiredRule]}
                        initialValue={0}
                        addonAfter="%"
                        fieldProps={{
                          min: 0,
                          onChange: (e) => {
                            if (e || e === 0) {
                              const { money } = action.getCurrentRowData();
                              const amount = new Decimal(money);
                              const rate = new Decimal(e).dividedBy(100);
                              const taxAmount = amount.times(rate).toDecimalPlaces(2);

                              action.setCurrentRowData({
                                tax: taxAmount.toNumber(),
                              });

                              const { invoiceNumberList } = formRef?.current?.getFieldsValue();
                              //计算invoiceNumberList内的金额的总和
                              const total = invoiceNumberList?.reduce((acc: Decimal, cur: any) => {
                                const listTotal = cur?.list?.reduce((a: Decimal, c: any) => {
                                  return a.plus(new Decimal(c?.money || 0));
                                }, new Decimal(0));
                                return acc.plus(listTotal || 0);
                              }, new Decimal(0));

                              //计算invoiceNumberList内的税额的总和
                              const totalTax = invoiceNumberList?.reduce(
                                (acc: Decimal, cur: any) => {
                                  const listTaxTotal = cur?.list?.reduce((a: Decimal, c: any) => {
                                    return a.plus(new Decimal(c?.tax || 0));
                                  }, new Decimal(0));
                                  return acc.plus(listTaxTotal || 0);
                                },
                                new Decimal(0),
                              );

                              formRef?.current?.setFieldsValue({
                                amountOfMoney: total.toNumber(),
                                taxAmount: totalTax.toNumber(),
                                totalAmount: total.plus(totalTax).toNumber(),
                              });
                            }
                          },
                        }}
                      />
                    </RKCol>
                    <RKCol lg={3} md={3} sm={3}>
                      <ProFormDigit
                        name="tax"
                        label="税额"
                        initialValue={0}
                        rules={[requiredRule]}
                        fieldProps={{
                          precision: 2,
                          onChange: (e) => {
                            if (e || e === 0) {
                              const { invoiceNumberList } = formRef?.current?.getFieldsValue();
                              //计算invoiceNumberList内的金额的总和
                              const total = invoiceNumberList?.reduce((acc: Decimal, cur: any) => {
                                const listTotal = cur?.list?.reduce((a: Decimal, c: any) => {
                                  return a.plus(new Decimal(c?.money || 0));
                                }, new Decimal(0));
                                return acc.plus(listTotal || 0);
                              }, new Decimal(0));

                              //计算invoiceNumberList内的税额的总和
                              const totalTax = invoiceNumberList?.reduce(
                                (acc: Decimal, cur: any) => {
                                  const listTaxTotal = cur?.list?.reduce((a: Decimal, c: any) => {
                                    return a.plus(new Decimal(c?.tax || 0));
                                  }, new Decimal(0));
                                  return acc.plus(listTaxTotal || 0);
                                },
                                new Decimal(0),
                              );

                              formRef?.current?.setFieldsValue({
                                amountOfMoney: total.toNumber(),
                                taxAmount: totalTax.toNumber(),
                                totalAmount: total.plus(totalTax).toNumber(),
                              });
                            }
                          },
                        }}
                      />
                    </RKCol>
                  </Row>
                </ProFormGroup>
              );
            }}
          </ProFormList>
        </ProFormList>
      </ProForm>
      <DrawerForm
        width={460}
        title="领票"
        open={drawerVisit}
        onOpenChange={setDrawerVisit}
        onFinish={async (values) => {
          const { contractNumber } = values;
          const contract = contractLists?.find((item) => item.contractNumber === contractNumber);
          const form = {
            id,
            contractId: contract?.id,
            contractType: contract?.contractType,
          };
          claim(form);
        }}
        autoFocusFirstInput
        drawerProps={{
          destroyOnClose: true,
        }}
        initialValues={{
          collectionType: 'CONTRACT',
        }}
        submitter={{
          searchConfig: {
            submitText: '领票',
          },
          submitButtonProps: {
            loading: claimLoading,
          },
        }}
      >
        <div className="rk-none">
          <ProFormText name="marginId" />
        </div>
        <ProFormSelect
          name="contractNumber"
          label="待领票列表"
          fieldProps={{
            showSearch: true,
            optionLabelProp: 'contractNumber',
          }}
          options={contractLists?.map((item: Record<string, any>) => {
            return {
              value: item.contractNumber,
              label: <RKSelectLabel title={item.contractNumber} info={item.contractName} />,
              contractNumber: item.contractNumber,
            };
          })}
          rules={[requiredRule]}
        />
      </DrawerForm>
    </PageContainer>
  );
};

export default withRouteEditing(CollectDetails);
