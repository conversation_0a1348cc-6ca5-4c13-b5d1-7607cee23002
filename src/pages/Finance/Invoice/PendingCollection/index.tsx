import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef } from 'react';

import { missingTrackContract } from '@/services/oa/collectTicket';
import { defaultTableConfig } from '@/utils/setting';
import { history } from '@umijs/max';

const Invoicing: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  // 表格
  const columns: ProColumns<API.PendingReceiptContractResp>[] = [
    {
      title: '合同号',
      dataIndex: 'contractNumber',
      width: 200,
      copyable: true,
      fixed: 'left',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => {
              if (entity.contractType === 'PC') {
                history.push(`/contract/purchase/edit/${entity.contractId}`);
              } else {
                history.push(`/contract/internal/edit/${entity.contractId}`);
              }
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
      width: 300,
    },
    {
      title: '单位名称',
      dataIndex: 'institutionName',
      width: 280,
      hideInSearch: true,
    },
    {
      title: '销售',
      dataIndex: 'salesman',
      width: 120,
    },
    {
      title: '总金额',
      dataIndex: 'totalAmount',
      width: 120,
      valueType: 'money',
      fieldProps: {
        min: 0,
      },
      hideInSearch: true,
    },
    {
      title: '已付款金额',
      dataIndex: 'payedAmount',
      width: 120,
      valueType: 'money',
      fieldProps: {
        min: 0,
      },
      hideInSearch: true,
    },
    {
      title: '已收票金额',
      dataIndex: 'receivedAmount',
      width: 120,
      valueType: 'money',
      fieldProps: {
        min: 0,
      },
      hideInSearch: true,
    },
    {
      title: '待收票金额',
      dataIndex: 'awaitReceiptAmount',
      width: 120,
      valueType: 'money',
      fieldProps: {
        min: 0,
      },
      hideInSearch: true,
    },
    {
      title: '总税额',
      dataIndex: 'totalTax',
      width: 120,
      valueType: 'money',
      fieldProps: {
        min: 0,
      },
      hideInSearch: true,
    },
    {
      title: '当前税额',
      dataIndex: 'currentTax',
      width: 120,
      valueType: 'money',
      fieldProps: {
        min: 0,
      },
      hideInSearch: true,
    },
    {
      title: '更新日期',
      dataIndex: 'updateDate',
      width: 120,
      hideInSearch: true,
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.PendingReceiptContractResp>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="合同待收票记录表"
        request={async (params) => {
          const { contractNumber, contractName, salesman, current, pageSize } = params;
          const search = {
            contractNumber,
            contractName,
            salesman,
          };
          const res = await missingTrackContract({
            pageNum: current,
            pageSize,
            search,
          });
          return {
            data: res.data?.records || [],
            total: res.data?.total || 0,
            success: res.code === 200,
          };
        }}
      />
    </PageContainer>
  );
};

export default Invoicing;
