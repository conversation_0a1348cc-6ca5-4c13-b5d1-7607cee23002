import RKCol from '@/components/RKCol';
import TitleLink from '@/components/TitleLink';
import { BILL_TYPE, INVOICE_TYPE, TICKET_STATUS } from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useUserList } from '@/hooks/useUserList';
import { selectInvoicedById } from '@/services/oa/conInvoiced';
import { applicationTicket } from '@/services/oa/contract';
import { getInvoicingBankOption } from '@/services/oa/dictionary';
import { onSuccessAndGoBack } from '@/utils';
import { requiredRule } from '@/utils/setting';
import type { FormListActionType } from '@ant-design/pro-components';
import {
  FooterToolbar,
  PageContainer,
  ProCard,
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormGroup,
  ProFormInstance,
  ProFormList,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useAccess, useLocation, useRequest } from '@umijs/max';
import { Button, Row, Space, Typography } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import Decimal from 'decimal.js';
import { useRef } from 'react';

const ApplyInvoice: React.FC<WithRouteEditingProps> = ({ id, isEditPage }) => {
  const { state } = useLocation() as { state: { isAll: string | undefined } };

  const formRef = useRef<ProFormInstance>();
  const invoiceListAction = useRef<FormListActionType<{ invoiceNo: string; list: [] }>>();
  const {
    canAddInvoiceTransactionRecord,
    canEditInvoiceTransactionRecord,
    canFlowStateInvoiceTransactionRecord,
  } = useAccess();
  const canEdit =
    (isEditPage && (canEditInvoiceTransactionRecord || canFlowStateInvoiceTransactionRecord)) ||
    (!isEditPage && canAddInvoiceTransactionRecord);

  // 申请开票
  const { run: apply, loading: applyLoading } = useRequest((value) => applicationTicket(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });

  //开票银行
  const { data: InvoiceBankList = [], loading: InvoiceBankLoading } = useRequest(() =>
    getInvoicingBankOption(),
  );

  //开票人列表
  const { userList, loading: userLoading } = useUserList();

  // 获取详情
  const { data = {}, loading } = useRequest(
    () =>
      selectInvoicedById({
        idReq: { id },
      }),
    {
      ready: isEditPage,
      onSuccess: (res) => {
        setTimeout(() => {
          formRef.current?.setFieldsValue(
            {
              ...res,
              applicationTime: res?.applicationTime || new Date(),
              ticketType: res?.ticketType || 'ST',
              status: res?.status || 'NOT_TICKET',
              operateStatus: 'SUBMIT',
              ticketAmount: state?.isAll === 'true' ? res?.ticketAmountAll : res?.ticketAmount,
              ticketAndRate: state?.isAll === 'true' ? res?.ticketAndRateAll : res?.ticketAndRate,
              rateAmount: state?.isAll === 'true' ? res?.rateAmountAll : res?.rateAmount,
              rate: res?.rate,
              invoiceNumberList: res?.invoiceNumberList?.length
                ? res?.invoiceNumberList
                : [
                    {
                      invoiceNo: '',
                      list: [
                        {
                          content: '',
                          model: '',
                          unit: '',
                          amount: 0,
                          money: 0,
                          taxRate: 0,
                          tax: 0,
                        },
                      ],
                    },
                  ],
            } || {},
          );
        }, 500);
      },
    },
  );
  // 申请已经提交
  const canOperationStatus = canFlowStateInvoiceTransactionRecord && !!data?.invoicingNumber;
  // 已开票
  const alreadyHaveTicket = data?.status === 'TICKET';

  const formDisabled = data?.operateStatus === 'SUBMIT' || !canEdit;

  return (
    <PageContainer header={{ title: false }} className="detail-container" loading={loading}>
      <ProForm<API.InvoicingReq>
        formRef={formRef}
        // 如果已经提交了申请，不可再更改
        disabled={formDisabled}
        submitter={
          ((data?.status !== 'TICKET' && canEdit) ||
            (data?.operateStatus !== 'SUBMIT' && canEdit)) && {
            searchConfig: {
              submitText: '保存',
              resetText: '取消',
            },
            onReset: () => {
              history.go(-1);
            },
            render: (props, doms) => {
              return <FooterToolbar>{doms}</FooterToolbar>;
            },
            submitButtonProps: {
              loading: applyLoading,
              disabled: false,
            },
            resetButtonProps: {
              disabled: false,
            },
          }
        }
        onFinish={async (values) => {
          apply(values);
        }}
      >
        <div className="rk-none">
          {/* 关联收款计划 */}
          <ProFormText name="id" />
          <ProFormText name="relevancyCollPlanId" />
          <ProFormText name="operateStatus" />
          <ProFormText name="contractId" />
          <ProFormText name="status" />
        </div>
        <Row gutter={24}>
          <ProFormDependency name={['invoicingNumber']}>
            {({ invoicingNumber }) => {
              if (!invoicingNumber) return null;
              return (
                <RKCol>
                  <ProFormText name="invoicingNumber" label="开票记录号" disabled />
                </RKCol>
              );
            }}
          </ProFormDependency>
          <ProFormDependency name={['contractId']}>
            {({ contractId }) => {
              return (
                <RKCol>
                  <ProFormText
                    name="contractNumber"
                    label={
                      <TitleLink path={contractId && `/contract/main/edit/${contractId}`}>
                        合同号
                      </TitleLink>
                    }
                    disabled
                  />
                </RKCol>
              );
            }}
          </ProFormDependency>
          <RKCol>
            <ProFormText name="salePerson" label="销售" disabled />
          </RKCol>
          <RKCol>
            <ProFormDatePicker name="applicationTime" label="开票申请日期" disabled />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="ticketType"
              label="开票类型"
              options={INVOICE_TYPE}
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormText name="institutionName" label="单位名称" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormText name="taxpayerNum" label="纳税人识别号" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormText name="bank" label="开户银行" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormText name="account" label="银行账号" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormText name="contactNumber" label="联系电话" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormText name="registeredAddress" label="注册地址" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormMoney
              name="ticketAndRate"
              label="合计金额"
              rules={[requiredRule]}
              fieldProps={{
                min: 0,
              }}
              disabled
            />
          </RKCol>
          <RKCol>
            <ProFormDigit
              name="rateAmount"
              label="合计税额"
              rules={[requiredRule]}
              disabled
              fieldProps={{
                precision: 2,
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormMoney
              name="ticketAmount"
              label="价税合计"
              rules={[requiredRule]}
              fieldProps={{
                min: 0,
              }}
              disabled
            />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              disabled={alreadyHaveTicket}
              name="remark"
              label="备注"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 3,
                },
              }}
            />
          </RKCol>
          {(canOperationStatus || alreadyHaveTicket) && (
            <>
              <RKCol>
                <ProFormSelect
                  name="billType"
                  label="票据类型"
                  options={BILL_TYPE}
                  rules={[requiredRule]}
                  disabled={alreadyHaveTicket || !canOperationStatus}
                />
              </RKCol>
              <RKCol>
                <ProFormDatePicker
                  name="ticketTime"
                  label="开票日期"
                  disabled={alreadyHaveTicket || !canOperationStatus}
                  rules={[requiredRule]}
                />
              </RKCol>
              <RKCol>
                <ProFormSelect
                  name="status"
                  label="开票状态"
                  options={TICKET_STATUS}
                  fieldProps={{
                    allowClear: false,
                  }}
                  disabled={alreadyHaveTicket || !canOperationStatus}
                  rules={[requiredRule]}
                />
              </RKCol>
              <RKCol>
                <ProFormSelect
                  name="drawerId"
                  label="开票人"
                  rules={[requiredRule]}
                  fieldProps={{
                    loading: userLoading,
                    fieldNames: {
                      value: 'id',
                      label: 'username',
                    },
                    showSearch: true,
                  }}
                  disabled={alreadyHaveTicket || !canOperationStatus}
                  transform={(value, namePath) => {
                    return {
                      [namePath]: value,
                      drawerName: userList?.find((item) => item.id === value)?.username,
                    };
                  }}
                  options={userList as DefaultOptionType[]}
                />
              </RKCol>
            </>
          )}
        </Row>
        {(canOperationStatus || alreadyHaveTicket) && (
          <ProFormDependency name={['ticketAndRate', 'invoiceNumberList']}>
            {({ ticketAndRate, invoiceNumberList }) => {
              const decimalTicketAndRate = new Decimal(ticketAndRate || 0);
              //计算invoiceNumberList内的金额的总和
              const decimalTotal = new Decimal(
                invoiceNumberList?.reduce((acc: number, cur: any) => {
                  return new Decimal(acc || 0)
                    .plus(
                      cur?.list?.reduce(
                        (a: number, c: any) =>
                          new Decimal(a || 0).plus(new Decimal(c?.money || 0)).toNumber(),
                        0,
                      ) || 0,
                    )
                    .toNumber();
                }, 0) || 0,
              );
              const total = decimalTotal.toNumber();
              const isTotalLessThanTicketAndRate = total < ticketAndRate;

              return (
                <>
                  <Typography.Title level={5} style={{ marginBlock: 16 }}>
                    <Space>
                      <div>发票信息</div>
                      {isTotalLessThanTicketAndRate && (
                        <Button
                          type="primary"
                          size="small"
                          disabled={alreadyHaveTicket || !canOperationStatus}
                          onClick={() => {
                            invoiceListAction?.current?.add();
                          }}
                        >
                          添加发票
                        </Button>
                      )}
                    </Space>
                  </Typography.Title>
                  <ProFormList
                    name="invoiceNumberList"
                    label=""
                    required
                    min={1}
                    actionRef={invoiceListAction}
                    creatorButtonProps={false}
                    copyIconProps={
                      formDisabled && (alreadyHaveTicket || !canOperationStatus)
                        ? false
                        : {
                            tooltipText: '复制此项到末尾',
                          }
                    }
                    deleteIconProps={
                      formDisabled && (alreadyHaveTicket || !canOperationStatus)
                        ? false
                        : {
                            tooltipText: '不需要这行了',
                          }
                    }
                    itemRender={({ listDom, action }, { record }) => {
                      return (
                        <ProCard
                          bordered
                          extra={action}
                          title={record?.name}
                          style={{
                            marginBlockEnd: 8,
                          }}
                        >
                          {listDom}
                        </ProCard>
                      );
                    }}
                  >
                    <ProFormGroup>
                      <ProFormText
                        disabled={alreadyHaveTicket || !canOperationStatus}
                        name="invoiceNo"
                        label="发票号"
                        rules={[requiredRule]}
                      />
                    </ProFormGroup>
                    <ProFormList
                      name="list"
                      label=""
                      min={1}
                      creatorButtonProps={
                        isTotalLessThanTicketAndRate
                          ? {
                              creatorButtonText: '添加明细',
                              disabled: alreadyHaveTicket || !canOperationStatus,
                            }
                          : false
                      }
                      copyIconProps={
                        formDisabled && (alreadyHaveTicket || !canOperationStatus)
                          ? false
                          : {
                              tooltipText: '复制此项到末尾',
                            }
                      }
                      deleteIconProps={
                        formDisabled && (alreadyHaveTicket || !canOperationStatus)
                          ? false
                          : {
                              tooltipText: '不需要这行了',
                            }
                      }
                    >
                      {(f, index, action) => {
                        const { money } = action.getCurrentRowData();
                        const leftMoney = decimalTicketAndRate
                          .plus(new Decimal(money || 0))
                          .minus(decimalTotal)
                          .toNumber();
                        return (
                          <ProFormGroup key="group">
                            <Row gutter={24}>
                              <RKCol lg={5} md={5} sm={5}>
                                <ProFormTextArea
                                  name="content"
                                  label="开票内容"
                                  disabled={alreadyHaveTicket || !canOperationStatus}
                                  fieldProps={{
                                    autoSize: {
                                      minRows: 1,
                                      maxRows: 3,
                                    },
                                  }}
                                  rules={[requiredRule]}
                                />
                              </RKCol>
                              <RKCol lg={3} md={3} sm={3}>
                                <ProFormText
                                  disabled={alreadyHaveTicket || !canOperationStatus}
                                  name="model"
                                  label="规格型号"
                                  rules={[requiredRule]}
                                />
                              </RKCol>
                              <RKCol lg={3} md={3} sm={3}>
                                <ProFormText
                                  disabled={alreadyHaveTicket || !canOperationStatus}
                                  name="unit"
                                  label="单位"
                                  rules={[requiredRule]}
                                />
                              </RKCol>
                              <RKCol lg={3} md={3} sm={3}>
                                <ProFormDigit
                                  disabled={alreadyHaveTicket || !canOperationStatus}
                                  name="amount"
                                  label="数量"
                                  rules={[requiredRule]}
                                />
                              </RKCol>
                              <RKCol lg={3} md={3} sm={3}>
                                <ProFormMoney
                                  name="money"
                                  label="金额"
                                  initialValue={0}
                                  rules={[requiredRule]}
                                  max={leftMoney}
                                  disabled={alreadyHaveTicket || !canOperationStatus}
                                  fieldProps={{
                                    min: 0,
                                    onChange: (e) => {
                                      if (e || e === 0) {
                                        const { taxRate } = action.getCurrentRowData();
                                        const taxInclusiveAmount = Number(
                                          (e * (taxRate / 100)).toFixed(2),
                                        );
                                        action.setCurrentRowData({
                                          tax: taxInclusiveAmount || 0,
                                        });
                                      }
                                    },
                                  }}
                                />
                              </RKCol>
                              <RKCol lg={3} md={3} sm={3}>
                                <ProFormDigit
                                  name="taxRate"
                                  label="税率"
                                  initialValue={0}
                                  rules={[requiredRule]}
                                  addonAfter="%"
                                  disabled={alreadyHaveTicket || !canOperationStatus}
                                  fieldProps={{
                                    min: 0,
                                    onChange: (e) => {
                                      if (e || e === 0) {
                                        const { money } = action.getCurrentRowData();
                                        const taxInclusiveAmount = Number(
                                          (money * (e / 100)).toFixed(2),
                                        );
                                        action.setCurrentRowData({
                                          tax: Number(taxInclusiveAmount || 0),
                                        });
                                      }
                                    },
                                  }}
                                />
                              </RKCol>
                              <RKCol lg={3} md={3} sm={3}>
                                <ProFormDigit
                                  name="tax"
                                  label="税额"
                                  initialValue={0}
                                  rules={[requiredRule]}
                                  fieldProps={{
                                    precision: 2,
                                  }}
                                  disabled={alreadyHaveTicket || !canOperationStatus}
                                />
                              </RKCol>
                            </Row>
                          </ProFormGroup>
                        );
                      }}
                    </ProFormList>
                  </ProFormList>
                </>
              );
            }}
          </ProFormDependency>
        )}
        <Typography.Title level={5} style={{ marginBlock: 16 }}>
          销货单位开票信息
        </Typography.Title>
        <Row gutter={24}>
          <RKCol>
            <ProFormSelect
              name="collectionBank"
              label="开票银行别名"
              rules={[requiredRule]}
              fieldProps={{
                fieldNames: {
                  value: 'bankAs',
                  label: 'bankAs',
                },
                showSearch: true,
                loading: InvoiceBankLoading,
                optionLabelProp: 'bankAs',
                onChange: (val, option) => {
                  const {
                    institutionName = '',
                    taxpayerNum = '',
                    bank = '',
                    account = '',
                    contactNumber = '',
                    registeredAddress = '',
                  } = (option as API.BanksDto) || {};
                  formRef.current?.setFieldsValue({
                    salesFirm: institutionName,
                    salesTaxpayerNum: taxpayerNum,
                    salesAccount: `${bank}${account}`,
                    salesContact: `${registeredAddress}${contactNumber}`,
                  });
                },
              }}
              options={InvoiceBankList as DefaultOptionType[]}
              disabled
            />
          </RKCol>
          <RKCol>
            <ProFormText name="salesFirm" label="名称" rules={[requiredRule]} disabled />
          </RKCol>
          <RKCol>
            <ProFormText
              name="salesTaxpayerNum"
              label="纳税人识别号"
              rules={[requiredRule]}
              disabled
            />
          </RKCol>
          <RKCol lg={12} md={16} sm={24}>
            <ProFormTextArea
              name="salesContact"
              label="地址、电话"
              rules={[requiredRule]}
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 2,
                },
              }}
              disabled
            />
          </RKCol>
          <RKCol lg={12} md={16} sm={24}>
            <ProFormTextArea
              name="salesAccount"
              label="开户行及账号"
              rules={[requiredRule]}
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 2,
                },
              }}
              disabled
            />
          </RKCol>
        </Row>
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(ApplyInvoice);
