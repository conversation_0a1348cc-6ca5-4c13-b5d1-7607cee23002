import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef } from 'react';

import { TICKET_STATUS } from '@/enums';
import { sortMap } from '@/pages/ReportAnalysis/WorkloadStatistics';
import { abandonedInvoicing, cancelInvoicing, pageInvoiced } from '@/services/oa/conInvoiced';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { message, Modal } from 'antd';

const Invoicing: React.FC = () => {
  const { canRevokeInvoiceTransactionRecord = false, canCancelInvoiceTransactionRecord = false } =
    useAccess();
  const tableRef = useRef<ActionType | undefined>();
  // 撤销开票
  const { run: cancel } = useRequest((id) => cancelInvoicing({ idReq: { id } }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('操作成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  // 作废开票
  const { run: abandoned } = useRequest((id) => abandonedInvoicing({ idReq: { id } }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('操作成功');
      tableRef.current?.reload?.();
    },
    formatResult: (res) => res,
  });

  const handleCancel = async (row: API.InvoicingResp) => {
    Modal.confirm({
      title: '确认撤销',
      content: `您确定要撤销开票申请“${row.invoicingNumber}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        cancel(row.id);
      },
    });
  };

  const handleAbandoned = async (row: API.InvoicingResp) => {
    Modal.confirm({
      title: '确认作废',
      content: `您确定要作废开票记录"${row.invoicingNumber}"吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        abandoned(row.id);
      },
    });
  };
  // 表格
  const columns: ProColumns<API.InvoicingResp>[] = [
    {
      title: '开票记录号',
      dataIndex: 'invoicingNumber',
      width: 200,
      copyable: true,
      fixed: 'left',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/finance/invoice/invoicing-list/${entity.id}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '开票状态',
      dataIndex: 'status',
      valueEnum: option2enum(TICKET_STATUS),
      width: 100,
      hideInSearch: true,
    },
    {
      title: '销售',
      dataIndex: 'salePerson',
      width: 120,
    },
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      width: 120,
      hideInTable: true,
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
      width: 120,
      hideInTable: true,
    },
    {
      title: '开票申请日期',
      dataIndex: 'applicationTime',
      width: 120,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '开票日期',
      dataIndex: 'ticketTime',
      width: 120,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '单位名称',
      dataIndex: 'institutionName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '开票金额',
      dataIndex: 'ticketAmount',
      width: 120,
      valueType: 'money',
      fieldProps: {
        min: 0,
      },
      sorter: true,
    },
    {
      title: '操作',
      fixed: 'right',
      width: 100,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !canRevokeInvoiceTransactionRecord && !canCancelInvoiceTransactionRecord,
      render: (text, record) => {
        const { status, operateStatus } = record;
        return (
          <>
            <Access accessible={canRevokeInvoiceTransactionRecord}>
              {status !== 'TICKET' && <a onClick={() => handleCancel(record)}>撤销</a>}
            </Access>
            <Access accessible={canCancelInvoiceTransactionRecord}>
              {status === 'TICKET' && operateStatus !== 'CANCEL' && (
                <a onClick={() => handleAbandoned(record)}>作废</a>
              )}
            </Access>
          </>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.InvoicingResp>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="开票记录表"
        request={async (params, sort) => {
          const {
            contractNumber,
            contractName,
            invoicingNumber,
            salePerson,
            institutionName,
            ticketAmount,
            current,
            pageSize,
          } = params;
          const sortTicketAmount =
            sort && sort?.ticketAmount ? sortMap[sort.ticketAmount] : undefined;
          const sortApplicationTime =
            sort && sort?.applicationTime ? sortMap[sort.applicationTime] : undefined;
          const sortTicketTime = sort && sort?.ticketTime ? sortMap[sort.ticketTime] : undefined;
          const search = {
            invoicingNumber,
            salePerson,
            institutionName,
            ticketAmount,
            contractNumber,
            contractName,
          };
          const res = await pageInvoiced({
            pageNum: current,
            pageSize,
            search,
            sortApplicationTime,
            sortTicketAmount,
            sortTicketTime,
          });
          return {
            data: res.data?.records || [],
            total: res.data?.total || 0,
            success: res.code === 200,
          };
        }}
      />
    </PageContainer>
  );
};

export default Invoicing;
