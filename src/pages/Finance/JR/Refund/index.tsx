import { PAYMENT_TYPE, WRITE_OFF_STATUS } from '@/enums';
import { useDepartment } from '@/hooks/useDepartment';
import { sortMap } from '@/pages/ReportAnalysis/WorkloadStatistics';

import { logoffList } from '@/services/oa/paymentApplication';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProTable,
  StatisticCard,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Space } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import React, { useRef, useState } from 'react';
import CollectionDrawerForm from './components/CollectionDrawerForm';
import ConversionDrawerForm from './components/ConversionDrawerForm';

const Refund: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [initialValues, setInitialValues] = useState<any | undefined>();
  const [drawerVisit, setDrawerVisit] = useState(false);
  const [conversionDrawerVisit, setConversionDrawerVisit] = useState(false);
  const { departmentList, loading: departmentLoading } = useDepartment();
  const [statisticData, setStatisticData] = useState<Record<string, any>>();

  const onWriteOff = (record: any) => {
    setDrawerVisit(true);
    setInitialValues(record);
  };

  const onConversion = (record: any) => {
    setConversionDrawerVisit(true);
    setInitialValues(record);
  };

  const columns: ProColumns[] = [
    {
      title: '付款记录号',
      dataIndex: 'payNumber',
      copyable: true,
      width: 200,
      fixed: 'left',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/finance/JR/payment-list/${entity.id}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '付款日期',
      dataIndex: 'payTime',
      hideInSearch: true,
      width: 120,
      sorter: true,
    },
    {
      title: '付款金额',
      dataIndex: 'payMoney',
      valueType: 'money',
      width: 150,
      sorter: true,
    },
    {
      title: '单位名称(账户名)',
      dataIndex: 'institutionName',
      width: 300,
      hideInSearch: true,
    },
    {
      title: '单位名称',
      dataIndex: 'institutionName',
      hideInTable: true,
    },
    {
      title: '开户银行',
      dataIndex: 'bank',
      hideInSearch: true,
      width: 300,
    },
    {
      title: '银行账号',
      dataIndex: 'bankAcNo',
      hideInSearch: true,
      width: 200,
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      hideInSearch: true,
      width: 300,
    },
    {
      title: '用途',
      dataIndex: 'useWay',
      hideInSearch: true,
      ellipsis: true,
      width: 150,
    },
    {
      title: '申请人',
      dataIndex: 'applicationUser',
      width: 120,
    },
    {
      title: '部门',
      dataIndex: 'department',
      valueType: 'select',
      hideInSearch: true,

      fieldProps: {
        options: departmentList as DefaultOptionType[],
        loading: departmentLoading,
        fieldNames: {
          label: 'departmentName',
          value: 'id',
        },
      },
      width: 120,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      hideInSearch: true,
      ellipsis: true,
      width: 150,
    },
    {
      title: '资金类别',
      dataIndex: 'fundingCategory',
      valueEnum: option2enum(PAYMENT_TYPE),
      width: 100,
      hideInSearch: true,
    },
    {
      title: '计划退款日期',
      dataIndex: 'expectedReturnTime',
      valueType: 'date',
      width: 120,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '退款日期',
      dataIndex: 'writeOffsTime',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
      sorter: true,
    },
    {
      title: '保证金申请编号',
      dataIndex: 'bondNumber',
      width: 150,
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/finance/payment/general/details/${entity.bondId}`)}
          >
            {dom}
          </a>
        );
      },
      hideInSearch: true,
    },
    {
      title: '销账标志',
      dataIndex: 'refundTag',
      valueType: 'select',
      valueEnum: option2enum(WRITE_OFF_STATUS),
      width: 120,
    },
    {
      title: '操作',
      width: 120,
      fixed: 'right',
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            {record?.refundTag === 'NOT_LOGOFF' && (
              <>
                <a key="write-off" onClick={() => onWriteOff(record)}>
                  销账
                </a>
                <a key="conversion" onClick={() => onConversion(record)}>
                  转换
                </a>
              </>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <StatisticCard.Group style={{ marginBottom: 16 }}>
        <StatisticCard
          statistic={{
            title: '待销账',
            value: statisticData?.awaitRefund,
            prefix: '￥',
            status: 'processing',
          }}
        />
      </StatisticCard.Group>
      <ProTable
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        columns={columns}
        headerTitle="预退款流水表"
        request={async (params, sort) => {
          const { payNumber, payMoney, institutionName, refundTag, applicationUser } = params;
          const sortPayMoney = sort && sort?.payMoney ? sortMap[sort.payMoney] : undefined;
          const sortPayTime = sort && sort?.payTime ? sortMap[sort.payTime] : undefined;
          const sortExpectedReturnTime =
            sort && sort?.expectedReturnTime ? sortMap[sort.expectedReturnTime] : undefined;
          const sortWriteOffsTime =
            sort && sort?.writeOffsTime ? sortMap[sort.writeOffsTime] : undefined;
          const res = await logoffList({
            applicationUser,
            payNumber,
            payMoney,
            institutionName,
            refundTag,
            sortPayMoney,
            sortPayTime,
            sortExpectedReturnTime,
            sortWriteOffsTime,
          });

          const { paymentAppList, logoffAmountCount } = res.data || {};
          setStatisticData({
            awaitRefund: logoffAmountCount,
          });
          return { data: paymentAppList || [], success: true };
        }}
      />
      <CollectionDrawerForm
        initialValues={initialValues}
        open={drawerVisit}
        onOpenChange={(visible) => {
          setDrawerVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
      <ConversionDrawerForm
        initialValues={initialValues}
        open={conversionDrawerVisit}
        onOpenChange={(visible) => {
          setConversionDrawerVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default Refund;
