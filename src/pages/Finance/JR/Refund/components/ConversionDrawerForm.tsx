import { PAYMENT_TYPE } from '@/enums';
import { payTypeConverted } from '@/services/oa/paymentApplication';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  ProFormInstance,
  ProFormMoney,
  ProFormSelect,
} from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useRef } from 'react';

interface ConversionDrawerFormProps {
  open: boolean;
  onOpenChange: (visible: boolean) => void;
  onFinish: () => Promise<void>;
  initialValues?: Record<string, any>;
}

const ConversionDrawerForm: React.FC<ConversionDrawerFormProps> = ({
  open,
  onOpenChange,
  onFinish,
  initialValues,
}) => {
  const formRef = useRef<ProFormInstance>();

  return (
    <DrawerForm
      formRef={formRef}
      open={open}
      onOpenChange={(visible) => {
        if (!visible) {
          formRef.current?.resetFields();
        }
        onOpenChange(visible);
      }}
      title="转换付款类型"
      width="30%"
      onFinish={async (values) => {
        const res = await payTypeConverted({
          id: initialValues?.id,
          paymentType: values.paymentType,
          paymentAmount: values.paymentAmount,
        });
        if (res.code !== 200) return false;
        message.success('操作成功');
        onFinish();
        formRef.current?.resetFields();
        return true;
      }}
    >
      <div style={{ marginBottom: 24 }}>
        当前付款类型：
        {PAYMENT_TYPE.find((item) => item.value === initialValues?.fundingCategory)?.label ||
          initialValues?.fundingCategory}
      </div>
      <div style={{ marginBottom: 24 }}>当前付款金额：￥{initialValues?.payMoney}</div>
      <ProFormSelect
        name="paymentType"
        label="转换付款类型"
        options={PAYMENT_TYPE.filter((item) => item.value !== 'GOODS')}
        rules={[requiredRule]}
      />
      <ProFormMoney
        name="paymentAmount"
        label="转换金额"
        rules={[
          requiredRule,
          {
            validator: async (_, value) => {
              if (value > initialValues?.payMoney) {
                throw new Error('转换金额不能大于当前付款金额');
              }
            },
          },
        ]}
      />
    </DrawerForm>
  );
};

export default ConversionDrawerForm;
