import RKCol from '@/components/RKCol';
import { PAYMENT, PAYMENT_STATUS, PAYMENT_TYPE } from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { selectPaymentById, updatePayment } from '@/services/oa/conInvoiced';

import { onSuccessAndRefresh } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormInstance,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { history, useAccess, useLocation, useRequest } from '@umijs/max';
import { Collapse, Row } from 'antd';
import { useRef } from 'react';

const DIS_PAYMENT_STATUS = PAYMENT_STATUS.map((item) => {
  return {
    ...item,
    disabled: item.value === 'CANCELED',
  };
});

const PaymentRecord: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  const formRef = useRef<ProFormInstance>();
  const { pathname } = useLocation();
  // 判断是否为待付款详情页
  const { canEditOutstandingPaymentTransactionRecord } = useAccess();
  const isOutstandingPage = pathname.includes('finance/JR/outstanding-payment');

  // 获取详情
  const { data, loading, refresh } = useRequest(
    () =>
      selectPaymentById({
        idReq: { id },
      }),
    {
      ready: isEditPage,
      refreshDeps: [id], // 添加 id 作为依赖项，当 id 变化时会重新请求
      onSuccess: (res) => {
        setTimeout(() => {
          formRef.current?.setFieldsValue(res || {});
        }, 500);
      },
    },
  );

  // 是待付款详情页有权限且未付款才可编辑
  const canEdit =
    isOutstandingPage &&
    canEditOutstandingPaymentTransactionRecord &&
    !['PAY', 'CANCELED'].includes(data?.payStatus as string);

  const { run: update, loading: updateLoading } = useRequest((value) => updatePayment(value), {
    manual: true,
    onSuccess: (res) => onSuccessAndRefresh(res, refresh),
    formatResult: (res) => res,
  });

  return (
    <PageContainer header={{ title: false }} className="detail-container" loading={loading}>
      <ProForm<API.PaymentResp>
        formRef={formRef}
        disabled
        submitter={
          canEdit
            ? {
                searchConfig: {
                  submitText: '保存',
                  resetText: '取消',
                },
                onReset: () => {
                  history.go(-1);
                },

                render: (props, doms) => {
                  return <FooterToolbar>{doms}</FooterToolbar>;
                },
                submitButtonProps: {
                  loading: updateLoading,
                  disabled: false,
                },
                resetButtonProps: {
                  disabled: false,
                },
              }
            : false
        }
        onFinish={async (values) => {
          update(values);
        }}
      >
        <div className="rk-none">
          <ProFormText name="id" />
          <ProFormText name="contractId" />
          <ProFormText name="operateStatus" />
          <ProFormText name="payPlanId" />
          <ProFormText name="paymentAppId" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText name="payNumber" label="付款记录号" />
          </RKCol>
          <ProFormDependency name={['contractNumber']}>
            {({ contractNumber }) => {
              if (!contractNumber) return '';
              return (
                <RKCol>
                  <ProFormText name="contractNumber" label="合同编号" disabled />
                </RKCol>
              );
            }}
          </ProFormDependency>

          <RKCol>
            <ProFormDatePicker name="estimatePayTime" label="计划付款日期" disabled={!canEdit} />
          </RKCol>
          <RKCol>
            <ProFormText name="institutionName" label="单位名称(账户名)" />
          </RKCol>
          <RKCol>
            <ProFormText name="bank" label="开户银行" />
          </RKCol>
          <RKCol>
            <ProFormText name="account" label="银行账号" />
          </RKCol>
          <RKCol>
            <ProFormSelect name="payWay" label="付款方式" options={PAYMENT} />
          </RKCol>
          <ProFormDependency name={['paymentType']}>
            {({ paymentType }) => {
              return (
                <RKCol>
                  <ProFormSelect
                    name="paymentType"
                    label="付款类型"
                    options={PAYMENT_TYPE.map((item) => ({
                      ...item,
                      disabled: item.value === 'GOODS',
                    }))}
                    disabled={!canEdit || paymentType === 'GOODS'}
                  />
                </RKCol>
              );
            }}
          </ProFormDependency>
          <RKCol>
            <ProFormMoney name="payAmount" label="付款金额" />
          </RKCol>
          <RKCol>
            <ProFormText name="remark" label="备注" />
          </RKCol>
          <RKCol>
            <ProFormText name="useWay" label="用途" />
          </RKCol>

          <RKCol>
            <ProFormText name="applicationUser" label="申请人" />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="payStatus"
              label="付款状态"
              options={DIS_PAYMENT_STATUS}
              fieldProps={{
                allowClear: false,
              }}
              disabled={!canEdit}
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker
              dependencies={['payStatus']}
              name="payTime"
              label="付款日期"
              disabled={!canEdit}
              // rules={canEdit ? [requiredRule] : []}
              rules={[
                // 根据 payStatus 动态决定是否必填
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (getFieldValue('payStatus') === 'PAY' && !value) {
                      return Promise.reject(new Error('此项为必填项！'));
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            />
          </RKCol>
        </Row>

        {data?.paymentConvertList && data?.paymentConvertList?.length ? (
          <Collapse defaultActiveKey={['1']} ghost>
            <Collapse.Panel key="1" header="转换记录" collapsible="header">
              <ProTable<API.PaymentConvertResp>
                {...defaultTableConfig}
                scroll={{ x: '100%' }}
                columns={[
                  {
                    title: '付款流水号',
                    dataIndex: 'payNumber',
                    width: 180,
                    render(dom, entity) {
                      return (
                        <a onClick={() => history.push(`/finance/JR/payment-list/${entity.id}`)}>
                          {dom}
                        </a>
                      );
                    },
                  },
                  {
                    title: '实际付款金额',
                    valueType: 'money',
                    dataIndex: 'payAmount',
                    width: 180,
                  },
                  {
                    title: '转换说明',
                    dataIndex: 'remark',
                    width: 200,
                  },
                  {
                    title: '转换时间',
                    dataIndex: 'convertTime',
                    width: 130,
                  },
                  {
                    title: '操作人',
                    dataIndex: 'convertUser',
                    width: 100,
                  },
                ]}
                dataSource={data?.paymentConvertList}
                options={false}
              />
            </Collapse.Panel>
          </Collapse>
        ) : null}
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(PaymentRecord);
