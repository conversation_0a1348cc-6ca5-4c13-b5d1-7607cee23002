import {
  ActionType,
  PageContainer,
  ProColumns,
  ProTable,
  StatisticCard,
} from '@ant-design/pro-components';
import React, { createRef, useMemo, useRef, useState } from 'react';

import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { PAYMENT, PAYMENT_STATUS, PAYMENT_TYPE, WRITE_OFF_STATUS } from '@/enums';
import { sortMap } from '@/pages/ReportAnalysis/WorkloadStatistics';
import { cancelPayment, pagePayment, passPayApplication } from '@/services/oa/conInvoiced';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { Access, history, Link, useAccess, useRequest } from '@umijs/max';
import { Button, Form, FormInstance, Input, message, Modal, Space, Typography } from 'antd';

const { Text } = Typography;

const actions = [
  {
    key: 'allow',
    label: '允许付款',
  },
];

const Payment: React.FC = () => {
  const formRef = createRef<FormInstance>();
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.PaymentResp[]>([]);
  const { canAllowPaymentRecord, canCancelPaymentRecord = false } = useAccess();

  const [statisticData, setStatisticData] = useState<Record<string, any>>();
  // 允许付款
  const { run: allow, fetches } = useRequest((ids) => passPayApplication({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('操作成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
    fetchKey: (res) => res.id,
  });
  // 取消付款
  const { run: cancel, loading: cancelLoading } = useRequest((params) => cancelPayment(params), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success('操作成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const handleAllow = async (rows: API.PaymentResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.payNumber!);
    });
    Modal.confirm({
      title: '确认',
      content: `您确定要允许“${names.join('、')}”付款吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        allow(ids);
      },
    });
  };

  const handleCancel = (record: API.PaymentResp) => {
    Modal.confirm({
      title: '请输入取消原因',
      content: (
        <Form ref={formRef}>
          <Form.Item
            name="cancelDesc"
            label="原因"
            rules={[
              {
                required: true,
                message: '请输入取消原因！',
              },
            ]}
          >
            <Input.TextArea
              autoSize={{
                minRows: 1,
                maxRows: 3,
              }}
              placeholder="请输入取消原因"
            />
          </Form.Item>
        </Form>
      ),
      okText: '确认',
      cancelText: '取消',
      onOk: (close) => {
        formRef.current?.validateFields().then((values) => {
          const { cancelDesc } = values;
          cancel({ id: record.id, cancelDesc });
          close();
        });
      },
    });
  };

  // 计算付款总金额
  const totalAmount = useMemo(
    () => selectedRows.reduce((sum, order) => sum + (order?.payAmount || 0), 0),
    [selectedRows],
  );

  // 表格columns
  const columns: ProColumns<API.PaymentResp>[] = [
    {
      title: '付款编号',
      dataIndex: 'payNumber',
      width: 200,
      copyable: true,
      fixed: 'left',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/finance/JR/payment-list/${entity.id}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '付款状态',
      dataIndex: 'payStatus',
      width: 120,
      valueEnum: option2enum(PAYMENT_STATUS),
    },
    {
      title: '付款类型',
      dataIndex: 'paymentType',
      width: 120,
      valueEnum: option2enum(PAYMENT_TYPE),
    },
    {
      title: '是否允许付款',
      dataIndex: 'status',
      width: 120,
      hideInSearch: true,
      renderText(text) {
        return text === 'ALLOW' ? '是' : '否';
      },
    },
    {
      title: '付款方式',
      dataIndex: 'payWay',
      valueEnum: option2enum(PAYMENT),
      width: 100,
      hideInSearch: true,
    },
    {
      title: '计划付款日期',
      dataIndex: 'estimatePayTime',
      width: 120,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '付款金额',
      dataIndex: 'payAmount',
      width: 150,
      valueType: 'money',
      sorter: true,
    },
    {
      title: '单位名称',
      dataIndex: 'institutionName',
      hideInTable: true,
    },
    {
      title: '单位名称(账户名)',
      dataIndex: 'institutionName',
      width: 150,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '付款日期',
      dataIndex: 'payTime',
      width: 120,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '申请人',
      dataIndex: 'applicationUser',
      width: 120,
    },
    {
      title: '申请编号',
      dataIndex: 'paymentAppNumber',
      width: 180,
      render(dom, entity) {
        const { paymentType, paymentAppNumber, paymentAppId } = entity;
        const path =
          paymentType === 'GOODS'
            ? '/finance/payment/contract/'
            : '/finance/payment/general/details/';
        return paymentAppId && <Link to={`${path}${paymentAppId}`}>{paymentAppNumber}</Link>;
      },
    },
    {
      title: '销账标识',
      dataIndex: 'writeOffsTag',
      valueEnum: option2enum(WRITE_OFF_STATUS),
      width: 100,
      renderText(text, record) {
        const { paymentType = '' } = record;
        if (['GENERAL', 'GOODS'].includes(paymentType)) return '-';
        return text;
      },
    },
    {
      title: '取消原因',
      dataIndex: 'cancelDesc',
      width: 150,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '操作',
      fixed: 'right',
      width: 150,
      key: 'option',
      valueType: 'option',
      align: 'center',
      // 只有总经办才可看见
      hideInTable: !canAllowPaymentRecord && !canCancelPaymentRecord,
      render: (text, record) => {
        const { id, status, payStatus } = record;
        return (
          <Space>
            {status !== 'ALLOW' && payStatus !== 'CANCELED' && (
              <Button
                type="link"
                key="allow"
                className="inner-table-link"
                loading={fetches?.[id!]?.loading}
                onClick={() => handleAllow([record])}
              >
                允许付款
              </Button>
            )}
            <Access accessible={canCancelPaymentRecord}>
              {!['PAY', 'CANCELED'].includes(payStatus as string) && (
                <Button
                  type="link"
                  className="inner-table-link"
                  key="cancel"
                  loading={cancelLoading}
                  onClick={() => handleCancel(record)}
                >
                  取消付款
                </Button>
              )}
            </Access>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <StatisticCard.Group style={{ marginBottom: 16 }}>
        <StatisticCard
          statistic={{
            title: '待付款',
            value: statisticData?.awaitPayAmount,
            prefix: '￥',
            status: 'processing',
          }}
        />
      </StatisticCard.Group>
      <ProTable<API.PaymentResp>
        {...defaultTableConfig}
        rowSelection={
          canAllowPaymentRecord && {
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRows(selectedRows);
            },
            getCheckboxProps: (record) => ({
              disabled: record.status === 'ALLOW',
            }),
          }
        }
        search={{
          filterType: 'query',
          defaultCollapsed: false,
          labelWidth: 120,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="付款记录表"
        request={async (params, sort) => {
          const { payStatus, writeOffsTag, paymentType, current, pageSize, ...rest } = params;
          const sortPayAmount = sort && sort?.payAmount ? sortMap[sort.payAmount] : undefined;
          const sortPayTime = sort && sort?.payTime ? sortMap[sort.payTime] : undefined;
          const sortEstimatePayTime =
            sort && sort?.estimatePayTime ? sortMap[sort.estimatePayTime] : undefined;
          const filter = { payStatus, writeOffsTag, paymentType };
          const search = { ...rest };

          const res = await pagePayment({
            pageNum: current,
            pageSize,
            search,
            filter,
            sortPayAmount,
            sortPayTime,
            sortEstimatePayTime,
          });

          setStatisticData({
            awaitPayAmount: res.data?.awaitPayAmount,
            payedAmount: res.data?.payedAmount,
          });
          return {
            data: res.data?.records || [],
            success: true,
            total: res.data?.total,
          };
        }}
      />
      <OperateFooterToolbar
        selectedRows={selectedRows}
        actions={actions}
        extra={
          <Text type="secondary" strong>
            总金额 <a>￥{totalAmount}</a>
          </Text>
        }
        onOperation={() => {
          handleAllow(selectedRows);
        }}
      />
    </PageContainer>
  );
};

export default Payment;
