import {
  ActionType,
  PageContainer,
  ProColumns,
  ProTable,
  StatisticCard,
} from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import { PAYMENT, PAYMENT_STATUS, PAYMENT_TYPE } from '@/enums';
import { sortMap } from '@/pages/ReportAnalysis/WorkloadStatistics';
import { pageAwaitPayment } from '@/services/oa/conInvoiced';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { history } from '@umijs/max';

const OutstandingPayment: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [statisticData, setStatisticData] = useState<Record<string, any>>();

  // 表格
  const columns: ProColumns<API.PaymentResp>[] = [
    {
      title: '付款编号',
      dataIndex: 'payNumber',
      width: 200,
      copyable: true,
      fixed: 'left',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/finance/JR/outstanding-payment/${entity.id}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '付款状态',
      dataIndex: 'payStatus',
      width: 120,
      hideInSearch: true,
      valueEnum: option2enum(PAYMENT_STATUS),
    },
    {
      title: '付款类型',
      dataIndex: 'paymentType',
      width: 120,
      valueEnum: option2enum(PAYMENT_TYPE),
    },
    {
      title: '付款方式',
      dataIndex: 'payWay',
      valueEnum: option2enum(PAYMENT),
      width: 100,
      hideInSearch: true,
    },
    {
      title: '计划日期',
      dataIndex: 'estimatePayTime',
      width: 120,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '付款金额',
      dataIndex: 'payAmount',
      width: 150,
      hideInSearch: true,
      valueType: 'money',
      sorter: true,
    },
    {
      title: '单位名称(账户名)',
      dataIndex: 'institutionName',
      width: 150,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '付款日期',
      dataIndex: 'payTime',
      width: 120,
      hideInSearch: true,
      sorter: true,
    },

    {
      title: '申请人',
      dataIndex: 'applicationUser',
      width: 120,
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <StatisticCard.Group style={{ marginBottom: 16 }}>
        <StatisticCard
          statistic={{
            title: '待付款',
            value: statisticData?.awaitPayAmount,
            prefix: '￥',
            status: 'processing',
          }}
        />
      </StatisticCard.Group>
      <ProTable<API.PaymentResp>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="待付款记录表"
        request={async (params, sort) => {
          const { payNumber, applicationUser, paymentType, current, pageSize } = params;
          const sortEstimatePayTime =
            sort && sort?.estimatePayTime ? sortMap[sort.estimatePayTime] : undefined;
          const sortPayAmount = sort && sort?.payAmount ? sortMap[sort.payAmount] : undefined;
          const sortPayTime = sort && sort?.payTime ? sortMap[sort.payTime] : undefined;
          const search = { payNumber, applicationUser };
          const filter = { paymentType };
          const res = await pageAwaitPayment({
            pageNum: current,
            pageSize,
            search,
            filter,
            sortEstimatePayTime,
            sortPayAmount,
            sortPayTime,
          });
          const { amountToBePaid, records, total } = res.data as API.PaymentPagePaymentResp;
          setStatisticData({
            awaitPayAmount: amountToBePaid || 0,
          });
          return {
            success: res.code === 200,
            data: records || [],
            total: total || 0,
          };
        }}
      />
    </PageContainer>
  );
};

export default OutstandingPayment;
