import RKCol from '@/components/RKCol';
import RKSelectLabel from '@/components/RKSelectLabel';
import TitleLink from '@/components/TitleLink';
import { CLAIM_STATUS, COLLECTION_TYPE, PAYMENT } from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import {
  cancelClaim,
  confirmCollect,
  createCollect,
  selectCollPlan1,
  selectReceiptById,
} from '@/services/oa/conInvoiced';
import { paymentApplicationDropDownList } from '@/services/oa/paymentApplication';
import { onSuccessAndGoBack, onSuccessAndRefresh } from '@/utils';
import { requiredRule } from '@/utils/setting';

import {
  DrawerForm,
  FooterToolbar,
  PageContainer,
  PageHeader,
  ProForm,
  ProFormDatePicker,
  ProFormDateTimePicker,
  ProFormDependency,
  ProFormInstance,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useAccess, useModel, useRequest } from '@umijs/max';
import { Button, Modal, Row } from 'antd';
import { useMemo, useRef, useState } from 'react';

const ReceiptDetails: React.FC<WithRouteEditingProps> = ({ id, isEditPage }) => {
  const formRef = useRef<ProFormInstance>();
  const {
    canClaimedReceiptTransactionRecord = false,
    canUnclaimReceiptTransactionRecord = false,
    canEditReceiptTransactionRecord = false,
    canAddReceiptTransactionRecord = false,
  } = useAccess();
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [drawerVisit, setDrawerVisit] = useState(false);
  // 获取详情
  const {
    data = {},
    loading,
    refresh,
  } = useRequest(
    () =>
      selectReceiptById({
        idReq: { id },
      }),
    {
      ready: isEditPage,
      onSuccess: (res) => {
        setTimeout(() => {
          formRef.current?.setFieldsValue(
            {
              ...res,
              collectionWay: res?.collectionWay || 'FP',
            } || {},
          );
        }, 500);
      },
    },
  );

  // 是否认领
  const isCLAIM = useMemo(() => data?.claimStatus === 'CLAIM', [data]);

  // 认领
  const { run: claim, loading: claimLoading } = useRequest((value) => confirmCollect(value), {
    manual: true,
    onSuccess: (res) => {
      if (res.code === 200) {
        setDrawerVisit(false);
        onSuccessAndRefresh(res, refresh);
      }
    },
    formatResult: (res) => res,
  });
  // 撤销认领
  const { run: cancelClaimFn, loading: cancelClaimLoading } = useRequest(
    () => cancelClaim({ receiptId: id }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 200) {
          onSuccessAndRefresh(res, refresh);
        }
      },
      formatResult: (res) => res,
    },
  );

  const handleCancel = async () => {
    Modal.confirm({
      title: '确认撤销',
      content: `您确定要撤销认领吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        cancelClaimFn();
      },
    });
  };

  // 创建收款
  const { run: create, loading: createLoading } = useRequest((value) => createCollect(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });

  // 收款期次
  const { data: collectionList, loading: collectionLoading } = useRequest(
    () =>
      selectCollPlan1({
        institutionName: data.institutionName!,
      }),
    {
      ready: data?.claimStatus === 'NOT_CLAIM',
      refreshDeps: [data],
    },
  );
  // 保证金收款
  const { data: marginList, loading: marginLoading } = useRequest(
    () =>
      paymentApplicationDropDownList({
        institutionName: data.institutionName!,
      }),
    {
      ready: data?.claimStatus === 'NOT_CLAIM',
      refreshDeps: [data],
    },
  );

  const canEdit =
    (canEditReceiptTransactionRecord && isEditPage) ||
    (!isEditPage && canAddReceiptTransactionRecord);

  return (
    <PageContainer header={{ title: false }} className="detail-container" loading={loading}>
      {!isCLAIM && isEditPage && canClaimedReceiptTransactionRecord && (
        <PageHeader
          className="rk-page-header"
          extra={
            <Button type="primary" onClick={() => setDrawerVisit(true)}>
              认领
            </Button>
          }
        />
      )}
      {isCLAIM && isEditPage && canUnclaimReceiptTransactionRecord && (
        <PageHeader
          className="rk-page-header"
          extra={
            <Button type="primary" onClick={handleCancel} loading={cancelClaimLoading}>
              撤销认领
            </Button>
          }
        />
      )}

      <ProForm<API.ReceiptsResp>
        formRef={formRef}
        disabled={isCLAIM}
        submitter={
          data?.claimStatus !== 'CLAIM' &&
          canEdit && {
            searchConfig: {
              submitText: '保存',
              resetText: '取消',
            },
            onReset: () => {
              history.go(-1);
            },
            render: (props, doms) => {
              return <FooterToolbar>{doms}</FooterToolbar>;
            },
            submitButtonProps: {
              loading: createLoading || claimLoading,
            },
          }
        }
        initialValues={{
          claimStatus: 'NOT_CLAIM',
        }}
        onFinish={async (values) => {
          create(values);
        }}
      >
        <div className="rk-none">
          <ProFormText name="id" />
          {/* 关联收款计划主键 */}
          <ProFormText name="relevancyCollPlanId" />
          {/* 合同id */}
          <ProFormText name="contractId" />
          <ProFormText name="claimUserId" />
          <ProFormText name="claimStatus" />
          <ProFormText name="documentNumber" />
        </div>
        <Row gutter={24}>
          {isEditPage && (
            <RKCol>
              <ProFormText name="collectNumber" label="收款记录号" disabled />
            </RKCol>
          )}
          <RKCol>
            <ProFormDateTimePicker
              name="receiveTime"
              label="收款日期"
              rules={[requiredRule]}
              disabled={isEditPage}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="collectionWay"
              label="收款方式"
              options={PAYMENT}
              rules={[requiredRule]}
              disabled={isEditPage}
            />
          </RKCol>
          <RKCol>
            <ProFormMoney
              name="receiveAmount"
              label="收款金额"
              rules={[requiredRule]}
              min={0}
              disabled={isEditPage}
            />
          </RKCol>
          <RKCol>
            <ProFormText
              name="institutionName"
              label="单位名称(账户名)"
              rules={[requiredRule]}
              disabled={isEditPage}
            />
          </RKCol>
          <RKCol>
            <ProFormText
              name="bank"
              label="对方开户银行"
              rules={[requiredRule]}
              disabled={isEditPage}
            />
          </RKCol>
          <RKCol>
            <ProFormText
              name="account"
              label="对方银行账号"
              rules={[requiredRule]}
              disabled={isEditPage}
            />
          </RKCol>
          <RKCol>
            <ProFormText name="overview" label="摘要" />
          </RKCol>
          <RKCol>
            <ProFormText name="useWay" label="用途" />
          </RKCol>
          <RKCol>
            <ProFormText name="remark" label="详细信息" />
          </RKCol>
          <RKCol>
            <ProFormDatePicker
              name="inputTime"
              label="录入日期"
              disabled
              initialValue={Date.now()}
            />
          </RKCol>
          {isEditPage && isCLAIM && (
            <>
              <RKCol>
                <ProFormDatePicker name="claimTime" label="认领日期" disabled />
              </RKCol>
              <RKCol>
                <ProFormText name="claimUser" label="认领人" disabled />
              </RKCol>
              <RKCol>
                <ProFormSelect
                  name="claimStatus"
                  label="认领状态"
                  options={CLAIM_STATUS}
                  rules={[requiredRule]}
                  disabled
                />
              </RKCol>
              <ProFormDependency name={['contractList']}>
                {({ contractList }) => {
                  return (
                    <>
                      {contractList?.length
                        ? contractList.map((item: API.ReceiptsContractResp, index: number) => {
                            return (
                              <>
                                <RKCol key={item.contractId}>
                                  <ProFormText
                                    fieldProps={{
                                      value: item.contractNumber,
                                    }}
                                    label={
                                      <TitleLink
                                        path={
                                          item.contractId &&
                                          `/contract/main/edit/${item.contractId}`
                                        }
                                      >
                                        {'关联合同编号' +
                                          (contractList.length > 1 ? index + 1 : '')}
                                      </TitleLink>
                                    }
                                    disabled
                                  />
                                </RKCol>
                                <RKCol>
                                  <ProFormText
                                    fieldProps={{
                                      value: item.contractName,
                                    }}
                                    label={'合同名称' + (contractList.length > 1 ? index + 1 : '')}
                                    disabled
                                  />
                                </RKCol>
                              </>
                            );
                          })
                        : null}
                    </>
                  );
                }}
              </ProFormDependency>
            </>
          )}
          <ProFormDependency name={['documentNumber', 'marginId']}>
            {({ documentNumber, marginId }) => {
              if (documentNumber)
                return (
                  <RKCol>
                    <ProFormText
                      name="documentNumber"
                      label={
                        <TitleLink
                          path={marginId && `/finance/payment/general/details/${marginId}`}
                        >
                          保证金编号
                        </TitleLink>
                      }
                      disabled
                    />
                  </RKCol>
                );
            }}
          </ProFormDependency>
        </Row>
      </ProForm>
      <DrawerForm
        width={460}
        title="认领"
        open={drawerVisit}
        onOpenChange={setDrawerVisit}
        onFinish={async (values) => {
          const form = formRef.current?.getFieldsValue();
          const { claimTime, collectionType } = values;

          let res = {
            ...form,
            claimUserId: currentUser?.id,
            claimUser: currentUser?.username,
            claimStatus: 'CLAIM',
            claimTime,
            collectionType,
          };
          if (collectionType === 'CONTRACT') {
            const contractArr = values?.collPlan?.map((item: API.CollectionPlanResp) => {
              const { contractName, contractNumber, contractId, id: collPlanId } = item;
              return {
                contractName,
                contractNumber,
                contractId,
                collPlanId,
              };
            });
            res = {
              ...res,
              collPlan: contractArr,
            };
          } else {
            res = {
              ...res,
              marginId: values?.marginId,
              documentNumber: values?.documentNumber,
            };
          }

          claim(res);
        }}
        autoFocusFirstInput
        drawerProps={{
          destroyOnClose: true,
        }}
        initialValues={{
          collectionType: 'CONTRACT',
        }}
      >
        <div className="rk-none">
          <ProFormDatePicker name="claimTime" label="录入日期" disabled initialValue={Date.now()} />
          <ProFormText name="marginId" />
          <ProFormText name="documentNumber" />
        </div>
        <ProFormSelect
          name="collectionType"
          label="收款类型"
          options={COLLECTION_TYPE}
          rules={[requiredRule]}
        />
        <ProFormDependency name={['collectionType', 'collPlan', 'deposit']}>
          {({ collectionType, collPlan, deposit }) => {
            if (collectionType === 'CONTRACT')
              return (
                <ProFormSelect
                  name="collPlan"
                  label={
                    <TitleLink
                      path={collPlan?.contractId && `/contract/main/edit/${collPlan?.contractId}`}
                    >
                      收款期次
                    </TitleLink>
                  }
                  rules={[requiredRule]}
                  fieldProps={{
                    loading: collectionLoading,
                    showSearch: true,
                    labelInValue: true,
                  }}
                  mode="multiple"
                  options={collectionList?.map((item) => {
                    const {
                      contractNumber,
                      period,
                      contractName,
                      estimateReTime,
                      estimateReAmount,
                      id,
                    } = item;
                    return {
                      value: `${id}${contractName}`,
                      label: (
                        <RKSelectLabel
                          title={`${contractNumber}第${period}期`}
                          info={`${contractName}，其预计收款时间${estimateReTime}，预计收款金额￥${estimateReAmount}`}
                        />
                      ),
                      ...item,
                    };
                  })}
                />
              );
            return (
              <ProFormSelect
                name="deposit"
                label={
                  <TitleLink path={deposit && `/finance/payment/general/details/${deposit?.id}`}>
                    保证金
                  </TitleLink>
                }
                tooltip="如果收款到保证金，数据将进入退款记录"
                rules={[requiredRule]}
                fieldProps={{
                  loading: marginLoading,
                  showSearch: true,
                  labelInValue: true,
                  filterOption: (inputValue, option) => {
                    return option?.keywords.indexOf(inputValue) >= 0;
                  },
                }}
                options={marginList?.map((item) => {
                  const { documentNumber, paymentAmount, projectName, applicationDate } = item;
                  return {
                    value: item.documentNumber,
                    label: (
                      <RKSelectLabel
                        title={documentNumber}
                        info={`来源于项目“${projectName}”，申请日期: ${applicationDate}，付款金额￥${paymentAmount}`}
                      />
                    ),
                    keywords: `${documentNumber}${projectName}${applicationDate}${paymentAmount}`,
                    ...item,
                  };
                })}
                transform={(value) => ({
                  marginId: value.id,
                  documentNumber: value.documentNumber,
                })}
              />
            );
          }}
        </ProFormDependency>
      </DrawerForm>
    </PageContainer>
  );
};

export default withRouteEditing(ReceiptDetails);
