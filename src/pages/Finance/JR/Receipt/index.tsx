import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef } from 'react';

import { CLAIM_STATUS } from '@/enums';
import { useUserList } from '@/hooks/useUserList';
import { rangePresets } from '@/pages/ReportAnalysis/components/DimensionSetting';
import { sortMap } from '@/pages/ReportAnalysis/WorkloadStatistics';
import { deleteCollect, pageReceipts } from '@/services/oa/conInvoiced';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import dayjs from 'dayjs';

const Receipt: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const { canAddReceiptTransactionRecord = false, canDeleteReceiptTransactionRecord = false } =
    useAccess();
  const { userList, loading: userLoading } = useUserList();

  // 删除
  const { run: deleteRecord } = useRequest((id) => deleteCollect({ id }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const handleDelete = async (row: API.ReceiptsReq) => {
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除流水“${row.collectNumber}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(row.id);
      },
    });
  };

  const columns: ProColumns<API.ReceiptsReq>[] = [
    {
      title: '流水号',
      dataIndex: 'collectNumber',
      width: 200,
      copyable: true,
      fixed: 'left',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/finance/JR/receipt-list/details/${entity.id}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '认领状态',
      dataIndex: 'claimStatus',
      width: 100,
      valueEnum: option2enum(CLAIM_STATUS),
      hideInSearch: true,
    },
    {
      title: '认领人姓名',
      dataIndex: 'claimUser',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '收款日期',
      dataIndex: 'receiveTime',
      width: 170,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '收款金额',
      dataIndex: 'receiveAmount',
      valueType: 'money',
      width: 150,
      fieldProps: {
        min: 0,
      },
      sorter: true,
    },
    {
      title: '对方单位名称(账户名)',
      dataIndex: 'institutionName',
      width: 150,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '单位名称',
      dataIndex: 'institutionName',
      width: 150,
      hideInTable: true,
      ellipsis: true,
    },
    {
      title: '认领人',
      dataIndex: 'claimUser',
      width: 120,
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: userList,
        loading: userLoading,
        showSearch: true,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        optionFilterProp: 'label',
        filterOption: true,
      },
    },

    {
      title: '日期范围',
      valueType: 'dateRange',
      dataIndex: 'days',
      fieldProps: {
        presets: rangePresets,
      },
      initialValue: [dayjs().startOf('y'), dayjs().endOf('y')],
      hideInTable: true,
    },
    {
      title: '操作',
      fixed: 'right',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !canDeleteReceiptTransactionRecord,
      render: (text, record) => {
        return (
          <Access accessible={canDeleteReceiptTransactionRecord}>
            <Button
              type="link"
              className="inner-table-link"
              onClick={() => handleDelete(record)}
              disabled={record?.claimStatus === 'CLAIM'}
            >
              删除
            </Button>
          </Access>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.ReceiptsReq>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="收款记录表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddReceiptTransactionRecord}>
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  history.push('/finance/JR/receipt-list/add');
                }}
              >
                新建收款记录
              </Button>
            </Access>,
          ],
        }}
        request={async (params, sort) => {
          const {
            collectNumber,
            institutionName,
            receiveAmount,
            claimUser,
            days,
            current,
            pageSize,
            ...restProps
          } = params;
          const sortReceiveAmount =
            sort && sort?.receiveAmount ? sortMap[sort.receiveAmount] : undefined;
          const sortReceiveTime = sort && sort?.receiveTime ? sortMap[sort.receiveTime] : undefined;
          const scope =
            days &&
            [
              {
                key: 'ge',
                name: 'receiveTime',
                val: dayjs(days[0]).format('YYYY-MM-DD'),
              },
              {
                key: 'le',
                name: 'receiveTime',
                val: dayjs(days[1]).format('YYYY-MM-DD'),
              },
            ].filter(Boolean);
          const extra = { claimUser };

          const search = {
            collectNumber,
            institutionName,
            receiveAmount,
            ...restProps,
          };

          const res = await pageReceipts({
            pageNum: current,
            pageSize,
            search,
            extra,
            scope,
            sortReceiveAmount,
            sortReceiveTime,
          });
          return {
            data: res.data?.records || [],
            success: true,
            total: res.data?.total,
          };
        }}
      />
    </PageContainer>
  );
};

export default Receipt;
