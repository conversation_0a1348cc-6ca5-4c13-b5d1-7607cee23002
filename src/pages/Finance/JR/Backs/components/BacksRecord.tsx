import RKCol from '@/components/RKCol';
import TitleLink from '@/components/TitleLink';
import { PAYMENT_TYPE, PAY_CONDITION, WRITE_OFF_STATUS } from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useDepartment } from '@/hooks/useDepartment';
import { selectRefundById } from '@/services/oa/conInvoiced';
import { queryFormData } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormInstance,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Row } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import React, { useRef } from 'react';

const BacksRecord: React.FC<WithRouteEditingProps> = ({ id }) => {
  const formRef = useRef<ProFormInstance>();
  const isEdit = !!id;
  const { departmentList, loading: departmentLoading } = useDepartment();

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<any>
        formRef={formRef}
        disabled={isEdit}
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          onReset: () => {
            history.go(-1);
          },
          render: (props, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
          resetButtonProps: {
            style: {
              // 隐藏重置按钮
              display: 'none',
            },
          },
          submitButtonProps: {
            style: {
              // 隐藏重置按钮
              display: 'none',
            },
          },
        }}
        onFinish={async () => {
          return true;
        }}
        request={async () =>
          queryFormData(
            {
              idReq: { id },
            },
            isEdit,
            selectRefundById,
          )
        }
      >
        <div style={{ display: 'none' }}>
          <ProFormText name="id" label="id" />
        </div>
        <Row gutter={24}>
          <ProFormDependency name={['paymentId']}>
            {({ paymentId }) => {
              return (
                <RKCol>
                  <ProFormText
                    width="md"
                    name="payNumber"
                    label={
                      <TitleLink path={paymentId && `/finance/JR/payment-list/${paymentId}`}>
                        付款记录号(来自付款汇总)
                      </TitleLink>
                    }
                    rules={[requiredRule]}
                  />
                </RKCol>
              );
            }}
          </ProFormDependency>
          <RKCol>
            <ProFormSelect
              width="md"
              name="payWay"
              label="付款方式"
              rules={[requiredRule]}
              options={PAY_CONDITION}
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker width="md" name="payTime" label="付款日期" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormMoney
              width="md"
              name="payAmount"
              label="付款金额"
              min={0}
              locale="zh-CN"
              fieldProps={{ precision: 2, step: 0.1 }}
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormText
              width="md"
              name="institutionName"
              label="单位名称(账户名)"
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormText width="md" name="bank" label="开户银行" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormText width="md" name="account" label="银行账号" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormText width="md" name="remark" label="备注" />
          </RKCol>
          <RKCol>
            <ProFormText width="md" name="useWay" label="用途" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormSelect
              width="md"
              name="department"
              label="部门"
              rules={[requiredRule]}
              fieldProps={{
                loading: departmentLoading,
                fieldNames: {
                  value: 'id',
                  label: 'departmentName',
                },
                showSearch: true,
              }}
              options={departmentList as DefaultOptionType[]}
            />
          </RKCol>
          <RKCol>
            <ProFormText width="md" name="applicationUser" label="申请人" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormText width="md" name="projectName" label="项目名称" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormText width="md" name="description" label="描述" />
          </RKCol>
          <RKCol>
            <ProFormSelect
              width="md"
              name="fundingCategory"
              label="资金类别"
              rules={[requiredRule]}
              options={PAYMENT_TYPE}
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker width="md" name="estimateRefundTime" label="计划退款日期" />
          </RKCol>
          <RKCol>
            <ProFormDatePicker width="md" name="refundTime" label="退款日期" />
          </RKCol>
          <RKCol>
            <ProFormSelect
              disabled
              width="md"
              name="refundTag"
              label="销账标志"
              options={WRITE_OFF_STATUS}
            />
          </RKCol>
          <ProFormDependency name={['collectId']}>
            {({ collectId }) => {
              return (
                <RKCol>
                  <ProFormText
                    disabled
                    width="md"
                    name="collectNumber"
                    label={
                      <TitleLink
                        path={collectId && `/finance/JR/receipt-list/details/${collectId}`}
                      >
                        收款记录号(来自收款记录)
                      </TitleLink>
                    }
                  />
                </RKCol>
              );
            }}
          </ProFormDependency>
        </Row>
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(BacksRecord);
