import { PAYMENT_TYPE, WRITE_OFF_STATUS } from '@/enums';
import { sortMap } from '@/pages/ReportAnalysis/WorkloadStatistics';
import { pageRefund } from '@/services/oa/conInvoiced';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProTable,
  StatisticCard,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import React, { useRef, useState } from 'react';

const Backs: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [count, setCount] = useState(0);
  // 表格
  const columns: ProColumns<API.RefundResp>[] = [
    {
      title: '编号',
      dataIndex: 'refundNumber',
      copyable: true,
      ellipsis: true,
      width: 150,
      render: (text, record) => (
        <a className="rk-a-span" onClick={() => history.push(`/finance/JR/backs/${record.id}`)}>
          {text}
        </a>
      ),
    },
    {
      title: '申请人',
      dataIndex: 'applicationUser',
    },

    {
      title: '资金类别',
      dataIndex: 'fundingCategory',
      valueEnum: option2enum(PAYMENT_TYPE),
      hideInSearch: true,
    },

    {
      title: '退款金额',
      dataIndex: 'refAmount',
      valueType: 'money',
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '销账标志',
      dataIndex: 'refundTag',
      valueEnum: option2enum(WRITE_OFF_STATUS),
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <StatisticCard.Group style={{ marginBottom: 16 }}>
        <StatisticCard
          statistic={{
            title: '待销帐总金额',
            value: count,
            prefix: '￥',
            status: 'processing',
          }}
        />
      </StatisticCard.Group>
      <ProTable<API.RefundResp>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        rowKey="id"
        actionRef={tableRef}
        columns={columns}
        headerTitle="退款记录表"
        request={async (params, sort) => {
          const { refundTag, current, pageSize, ...rest } = params;
          const sortRefAmount = sort && sort?.refAmount ? sortMap[sort.refAmount] : undefined;
          const search = { ...rest };
          const filter = { refundTag };
          const res = await pageRefund({
            pageNum: current,
            pageSize,
            filter,
            sortRefAmount,
            search,
          });
          const { awaitRefundAmount } = res.data as API.FlowPageRefundResp;
          setCount(awaitRefundAmount || 0);

          return {
            success: res.code === 200,
            data: res?.data?.records || [],
            total: res?.data?.total || 0,
          };
        }}
      />
    </PageContainer>
  );
};

export default Backs;
