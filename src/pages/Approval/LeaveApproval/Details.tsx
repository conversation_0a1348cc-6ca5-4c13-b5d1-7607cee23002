import RKCol from '@/components/RKCol';
import RKPageHeader from '@/components/RKPageHeader';
import withRouteEditing from '@/hoc/withRouteEditing';
import { useDepartment } from '@/hooks/useDepartment';
import {
  DrawerFormProps,
  PageContainer,
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Row } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { useEffect, useRef } from 'react';
const LeaveApprovalDetails: React.FC<DrawerFormProps> = withRouteEditing(({ id }) => {
  const formRef = useRef<ProFormInstance>();
  const { departmentList, loading: departmentLoading } = useDepartment();
  const { approvalDetails } = useModel('useApprovalModel');
  useEffect(() => {
    formRef.current?.setFieldsValue(approvalDetails?.fromData);
  }, [approvalDetails]);

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<API.LeaveAppInsertReq & { leaveBalance: number; halfDate?: string }>
        formRef={formRef}
        submitter={false}
        disabled
      >
        <RKPageHeader
          id={id}
          status={(approvalDetails?.activiStatus as unknown as string) || '9'}
          title={approvalDetails?.fromData?.documentNumber}
          approveType="LEAVE_APPROVAL_PROCESS"
        />
        <Row gutter={24}>
          <RKCol>
            <ProFormText
              name="employee"
              label="员工编号"
              fieldProps={{
                autoComplete: 'none',
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormText name="employeeName" label="员工姓名" />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="department"
              label="部门"
              fieldProps={{
                loading: departmentLoading,
                fieldNames: {
                  value: 'id',
                  label: 'departmentName',
                },
                showSearch: true,
              }}
              options={departmentList as DefaultOptionType[]}
            />
          </RKCol>
          <RKCol>
            <ProFormText name="leaveTypeName" label="休假类型" />
          </RKCol>
          <RKCol>
            <ProFormDigit name="leaveBalance" label="可用天数" />
          </RKCol>
          <RKCol>
            <ProFormDatePicker className="ant-picker" name="fromDate" label="开始日期" />
          </RKCol>
          <RKCol>
            <ProFormDatePicker className="ant-picker" name="toDate" label="结束日期" />
          </RKCol>
          <RKCol>
            <ProFormDatePicker className="ant-picker" name="halfDate" label="上半天班日期" />
          </RKCol>
          <RKCol>
            <ProFormDigit
              name="totalLeaveDays"
              label="总休假天数"
              min={0}
              fieldProps={{
                autoComplete: 'none',
                addonAfter: '天',
              }}
            />
          </RKCol>
          <RKCol lg={18} md={18} sm={18}>
            <ProFormTextArea
              name="description"
              label="原因"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 3,
                },
              }}
            />
          </RKCol>
        </Row>
      </ProForm>
    </PageContainer>
  );
});
export default LeaveApprovalDetails;
