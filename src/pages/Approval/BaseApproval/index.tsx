import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import { APPROVAL_STATUS } from '@/enums';
import { getProcessInstancesForUser } from '@/services/oa/flow';
import { option2enum, queryApprovalTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { history, useLocation } from '@umijs/max';

type BaseApprovalProps = {
  type: string;
  detailPath?: string;
  headerTitle: string;
  redirectFn?: (record: Record<string, any>) => void;
};

const status = [
  {
    label: '全部',
    key: '0',
  },
  {
    label: '待处理',
    key: '1',
  },
  {
    label: '已处理',
    key: '2',
  },
  {
    label: '已发起',
    key: '3',
  },
];

const BaseApproval: React.FC<BaseApprovalProps> = ({
  type,
  detailPath,
  headerTitle,
  redirectFn,
}) => {
  const tableRef = useRef<ActionType | undefined>();

  const { search } = useLocation();

  // 解析URL查询参数
  const queryParams = new URLSearchParams(search);

  const currentKey = queryParams.get('pendingApproval') || '1';

  const [activeKey, setActiveKey] = useState<string>(currentKey);

  const showMoneyFilesArr = [
    'REIMBURSEMENT_APPROVAL',
    'UNIVERSAL_PAYMENT',
    'TRAINING_REIMBURSEMENT_APPROVAL',
    'CONTRACT_PROCUREMENT_PAYMENT_APPLICATION',
  ];

  // 表格
  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '编号',
      dataIndex: 'searchNumber',
      hideInTable: true,
    },
    // {
    //   title: '状态',
    //   dataIndex: 'pendingApproval',
    //   hideInTable: true,
    //   valueType: 'radio',
    //   initialValue: '1',
    //   fieldProps: {
    //     allowClear: false,
    //     optionType: 'button',
    //     buttonStyle: 'solid',
    //     options: status,
    //   },
    // },

    {
      title: '发起人',
      dataIndex: 'searchStartUsername',
      hideInTable: true,
    },
    {
      title: '审批状态',
      dataIndex: 'filterActiviStatus',
      hideInTable: true,
      valueType: 'select',
      initialValue: '',
      fieldProps: {
        options: [{ label: '全部', value: '' }, ...APPROVAL_STATUS].filter(
          (item) => item.value !== '0',
        ),
      },
      hideInSearch: ['1', '2'].includes(activeKey),
    },
    {
      title: '编号',
      dataIndex: 'documentNumber',
      width: 180,
      copyable: true,
      hideInSearch: true,
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => {
              redirectFn?.(entity);
              if (detailPath) history.push(`${detailPath}${entity.processInstanceId}`);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      width: 120,
      hideInSearch: true,
      valueEnum: option2enum(APPROVAL_STATUS),
    },
    {
      title: '审核人',
      dataIndex: 'authUsers',
      width: 120,
      hideInSearch: true,
      renderText(text) {
        return text?.join(',');
      },
    },
    {
      title: '发起人',
      dataIndex: 'startUsername',
      width: 120,
      hideInSearch: true,
    },

    {
      title: '发起时间',
      dataIndex: 'startTime',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '金额',
      dataIndex: 'extra',
      width: 120,
      hideInSearch: true,
      hideInTable: !showMoneyFilesArr.includes(type),
      valueType: 'money',
      renderText(text, entity) {
        return entity?.extra?.money;
      },
    },
    {
      title: '出发日期',
      dataIndex: 'extra',
      width: 120,
      hideInSearch: true,
      hideInTable: !(type === 'REIMBURSEMENT_APPROVAL'),
      renderText(text, entity) {
        return entity?.extra?.startTime;
      },
    },
    {
      title: '返程日期',
      dataIndex: 'extra',
      width: 120,
      hideInSearch: true,
      hideInTable: !(type === 'REIMBURSEMENT_APPROVAL'),
      renderText(text, entity) {
        return entity?.extra?.endTime;
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.HistoricProcessInstanceResp>
        {...defaultTableConfig}
        rowKey="processInstanceId"
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle={headerTitle}
        params={{
          type,
          pendingApproval: activeKey,
        }}
        form={{
          syncToUrl: true,
          syncToInitialValues: false,
          extraUrlParams: {
            pendingApproval: activeKey,
          },
        }}
        toolbar={{
          menu: {
            type: 'tab',
            activeKey: activeKey,
            items: status,
            onChange: (key) => {
              setActiveKey(key as string);
            },
          },
        }}
        // polling={(dataSource) =>
        //   dataSource?.find((item) => item?.activiStatus === '1') ? 5000 : 0
        // }
        request={async (params) => {
          const { filterActiviStatus } = params;
          return queryApprovalTable(
            {
              ...params,
              filterActiviStatus: ['1', '2'].includes(activeKey) ? '' : filterActiviStatus,
            },
            getProcessInstancesForUser,
          );
        }}
      />
    </PageContainer>
  );
};

export default BaseApproval;
