import RKCol from '@/components/RKCol';
import RKPageHeader from '@/components/RKPageHeader';
import withRouteEditing from '@/hoc/withRouteEditing';
import { useDepartment } from '@/hooks/useDepartment';
import {
  DrawerFormProps,
  PageContainer,
  ProForm,
  ProFormDateTimePicker,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Row } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { useEffect, useRef } from 'react';
const ResourceBorrowDetails: React.FC<DrawerFormProps> = withRouteEditing(({ id }) => {
  const formRef = useRef<ProFormInstance>();
  const { departmentList, loading: departmentLoading } = useDepartment();
  const { approvalDetails } = useModel('useApprovalModel');
  useEffect(() => {
    formRef.current?.setFieldsValue(approvalDetails?.fromData);
  }, [approvalDetails]);

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<API.LeaveAppInsertReq & { leaveBalance: number; halfDate?: string }>
        formRef={formRef}
        submitter={false}
        disabled
      >
        <RKPageHeader
          id={id}
          status={(approvalDetails?.activiStatus as unknown as string) || '9'}
          title={approvalDetails?.fromData?.documentNumber}
          approveType="RESOURCE_APPLICATION"
        />
        <Row gutter={24}>
          <RKCol>
            <ProFormText
              name="employee"
              label="员工编号"
              fieldProps={{
                autoComplete: 'none',
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormText name="employeeName" label="员工姓名" />
          </RKCol>
          <RKCol>
            <ProFormText name="content" label="申请内容" />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="resourceDepartment"
              label="资源所属部门"
              fieldProps={{
                loading: departmentLoading,
                fieldNames: {
                  value: 'id',
                  label: 'departmentName',
                },
                showSearch: true,
              }}
              options={departmentList as DefaultOptionType[]}
            />
          </RKCol>
          <RKCol>
            <ProFormDateTimePicker className="ant-picker" name="useTime" label="预计使用时间" />
          </RKCol>
          <RKCol>
            <ProFormDateTimePicker className="ant-picker" name="stillTime" label="预计归还时间" />
          </RKCol>
        </Row>
        <Row gutter={24}>
          <RKCol lg={12} md={12} sm={12}>
            <ProFormTextArea
              name="useCause"
              label="使用原因"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 3,
                },
              }}
            />
          </RKCol>
        </Row>
      </ProForm>
    </PageContainer>
  );
});
export default ResourceBorrowDetails;
