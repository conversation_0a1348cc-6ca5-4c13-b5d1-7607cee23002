import RKCol from '@/components/RKCol';
import RKPageHeader from '@/components/RKPageHeader';
import withRouteEditing from '@/hoc/withRouteEditing';
import { leaveAppBalance, leaveAppPage } from '@/services/oa/leaveApp';
import {
  DrawerFormProps,
  PageContainer,
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
} from '@ant-design/pro-components';
import { useModel, useRequest } from '@umijs/max';
import { Row } from 'antd';
import { useEffect, useRef } from 'react';
const RevokeLeaveDetails: React.FC<DrawerFormProps> = withRouteEditing(({ id }) => {
  const formRef = useRef<ProFormInstance>();

  const { approvalDetails } = useModel('useApprovalModel');
  useEffect(() => {
    formRef.current?.setFieldsValue(approvalDetails?.fromData);
  }, [approvalDetails]);

  //所有休假申请数据
  const { data: leaveApplicationList = {} } = useRequest(() =>
    leaveAppPage({
      pageNum: 1,
      pageSize: 10000,
    }),
  );
  //假期类型
  const { data: leaveList = [] } = useRequest(() => leaveAppBalance());

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<API.LeaveSellInfoResp> formRef={formRef} submitter={false} disabled>
        <RKPageHeader
          id={id}
          status={(approvalDetails?.activiStatus as unknown as string) || '9'}
          title={approvalDetails?.fromData?.documentNumber}
          approveType="REVOCATION_APPROVAL"
        />
        <Row gutter={24}>
          <RKCol>
            <ProFormSelect
              width="md"
              name="leaveAppId"
              label="休假申请"
              options={leaveApplicationList?.records
                ?.filter((item) => item.activiStatus === '2')
                .map((item) => ({
                  value: item.id,
                  label: item.documentNumber,
                }))}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              disabled
              name="leaveType"
              label="休假类型"
              placeholder="请输入"
              options={leaveList.map((item) => ({
                value: item.id,
                label: item.leaveTypeName,
              }))}
              width="md"
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker disabled width="md" name="fromDate" label="休假开始时间" />
          </RKCol>
          <RKCol>
            <ProFormDatePicker disabled width="md" name="toDate" label="休假结束时间" />
          </RKCol>
          <RKCol>
            <ProFormDigit
              name="totalLeaveDays"
              label="总休假天数"
              placeholder="请输入"
              min={0}
              fieldProps={{
                autoComplete: 'none',
                addonAfter: '天',
              }}
              width="md"
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker className="ant-picker" name="sellDate" label="销假日期" />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="half"
              label="是否销假半天"
              placeholder="请输入"
              options={[
                { value: 0, label: '否' },
                { value: 1, label: '是' },
              ]}
              width="md"
            />
          </RKCol>
        </Row>
      </ProForm>
    </PageContainer>
  );
});
export default RevokeLeaveDetails;
