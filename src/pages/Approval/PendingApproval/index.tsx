import { APPROVAL_TYPE } from '@/enums';
import { approval } from '@/services/oa/home';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Tag } from 'antd';
import { useRef } from 'react';

export const typeMap: Record<string, string> = {
  CUST: 'customer',
  INST: 'institution',
  SUPP: 'suppliers',
};

const columns: ProColumns<any>[] = [
  {
    title: '编号',
    dataIndex: 'documentNumber',
    width: 150,
    copyable: true,
    render(dom, entity) {
      const { type = '', documentNumber = '', id = '' } = entity;
      // 业务伙伴特殊处理
      if (type === 'BUSINESS_PARTNER') {
        const prefix = documentNumber.split('-').shift();
        const path = typeMap[prefix];
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/approval/partner/${path}-details/${id}`)}
          >
            {dom}
          </a>
        );
      }

      const obj = APPROVAL_TYPE.find((item) => item.value === type);

      return (
        <a className="rk-a-span" onClick={() => history.push(`${obj?.path}/details/${id}`)}>
          {dom}
        </a>
      );
    },
  },
  {
    title: '审核人',
    dataIndex: 'authUsers',
    width: 120,
    hideInSearch: true,
    renderText(text) {
      return text?.join(',');
    },
  },
  {
    title: '发起人',
    dataIndex: 'sponsor',
    width: 120,
  },

  {
    title: '发起时间',
    dataIndex: 'initiationTime',
    width: 120,
    hideInSearch: true,
  },
  {
    title: '来源',
    dataIndex: 'type',
    width: 120,
    hideInSearch: true,
    render: (dom, entity) => {
      const { type } = entity;
      const types = option2enum(APPROVAL_TYPE);
      const val = types[type!];
      return val ? <Tag color={val?.textColor}>{val.text}</Tag> : dom;
    },
  },
  {
    title: '来源',
    dataIndex: 'type',
    valueType: 'select',
    fieldProps: () => ({
      showSearch: true,
    }),
    valueEnum: option2enum(APPROVAL_TYPE),
    hideInTable: true,
  },
];

const PendingApproval: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="待审批列表"
        polling={5000}
        request={async (params) => {
          const { sponsor, type, documentNumber, pageSize, current } = params;
          const msg = await approval({
            type: type,
            searchNumber: documentNumber,
            searchStartUsername: sponsor,
            pageNum: current,
            pageSize: pageSize,
          });

          return {
            data: msg?.data?.historicProcessInstanceRespList || [],
            success: true,
            total: msg?.data?.count,
          };
        }}
      />
    </PageContainer>
  );
};

export default PendingApproval;
