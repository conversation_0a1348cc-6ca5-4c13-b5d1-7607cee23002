import RKPageHeader from '@/components/RKPageHeader';
import withRouteEditing from '@/hoc/withRouteEditing';
import {
  DrawerFormProps,
  PageContainer,
  ProForm,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { useEffect, useRef } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

const ResourceBorrowDetails: React.FC<DrawerFormProps> = withRouteEditing(({ id }) => {
  const formRef = useRef<ProFormInstance>();
  const { approvalDetails } = useModel('useApprovalModel');
  useEffect(() => {
    formRef.current?.setFieldsValue(approvalDetails?.fromData);
  }, [approvalDetails]);

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<API.LeaveAppInsertReq & { leaveBalance: number; halfDate?: string }>
        formRef={formRef}
        submitter={false}
        disabled
      >
        <RKPageHeader
          id={id}
          status={(approvalDetails?.activiStatus as unknown as string) || '9'}
          title={approvalDetails?.fromData?.title}
          approveType="ANNOUNCEMENT_APPROVAL"
        />
        <ProFormText
          name="title"
          label="标题"
          fieldProps={{
            autoComplete: 'none',
          }}
        />
        <ProForm.Item name="content" label="公告内容">
          <div
            style={{
              height: 'auto',
              maxHeight: 'calc(100vh - 300px)',
              overflow: 'auto',
            }}
          >
            <ReactQuill
              value={approvalDetails?.fromData?.content}
              readOnly={true}
              theme="snow"
              modules={{
                toolbar: false,
              }}
              style={{
                height: 'auto',
              }}
            />
          </div>
        </ProForm.Item>
      </ProForm>
    </PageContainer>
  );
});
export default ResourceBorrowDetails;
