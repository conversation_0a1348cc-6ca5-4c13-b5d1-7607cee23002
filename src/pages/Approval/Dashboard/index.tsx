import { APPROVAL_TYPE } from '@/enums';
import { pendingApproval } from '@/services/oa/flow';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Badge, Col, Row, Space, Typography } from 'antd';
import React from 'react';

const colSpan = { xs: 12, sm: 12, md: 8, lg: 8, xl: 8 };
const { Text, Title } = Typography;

const Dashboard: React.FC = () => {
  const { data } = useRequest(() => pendingApproval(), {});
  return (
    <PageContainer header={{ title: false }}>
      <div>
        <ProCard gutter={24} title="审批管理" wrap>
          {APPROVAL_TYPE.map((item, index) => (
            <ProCard
              style={{ marginBottom: 24 }}
              colSpan={colSpan}
              bordered
              hoverable
              key={index}
              onClick={() => {
                history.push(item.path);
              }}
            >
              <Badge dot count={data?.[item.value] || 0} offset={[8, 0]}>
                <Row gutter={16}>
                  <Col>
                    <img src="/images/approval.png" width={48} />
                  </Col>
                  <Col>
                    <Title level={5}>{item.label}</Title>
                    <Space>
                      <Text type="secondary">待处理:</Text>
                      <a>{data?.[item.value] || 0}</a>
                    </Space>
                  </Col>
                </Row>
              </Badge>
            </ProCard>
          ))}
        </ProCard>
      </div>
    </PageContainer>
  );
};

export default Dashboard;
