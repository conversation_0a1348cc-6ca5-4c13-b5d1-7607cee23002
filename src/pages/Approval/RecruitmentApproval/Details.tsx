import RKCol from '@/components/RKCol';
import RK<PERSON>ageHeader from '@/components/RKPageHeader';
import withRouteEditing from '@/hoc/withRouteEditing';
import { useDepartment } from '@/hooks/useDepartment';
import { useGradeList } from '@/hooks/useGradeList';
import { usePositionList } from '@/hooks/usePositionList';
import { GENDER, TOP_EDUCATION } from '@/pages/HumanResources/Employees/components/EmployeesEnums';
import {
  DrawerFormProps,
  PageContainer,
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Row, Typography } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import dayjs from 'dayjs';
import { useEffect, useRef } from 'react';
const RecruitmentApprovalDetails: React.FC<DrawerFormProps> = withRouteEditing(({ id }) => {
  const formRef = useRef<ProFormInstance>();
  const { departmentList, loading: departmentLoading } = useDepartment();
  const { positionList, loading: positionLoading } = usePositionList();
  const { gradeList, loading: gradeLoading } = useGradeList();
  const { approvalDetails } = useModel('useApprovalModel');
  useEffect(() => {
    formRef.current?.setFieldsValue(approvalDetails?.fromData);
  }, [approvalDetails]);

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<API.RecruitInfoResp> formRef={formRef} submitter={false} disabled>
        <RKPageHeader
          id={id}
          status={(approvalDetails?.activiStatus as unknown as string) || '9'}
          title={approvalDetails?.fromData?.documentNumber}
          approveType="RECRUITMENT_APPLICATION"
        />
        <div className="rk-none">
          <ProFormText name="id" />
          <ProFormText name="applicantId" />
          <ProFormText name="documentNumber" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText disabled name="applicantName" label="申请人" />
          </RKCol>
          <RKCol>
            <ProFormDatePicker disabled name="applicationTime" label="申请日期" />
          </RKCol>
          <RKCol>
            <ProFormDatePicker
              name="expectationTime"
              label="期望到岗日期"
              transform={(value, namePath) => {
                return {
                  [namePath]: dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
                };
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="positionNameId"
              label="需求岗位名称"
              fieldProps={{
                loading: positionLoading,
                fieldNames: {
                  value: 'id',
                  label: 'positionName',
                },
                showSearch: true,
                onChange: (val, option) => {
                  const { departmentId } = option as { departmentId?: string };
                  formRef.current?.setFieldValue('affiliationDepartmentId', departmentId);
                },
              }}
              options={positionList as DefaultOptionType[]}
            />
          </RKCol>
          <RKCol>
            <ProFormDigit
              name="positionNum"
              label="需求人数"
              min={0}
              fieldProps={{
                autoComplete: 'none',
                addonAfter: '位',
              }}
              transform={(val, namePath) => ({ [namePath]: String(val) })}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              disabled
              name="affiliationDepartmentId"
              label="隶属部门"
              fieldProps={{
                loading: departmentLoading,
                fieldNames: {
                  value: 'id',
                  label: 'departmentName',
                },
                showSearch: true,
              }}
              options={departmentList as DefaultOptionType[]}
            />
          </RKCol>

          <RKCol>
            <ProFormSelect
              name="demandGrade"
              label="需求岗位等级"
              fieldProps={{
                loading: gradeLoading,
                fieldNames: {
                  value: 'id',
                  label: 'grade',
                },
                showSearch: true,
              }}
              options={gradeList as DefaultOptionType[]}
            />
          </RKCol>
          <RKCol>
            <ProFormText name="territory" label="工作属地" />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="applicationCause"
              label="申请原因"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 3,
                },
              }}
            />
          </RKCol>
        </Row>
        <Typography.Title level={5} style={{ marginBlock: 16 }}>
          职位要求
        </Typography.Title>
        <Row gutter={24}>
          <RKCol>
            <ProFormSelect name="gender" label="性别" placeholder="请输入" options={GENDER} />
          </RKCol>
          <RKCol>
            <ProFormText name="age" label="年龄" placeholder="请输入" />
          </RKCol>
          <RKCol>
            <ProFormText name="wages" label="薪酬" placeholder="请输入" />
          </RKCol>
          <RKCol>
            <ProFormText name="industryBack" label="行业背景" placeholder="请输入" />
          </RKCol>
          <RKCol>
            <ProFormText name="workYears" label="工作年限" placeholder="请输入" />
          </RKCol>
          <RKCol>
            <ProFormText name="frequency" label="跳槽频率" placeholder="请输入" />
          </RKCol>
          <RKCol>
            <ProFormSelect name="degree" label="学历" options={TOP_EDUCATION} />
          </RKCol>
          <RKCol>
            <ProFormText name="specialized" label="专业" placeholder="请输入" />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="priorityCondition"
              label="优先条件"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 3,
                },
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="chenCheng"
              label="其他补充要求"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 3,
                },
              }}
            />
          </RKCol>
        </Row>
        <Typography.Title level={5} style={{ marginBlock: 16 }}>
          招聘需求
        </Typography.Title>
        <ProFormTextArea
          name="recruitDemand"
          label="招聘需求"
          fieldProps={{
            autoSize: {
              minRows: 3,
              maxRows: 8,
            },
          }}
        />
        <Typography.Title level={5} style={{ marginBlock: 16 }}>
          岗位职责
        </Typography.Title>
        <ProFormTextArea
          name="duty"
          label="岗位职责"
          fieldProps={{
            autoSize: {
              minRows: 3,
              maxRows: 8,
            },
          }}
        />
      </ProForm>
    </PageContainer>
  );
});
export default RecruitmentApprovalDetails;
