import RKCol from '@/components/RKCol';
import RKPageHeader from '@/components/RKPageHeader';
import TitleLink from '@/components/TitleLink';
import { RESTITUTION_STATUS } from '@/enums';
import withRouteEditing from '@/hoc/withRouteEditing';
import { trainProjectList } from '@/services/oa/train';
import {
  DrawerFormProps,
  PageContainer,
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useModel, useRequest } from '@umijs/max';
import { Row } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useRef } from 'react';

const DepositApprovalDetails: React.FC<DrawerFormProps> = withRouteEditing(({ id }) => {
  const formRef = useRef<ProFormInstance>();
  const { approvalDetails } = useModel('useApprovalModel');

  useEffect(() => {
    formRef.current?.setFieldsValue(approvalDetails?.fromData);
  }, [approvalDetails]);

  // 获取项目
  const { data: nbProjectList } = useRequest(() => trainProjectList());

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<API.TrainReimbursementResp> formRef={formRef} submitter={false} disabled>
        <RKPageHeader
          id={id}
          status={(approvalDetails?.activiStatus as unknown as string) || '9'}
          title={approvalDetails?.fromData?.trainNumber}
          approveType="TRAINING_REIMBURSEMENT_APPROVAL"
        />
        <div style={{ display: 'none' }}>
          <ProFormText name="id" label="id" placeholder="请输入" />
          <ProFormText name="employeeId" label="employeeId" placeholder="请输入" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText name="employeeName" label="员工姓名" />
          </RKCol>
          <RKCol>
            <ProFormText name="employeeNumber" label="员工编号" />
          </RKCol>
          <RKCol>
            <ProFormMoney
              name="trainSpend"
              label="培训费用"
              locale="zh_CN"
              min={0}
              fieldProps={{ precision: 2 }}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="operateState"
              label="发票"
              valueEnum={{ SUBMIT: '已提交', NOT_SUBMIT: '未提交', NULL: '无' }}
            />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="trainContent"
              label="培训内容"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 3,
                },
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormDigit
              name="count"
              label="返还期数"
              placeholder="请输入"
              min={0}
              fieldProps={{ precision: 0, addonAfter: '个月' }}
            />
          </RKCol>
          <RKCol>
            <ProFormDatePicker.Month
              width="md"
              name="startTime"
              label="返还起始月"
              placeholder="请输入"
              transform={(value, namePath) => {
                return {
                  [namePath]: `${dayjs(value).format('YYYY-MM')}-10`,
                };
              }}
              fieldProps={{
                format: 'YYYY-MM-DD',
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormDigit
              name="remainderCount"
              label="剩余返还月"
              placeholder="请输入"
              min={0}
              fieldProps={{ precision: 0, addonAfter: '个月' }}
            />
          </RKCol>

          <RKCol>
            <ProFormDatePicker.Month
              width="md"
              name="endTime"
              label="返还结束月"
              placeholder="请输入"
              transform={(value, namePath) => {
                return {
                  [namePath]: `${dayjs(value).format('YYYY-MM')}-10`,
                };
              }}
              fieldProps={{
                format: 'YYYY-MM-DD',
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormMoney
              name="remainderAmount"
              label="剩余返还金额"
              locale="zh_CN"
              min={0}
              fieldProps={{ precision: 2, step: 0.1 }}
            />
          </RKCol>
          <RKCol>
            <ProFormMoney
              name="nextYearAmount"
              label="次年返还金额"
              placeholder="请输入"
              locale="zh_CN"
              min={0}
              fieldProps={{ precision: 2, step: 0.1 }}
            />
          </RKCol>
          <RKCol>
            <ProFormMoney
              name="amount"
              label="每月返还额"
              placeholder="请输入"
              locale="zh_CN"
              min={0}
              fieldProps={{ precision: 2, step: 0.1 }}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="state"
              label="返还状态"
              placeholder="请输入"
              options={RESTITUTION_STATUS}
            />
          </RKCol>
          <RKCol>
            <ProFormDependency name={['projectId']}>
              {({ projectId }) => {
                return (
                  <ProFormSelect
                    name="projectId"
                    label={
                      <TitleLink path={projectId && `/project/internal/edit/${projectId}`}>
                        项目编号
                      </TitleLink>
                    }
                    options={nbProjectList?.map((item: API.ProjectNameIdResp) => {
                      return {
                        value: item.id,
                        label: item.projectNumber,
                      };
                    })}
                  />
                );
              }}
            </ProFormDependency>
          </RKCol>
        </Row>
      </ProForm>
    </PageContainer>
  );
});
export default DepositApprovalDetails;
