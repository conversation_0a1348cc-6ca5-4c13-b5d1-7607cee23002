import { history } from '@umijs/max';
import React from 'react';
import BaseApproval from '../BaseApproval';

const typeMap: Record<string, string> = {
  CLIENT: 'customer',
  INSTITUTION: 'institution',
  VENDOR: 'suppliers',
};

const PartnerApproval: React.FC = () => {
  return (
    <BaseApproval
      type="BUSINESS_PARTNER"
      headerTitle="业务伙伴审核列表"
      redirectFn={(record) => {
        const path = typeMap?.[record?.extra?.partnerType];
        if (!path) return;
        history.push(`/approval/partner/${path}-details/${record.processInstanceId}`);
      }}
    />
  );
};

export default PartnerApproval;
