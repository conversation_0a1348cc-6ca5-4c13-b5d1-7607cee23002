import { PROJECT_STATUS } from '@/enums';
import { useUserList } from '@/hooks/useUserList';
import { sortMap } from '@/pages/ReportAnalysis/WorkloadStatistics';
import { getDepartmentTree } from '@/services/oa/department';
import { projectDeliverySh } from '@/services/oa/profit';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { DefaultOptionType } from 'antd/es/select';
import React, { useRef, useState } from 'react';

const ProjectDelivery: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const initialPageSize = defaultTableConfig.pagination?.pageSize ?? 10;
  const [pagingParams, setPagingParams] = useState({ current: 1, pageSize: initialPageSize });
  const { userList = [], loading: userLoading } = useUserList(true);
  const { data: treeData = [] } = useRequest(() => getDepartmentTree());

  // 表格列定义
  const columns: ProColumns<API.ProjectDeliveryResp>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      search: false,
      width: 80,
      render: (_, __, index) => (pagingParams.current - 1) * pagingParams.pageSize + index + 1,
    },
    {
      title: '执行部门',
      dataIndex: 'executeDepartment',
      valueType: 'select',
      hideInTable: true,
      fieldProps: () => ({
        options: treeData
          ?.filter((item: API.DepartmentTreeResp) => item.status === '1')
          ?.map((item: API.DepartmentTreeResp) => ({
            label: item.departmentName,
            value: item.id,
          })),
        showSearch: true,
        optionFilterProp: 'label',
        filterOption: true,
      }),
    },
    {
      title: '执行部门',
      dataIndex: 'executeDepartment',
      search: false,
      ellipsis: true,
      width: 90,
    },
    {
      title: '项目编号',
      dataIndex: 'projectNumber',
      copyable: true,
      width: 150,
      ellipsis: true,
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/project/after-sales/edit/${entity.id}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      ellipsis: true,
      width: 200,
    },
    {
      dataIndex: 'status',
      title: '项目状态',
      width: 100,
      valueEnum: option2enum(PROJECT_STATUS),
    },
    {
      title: '客户简称',
      dataIndex: 'clientAbbreviation',
      ellipsis: true,
      width: 100,
    },
    {
      title: '项目经理',
      width: 100,
      dataIndex: 'projectMangerId',
      valueType: 'select',
      fieldProps: {
        loading: userLoading,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        showSearch: true,
        options: userList as DefaultOptionType[],
        filterOption: true,
        optionFilterProp: 'label',
      },
      render: (_, record) => record.projectMangerName,
    },
    {
      title: '销售经理',
      width: 100,
      dataIndex: 'salePersonId',
      valueType: 'select',
      fieldProps: {
        loading: userLoading,
        fieldNames: {
          value: 'id',
          label: 'username',
        },
        showSearch: true,
        options: userList as DefaultOptionType[],
        filterOption: true,
        optionFilterProp: 'label',
      },
      render: (_, record) => record.salePersonName,
    },
    {
      title: '项目预算',
      width: 110,
      dataIndex: 'projectBudget',
      search: false,
      valueType: 'money',
    },
    {
      title: '项目成本',
      width: 110,
      dataIndex: 'projectCost',
      search: false,
      valueType: 'money',
    },
    {
      title: '售前成本',
      width: 110,
      dataIndex: 'preCost',
      search: false,
      valueType: 'money',
    },
    {
      title: '开销预估',
      width: 110,
      dataIndex: 'costEstimate',
      search: false,
      valueType: 'money',
    },
    {
      title: '当前开销',
      width: 110,
      dataIndex: 'spend',
      search: false,
      valueType: 'money',
    },
    {
      title: '工作量预估(人天)',
      width: 130,
      dataIndex: 'estimatedWorkload',
      search: false,
      ellipsis: true,
    },
    {
      title: '执行工作量(人天)',
      width: 130,
      dataIndex: 'executedWorkload',
      search: false,
      ellipsis: true,
    },
    {
      title: '人工成本',
      width: 110,
      dataIndex: 'laborCost',
      search: false,
      valueType: 'money',
    },
    {
      title: '利润比',
      width: 110,
      dataIndex: 'profitRatio',
      search: false,
      valueType: 'percent',
      sorter: true,
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.ProjectDeliveryResp>
        {...defaultTableConfig}
        rowKey="id"
        scroll={{ x: '100%' }}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="售后项目交付"
        request={async (params, sort) => {
          const {
            current,
            pageSize,
            projectNumber,
            projectName,
            clientAbbreviation,
            projectMangerId,
            salePersonId,
            executeDepartment,
            status,
          } = params;

          const sortProfitRatio = sort && sort?.profitRatio ? sortMap[sort.profitRatio] : undefined;

          // 更新分页信息
          setPagingParams({ current: current as number, pageSize });

          const extra = {
            projectNumber,
            projectName,
            clientAbbreviation,
            projectMangerId,
            salePersonId,
            executeDepartmentId: executeDepartment,
            status,
          };

          // 移除空值
          for (const [key, value] of Object.entries(extra)) {
            if (value === undefined || value === '' || value === null) {
              Reflect.deleteProperty(extra, key);
            }
          }

          const res = await projectDeliverySh({
            pageNum: current,
            pageSize,
            extra,
            sortProfitRatio,
          });
          return {
            data: res.data?.records || [],
            total: res.data?.total || 0,
            success: res.code === 200,
          };
        }}
      />
    </PageContainer>
  );
};

export default ProjectDelivery;
