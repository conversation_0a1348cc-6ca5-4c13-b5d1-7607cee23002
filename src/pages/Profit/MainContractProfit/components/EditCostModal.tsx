import { profitMainContractEdit } from '@/services/oa/profit';
import {
  ModalForm,
  ModalFormProps,
  ProFormDigit,
  ProFormInstance,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import React, { useRef } from 'react';

interface EditCostModalProps extends ModalFormProps {
  record?: API.ContractProfitResp;
  onSuccess?: () => void;
}

const EditCostModal: React.FC<EditCostModalProps> = ({ record, open, onOpenChange, onSuccess }) => {
  const formRef = useRef<ProFormInstance>();

  const { run: editCost, loading } = useRequest(
    (params: API.MainCostEditReq) => profitMainContractEdit(params),
    {
      manual: true,
      onSuccess: (result) => {
        if (result.code === 200) {
          message.success('编辑成功');
          onSuccess?.();
          return true;
        } else {
          message.error(result.message || '编辑失败');
          return false;
        }
      },
      onError: () => {
        message.error('操作失败');
        return false;
      },
      formatResult: (res) => res,
    },
  );

  return (
    <ModalForm
      title="编辑"
      formRef={formRef}
      open={open}
      width="25%"
      onOpenChange={onOpenChange}
      onFinish={async (values) => {
        if (!record?.id) {
          message.error('记录ID不存在');
          return false;
        }

        const params: API.MainCostEditReq = {
          id: record.id,
          fundCost: values.fundCost,
          feesCost: values.feesCost,
        };

        editCost(params);
        return true;
      }}
      modalProps={{
        destroyOnClose: true,
      }}
      initialValues={{
        fundCost: record?.fundCost,
        feesCost: record?.feesCost,
      }}
      submitter={{
        submitButtonProps: {
          loading: loading,
        },
      }}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
          gap: '16px',
        }}
      >
        <ProFormDigit
          name="fundCost"
          label="资金成本"
          placeholder="请输入资金成本"
          min={0.01}
          width="xl"
          fieldProps={{
            precision: 2,
          }}
          rules={[
            {
              required: true,
              message: '请输入资金成本',
            },
            {
              type: 'number',
              min: 0.01,
              message: '资金成本必须大于0',
            },
          ]}
        />
        <ProFormDigit
          name="feesCost"
          label="费用成本"
          placeholder="请输入费用成本"
          min={0.01}
          width="xl"
          fieldProps={{
            precision: 2,
          }}
          rules={[
            {
              required: true,
              message: '请输入费用成本',
            },
            {
              type: 'number',
              min: 0.01,
              message: '费用成本必须大于0',
            },
          ]}
        />
      </div>
    </ModalForm>
  );
};

export default EditCostModal;
