import { CONTRACT_STATUS, IS_ADVANCE } from '@/enums';
import { useUserList } from '@/hooks/useUserList';
import { profitMainContract } from '@/services/oa/profit';
import { option2enum, sortMap } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProTable,
  StatisticCard,
} from '@ant-design/pro-components';
import { Access, history, useAccess } from '@umijs/max';
import { DefaultOptionType } from 'antd/es/select';
import React, { useRef, useState } from 'react';
import EditCostModal from './components/EditCostModal';

const MainContractProfit: React.FC = () => {
  const { canEditProfitMainContract = false } = useAccess();
  const tableRef = useRef<ActionType | undefined>();
  const initialPageSize = defaultTableConfig.pagination?.pageSize ?? 10;
  const [pagingParams, setPagingParams] = useState({ current: 1, pageSize: initialPageSize });
  const { userList = [], loading: userLoading } = useUserList(true);

  // 编辑模态框状态
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<API.ContractProfitResp | undefined>();

  // 垫资总额
  const [totalAdvanceFund, setTotalAdvanceFund] = useState<number>(0);

  // 打开编辑模态框
  const handleEdit = (record: API.ContractProfitResp) => {
    setCurrentRecord(record);
    setEditModalVisible(true);
  };

  // 表格列定义
  const columns: ProColumns<API.ContractProfitResp>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      search: false,
      width: 80,
      render: (_, __, index) => (pagingParams.current - 1) * pagingParams.pageSize + index + 1,
    },
    {
      title: '合同号',
      dataIndex: 'contractNumber',
      copyable: true,
      width: 150,
      ellipsis: true,
      render(dom, entity) {
        return (
          <a className="rk-a-span" onClick={() => history.push(`/contract/main/edit/${entity.id}`)}>
            {dom}
          </a>
        );
      },
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
      ellipsis: true,
      width: 200,
      search: false,
    },
    {
      title: '销售',
      width: 100,
      dataIndex: 'salePerson',
      valueType: 'select',
      fieldProps: {
        loading: userLoading,
        fieldNames: {
          value: 'username',
          label: 'username',
        },
        showSearch: true,
        options: userList as DefaultOptionType[],
        filterOption: true,
        optionFilterProp: 'label',
      },
    },
    {
      title: '是否垫资',
      dataIndex: 'hasAdvanceFund',
      valueType: 'select',
      width: 100,
      valueEnum: option2enum(IS_ADVANCE),
    },
    {
      title: '合同状态',
      dataIndex: 'contractStatus',
      valueType: 'select',
      width: 80,
      initialValue: 'EXECUTING',
      valueEnum: option2enum(CONTRACT_STATUS),
    },
    {
      title: '客户简称',
      dataIndex: 'customerAbbreviation',
      ellipsis: true,
      width: 120,
    },
    {
      title: '合同金额',
      width: 120,
      dataIndex: 'contractAmount',
      search: false,
      valueType: 'money',
    },
    {
      title: '垫资金额',
      width: 120,
      dataIndex: 'advanceAmount',
      valueType: 'money',
      hideInSearch: true,
    },
    {
      title: '已收款',
      width: 120,
      dataIndex: 'amountCollected',
      search: false,
      valueType: 'money',
    },
    {
      title: '采购成本',
      width: 120,
      dataIndex: 'purchaseCost',
      search: false,
      valueType: 'money',
    },
    {
      title: '缴税成本',
      width: 120,
      dataIndex: 'taxCost',
      search: false,
      valueType: 'money',
    },
    {
      title: '售前项目成本',
      width: 120,
      dataIndex: 'preCost',
      search: false,
      valueType: 'money',
    },
    {
      title: '销售项目成本',
      width: 120,
      dataIndex: 'saleCost',
      search: false,
      valueType: 'money',
    },
    {
      title: '交付成本',
      width: 120,
      dataIndex: 'deliveryCost',
      search: false,
      valueType: 'money',
    },
    {
      title: '资金成本',
      width: 120,
      dataIndex: 'fundCost',
      search: false,
      valueType: 'money',
    },
    {
      title: '费用成本',
      width: 120,
      dataIndex: 'feesCost',
      search: false,
      valueType: 'money',
    },
    {
      title: '利润',
      width: 120,
      dataIndex: 'profit',
      search: false,
      valueType: 'money',
    },
    {
      title: '利润比',
      width: 100,
      dataIndex: 'profitRatio',
      search: false,
      sorter: true,
      render: (_, entity) => {
        return entity?.profitRatio + '%' || '-';
      },
    },
    {
      title: '操作',
      width: 80,
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      align: 'center',
      render: (_, record) => (
        <Access accessible={canEditProfitMainContract}>
          <a key="edit" onClick={() => handleEdit(record)}>
            编辑
          </a>
        </Access>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <StatisticCard.Group style={{ marginBottom: 16 }}>
        <StatisticCard
          statistic={{
            title: '垫资总金额',
            value: totalAdvanceFund ?? 0,
            prefix: '￥',
            status: 'processing',
          }}
        />
      </StatisticCard.Group>
      <ProTable<API.ContractProfitResp>
        {...defaultTableConfig}
        rowKey="id"
        scroll={{ x: '100%' }}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="主合同利润报表"
        request={async (params, sort) => {
          const {
            current,
            pageSize,
            contractNumber,
            customerAbbreviation,
            salePerson,
            contractStatus,
            hasAdvanceFund,
          } = params;

          const sortProfitRatio = sort && sort?.profitRatio ? sortMap[sort.profitRatio] : undefined;

          // 更新分页信息
          setPagingParams({ current: current as number, pageSize });

          const search = {
            contractNumber,
            customerAbbreviation,
          };

          const filter = {
            salePerson,
            contractStatus,
          };

          const extra = {
            hasAdvanceFund,
          };

          for (const [key, value] of Object.entries(search)) {
            if (value === undefined || value === '' || value === null) {
              Reflect.deleteProperty(search, key);
            }
          }
          for (const [key, value] of Object.entries(filter)) {
            if (value === undefined || value === '' || value === null) {
              Reflect.deleteProperty(filter, key);
            }
          }
          for (const [key, value] of Object.entries(extra)) {
            if (value === undefined || value === '' || value === null) {
              Reflect.deleteProperty(extra, key);
            }
          }

          const res = await profitMainContract({
            pageNum: current,
            pageSize,
            search,
            filter,
            extra,
            sortProfitRatio,
          });

          setTotalAdvanceFund(res?.data?.advanceAmountCount ?? 0);

          return {
            data: res.data?.records || [],
            total: res.data?.total || 0,
            success: res.code === 200,
          };
        }}
      />

      {/* 编辑费用成本模态框 */}
      <EditCostModal
        open={editModalVisible}
        onOpenChange={setEditModalVisible}
        record={currentRecord}
        onSuccess={() => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default MainContractProfit;
