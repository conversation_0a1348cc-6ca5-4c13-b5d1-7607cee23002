import { Modal } from 'antd';
import type { PDFDocumentProxy } from 'pdfjs-dist';
import { FC, useEffect, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';
import styles from './index.less';

// 设置PDF.js的worker路径
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/legacy/build/pdf.worker.min.js',
  import.meta.url,
).toString();

const PDFPreviewModal: FC<{
  previewOpen: boolean;
  setPreviewOpen: (val: boolean) => void;
  previewUrl: string;
}> = ({ previewOpen, setPreviewOpen, previewUrl }) => {
  const [numPages, setNumPages] = useState<number>(0);
  const [pageWidth, setPageWidth] = useState(800);
  const onDocumentLoadSuccess = ({ numPages: nextNumPages }: PDFDocumentProxy) => {
    setNumPages(nextNumPages);
  };

  useEffect(() => {
    const updatePageWidth = () => {
      const newPageWidth = window.innerWidth * 0.8;
      setPageWidth(newPageWidth);
    };

    // 模态框打开时更新页面宽度
    if (previewOpen) {
      updatePageWidth();
    }

    // 监听窗口大小变化
    window.addEventListener('resize', updatePageWidth);

    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', updatePageWidth);
    };
  }, [previewOpen]);

  return (
    <Modal
      className={styles.modal}
      width="auto"
      open={previewOpen}
      onCancel={() => setPreviewOpen(false)}
      footer={null} // 不显示默认的底部按钮
      centered
    >
      <Document
        file={previewUrl}
        onLoadSuccess={onDocumentLoadSuccess}
        // options={{
        //   cMapUrl: 'cmaps/',
        //   cMapPacked: true,
        // }}
      >
        {numPages > 0
          ? Array.from(new Array(numPages), (el, index) => (
              <Page key={`page_${index + 1}`} pageNumber={index + 1} width={pageWidth} />
            ))
          : null}
      </Document>
    </Modal>
  );
};

export default PDFPreviewModal;
