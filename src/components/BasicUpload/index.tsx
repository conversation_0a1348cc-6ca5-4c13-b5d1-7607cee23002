import PDFPreviewModal from '@/components/PDFPreviewModal';
import { BASE_URL } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { message, Modal, Upload, UploadFile, UploadProps } from 'antd';

import React, { forwardRef, useImperativeHandle, useState } from 'react';

export type UploadParams = {
  /** 上传的文件类型 */
  fileType?: string;
  /** 上传的文件大小 */
  fileSize?: number;
  /** 上传的文件数量 */
  fileCount?: number;
  /** 是否支持多选文件 */
  multiple?: boolean;
};
export interface UploaderRef {
  fileList: UploadFile[];
}
type BasicUploadProps = UploadParams & {
  ref?: React.Ref<any>;
};

const BasicUpload: React.ForwardRefRenderFunction<UploaderRef, BasicUploadProps> = (
  {
    fileType = '.jpeg,.jpg,.png,image/jpeg,image/jpg,image/png,application/pdf',
    fileSize = 20 * 1024 * 1024,
    fileCount = 5,
    multiple = false,
  },
  ref,
) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [currentFileType, setCurrentFileType] = useState<string>(''); //当前文件的格式
  const [previewImage, setPreviewImage] = useState('');

  useImperativeHandle(ref, () => ({
    fileList,
  }));

  //pdf或图片预览
  const handlePreview = async (file: UploadFile) => {
    const { type = '', thumbUrl } = file;
    setPreviewOpen(true);
    setCurrentFileType(type);
    setPreviewImage(file.response?.data || thumbUrl);
  };

  //文件列表变动时回调
  const handleChange: UploadProps['onChange'] = ({ file: file, fileList: newFileList }) => {
    const { response } = file;
    if (file.status === 'done') {
      const { code } = response;
      if (code === 200) {
        message.success('上传成功!');
      } else {
        message.error('上传失败!');
      }
    }
    if (file?.status === 'error') {
      message.error('上传失败!');
    }

    //使onPreview能够使用
    const url = file.response?.data;
    file.thumbUrl = file.response ? url : file.thumbUrl;
    // 过滤掉文件大小超过限制的文件
    const filteredFiles = newFileList.filter((file) => file.status);
    setFileList(filteredFiles);
  };

  const beforeUpload: UploadProps['beforeUpload'] = (file) => {
    return new Promise((resolve, reject) => {
      if (file.size > fileSize) {
        message.error(`文件大小超过限制（最大${fileSize / 1024 / 1024}MB）`);
        reject(); // 阻止文件上传并拒绝 Promise
      } else {
        resolve(); // 允许文件上传
      }
    });
  };

  const handleCancel = () => setPreviewOpen(false);

  return (
    <>
      <Upload
        action={`${BASE_URL}/api/common/file/upload`}
        listType="picture-card"
        accept={fileType}
        headers={{ Authorization: localStorage.getItem('RKLINK_OA_TOKEN')! }}
        method="post"
        fileList={fileList}
        onPreview={handlePreview}
        onChange={handleChange}
        maxCount={fileCount}
        beforeUpload={beforeUpload}
        multiple={multiple}
        onRemove={(file) => {
          const filteredFiles = fileList.filter((i) => i.uid !== file.uid);
          setFileList(filteredFiles);
          return false;
        }}
      >
        <div>
          <PlusOutlined />
          <div style={{ marginTop: 8 }}>上传附件</div>
        </div>
      </Upload>
      {currentFileType === 'pdf' || previewImage.includes('pdf') ? (
        <PDFPreviewModal
          setPreviewOpen={setPreviewOpen}
          previewOpen={previewOpen}
          previewUrl={previewImage}
        />
      ) : (
        <Modal width="65vw" open={previewOpen} title={null} footer={null} onCancel={handleCancel}>
          <div style={{ maxHeight: '70vh', overflow: 'auto', textAlign: 'center' }}>
            <img
              alt="example"
              style={{
                width: '100%',
                objectFit: 'fill',
              }}
              src={previewImage}
            />
          </div>
        </Modal>
      )}
    </>
  );
};

export default forwardRef<UploaderRef, BasicUploadProps>(BasicUpload);
