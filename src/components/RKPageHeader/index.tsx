import { APPROVAL_STATUS } from '@/enums';
import BaseTemplate from '@/pages/Print/template/BaseTemplate';
import CustomerServiceReport1 from '@/pages/Print/template/CustomerServiceReport1';
import CustomerServiceReport2 from '@/pages/Print/template/CustomerServiceReport2';
import ExpenseAccountTemplate from '@/pages/Print/template/ExpenseAccountTemplate';
import { PrintProps } from '@/pages/Print/typings';
import PrintTemplate from '@/pages/Sale/PerformanceResult/components/PrintTemplate';
import { agree, getProcessInstancesRank, refuse, reject, startWorkFlow } from '@/services/oa/flow';
import { getRandomId, option2enum } from '@/utils';
import { ArrowDownOutlined, ArrowUpOutlined, PrinterOutlined } from '@ant-design/icons';
import {
  ModalForm,
  PageHeader as ProPageHeader,
  PageHeaderProps as ProPageHeaderProps,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Link, useLocation, useModel, useRequest } from '@umijs/max';
import { Button, Dropdown, MenuProps, message, Space, Tag, Tooltip } from 'antd';
import React, { createRef, useCallback, useRef, useState } from 'react';
import ReactToPrint from 'react-to-print';
import RKApproveSteps from '../RKApproveSteps';

const findAdjacentInArray = (
  arr: string[],
  target: string,
  direction: 'next' | 'prev',
): string | undefined => {
  const index = arr.indexOf(target);

  // 确保目标值存在于数组中
  if (index === -1) {
    return undefined;
  }

  // 根据方向计算偏移量
  const offset = direction === 'next' ? 1 : -1;

  // 获取目标值的上一个或下一个索引
  const adjacentIndex = index + offset;

  // 检查新索引是否在数组范围内
  if (adjacentIndex >= 0 && adjacentIndex < arr.length) {
    return arr[adjacentIndex];
  } else {
    return undefined; // 如果超出范围，则返回 undefined
  }
};

type PageHeaderProps = ProPageHeaderProps & {
  /**
   * @name 审核状态 与 tags 互斥
   */
  status?: string;
  /**
   * @name 打印配置
   */
  print?: {
    /**
     * @name 打印数据
     */
    printData?: PrintProps | Record<string, any>;
    /**
     * 打印类型
     */
    printType?: string;
  };
  /**
   * 头部的页面类型
   */
  pageHeaderType?: 'drawer' | 'page';
  /**
   * @name 审批类型
   */
  approveType?: string;
  id?: string;
  /**
   * @name 操作回调callback
   */
  onOperationCallback?: () => void;
  onSave?: () => Promise<any>;
  onSubmit?: () => void;
  saveLoading?: boolean;
  saveDisabled?: boolean;
  //  是否通自动审核通过 0 非自动 1 自动
  hasAutoActivi?: string;
};
const RKPageHeader: React.FC<PageHeaderProps> = ({
  extra,
  status = '',
  print,
  pageHeaderType = 'page',
  approveType,
  id = '',
  onOperationCallback,
  title,
  onSave,
  saveDisabled = false,
  saveLoading,
  onSubmit,
  hasAutoActivi,
  ...restProps
}) => {
  const { initialState } = useModel('@@initialState');
  const { approvalDetails } = useModel('useApprovalModel');
  const [modalVisit, setModalVisit] = useState(false);
  const approvalType = useRef('');
  const { currentUser } = initialState || {};
  // 随机数
  const cacheKey = useRef<string>();
  // 创建一个包含 ref 的组件引用
  const componentRef = createRef<HTMLDivElement>();
  const componentRef1 = createRef<HTMLDivElement>();
  const componentRef2 = createRef<HTMLDivElement>();

  // 判断是否显示审批按钮页面
  const { pathname } = useLocation();
  const isApprovalPage = pathname.includes('/approval/');
  // 打印相关

  const Print = () => {
    if (!print) return <></>;
    if (print?.printType === 'csr') {
      const documentTitle = `${(print?.printData as Record<string, any>)?.documentNumber}(${
        (print?.printData as Record<string, any>)?.startTime?.split(' ')?.[0]
      }_${(print?.printData as Record<string, any>)?.endTime?.split(' ')?.[0]})`;
      const items: MenuProps['items'] = [
        {
          key: 'csr1',
          label: (
            <ReactToPrint
              trigger={() => <a>客户现场服务报告单</a>} // 触发打印的按钮
              content={() => componentRef1.current} // 引用要打印的组件
              documentTitle={`客户现场服务报告单-${documentTitle}`}
              pageStyle={`@page { size: A4; margin: 14mm 12mm; color: #333; } @media print { body { -webkit-print-color-adjust: exact;  }`}
            />
          ),
        },
        {
          key: 'csr2',
          label: (
            <ReactToPrint
              trigger={() => <a>客户咨询服务报告单</a>} // 触发打印的按钮
              documentTitle={`客户咨询服务报告单${documentTitle}`}
              content={() => componentRef2.current} // 引用要打印的组件
              pageStyle={`@page { size: A4; margin: 14mm 12mm; color: #333; } @media print { body { -webkit-print-color-adjust: exact;  }`}
            />
          ),
        },
      ];

      return (
        <Dropdown menu={{ items }} placement="bottom" arrow disabled={false}>
          <Button type="dashed" disabled={false} icon={<PrinterOutlined />}>
            打印
          </Button>
        </Dropdown>
      );
    }
    if (print?.printType === 'performance') {
      return (
        <ReactToPrint
          trigger={() => (
            <Button type="dashed" icon={<PrinterOutlined />} disabled={false}>
              打印
            </Button>
          )} // 触发打印的按钮
          documentTitle={`${
            (print?.printData as Record<string, any>)?.receiveYear
          }年度销售业绩考核表-${(print?.printData as Record<string, any>)?.salePerson}`}
          content={() => componentRef.current}
          pageStyle={`@page { size: landscape; margin: 14mm 12mm; color: #333; } @media print { body { -webkit-print-color-adjust: exact;  }`}
        />
      );
    }
    return (
      <ReactToPrint
        trigger={() => (
          <Button type="dashed" icon={<PrinterOutlined />} disabled={false}>
            打印
          </Button>
        )} // 触发打印的按钮
        documentTitle={`${(print?.printData as Record<string, any>)?.documentNumber}`}
        content={() => componentRef.current} // 引用要打印的组件
        pageStyle={`@page { size: A4; margin: 14mm 12mm; color: #333; } @media print { body { -webkit-print-color-adjust: exact;  }`}
      />
    );
  };
  // 状态显示
  const CustomStatus = () => {
    const valueEnum = option2enum(APPROVAL_STATUS);
    const value = valueEnum[status];
    if (!value?.text) return <Tag>未知</Tag>;
    if (status === '2' && hasAutoActivi === '1') return <Tag color="#87d068">自动通过</Tag>;
    return <Tag color={value?.status}>{value?.text}</Tag>;
  };

  const onSuccess = (res: Record<string, any>) => {
    if (res.code === 200) {
      message.success('操作成功');
      onOperationCallback?.();
      cacheKey.current = getRandomId();
    }
  };

  // 提交流程
  const { run: submit, loading: submitLoading } = useRequest(
    () =>
      startWorkFlow({
        businessKey: id,
        type: approveType,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        onSuccess(res);
        if (res?.code === 200) {
          onSubmit?.();
        }
      },
      formatResult: (res) => res,
      debounceInterval: 1000,
    },
  );
  // 通过流程
  const { run: runAgree, loading: agreeLoading } = useRequest(
    () =>
      agree({
        processInstanceId: id,
      }),
    {
      manual: true,
      onSuccess,
      formatResult: (res) => res,
    },
  );
  // 拒绝流程
  const { run: runRefuse, loading: refuseLoading } = useRequest(
    (comments) =>
      refuse({
        processInstanceId: id,
        type: approveType,
        comments,
      }),
    {
      manual: true,
      onSuccess,
      formatResult: (res) => res,
    },
  );
  // 驳回流程
  const { run: reset, loading: resetLoading } = useRequest(
    (comments) =>
      reject({
        processInstanceId: id,
        type: approveType,
        comments,
      }),
    {
      manual: true,
      onSuccess,
      formatResult: (res) => res,
    },
  );

  const ApprovalBtn = () => {
    if (status === '0') {
      return (
        <Button
          type="primary"
          onClick={async () => {
            const res = await onSave?.();

            if (res !== 200) return;
            submit();
          }}
          loading={saveLoading || submitLoading}
          disabled={saveDisabled}
        >
          保存并提交
        </Button>
      );
    }

    if (
      isApprovalPage &&
      approvalDetails?.currentAudiUsers?.includes(currentUser?.id as string) &&
      status === '1'
    ) {
      return (
        <Space>
          <Button
            type="primary"
            onClick={() => {
              runAgree();
            }}
            loading={agreeLoading}
            disabled={false}
          >
            通过
          </Button>
          {/* 如果是合同付款的审批，不允许拒绝 */}
          {approveType !== 'CONTRACT_PROCUREMENT_PAYMENT_APPLICATION' && (
            <Button
              type="primary"
              ghost
              onClick={() => {
                approvalType.current = 'refuse';
                setModalVisit(true);
              }}
              loading={refuseLoading}
              disabled={false}
            >
              拒绝
            </Button>
          )}
          <Button
            ghost
            type="primary"
            onClick={() => {
              approvalType.current = 'reset';
              setModalVisit(true);
            }}
            loading={resetLoading}
            disabled={false}
          >
            驳回
          </Button>
        </Space>
      );
    }

    return <></>;
  };

  // 获取未处理流程
  const { data: unprocessedList } = useRequest(
    () =>
      getProcessInstancesRank({
        type: approveType,
      }),
    {
      refreshDeps: [approveType, id],
    },
  );
  const Jump = useCallback(() => {
    if (!isApprovalPage) return <></>;
    const ids = unprocessedList?.map((item) => item.processInstanceId!) || [];
    const prev = findAdjacentInArray(ids, id, 'prev');
    const next = findAdjacentInArray(ids, id, 'next');
    const otherIds = ids.filter((item) => item !== id);
    const other = otherIds.at(0);
    const url = pathname.replace(id, '');

    return (
      <Space>
        <Link to={prev ? `${url}${prev}` : ''} prefetch reloadDocument>
          <Tooltip title="上一条审批">
            <Button type="primary" disabled={!prev} icon={<ArrowUpOutlined />} />
          </Tooltip>
        </Link>
        <Link to={next ? `${url}${next}` : ''} prefetch reloadDocument>
          <Tooltip title="下一条审批">
            <Button type="primary" disabled={!next} icon={<ArrowDownOutlined />} />
          </Tooltip>
        </Link>
        <Link to={!prev && !next && other ? `${url}${other}` : ''} prefetch reloadDocument>
          <Tooltip title="下一条审批">
            <Button type="primary" disabled={!other} icon={<ArrowDownOutlined />} />
          </Tooltip>
        </Link>
      </Space>
    );
  }, [unprocessedList, id]);

  return (
    <>
      <ProPageHeader
        className={!print && !title && !status ? 'rk-none' : ''}
        title={title}
        style={
          pageHeaderType === 'page'
            ? {
                padding: '16px 0 24px 0',
              }
            : { padding: '0 0 24px 0' }
        }
        tags={status ? <CustomStatus /> : []}
        extra={
          <Space>
            <Print />
            <ApprovalBtn />
            <Jump />
            {extra}
          </Space>
        }
        {...restProps}
        footer={
          status &&
          status !== '0' && (
            <RKApproveSteps
              id={id!}
              direction={pageHeaderType === 'drawer' ? 'vertical' : 'horizontal'}
              cacheKey={cacheKey.current}
            />
          )
        }
      />

      {print?.printType === 'base' && (
        <div className="rk-none">
          <BaseTemplate {...(print?.printData as PrintProps)} ref={componentRef} />
        </div>
      )}

      {print?.printType === 'reimbursement' && (
        <div className="rk-none">
          <ExpenseAccountTemplate
            ref={componentRef}
            data={print?.printData as Record<string, any>}
          />
        </div>
      )}

      {print?.printType === 'csr' && (
        <div className="rk-none">
          <CustomerServiceReport1
            ref={componentRef1}
            data={print?.printData as Record<string, any>}
          />
          <CustomerServiceReport2
            ref={componentRef2}
            data={print?.printData as Record<string, any>}
          />
        </div>
      )}

      {print?.printType === 'performance' && (
        <div className="rk-none">
          <PrintTemplate
            ref={componentRef}
            data={print.printData as Record<string, any> & { receiveYear: string }}
          />
        </div>
      )}

      <ModalForm<{
        comments: string;
      }>
        width={400}
        title={approvalType.current === 'reset' ? '驳回意见' : '拒绝意见'}
        modalProps={{
          destroyOnClose: true,
        }}
        disabled={false}
        open={modalVisit}
        onOpenChange={setModalVisit}
        onFinish={async (values) => {
          if (approvalType.current === 'reset') {
            reset(values.comments);
          } else {
            runRefuse(values.comments);
          }
          return true;
        }}
      >
        <ProFormTextArea
          name="comments"
          fieldProps={{
            autoSize: {
              minRows: 4,
              maxRows: 6,
            },
            maxLength: 20,
          }}
        />
      </ModalForm>
    </>
  );
};
export default RKPageHeader;
