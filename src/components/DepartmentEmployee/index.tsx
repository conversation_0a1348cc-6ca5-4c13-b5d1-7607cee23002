import { userDropDownList } from '@/services/oa/auth';
import { getDepartmentWeeklyTree } from '@/services/oa/department';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Modal } from 'antd';
import React, { useRef, useState } from 'react';

interface Props {
  title: string;
  open: boolean;
  onFinish: (selectedRows: API.UserPageResp[]) => void;
  close: () => void;
}

const DepartmentEmployee: React.FC<Props> = ({ title, open, onFinish, close }) => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.UserPageResp[]>([]);

  const { data: departmentTreeList = [], loading: departmentTreeLoading } = useRequest(
    () => getDepartmentWeeklyTree(),
    {
      formatResult: (res) =>
        res?.data?.map((i) => ({
          children: i.child?.map((chi) => ({
            value: chi.id,
            label: chi.departmentName,
          })),
          value: i.id,
          label: i.departmentName,
        })) || [],
    },
  );

  // 表格
  const columns: ProColumns<API.UserPageResp>[] = [
    {
      title: '编号',
      dataIndex: 'employeeNumber',
      copyable: true,
      ellipsis: true,
      width: 100,
      search: false,
    },
    {
      title: '姓名',
      dataIndex: 'employeeName',
    },
    {
      title: '部门',
      dataIndex: 'department',
      valueType: 'treeSelect',
      fieldProps: {
        loading: departmentTreeLoading,
        options: departmentTreeList,
      },
    },
  ];
  return (
    <Modal
      title={title || '请选择员工'}
      width={1000}
      open={open}
      onOk={() => onFinish(selectedRows)}
      onCancel={close}
    >
      <ProTable<API.UserPageResp>
        {...defaultTableConfig}
        actionRef={tableRef}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        columns={columns}
        headerTitle="员工列表"
        rowSelection={{
          type: 'radio',
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        request={async (params) => {
          const { department, employeeName } = params;
          const res = await userDropDownList({
            activation: true,
            departmentId: department,
            username: employeeName,
          });
          if (res.code === 200) {
            return {
              data: res?.data,
              success: true,
              total: res?.data?.length,
            };
          }
          return {
            data: [],
            success: true,
            total: 0,
          };

          // const newUserList = userList?.filter((i: API.UserPageResp) => {
          //   return i.username?.includes(employeeName) && i.departmentId === department;
          // });

          // const extra = { department };
          // const search = { employeeName };
          // const filter = { activation: '1' }; // 激活状态的用户
          // return queryPagingTable<API.PageReq>(
          //   { current, pageSize, extra, search, filter },
          //   userPage,
          // );
        }}
      />
    </Modal>
  );
};

export default DepartmentEmployee;
