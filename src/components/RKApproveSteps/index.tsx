import { getActiviInfo, getActiviInfoBusinessKey } from '@/services/oa/flow';
import { LoadingOutlined } from '@ant-design/icons';
import { useLocation, useModel, useRequest } from '@umijs/max';
import { Space, Steps, StepsProps, Tooltip, Typography } from 'antd';
import { memo, useEffect, useMemo } from 'react';
import styles from './index.less';
const { Text } = Typography;
export interface RKStepsProps extends StepsProps {
  id: string;
  cacheKey?: string;
}
const RKApproveSteps: React.FC<RKStepsProps> = ({ id, direction, cacheKey }) => {
  const { updateApprovalDetails, approvalDetails } = useModel('useApprovalModel');
  // 判断是否显示审批按钮页面
  const { pathname } = useLocation();
  const isApprovalPage = pathname.includes('/approval/');
  // 通过id查询审批详情
  const { run: getInfo } = useRequest(() => getActiviInfo({ id }), {
    manual: true,
    ready: !!id,
    onSuccess: (res) => {
      updateApprovalDetails(res);
    },
  });
  // 通过业务id查询审批详情
  const { run: getInfoByBusiness } = useRequest(() => getActiviInfoBusinessKey({ id }), {
    manual: true,
    ready: !!id,
    onSuccess: (res) => {
      updateApprovalDetails(res);
    },
  });

  useEffect(() => {
    if (isApprovalPage) {
      getInfo();
    } else {
      getInfoByBusiness();
    }
  }, [isApprovalPage, id, cacheKey]);

  useEffect(() => {
    // 清数据
    return () => {
      updateApprovalDetails({});
    };
  }, []);

  const customItems = useMemo(
    () =>
      approvalDetails?.audiRecordList?.map((item) => {
        const {
          title = '',
          status = '',
          userName = [],
          audiTime = '',
          // @ts-ignore
          comments = '',
        } = item;
        return {
          title,
          status,
          comments,
          description: (
            <Space direction="vertical" size={4}>
              {userName.join(',')}
              <span style={{ fontSize: 12 }}>{audiTime}</span>
              {comments && (
                <Tooltip title={comments}>
                  <Text ellipsis style={{ width: '100%' }} type="secondary">
                    原因：{comments}
                  </Text>
                </Tooltip>
              )}
            </Space>
          ),
          icon: status === 'process' ? <LoadingOutlined /> : '',
        };
      }),
    [approvalDetails],
  );
  return (
    <Steps
      items={customItems as StepsProps[]}
      className={styles.steps}
      size="small"
      direction={direction}
    />
  );
};

export default memo(RKApproveSteps);
