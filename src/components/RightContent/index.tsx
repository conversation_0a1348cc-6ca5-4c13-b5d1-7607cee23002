import { useEmotionCss } from '@ant-design/use-emotion-css';
import { useModel } from '@umijs/max';
import React from 'react';
import ApprovalIcon from './ApprovalIcon';
import Avatar from './AvatarDropdown';
import Date from './Date';

const GlobalHeaderRight: React.FC = () => {
  const className = useEmotionCss(() => {
    return {
      display: 'flex',
      height: '48px',
      minWidth: '100px',
      marginLeft: 'auto',
      overflow: 'hidden',
      gap: 8,
      alignItems: 'center',
    };
  });

  const { initialState } = useModel('@@initialState');

  if (!initialState || !initialState.settings) {
    return null;
  }

  return (
    <div className={className}>
      <ApprovalIcon />
      <Date />
      <Avatar />
    </div>
  );
};
export default GlobalHeaderRight;
