import { pendingApproval } from '@/services/oa/flow';
import { AuditOutlined, BellOutlined, MessageOutlined } from '@ant-design/icons';
import { history, useModel, useRequest } from '@umijs/max';
import { Badge, Dropdown, MenuProps, Space } from 'antd';
import { useMemo } from 'react';

export default function ApprovalIcon() {
  // 获取当前用户信息
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const showApproval = useMemo(
    () => currentUser?.pagePermissions?.includes('APPROVAL'),
    [currentUser],
  );
  const { data = {} } = useRequest(() => pendingApproval(), {
    ready: showApproval,
    pollingInterval: 5000,
  });

  const count = useMemo(
    () =>
      Object.values(data).reduce((accumulator, currentValue) => {
        return accumulator + currentValue;
      }, 0),
    [data],
  );

  const items: MenuProps['items'] = [
    {
      key: '1',
      disabled: !showApproval,
      label: (
        <Badge count={count} offset={[4, 0]} size="small" dot>
          <Space onClick={() => history.push('/approval/pending-approval')}>
            <AuditOutlined />
            <span>待审批</span>
          </Space>
        </Badge>
      ),
    },
    {
      key: '2',
      disabled: true,
      label: (
        <Space>
          <MessageOutlined />
          消息
        </Space>
      ),
    },
  ];

  return (
    <Dropdown menu={{ items }}>
      <Badge count={count} offset={[4, 0]} size="small">
        <BellOutlined style={{ fontSize: 18, color: 'rgba(0, 0, 0, 0.45)' }} />
      </Badge>
    </Dropdown>
  );
}
