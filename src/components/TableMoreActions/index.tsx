import { MoreOutlined } from '@ant-design/icons';
import { Popover, Space } from 'antd';
import PropTypes from 'prop-types';
import React, { useMemo } from 'react';

interface Action {
  isShow: boolean;
  component: React.ReactNode;
  id?: string; // 添加id以备key使用
}

interface Props {
  allActions: () => Action[];
  MAX_VISIBLE_ACTIONS?: number;
}

const defaultProps = {
  MAX_VISIBLE_ACTIONS: 3,
};

const TableMoreActions: React.FC<Props> = ({
  allActions,
  MAX_VISIBLE_ACTIONS = defaultProps.MAX_VISIBLE_ACTIONS,
}) => {
  // 可见按钮数量必须为正整数
  const effectiveMaxVisibleActions = MAX_VISIBLE_ACTIONS <= 0 ? 3 : MAX_VISIBLE_ACTIONS;

  const actionsList = useMemo(() => allActions(), [allActions]);
  const canShowActions = useMemo(
    () => actionsList.filter((action) => action.isShow),
    [actionsList],
  );
  const visibleActions = useMemo(
    () => canShowActions.slice(0, effectiveMaxVisibleActions),
    [canShowActions, effectiveMaxVisibleActions],
  );
  const dropdownActions = useMemo(
    () => canShowActions.slice(effectiveMaxVisibleActions),
    [canShowActions, effectiveMaxVisibleActions],
  );

  const renderAction = (action: Action) => action.component;

  return (
    <Space>
      {visibleActions.map((action, index) => (
        <React.Fragment key={action.id || index}>{renderAction(action)}</React.Fragment>
      ))}

      {dropdownActions.length > 0 && (
        <Popover
          placement="bottomRight"
          content={
            <Space direction="vertical">
              {dropdownActions.map((action, index) => (
                <React.Fragment key={action.id || index}>{renderAction(action)}</React.Fragment>
              ))}
            </Space>
          }
        >
          <MoreOutlined className="ant-btn-link" style={{ cursor: 'pointer' }} />
        </Popover>
      )}
    </Space>
  );
};

TableMoreActions.propTypes = {
  allActions: PropTypes.func.isRequired,
  MAX_VISIBLE_ACTIONS: PropTypes.number,
};

TableMoreActions.defaultProps = defaultProps;

export default React.memo(TableMoreActions);
