import { remove } from '@/services/oa/nextcloud';
import { BASE_URL, defaultTableConfig } from '@/utils/setting';
import { UploadOutlined } from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { request } from '@umijs/max';
import { Button, message, Modal, Space, Upload } from 'antd';
import * as React from 'react';
import { useState } from 'react';

interface DocumentUploadModuleProps {
  /** 文档列表数据 */
  documentFiles?: API.ObjectFIleVO[];
  /** 文件类型 */
  fileType: string;
  /** 关联ID */
  id?: string;
  /** 是否显示上传按钮 */
  showUploadButton?: boolean;
  /** 上传权限检查 */
  canUpload?: boolean;
  /** 标题 */
  title?: string;
  /** 上传成功后的回调函数 */
  onUploadSuccess?: () => void;
}

const DocumentUploadModule: React.FC<DocumentUploadModuleProps> = ({
  documentFiles = [],
  fileType,
  id,
  showUploadButton = true,
  canUpload = true,
  title = '',
  onUploadSuccess,
}) => {
  const [uploadLoading, setUploadLoading] = useState(false);

  // 文件上传配置
  const uploadProps = {
    name: 'file',
    action: `${BASE_URL}/api/nextcloud/upload`,
    headers: {
      Authorization: localStorage.getItem('RKLINK_OA_TOKEN')!,
    },
    data: {
      fileType,
      id,
    },
    accept: '.xlsx,.xls,.docx,.doc,.pdf,.md,.txt',
    showUploadList: false,
    beforeUpload: (file: any) => {
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
        'application/msword', // .doc
        'application/pdf', // .pdf
        'text/markdown', // .md
        'text/plain', // .txt
      ];

      if (!allowedTypes.includes(file.type)) {
        message.error('只支持上传 Excel、Word、PDF、Markdown、文本文件！');
        return false;
      }

      setUploadLoading(true);
      return true;
    },
    onChange: (info: any) => {
      if (info.file.status === 'done') {
        setUploadLoading(false);
        if (info.file.response?.code === 200) {
          message.success('文件上传成功！');
          onUploadSuccess?.();
        } else {
          message.error('文件上传失败！');
        }
      } else if (info.file.status === 'error') {
        setUploadLoading(false);
        message.error('文件上传失败！');
      }
    },
  };

  // 获取文件扩展名
  const getFileExtension = (fileName: string): string => {
    return fileName.split('.').pop()?.toLowerCase() || '';
  };

  // 判断是否为支持的文件类型
  const isSupportedFileType = (fileName: string): boolean => {
    const extension = getFileExtension(fileName);
    const supportedTypes = ['xlsx', 'xls', 'docx', 'doc', 'pdf', 'md', 'markdown', 'txt'];
    return supportedTypes.includes(extension);
  };

  // 获取文件的MIME类型
  const getMimeType = (fileName: string): string => {
    const extension = getFileExtension(fileName);
    const mimeTypes: Record<string, string> = {
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      xls: 'application/vnd.ms-excel',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      doc: 'application/msword',
      pdf: 'application/pdf',
      md: 'text/markdown',
      markdown: 'text/markdown',
      txt: 'text/plain',
    };

    return mimeTypes[extension] || 'application/octet-stream';
  };

  // 处理下载
  const handleDownload = async (record: API.ObjectFIleVO) => {
    try {
      if (!isSupportedFileType(record.name || '')) {
        message.warning('暂不支持该文件类型的下载');
        return;
      }

      const res = await request('/api/nextcloud/download', {
        method: 'GET',
        params: {
          path: record.path,
          type: 'file',
        },
        responseType: 'blob',
        getResponse: true,
        skipErrorHandler: true,
      });

      if (res.status === 200) {
        const mimeType = getMimeType(record.name || '');
        const url = URL.createObjectURL(new Blob([res.data], { type: mimeType }));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', record.name || 'download');

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        message.success('下载成功');
      } else {
        message.error('下载失败');
      }
    } catch (error) {
      console.error('下载失败:', error);
      message.error('下载失败');
    }
  };

  // 处理删除
  const handleDelete = async (record: API.ObjectFIleVO) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除文件 "${record.name}" 吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const res = await remove({ path: record.path || '' });
          if (res.code === 200) {
            message.success('删除成功');
            onUploadSuccess?.();
          } else {
            message.error('删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      },
    });
  };

  const columns = [
    {
      title: '文件名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: API.ObjectFIleVO) => {
        if (record.previewUrl) {
          return (
            <a
              onClick={() => {
                window.open(record.previewUrl, '_blank');
              }}
            >
              {text}
            </a>
          );
        }
        return text;
      },
    },
    {
      title: '文件类别',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '操作',
      key: 'action',
      hideInTable: !showUploadButton || !canUpload,
      width: 120,
      render: (_: any, record: API.ObjectFIleVO) => (
        <Space size="small">
          <Button disabled={false} type="link" size="small" onClick={() => handleDownload(record)}>
            下载
          </Button>
          <Button
            disabled={false}
            type="link"
            size="small"
            danger
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ marginBottom: 24 }}>
      <ProTable
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        className="inner-table"
        columns={columns as ProColumns<API.ObjectFIleVO>[]}
        dataSource={documentFiles}
        rowKey="path"
        search={false}
        pagination={false}
        options={false}
        toolBarRender={() => [
          showUploadButton && canUpload ? (
            <Upload disabled={false} key="upload" {...uploadProps}>
              <Button
                disabled={false}
                type="primary"
                icon={<UploadOutlined />}
                loading={uploadLoading}
              >
                上传文档
              </Button>
            </Upload>
          ) : null,
        ]}
        headerTitle={title}
      />
    </div>
  );
};

export default DocumentUploadModule;
