import Icon, { CustomIconComponentProps } from '@ant-design/icons/lib/components/Icon';
import { Link } from '@umijs/max';
import { Space } from 'antd';
import React from 'react';

const ArrowSvg = () => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    fill="currentColor"
  >
    <path d="M 223.361 656.774 H 501.401 V 368.029 H 223.361 v 288.745 Z M 3.3391 394.331 v 231.082 c 0 37.8764 25.5309 31.3608 25.5309 31.3608 h 122.984 V 368.029 H 24.7458 s -21.4067 -0.678536 -21.4067 26.3028 Z m 1007.51 90.0756 c -35.2839 -34.4771 -377.398 -287.565 -377.398 -287.565 s -39.4069 -38.9818 -39.4069 19.456 v 151.729 h -0.414226 v 288.746 h 0.055993 v 161.675 c 0 47.9112 38.0065 6.25133 38.0065 6.25133 l 376.25 -291.311 c -0.000602 0.001204 28.6214 -23.9553 2.90681 -48.9811 Z m 0 0"></path>
  </svg>
);
export const ArrowIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={ArrowSvg} {...props} />
);
const TitleLink: React.FC<{
  children: React.ReactNode;
  path?: string;
}> = ({ children, path }) => {
  return (
    <Space>
      {children}
      {path && (
        <Link to={path}>
          <ArrowIcon />
        </Link>
      )}
    </Space>
  );
};

export default TitleLink;
