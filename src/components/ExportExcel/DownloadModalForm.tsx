import { requiredRule } from '@/utils/setting';
import {
  ModalForm,
  ModalFormProps,
  ProFormDateRangePicker,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { request } from '@umijs/max';
import { message } from 'antd';
import { TimeRangePickerProps } from 'antd/es/time-picker';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';

const DownloadModalForm: React.FC<ModalFormProps & { type: string }> = ({
  open,
  onOpenChange,
  type,
}) => {
  const formRef = useRef<ProFormInstance>();
  const [btnLoading, setBtnLoading] = useState<boolean>(false);

  //自定义预设时间范围快捷选择
  const weekRangePresets: TimeRangePickerProps['presets'] = [
    {
      label: '上周',
      value: [
        dayjs().add(-1, 'week').startOf('week').add(1, 'day'),
        dayjs().add(-1, 'week').endOf('week').add(1, 'day'),
      ],
    },
    {
      label: '本周',
      value: [dayjs().startOf('week').add(1, 'day'), dayjs().endOf('week').add(1, 'day')],
    },
    {
      label: '本月',
      value: [dayjs().startOf('month'), dayjs().endOf('month')],
    },
    {
      label: '近三个月',
      value: [dayjs().subtract(3, 'month'), dayjs()],
    },
    {
      label: '近半年',
      value: [dayjs().subtract(6, 'month'), dayjs()],
    },
  ];
  const contractRangePresets: TimeRangePickerProps['presets'] = [
    { label: '本月', value: [dayjs().startOf('M'), dayjs().endOf('M')] },
    { label: '本季度', value: [dayjs().startOf('quarter'), dayjs().endOf('quarter')] },
    { label: '本年', value: [dayjs().startOf('y'), dayjs().endOf('y')] },
    { label: '近三个月', value: [dayjs().subtract(3, 'month'), dayjs()] },
    { label: '近半年', value: [dayjs().subtract(6, 'month'), dayjs()] },
    { label: '近1年', value: [dayjs().subtract(1, 'year'), dayjs()] },
    { label: '近2年', value: [dayjs().subtract(2, 'year'), dayjs()] },
  ];

  return (
    <ModalForm
      width="auto"
      title="导出"
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        setBtnLoading(true);
        const { startTime, endTime } = value;
        let path = '',
          filename = '';
        switch (type) {
          case 'weekly-report':
            path = '/api/week/week/attendance-export';
            filename = `${startTime}至${endTime}周报`;
            break;
          case 'contract-main':
            path = '/api/contract/export-main';
            filename = `${startTime}至${endTime}主合同台账`;
            break;
          case 'contract-internal':
            path = '/api/contract/export-inner';
            filename = `${startTime}至${endTime}内部合同台账`;
            break;
          default:
            path = '/api/contract/export-pur';
            filename = `${startTime}至${endTime}采购合同台账`;
        }
        const res = await request(`${path}`, {
          params: value,
          method: 'get',
          responseType: 'blob',
          getResponse: true,
          skipErrorHandler: true,
        })
          .then((response) => {
            const url = URL.createObjectURL(
              new Blob([response.data], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              }),
            );
            const link = document.createElement('a');
            link.style.display = 'none';
            link.href = url;
            link.setAttribute('download', filename); // 指定下载的文件名和类型
            document.body.appendChild(link);
            link.click();
            setBtnLoading(false);
            return response.status;
          })
          .catch(() => {
            message.error('导出失败');
            setBtnLoading(false);
          });
        return res === 200;
      }}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        centered: true,
      }}
      submitter={{
        submitButtonProps: {
          loading: btnLoading,
        },
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
      </div>
      <ProFormDateRangePicker
        width="md"
        className="ant-picker"
        name="Time"
        label="日期"
        rules={[requiredRule]}
        fieldProps={{
          presets: type === 'weekly-report' ? weekRangePresets : contractRangePresets,
        }}
        transform={(value) => ({ startTime: value?.at(0), endTime: value?.at(1) })}
      />
    </ModalForm>
  );
};

export default DownloadModalForm;
