import { dictionaryList } from '@/services/oa/operationRecord';
import { useToken } from '@ant-design/pro-components';
import { useLocation, useParams, useRequest } from '@umijs/max';
import { Collapse, Space, Spin, Tag, Timeline, Typography } from 'antd';
import React, { memo, useMemo } from 'react';

const { Text } = Typography;
const ChangeLog: React.FC = () => {
  const { id = '' } = useParams();
  // 判断是否是审批页面
  const { pathname } = useLocation();
  const isApprovalPage = pathname.includes('/approval/');
  const { token } = useToken();
  const { data, loading } = useRequest(() => dictionaryList({ req: { id } }), {
    ready: !!id,
  });

  const items = useMemo(
    () =>
      data?.map((item, index) => {
        const { username = '', createdDate = '', action = '', content = '' } = item;
        const children = (
          <>
            <div>
              <Space>
                <Text strong>{username}</Text>
                <Text type="secondary">{createdDate}</Text>
                <Tag color={action === '新增' ? 'cyan' : 'green'}>{action}</Tag>
              </Space>
            </div>
            <Text type="secondary" style={{ whiteSpace: 'pre-line' }}>
              {content}
            </Text>
          </>
        );
        return {
          color: index === 0 ? token.colorPrimary : token.colorTextQuaternary,
          children,
        };
      }),
    [data],
  );
  if (isApprovalPage || !items?.length) return <></>;
  return (
    <Spin spinning={loading}>
      <Collapse defaultActiveKey={[]} ghost collapsible="header">
        <Collapse.Panel key="1" header="变更记录 ">
          <Timeline items={items} />
        </Collapse.Panel>
      </Collapse>
    </Spin>
  );
};

export default memo(ChangeLog);
