import { getRandomId } from '@/utils';
import type {
  EditableFormInstance,
  EditableProTableProps,
  ProColumns,
} from '@ant-design/pro-components';
import { EditableProTable } from '@ant-design/pro-components';
import { Button, Space } from 'antd';
import { produce } from 'immer';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

type ConfigProps = {
  copyType?: 'end' | 'next';
  ignoreField?: string[];
  width?: string | number;
};

type RKEditableProTableProps<T = Record<string, any>> = EditableProTableProps<
  T,
  Record<string, any>
> & {
  columns: ProColumns<T>[];
  value?: T[];
  onChange?: (value: T[]) => void;
  copy?: boolean;
  isDelete?: boolean;
  readonly?: boolean;
  disabled?: boolean;
  operatorConfig?: ConfigProps;
  /**
   * 创建按钮的禁用
   */
  createBtnDisabled?: boolean;
};

const RKFormEditableProTable: React.FC<RKEditableProTableProps> = ({
  columns,
  value,
  onChange,
  copy = true,
  isDelete = true,
  readonly = false,
  operatorConfig,
  disabled,
  createBtnDisabled,
  ...restProps
}) => {
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>();
  useEffect(() => {
    setEditableRowKeys(value?.map((item) => !item.readonly && (item.id || item.key_)));
  }, [value]);

  const editorFormRef = useRef<EditableFormInstance>();
  const operationColumn: ProColumns<Record<string, any>>[] =
    readonly || (!isDelete && !copy)
      ? []
      : [
          {
            title: '操作',
            valueType: 'option',
            width: operatorConfig?.width || 130,
            fixed: 'right',
            align: 'center',
          },
        ];

  const customColumns = useMemo(
    () => [
      ...columns.map((item) => {
        if (item.fieldProps) {
          const updatedFieldProps =
            typeof item.fieldProps === 'function'
              ? (value: any, config: any) => {
                  if (readonly) {
                    // @ts-ignore
                    const originalProps = item.fieldProps(value, config);
                    return {
                      ...originalProps,
                      disabled: true,
                    };
                  }
                  // @ts-ignore
                  return item.fieldProps(value, config);
                }
              : {
                  disabled: readonly,
                  ...item.fieldProps,
                };

          return {
            ...item,
            fieldProps: updatedFieldProps,
          };
        } else {
          return { ...item, fieldProps: { disabled: readonly } };
          // 没有 fieldProps，返回原 item
        }
      }),
      ...operationColumn,
    ],
    [columns, readonly, operationColumn],
  );

  const renderCopyBtn = (row: Record<string, any>) => {
    if (!copy) return;
    let newRow = { ...row };
    if (operatorConfig?.ignoreField?.length) {
      operatorConfig?.ignoreField?.forEach((item) => {
        delete newRow[item];
      });
    }

    if (operatorConfig?.copyType === 'next') {
      return (
        <Button
          type="link"
          className="inner-table-link"
          disabled={disabled || createBtnDisabled}
          onClick={() => {
            const rowIndex = value?.findIndex((item) => item.key_ === newRow.key_) || 0;
            const updateRow = {
              ...newRow,
              key_: getRandomId(),
            };
            const updateData = produce(value || [], (draft) => {
              draft.splice(rowIndex + 1, 0, updateRow);
              return draft;
            });
            onChange?.(updateData);
          }}
        >
          复制此行
        </Button>
      );
    }
    return (
      <EditableProTable.RecordCreator
        key="copy"
        newRecordType="dataSource"
        record={{
          ...newRow,
          key_: getRandomId(),
        }}
      >
        <Button type="link" className="inner-table-link" disabled={disabled || createBtnDisabled}>
          复制此行
        </Button>
      </EditableProTable.RecordCreator>
    );
  };

  const actionRender = useCallback(
    (row: Record<string, any>) => {
      return (
        <Space>
          {isDelete && (
            <Button
              type="link"
              className="inner-table-link"
              key="delete"
              disabled={disabled ?? row?.disabledDelete}
              onClick={() => {
                onChange?.(value?.filter((item) => item.key_ !== row.key_) || []);
              }}
            >
              删除
            </Button>
          )}

          {renderCopyBtn(row)}
        </Space>
      );
    },
    [isDelete, disabled, value, onChange, renderCopyBtn],
  );

  return (
    <EditableProTable
      className="inner-table"
      rowKey={(record) => record?.id || record?.key_}
      editableFormRef={editorFormRef}
      recordCreatorProps={{
        newRecordType: 'dataSource',
        disabled: disabled || createBtnDisabled,
        record: () => ({ key_: getRandomId() }),
      }}
      columns={customColumns}
      value={value}
      scroll={{ x: 'max-content' }}
      editable={{
        type: 'multiple',
        editableKeys,
        onChange: setEditableRowKeys,
        // @ts-ignore
        actionRender,
        onValuesChange: (record, recordList) => {
          onChange?.(recordList);
        },
      }}
      {...restProps}
    />
  );
};

export default RKFormEditableProTable;
