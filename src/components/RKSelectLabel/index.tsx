import { Typography } from 'antd';
import React from 'react';

const { Text } = Typography;

const RKSelectLabel: React.FC<{
  title?: string;
  info?: string;
  disabled?: boolean;
}> = ({ title = '', info = '', disabled = false }) => {
  return (
    <div>
      <Text strong disabled={disabled}>
        {title}
      </Text>
      <br />
      <Text type="secondary" disabled={disabled} style={{ whiteSpace: 'normal' }}>
        {info}
      </Text>
    </div>
  );
};

export default RKSelectLabel;
