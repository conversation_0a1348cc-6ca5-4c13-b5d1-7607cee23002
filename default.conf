server {
    listen       80;
    listen  [::]:80;
    server_name  localhost;
    root   /usr/share/nginx/html;
    index  index.html index.htm;
    absolute_redirect off;
    client_max_body_size 20m;

    location / {
        try_files $uri $uri/ /index.html;
    }
    location ^~ /oa {
      proxy_set_header Origin '';
      add_header Access-Control-Allow-Credentials true;
      add_header Access-Control-Allow-Headers $http_access_control_request_headers;
      add_header Access-Control-Allow-Methods POST,GET,OPTIONS,DELETE,PUT,HEAD,PATCH;
      add_header Access-Control-Allow-Origin $http_origin;
      add_header Access-Control-Expose-Headers $http_access_control_request_headers;
      if ($request_method = 'OPTIONS') {
        return 204;
      }
      if ($request_method != 'OPTIONS'){
        proxy_pass "http://oa-service:8080";
      }
    }
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
