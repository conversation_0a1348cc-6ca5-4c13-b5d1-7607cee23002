stages:
  - npm-install
  - trigger
variables:
  VERSION: '$CI_COMMIT_REF_NAME-$CI_COMMIT_SHA'
  FRONTEND_BRANCH_NAME: $CI_COMMIT_REF_NAME
  TOKEN: glptt-a911b7b7e09ab038e48581bebae9e455f6bfabf3
  SERVER_PROJECT_ID: 66
include:
  - project: 'develop/devops-projects/ci-template'
    ref: main
    file: 'yarn-cache-build.gitlab-ci.yml'
  - project: 'develop/devops-projects/ci-template'
    ref: main
    file: 'npm-trigger.gitlab-ci.yml'
